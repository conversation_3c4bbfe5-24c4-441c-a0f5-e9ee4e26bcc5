import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { UIComponent, UIConfiguration, Position, Size } from '@types/index';

interface UiBuilderState {
  // Current configuration being edited
  currentConfiguration: UIConfiguration | null;
  
  // Canvas state
  canvas: {
    zoom: number;
    pan: Position;
    gridSize: number;
    showGrid: boolean;
    showRulers: boolean;
    showGuides: boolean;
    snapToGrid: boolean;
  };
  
  // Selection state
  selection: {
    selectedComponentIds: string[];
    hoveredComponentId: string | null;
    multiSelectMode: boolean;
  };
  
  // Clipboard
  clipboard: {
    components: UIComponent[];
    operation: 'copy' | 'cut' | null;
  };
  
  // History for undo/redo
  history: {
    past: UIConfiguration[];
    present: UIConfiguration | null;
    future: UIConfiguration[];
    maxHistorySize: number;
  };
  
  // UI state
  ui: {
    leftPanelWidth: number;
    rightPanelWidth: number;
    leftPanelCollapsed: boolean;
    rightPanelCollapsed: boolean;
    activeLeftTab: string;
    activeRightTab: string;
    previewMode: boolean;
    devicePreview: 'desktop' | 'tablet' | 'mobile';
  };
  
  // Drag and drop state
  dragDrop: {
    isDragging: boolean;
    draggedComponent: UIComponent | null;
    dropTarget: string | null;
    dropPosition: 'before' | 'after' | 'inside' | null;
  };
  
  // Loading states
  loading: {
    saving: boolean;
    loading: boolean;
    publishing: boolean;
  };
  
  // Error state
  error: string | null;
}

const initialState: UiBuilderState = {
  currentConfiguration: null,
  canvas: {
    zoom: 1,
    pan: { x: 0, y: 0 },
    gridSize: 20,
    showGrid: true,
    showRulers: true,
    showGuides: true,
    snapToGrid: true,
  },
  selection: {
    selectedComponentIds: [],
    hoveredComponentId: null,
    multiSelectMode: false,
  },
  clipboard: {
    components: [],
    operation: null,
  },
  history: {
    past: [],
    present: null,
    future: [],
    maxHistorySize: 50,
  },
  ui: {
    leftPanelWidth: 300,
    rightPanelWidth: 350,
    leftPanelCollapsed: false,
    rightPanelCollapsed: false,
    activeLeftTab: 'components',
    activeRightTab: 'properties',
    previewMode: false,
    devicePreview: 'desktop',
  },
  dragDrop: {
    isDragging: false,
    draggedComponent: null,
    dropTarget: null,
    dropPosition: null,
  },
  loading: {
    saving: false,
    loading: false,
    publishing: false,
  },
  error: null,
};

const uiBuilderSlice = createSlice({
  name: 'uiBuilder',
  initialState,
  reducers: {
    // Configuration actions
    setCurrentConfiguration: (state, action: PayloadAction<UIConfiguration>) => {
      state.currentConfiguration = action.payload;
      state.history.present = action.payload;
      state.history.past = [];
      state.history.future = [];
    },
    
    clearCurrentConfiguration: (state) => {
      state.currentConfiguration = null;
      state.history.present = null;
      state.history.past = [];
      state.history.future = [];
      state.selection.selectedComponentIds = [];
      state.selection.hoveredComponentId = null;
    },
    
    // Component actions
    addComponent: (state, action: PayloadAction<{ component: UIComponent; parentId?: string; index?: number }>) => {
      if (!state.currentConfiguration) return;
      
      const { component, parentId, index } = action.payload;
      
      if (parentId) {
        // Add to specific parent
        const parent = findComponentById(state.currentConfiguration.components, parentId);
        if (parent) {
          if (!parent.children) parent.children = [];
          if (index !== undefined) {
            parent.children.splice(index, 0, component);
          } else {
            parent.children.push(component);
          }
          component.parentId = parentId;
        }
      } else {
        // Add to root level
        if (index !== undefined) {
          state.currentConfiguration.components.splice(index, 0, component);
        } else {
          state.currentConfiguration.components.push(component);
        }
      }
      
      // Select the new component
      state.selection.selectedComponentIds = [component.id];
      
      // Add to history
      addToHistory(state);
    },
    
    updateComponent: (state, action: PayloadAction<{ id: string; updates: Partial<UIComponent> }>) => {
      if (!state.currentConfiguration) return;
      
      const { id, updates } = action.payload;
      const component = findComponentById(state.currentConfiguration.components, id);
      
      if (component) {
        Object.assign(component, updates);
        addToHistory(state);
      }
    },
    
    deleteComponent: (state, action: PayloadAction<string>) => {
      if (!state.currentConfiguration) return;
      
      const componentId = action.payload;
      removeComponentById(state.currentConfiguration.components, componentId);
      
      // Remove from selection
      state.selection.selectedComponentIds = state.selection.selectedComponentIds.filter(
        id => id !== componentId
      );
      
      // Clear hover if it was the deleted component
      if (state.selection.hoveredComponentId === componentId) {
        state.selection.hoveredComponentId = null;
      }
      
      addToHistory(state);
    },
    
    moveComponent: (state, action: PayloadAction<{ 
      componentId: string; 
      newParentId?: string; 
      newIndex: number 
    }>) => {
      if (!state.currentConfiguration) return;
      
      const { componentId, newParentId, newIndex } = action.payload;
      
      // Remove component from current location
      const component = removeComponentById(state.currentConfiguration.components, componentId);
      if (!component) return;
      
      // Add to new location
      if (newParentId) {
        const newParent = findComponentById(state.currentConfiguration.components, newParentId);
        if (newParent) {
          if (!newParent.children) newParent.children = [];
          newParent.children.splice(newIndex, 0, component);
          component.parentId = newParentId;
        }
      } else {
        state.currentConfiguration.components.splice(newIndex, 0, component);
        component.parentId = undefined;
      }
      
      addToHistory(state);
    },
    
    duplicateComponent: (state, action: PayloadAction<string>) => {
      if (!state.currentConfiguration) return;
      
      const componentId = action.payload;
      const component = findComponentById(state.currentConfiguration.components, componentId);
      
      if (component) {
        const duplicated = duplicateComponentRecursive(component);
        
        // Add duplicated component next to original
        if (component.parentId) {
          const parent = findComponentById(state.currentConfiguration.components, component.parentId);
          if (parent && parent.children) {
            const index = parent.children.findIndex(c => c.id === componentId);
            parent.children.splice(index + 1, 0, duplicated);
          }
        } else {
          const index = state.currentConfiguration.components.findIndex(c => c.id === componentId);
          state.currentConfiguration.components.splice(index + 1, 0, duplicated);
        }
        
        // Select the duplicated component
        state.selection.selectedComponentIds = [duplicated.id];
        
        addToHistory(state);
      }
    },
    
    // Selection actions
    selectComponent: (state, action: PayloadAction<string>) => {
      const componentId = action.payload;
      
      if (state.selection.multiSelectMode) {
        if (state.selection.selectedComponentIds.includes(componentId)) {
          state.selection.selectedComponentIds = state.selection.selectedComponentIds.filter(
            id => id !== componentId
          );
        } else {
          state.selection.selectedComponentIds.push(componentId);
        }
      } else {
        state.selection.selectedComponentIds = [componentId];
      }
    },
    
    selectMultipleComponents: (state, action: PayloadAction<string[]>) => {
      state.selection.selectedComponentIds = action.payload;
    },
    
    clearSelection: (state) => {
      state.selection.selectedComponentIds = [];
    },
    
    setHoveredComponent: (state, action: PayloadAction<string | null>) => {
      state.selection.hoveredComponentId = action.payload;
    },
    
    setMultiSelectMode: (state, action: PayloadAction<boolean>) => {
      state.selection.multiSelectMode = action.payload;
    },
    
    // Clipboard actions
    copyComponents: (state, action: PayloadAction<string[]>) => {
      if (!state.currentConfiguration) return;
      
      const componentIds = action.payload;
      const components = componentIds
        .map(id => findComponentById(state.currentConfiguration!.components, id))
        .filter(Boolean) as UIComponent[];
      
      state.clipboard.components = components.map(duplicateComponentRecursive);
      state.clipboard.operation = 'copy';
    },
    
    cutComponents: (state, action: PayloadAction<string[]>) => {
      if (!state.currentConfiguration) return;
      
      const componentIds = action.payload;
      const components = componentIds
        .map(id => findComponentById(state.currentConfiguration!.components, id))
        .filter(Boolean) as UIComponent[];
      
      state.clipboard.components = components.map(duplicateComponentRecursive);
      state.clipboard.operation = 'cut';
      
      // Remove components from configuration
      componentIds.forEach(id => {
        removeComponentById(state.currentConfiguration!.components, id);
      });
      
      // Clear selection
      state.selection.selectedComponentIds = [];
      
      addToHistory(state);
    },
    
    pasteComponents: (state, action: PayloadAction<{ parentId?: string; index?: number }>) => {
      if (!state.currentConfiguration || state.clipboard.components.length === 0) return;
      
      const { parentId, index } = action.payload;
      const pastedComponents = state.clipboard.components.map(duplicateComponentRecursive);
      
      pastedComponents.forEach((component, i) => {
        if (parentId) {
          const parent = findComponentById(state.currentConfiguration!.components, parentId);
          if (parent) {
            if (!parent.children) parent.children = [];
            const insertIndex = index !== undefined ? index + i : parent.children.length;
            parent.children.splice(insertIndex, 0, component);
            component.parentId = parentId;
          }
        } else {
          const insertIndex = index !== undefined ? index + i : state.currentConfiguration!.components.length;
          state.currentConfiguration!.components.splice(insertIndex, 0, component);
        }
      });
      
      // Select pasted components
      state.selection.selectedComponentIds = pastedComponents.map(c => c.id);
      
      // Clear clipboard if it was a cut operation
      if (state.clipboard.operation === 'cut') {
        state.clipboard.components = [];
        state.clipboard.operation = null;
      }
      
      addToHistory(state);
    },
    
    // Canvas actions
    setCanvasZoom: (state, action: PayloadAction<number>) => {
      state.canvas.zoom = Math.max(0.1, Math.min(5, action.payload));
    },
    
    setCanvasPan: (state, action: PayloadAction<Position>) => {
      state.canvas.pan = action.payload;
    },
    
    setGridSize: (state, action: PayloadAction<number>) => {
      state.canvas.gridSize = Math.max(5, Math.min(100, action.payload));
    },
    
    toggleGrid: (state) => {
      state.canvas.showGrid = !state.canvas.showGrid;
    },
    
    toggleRulers: (state) => {
      state.canvas.showRulers = !state.canvas.showRulers;
    },
    
    toggleGuides: (state) => {
      state.canvas.showGuides = !state.canvas.showGuides;
    },
    
    toggleSnapToGrid: (state) => {
      state.canvas.snapToGrid = !state.canvas.snapToGrid;
    },
    
    // History actions
    undo: (state) => {
      if (state.history.past.length === 0) return;
      
      const previous = state.history.past[state.history.past.length - 1];
      const newPast = state.history.past.slice(0, state.history.past.length - 1);
      
      if (state.history.present) {
        state.history.future = [state.history.present, ...state.history.future];
      }
      
      state.history.past = newPast;
      state.history.present = previous;
      state.currentConfiguration = previous;
    },
    
    redo: (state) => {
      if (state.history.future.length === 0) return;
      
      const next = state.history.future[0];
      const newFuture = state.history.future.slice(1);
      
      if (state.history.present) {
        state.history.past = [...state.history.past, state.history.present];
      }
      
      state.history.future = newFuture;
      state.history.present = next;
      state.currentConfiguration = next;
    },
    
    // UI actions
    setLeftPanelWidth: (state, action: PayloadAction<number>) => {
      state.ui.leftPanelWidth = Math.max(200, Math.min(600, action.payload));
    },
    
    setRightPanelWidth: (state, action: PayloadAction<number>) => {
      state.ui.rightPanelWidth = Math.max(250, Math.min(800, action.payload));
    },
    
    toggleLeftPanel: (state) => {
      state.ui.leftPanelCollapsed = !state.ui.leftPanelCollapsed;
    },
    
    toggleRightPanel: (state) => {
      state.ui.rightPanelCollapsed = !state.ui.rightPanelCollapsed;
    },
    
    setActiveLeftTab: (state, action: PayloadAction<string>) => {
      state.ui.activeLeftTab = action.payload;
    },
    
    setActiveRightTab: (state, action: PayloadAction<string>) => {
      state.ui.activeRightTab = action.payload;
    },
    
    togglePreviewMode: (state) => {
      state.ui.previewMode = !state.ui.previewMode;
    },
    
    setDevicePreview: (state, action: PayloadAction<'desktop' | 'tablet' | 'mobile'>) => {
      state.ui.devicePreview = action.payload;
    },
    
    // Drag and drop actions
    startDrag: (state, action: PayloadAction<UIComponent>) => {
      state.dragDrop.isDragging = true;
      state.dragDrop.draggedComponent = action.payload;
    },
    
    endDrag: (state) => {
      state.dragDrop.isDragging = false;
      state.dragDrop.draggedComponent = null;
      state.dragDrop.dropTarget = null;
      state.dragDrop.dropPosition = null;
    },
    
    setDropTarget: (state, action: PayloadAction<{ 
      targetId: string | null; 
      position: 'before' | 'after' | 'inside' | null 
    }>) => {
      state.dragDrop.dropTarget = action.payload.targetId;
      state.dragDrop.dropPosition = action.payload.position;
    },
    
    // Loading actions
    setSaving: (state, action: PayloadAction<boolean>) => {
      state.loading.saving = action.payload;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.loading = action.payload;
    },
    
    setPublishing: (state, action: PayloadAction<boolean>) => {
      state.loading.publishing = action.payload;
    },
    
    // Error actions
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

// Helper functions
function findComponentById(components: UIComponent[], id: string): UIComponent | null {
  for (const component of components) {
    if (component.id === id) {
      return component;
    }
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }
  return null;
}

function removeComponentById(components: UIComponent[], id: string): UIComponent | null {
  for (let i = 0; i < components.length; i++) {
    if (components[i].id === id) {
      return components.splice(i, 1)[0];
    }
    if (components[i].children) {
      const found = removeComponentById(components[i].children!, id);
      if (found) return found;
    }
  }
  return null;
}

function duplicateComponentRecursive(component: UIComponent): UIComponent {
  const duplicated: UIComponent = {
    ...component,
    id: `${component.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    children: component.children?.map(duplicateComponentRecursive),
  };
  
  // Update parent references in children
  if (duplicated.children) {
    duplicated.children.forEach(child => {
      child.parentId = duplicated.id;
    });
  }
  
  return duplicated;
}

function addToHistory(state: UiBuilderState) {
  if (!state.currentConfiguration) return;
  
  const newPresent = JSON.parse(JSON.stringify(state.currentConfiguration));
  
  if (state.history.present) {
    state.history.past = [...state.history.past, state.history.present];
    
    // Limit history size
    if (state.history.past.length > state.history.maxHistorySize) {
      state.history.past = state.history.past.slice(-state.history.maxHistorySize);
    }
  }
  
  state.history.present = newPresent;
  state.history.future = [];
}

export const {
  setCurrentConfiguration,
  clearCurrentConfiguration,
  addComponent,
  updateComponent,
  deleteComponent,
  moveComponent,
  duplicateComponent,
  selectComponent,
  selectMultipleComponents,
  clearSelection,
  setHoveredComponent,
  setMultiSelectMode,
  copyComponents,
  cutComponents,
  pasteComponents,
  setCanvasZoom,
  setCanvasPan,
  setGridSize,
  toggleGrid,
  toggleRulers,
  toggleGuides,
  toggleSnapToGrid,
  undo,
  redo,
  setLeftPanelWidth,
  setRightPanelWidth,
  toggleLeftPanel,
  toggleRightPanel,
  setActiveLeftTab,
  setActiveRightTab,
  togglePreviewMode,
  setDevicePreview,
  startDrag,
  endDrag,
  setDropTarget,
  setSaving,
  setLoading,
  setPublishing,
  setError,
} = uiBuilderSlice.actions;

export default uiBuilderSlice.reducer;
