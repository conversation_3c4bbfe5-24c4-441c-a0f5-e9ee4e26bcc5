import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/ui_metadata.dart';

/// API Service for Flutter UI Runtime
/// 
/// Provides HTTP client functionality with:
/// - Automatic authentication handling
/// - Request/response interceptors
/// - Error handling and retry logic
/// - Caching strategies
/// - Offline support

class ApiService {
  late final Dio _dio;
  late final Box _cacheBox;
  
  static const String baseUrl = 'https://api.ui-builder.com';
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration cacheTimeout = Duration(hours: 1);

  ApiService() {
    _initializeDio();
    _initializeCache();
  }

  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: defaultTimeout,
      receiveTimeout: defaultTimeout,
      sendTimeout: defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.addAll([
      _AuthInterceptor(),
      _CacheInterceptor(),
      _ErrorInterceptor(),
      _LoggingInterceptor(),
    ]);
  }

  Future<void> _initializeCache() async {
    _cacheBox = await Hive.openBox('api_cache');
  }

  /// Get UI configuration by ID
  Future<UIConfiguration?> getConfiguration(String configId) async {
    try {
      final response = await _dio.get('/configurations/$configId');
      return UIConfiguration.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get all configurations for user
  Future<List<UIConfiguration>> getConfigurations({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
  }) async {
    try {
      final response = await _dio.get('/configurations', queryParameters: {
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (category != null) 'category': category,
      });
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.map((json) => UIConfiguration.fromJson(json)).toList();
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Create new configuration
  Future<UIConfiguration> createConfiguration(UIConfiguration config) async {
    try {
      final response = await _dio.post('/configurations', data: config.toJson());
      return UIConfiguration.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Update existing configuration
  Future<UIConfiguration> updateConfiguration(UIConfiguration config) async {
    try {
      final response = await _dio.put('/configurations/${config.id}', data: config.toJson());
      return UIConfiguration.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Delete configuration
  Future<void> deleteConfiguration(String configId) async {
    try {
      await _dio.delete('/configurations/$configId');
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get templates
  Future<List<Template>> getTemplates({
    int page = 1,
    int limit = 20,
    String? category,
    bool? isPublic,
  }) async {
    try {
      final response = await _dio.get('/templates', queryParameters: {
        'page': page,
        'limit': limit,
        if (category != null) 'category': category,
        if (isPublic != null) 'public': isPublic,
      });
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.map((json) => Template.fromJson(json)).toList();
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get template by ID
  Future<Template?> getTemplate(String templateId) async {
    try {
      final response = await _dio.get('/templates/$templateId');
      return Template.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get user profile
  Future<UserProfile?> getUserProfile() async {
    try {
      final response = await _dio.get('/user/profile');
      return UserProfile.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Update user profile
  Future<UserProfile> updateUserProfile(UserProfile profile) async {
    try {
      final response = await _dio.put('/user/profile', data: profile.toJson());
      return UserProfile.fromJson(response.data);
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Upload file
  Future<String> uploadFile(String filePath, {String? folder}) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath),
        if (folder != null) 'folder': folder,
      });

      final response = await _dio.post('/upload', data: formData);
      return response.data['url'] as String;
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get analytics data
  Future<Map<String, dynamic>> getAnalytics({
    String? configId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final response = await _dio.get('/analytics', queryParameters: {
        if (configId != null) 'configId': configId,
        if (startDate != null) 'startDate': startDate.toIso8601String(),
        if (endDate != null) 'endDate': endDate.toIso8601String(),
      });
      
      return response.data as Map<String, dynamic>;
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Search configurations
  Future<List<UIConfiguration>> searchConfigurations(String query) async {
    try {
      final response = await _dio.get('/search/configurations', queryParameters: {
        'q': query,
      });
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.map((json) => UIConfiguration.fromJson(json)).toList();
    } catch (e) {
      throw ApiException.fromError(e);
    }
  }

  /// Get cached data
  T? getCachedData<T>(String key) {
    try {
      final cached = _cacheBox.get(key);
      if (cached != null) {
        final cacheData = CacheData.fromJson(cached);
        if (!cacheData.isExpired) {
          return cacheData.data as T;
        } else {
          _cacheBox.delete(key);
        }
      }
    } catch (e) {
      // Ignore cache errors
    }
    return null;
  }

  /// Set cached data
  Future<void> setCachedData(String key, dynamic data, {Duration? ttl}) async {
    try {
      final cacheData = CacheData(
        data: data,
        timestamp: DateTime.now(),
        ttl: ttl ?? cacheTimeout,
      );
      await _cacheBox.put(key, cacheData.toJson());
    } catch (e) {
      // Ignore cache errors
    }
  }

  /// Clear cache
  Future<void> clearCache() async {
    try {
      await _cacheBox.clear();
    } catch (e) {
      // Ignore cache errors
    }
  }
}

/// Authentication Interceptor
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Add authentication token
    final token = await _getAuthToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      final refreshed = await _refreshToken();
      if (refreshed) {
        // Retry the request
        final options = err.requestOptions;
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        try {
          final response = await Dio().fetch(options);
          handler.resolve(response);
          return;
        } catch (e) {
          // Refresh failed, continue with original error
        }
      }
    }
    handler.next(err);
  }

  Future<String?> _getAuthToken() async {
    // Get token from secure storage
    return null; // Placeholder
  }

  Future<bool> _refreshToken() async {
    // Refresh token logic
    return false; // Placeholder
  }
}

/// Cache Interceptor
class _CacheInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Check cache for GET requests
    if (options.method == 'GET') {
      final cacheKey = _generateCacheKey(options);
      // Cache logic would go here
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Cache successful GET responses
    if (response.requestOptions.method == 'GET' && response.statusCode == 200) {
      final cacheKey = _generateCacheKey(response.requestOptions);
      // Cache logic would go here
    }
    handler.next(response);
  }

  String _generateCacheKey(RequestOptions options) {
    return '${options.method}_${options.path}_${options.queryParameters.toString()}';
  }
}

/// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle different types of errors
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout) {
      // Retry logic for timeout errors
      _retryRequest(err, handler);
      return;
    }

    handler.next(err);
  }

  void _retryRequest(DioException err, ErrorInterceptorHandler handler) {
    // Implement retry logic with exponential backoff
    handler.next(err);
  }
}

/// Logging Interceptor
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print('API Request: ${options.method} ${options.path}');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print('API Response: ${response.statusCode} ${response.requestOptions.path}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    print('API Error: ${err.message}');
    handler.next(err);
  }
}

/// API Exception class
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final Map<String, dynamic>? details;

  ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
    this.details,
  });

  factory ApiException.fromError(dynamic error) {
    if (error is DioException) {
      return ApiException(
        message: error.message ?? 'Unknown error',
        statusCode: error.response?.statusCode,
        errorCode: error.response?.data?['code'],
        details: error.response?.data,
      );
    }
    
    return ApiException(message: error.toString());
  }

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $errorCode)';
  }
}

/// Cache Data model
class CacheData {
  final dynamic data;
  final DateTime timestamp;
  final Duration ttl;

  CacheData({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;

  Map<String, dynamic> toJson() => {
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'ttl': ttl.inMilliseconds,
  };

  factory CacheData.fromJson(Map<String, dynamic> json) => CacheData(
    data: json['data'],
    timestamp: DateTime.parse(json['timestamp']),
    ttl: Duration(milliseconds: json['ttl']),
  );
}

/// Providers
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

final configurationsProvider = FutureProvider.family<List<UIConfiguration>, Map<String, dynamic>>((ref, params) async {
  final apiService = ref.read(apiServiceProvider);
  return apiService.getConfigurations(
    page: params['page'] ?? 1,
    limit: params['limit'] ?? 20,
    search: params['search'],
    category: params['category'],
  );
});

final configurationProvider = FutureProvider.family<UIConfiguration?, String>((ref, configId) async {
  final apiService = ref.read(apiServiceProvider);
  return apiService.getConfiguration(configId);
});

final templatesProvider = FutureProvider.family<List<Template>, Map<String, dynamic>>((ref, params) async {
  final apiService = ref.read(apiServiceProvider);
  return apiService.getTemplates(
    page: params['page'] ?? 1,
    limit: params['limit'] ?? 20,
    category: params['category'],
    isPublic: params['isPublic'],
  );
});

final userProfileProvider = FutureProvider<UserProfile?>((ref) async {
  final apiService = ref.read(apiServiceProvider);
  return apiService.getUserProfile();
});
