import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Badge component
class UIBadge extends StatelessWidget {
  const UIBadge({
    super.key,
    required this.child,
    this.variant = UIBadgeVariant.primary,
    this.size = UISize.md,
    this.shape = UIBadgeShape.rounded,
    this.icon,
    this.onTap,
    this.onClose,
  });

  /// Named constructor for text badge
  const UIBadge.text({
    super.key,
    required String text,
    this.variant = UIBadgeVariant.primary,
    this.size = UISize.md,
    this.shape = UIBadgeShape.rounded,
    this.icon,
    this.onTap,
    this.onClose,
  }) : child = Text(text);

  /// Named constructor for count badge
  const UIBadge.count({
    super.key,
    required int count,
    this.variant = UIBadgeVariant.primary,
    this.size = UISize.md,
    this.shape = UIBadgeShape.rounded,
    this.icon,
    this.onTap,
    this.onClose,
  }) : child = Text(count.toString());

  final Widget child;
  final UIBadgeVariant variant;
  final UISize size;
  final UIBadgeShape shape;
  final IconData? icon;
  final VoidCallback? onTap;
  final VoidCallback? onClose;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final colors = _getVariantColors(colorScheme);
    final padding = _getSizePadding();
    final borderRadius = _getShapeBorderRadius();

    List<Widget> children = [];

    if (icon != null) {
      children.add(Icon(
        icon,
        size: _getIconSize(),
        color: colors.foreground,
      ));
      children.add(SizedBox(width: tokens.spacing.size1));
    }

    children.add(
      DefaultTextStyle(
        style: TextStyle(
          color: colors.foreground,
          fontSize: _getFontSize(),
          fontWeight: FontWeight.w500,
        ),
        child: child,
      ),
    );

    if (onClose != null) {
      children.add(SizedBox(width: tokens.spacing.size1));
      children.add(
        GestureDetector(
          onTap: onClose,
          child: Icon(
            Icons.close,
            size: _getIconSize(),
            color: colors.foreground,
          ),
        ),
      );
    }

    Widget badge = Container(
      padding: padding,
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: borderRadius,
        border: variant == UIBadgeVariant.outline 
          ? Border.all(color: colors.border!)
          : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );

    if (onTap != null) {
      badge = GestureDetector(
        onTap: onTap,
        child: badge,
      );
    }

    return badge;
  }

  _BadgeColors _getVariantColors(ColorScheme colorScheme) {
    switch (variant) {
      case UIBadgeVariant.primary:
        return _BadgeColors(
          background: colorScheme.primary,
          foreground: colorScheme.onPrimary,
        );
      case UIBadgeVariant.secondary:
        return _BadgeColors(
          background: colorScheme.secondary,
          foreground: colorScheme.onSecondary,
        );
      case UIBadgeVariant.success:
        return _BadgeColors(
          background: const Color(0xFF10B981),
          foreground: Colors.white,
        );
      case UIBadgeVariant.warning:
        return _BadgeColors(
          background: const Color(0xFFF59E0B),
          foreground: Colors.white,
        );
      case UIBadgeVariant.error:
        return _BadgeColors(
          background: colorScheme.error,
          foreground: colorScheme.onError,
        );
      case UIBadgeVariant.info:
        return _BadgeColors(
          background: const Color(0xFF3B82F6),
          foreground: Colors.white,
        );
      case UIBadgeVariant.outline:
        return _BadgeColors(
          background: Colors.transparent,
          foreground: colorScheme.onSurface,
          border: colorScheme.outline,
        );
    }
  }

  EdgeInsets _getSizePadding() {
    final tokens = DesignTokens.instance;
    switch (size) {
      case UISize.xs:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size1,
          vertical: tokens.spacing.size0_5,
        );
      case UISize.sm:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size1_5,
          vertical: tokens.spacing.size1,
        );
      case UISize.md:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size2,
          vertical: tokens.spacing.size1,
        );
      case UISize.lg:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size2_5,
          vertical: tokens.spacing.size1_5,
        );
      case UISize.xl:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size3,
          vertical: tokens.spacing.size2,
        );
      case UISize.xxl:
        return EdgeInsets.symmetric(
          horizontal: tokens.spacing.size4,
          vertical: tokens.spacing.size2_5,
        );
    }
  }

  BorderRadius _getShapeBorderRadius() {
    final tokens = DesignTokens.instance;
    switch (shape) {
      case UIBadgeShape.square:
        return BorderRadius.zero;
      case UIBadgeShape.rounded:
        return tokens.borderRadius.md;
      case UIBadgeShape.pill:
        return tokens.borderRadius.full;
    }
  }

  double _getIconSize() {
    switch (size) {
      case UISize.xs:
        return 12;
      case UISize.sm:
        return 14;
      case UISize.md:
        return 16;
      case UISize.lg:
        return 18;
      case UISize.xl:
        return 20;
      case UISize.xxl:
        return 24;
    }
  }

  double _getFontSize() {
    switch (size) {
      case UISize.xs:
        return 10;
      case UISize.sm:
        return 12;
      case UISize.md:
        return 14;
      case UISize.lg:
        return 16;
      case UISize.xl:
        return 18;
      case UISize.xxl:
        return 20;
    }
  }
}

/// Badge shape enum
enum UIBadgeShape {
  square,
  rounded,
  pill,
}

/// Badge colors helper class
class _BadgeColors {
  const _BadgeColors({
    required this.background,
    required this.foreground,
    this.border,
  });

  final Color background;
  final Color foreground;
  final Color? border;
}
