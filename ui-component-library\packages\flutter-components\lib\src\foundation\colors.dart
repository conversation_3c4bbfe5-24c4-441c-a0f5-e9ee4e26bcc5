import 'package:flutter/material.dart';

/// UI Builder color system
class UIBuilderColors {
  const UIBuilderColors._();

  // Primary colors
  static const Color primary50 = Color(0xFFF0F9FF);
  static const Color primary100 = Color(0xFFE0F2FE);
  static const Color primary200 = Color(0xFFBAE6FD);
  static const Color primary300 = Color(0xFF7DD3FC);
  static const Color primary400 = Color(0xFF38BDF8);
  static const Color primary500 = Color(0xFF0EA5E9);
  static const Color primary600 = Color(0xFF0284C7);
  static const Color primary700 = Color(0xFF0369A1);
  static const Color primary800 = Color(0xFF075985);
  static const Color primary900 = Color(0xFF0C4A6E);

  // Gray colors
  static const Color gray50 = Color(0xFFF9FAFB);
  static const Color gray100 = Color(0xFFF3F4F6);
  static const Color gray200 = Color(0xFFE5E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);

  // Success colors
  static const Color success50 = Color(0xFFF0FDF4);
  static const Color success100 = Color(0xFFDCFCE7);
  static const Color success500 = Color(0xFF10B981);
  static const Color success600 = Color(0xFF059669);
  static const Color success700 = Color(0xFF047857);

  // Warning colors
  static const Color warning50 = Color(0xFFFFFBEB);
  static const Color warning100 = Color(0xFFFEF3C7);
  static const Color warning500 = Color(0xFFF59E0B);
  static const Color warning600 = Color(0xFFD97706);
  static const Color warning700 = Color(0xFFB45309);

  // Error colors
  static const Color error50 = Color(0xFFFEF2F2);
  static const Color error100 = Color(0xFFFEE2E2);
  static const Color error500 = Color(0xFFEF4444);
  static const Color error600 = Color(0xFFDC2626);
  static const Color error700 = Color(0xFFB91C1C);

  // Info colors
  static const Color info50 = Color(0xFFEFF6FF);
  static const Color info100 = Color(0xFFDBEAFE);
  static const Color info500 = Color(0xFF3B82F6);
  static const Color info600 = Color(0xFF2563EB);
  static const Color info700 = Color(0xFF1D4ED8);

  /// Light color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary600,
    onPrimary: Colors.white,
    primaryContainer: primary100,
    onPrimaryContainer: primary900,
    secondary: gray600,
    onSecondary: Colors.white,
    secondaryContainer: gray100,
    onSecondaryContainer: gray900,
    tertiary: info600,
    onTertiary: Colors.white,
    tertiaryContainer: info100,
    onTertiaryContainer: info900,
    error: error600,
    onError: Colors.white,
    errorContainer: error100,
    onErrorContainer: error900,
    background: Colors.white,
    onBackground: gray900,
    surface: Colors.white,
    onSurface: gray900,
    surfaceVariant: gray50,
    onSurfaceVariant: gray700,
    outline: gray300,
    outlineVariant: gray200,
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: gray900,
    onInverseSurface: gray50,
    inversePrimary: primary300,
    surfaceTint: primary600,
  );

  /// Dark color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primary400,
    onPrimary: primary900,
    primaryContainer: primary800,
    onPrimaryContainer: primary100,
    secondary: gray400,
    onSecondary: gray900,
    secondaryContainer: gray800,
    onSecondaryContainer: gray100,
    tertiary: info400,
    onTertiary: info900,
    tertiaryContainer: info800,
    onTertiaryContainer: info100,
    error: error400,
    onError: error900,
    errorContainer: error800,
    onErrorContainer: error100,
    background: gray900,
    onBackground: gray100,
    surface: gray900,
    onSurface: gray100,
    surfaceVariant: gray800,
    onSurfaceVariant: gray300,
    outline: gray600,
    outlineVariant: gray700,
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: gray100,
    onInverseSurface: gray900,
    inversePrimary: primary600,
    surfaceTint: primary400,
  );

  /// Get semantic color by name
  static Color getSemanticColor(String name, {bool isDark = false}) {
    switch (name.toLowerCase()) {
      case 'success':
        return success500;
      case 'warning':
        return warning500;
      case 'error':
        return error500;
      case 'info':
        return info500;
      case 'primary':
        return isDark ? primary400 : primary600;
      case 'secondary':
        return isDark ? gray400 : gray600;
      default:
        return isDark ? gray400 : gray600;
    }
  }

  /// Get color by shade
  static Color getColorByShade(String colorName, int shade) {
    switch (colorName.toLowerCase()) {
      case 'primary':
        switch (shade) {
          case 50: return primary50;
          case 100: return primary100;
          case 200: return primary200;
          case 300: return primary300;
          case 400: return primary400;
          case 500: return primary500;
          case 600: return primary600;
          case 700: return primary700;
          case 800: return primary800;
          case 900: return primary900;
          default: return primary500;
        }
      case 'gray':
        switch (shade) {
          case 50: return gray50;
          case 100: return gray100;
          case 200: return gray200;
          case 300: return gray300;
          case 400: return gray400;
          case 500: return gray500;
          case 600: return gray600;
          case 700: return gray700;
          case 800: return gray800;
          case 900: return gray900;
          default: return gray500;
        }
      default:
        return gray500;
    }
  }
}
