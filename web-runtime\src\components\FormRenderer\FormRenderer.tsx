import React, { useState, useCallback, useMemo, useEffect } from 'react';
// Note: These would need to be installed as dependencies
// import { useForm, Controller, FieldValues, FieldError } from 'react-hook-form';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { z } from 'zod';
import { ComponentInstance, DeviceInfo } from '../../types/component';
import './FormRenderer.scss';

export interface FormField {
  id: string;
  name: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'date' | 'time' | 'datetime' | 'file' | 'range' | 'color' | 'url' | 'tel';
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  
  // Validation
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    custom?: string; // Custom validation expression
  };
  
  // Options for select/radio
  options?: Array<{
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  
  // Default value
  defaultValue?: any;
  
  // Layout
  layout?: {
    span?: number;
    offset?: number;
    order?: number;
  };
  
  // Conditional rendering
  conditional?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists';
    value: any;
  };
  
  // Help text
  helpText?: string;
  
  // Custom attributes
  attributes?: Record<string, any>;
}

export interface FormConfig {
  id: string;
  name: string;
  fields: FormField[];
  
  // Layout
  layout: 'vertical' | 'horizontal' | 'inline' | 'grid';
  columns?: number;
  gap?: string;
  
  // Submission
  submitUrl?: string;
  submitMethod?: 'POST' | 'PUT' | 'PATCH';
  submitHeaders?: Record<string, string>;
  
  // Validation
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
  
  // UI
  showRequiredIndicator?: boolean;
  showErrorSummary?: boolean;
  submitButtonText?: string;
  resetButtonText?: string;
  
  // Callbacks
  onSubmit?: (data: FieldValues) => void | Promise<void>;
  onReset?: () => void;
  onChange?: (data: FieldValues) => void;
  onFieldChange?: (fieldName: string, value: any) => void;
}

export interface FormRendererProps {
  config: FormConfig;
  initialData?: FieldValues;
  deviceInfo?: DeviceInfo;
  className?: string;
  debugMode?: boolean;
  onSubmit?: (data: FieldValues) => void | Promise<void>;
  onError?: (errors: Record<string, FieldError>) => void;
}

export const FormRenderer: React.FC<FormRendererProps> = ({
  config,
  initialData = {},
  deviceInfo,
  className,
  debugMode = false,
  onSubmit,
  onError
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Simple validation (would use Zod in real implementation)
  const validateField = useCallback((field: FormField, value: any): string | null => {
    if (field.required && (!value || value === '')) {
      return `${field.label} is required`;
    }

    if (field.validation) {
      if (field.type === 'email' && value && !/\S+@\S+\.\S+/.test(value)) {
        return 'Invalid email address';
      }

      if (field.validation.minLength && value && value.length < field.validation.minLength) {
        return `Minimum length is ${field.validation.minLength}`;
      }

      if (field.validation.maxLength && value && value.length > field.validation.maxLength) {
        return `Maximum length is ${field.validation.maxLength}`;
      }
    }

    return null;
  }, []);

  // Form state management (simplified without react-hook-form)
  const [formData, setFormData] = useState<FieldValues>(() => ({
    ...getDefaultValues(),
    ...initialData
  }));

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Get default values from form configuration
  function getDefaultValues(): FieldValues {
    const defaults: FieldValues = {};
    config.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        defaults[field.name] = field.defaultValue;
      }
    });
    return defaults;
  }

  // Handle form data changes
  const handleFieldChange = useCallback((fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));

    // Validate field if validateOnChange is enabled
    if (config.validateOnChange) {
      const field = config.fields.find(f => f.name === fieldName);
      if (field) {
        const error = validateField(field, value);
        setErrors(prev => ({
          ...prev,
          [fieldName]: error || ''
        }));
      }
    }

    config.onFieldChange?.(fieldName, value);
  }, [config, validateField]);

  useEffect(() => {
    config.onChange?.(formData);
  }, [formData, config]);

  // Check if field should be visible based on conditional logic
  const isFieldVisible = useCallback((field: FormField): boolean => {
    if (!field.conditional) return true;

    const { field: conditionField, operator, value } = field.conditional;
    const fieldValue = formData[conditionField];

    switch (operator) {
      case 'equals':
        return fieldValue === value;
      case 'not_equals':
        return fieldValue !== value;
      case 'contains':
        return String(fieldValue).includes(String(value));
      case 'greater_than':
        return Number(fieldValue) > Number(value);
      case 'less_than':
        return Number(fieldValue) < Number(value);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
      default:
        return true;
    }
  }, [formData]);

  // Handle form submission
  const onSubmitHandler = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, string> = {};
    config.fields.forEach(field => {
      const error = validateField(field, formData[field.name]);
      if (error) {
        newErrors[field.name] = error;
      }
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      // Call custom onSubmit handler
      if (onSubmit) {
        await onSubmit(formData);
      } else if (config.onSubmit) {
        await config.onSubmit(formData);
      } else if (config.submitUrl) {
        // Default API submission
        const response = await fetch(config.submitUrl, {
          method: config.submitMethod || 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.submitHeaders
          },
          body: JSON.stringify(formData)
        });

        if (!response.ok) {
          throw new Error(`Submission failed: ${response.statusText}`);
        }
      }

      setSubmitSuccess(true);
    } catch (error: any) {
      setSubmitError(error.message || 'Submission failed');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, config, validateField, onSubmit]);

  // Handle form reset
  const onResetHandler = useCallback(() => {
    const defaults = getDefaultValues();
    setFormData(defaults);
    setErrors({});
    setTouched({});
    setSubmitError(null);
    setSubmitSuccess(false);
    config.onReset?.();
  }, [config]);

  // Handle form errors
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const formErrors = Object.entries(errors).reduce((acc, [key, message]) => {
        if (message) {
          acc[key] = { message, type: 'validation' } as any;
        }
        return acc;
      }, {} as Record<string, any>);
      onError?.(formErrors);
    }
  }, [errors, onError]);

  // Render form field
  const renderField = useCallback((field: FormField) => {
    if (!isFieldVisible(field)) return null;

    const fieldError = errors[field.name];
    const fieldId = `${config.id}-${field.name}`;

    return (
      <div
        key={field.id}
        className={`form-field form-field-${field.type} ${fieldError ? 'form-field-error' : ''}`}
        style={{
          gridColumn: field.layout?.span ? `span ${field.layout.span}` : undefined,
          order: field.layout?.order
        }}
      >
        <label htmlFor={fieldId} className="form-label">
          {field.label}
          {field.required && config.showRequiredIndicator && (
            <span className="required-indicator">*</span>
          )}
        </label>

        <div className="form-input-wrapper">
          {(() => {
            const value = formData[field.name] || '';
            const commonProps = {
              id: fieldId,
              disabled: field.disabled,
              readOnly: field.readonly,
              placeholder: field.placeholder,
              onBlur: () => {
                setTouched(prev => ({ ...prev, [field.name]: true }));
                if (config.validateOnBlur) {
                  const error = validateField(field, formData[field.name]);
                  setErrors(prev => ({ ...prev, [field.name]: error || '' }));
                }
              },
              ...field.attributes
            };

            switch (field.type) {
              case 'textarea':
                return (
                  <textarea
                    {...commonProps}
                    value={value}
                    onChange={(e) => {
                      handleFieldChange(field.name, e.target.value);
                    }}
                    className="form-input form-textarea"
                  />
                );

              case 'select':
                return (
                  <select
                    {...commonProps}
                    value={value}
                    onChange={(e) => {
                      handleFieldChange(field.name, e.target.value);
                    }}
                    className="form-input form-select"
                  >
                    <option value="">Select an option</option>
                    {field.options?.map((option, index) => (
                      <option
                        key={index}
                        value={option.value}
                        disabled={option.disabled}
                      >
                        {option.label}
                      </option>
                    ))}
                  </select>
                );

              case 'multiselect':
                return (
                  <select
                    {...commonProps}
                    multiple
                    value={value || []}
                    onChange={(e) => {
                      const selectedValues = Array.from(e.target.selectedOptions, option => option.value);
                      handleFieldChange(field.name, selectedValues);
                    }}
                    className="form-input form-multiselect"
                  >
                    {field.options?.map((option, index) => (
                      <option
                        key={index}
                        value={option.value}
                        disabled={option.disabled}
                      >
                        {option.label}
                      </option>
                    ))}
                  </select>
                );

              case 'checkbox':
                return (
                  <label className="form-checkbox-wrapper">
                    <input
                      {...commonProps}
                      type="checkbox"
                      checked={value || false}
                      onChange={(e) => {
                        handleFieldChange(field.name, e.target.checked);
                      }}
                      className="form-checkbox"
                    />
                    <span className="form-checkbox-label">{field.label}</span>
                  </label>
                );

              case 'radio':
                return (
                  <div className="form-radio-group">
                    {field.options?.map((option, index) => (
                      <label key={index} className="form-radio-wrapper">
                        <input
                          type="radio"
                          name={field.name}
                          value={option.value}
                          checked={value === option.value}
                          disabled={field.disabled || option.disabled}
                          onChange={(e) => {
                            handleFieldChange(field.name, e.target.value);
                          }}
                          className="form-radio"
                        />
                        <span className="form-radio-label">{option.label}</span>
                      </label>
                    ))}
                  </div>
                );

              case 'file':
                return (
                  <input
                    {...commonProps}
                    type="file"
                    onChange={(e) => {
                      handleFieldChange(field.name, e.target.files);
                    }}
                    className="form-input form-file"
                  />
                );

              default:
                return (
                  <input
                    {...commonProps}
                    type={field.type}
                    value={value}
                    onChange={(e) => {
                      const newValue = field.type === 'number' || field.type === 'range'
                        ? Number(e.target.value)
                        : e.target.value;
                      handleFieldChange(field.name, newValue);
                    }}
                    className="form-input"
                    min={field.validation?.min}
                    max={field.validation?.max}
                    minLength={field.validation?.minLength}
                    maxLength={field.validation?.maxLength}
                    pattern={field.validation?.pattern}
                  />
                );
            return null;
          })()}
        </div>

        {field.helpText && (
          <div className="form-help-text">{field.helpText}</div>
        )}

        {fieldError && (
          <div className="form-error-message">{fieldError}</div>
        )}
      </div>
    );
  }, [config, formData, errors, isFieldVisible, handleFieldChange, validateField]);

  // Get form layout classes
  const formClassName = useMemo(() => {
    const classes = ['form-renderer'];
    
    classes.push(`form-layout-${config.layout}`);
    
    if (config.layout === 'grid' && config.columns) {
      classes.push(`form-grid-${config.columns}`);
    }
    
    if (className) {
      classes.push(className);
    }
    
    if (debugMode) {
      classes.push('form-debug');
    }

    return classes.join(' ');
  }, [config.layout, config.columns, className, debugMode]);

  // Get form styles
  const formStyles = useMemo(() => {
    const styles: React.CSSProperties = {};
    
    if (config.layout === 'grid' && config.columns) {
      styles.gridTemplateColumns = `repeat(${config.columns}, 1fr)`;
    }
    
    if (config.gap) {
      styles.gap = config.gap;
    }

    return styles;
  }, [config.layout, config.columns, config.gap]);

  return (
    <form
      className={formClassName}
      style={formStyles}
      onSubmit={onSubmitHandler}
      noValidate
    >
      {debugMode && (
        <div className="form-debug-info">
          <div>Form: {config.name}</div>
          <div>Layout: {config.layout}</div>
          <div>Fields: {config.fields.length}</div>
          <div>Valid: {Object.keys(errors).length === 0 ? 'Yes' : 'No'}</div>
        </div>
      )}

      {config.showErrorSummary && Object.keys(errors).length > 0 && (
        <div className="form-error-summary">
          <h4>Please correct the following errors:</h4>
          <ul>
            {Object.entries(errors).map(([fieldName, error]) => (
              <li key={fieldName}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {submitError && (
        <div className="form-submit-error">
          {submitError}
        </div>
      )}

      {submitSuccess && (
        <div className="form-submit-success">
          Form submitted successfully!
        </div>
      )}

      <div className="form-fields">
        {config.fields.map(renderField)}
      </div>

      <div className="form-actions">
        <button
          type="submit"
          disabled={isSubmitting}
          className="form-submit-button"
        >
          {isSubmitting ? 'Submitting...' : (config.submitButtonText || 'Submit')}
        </button>

        {config.resetButtonText && (
          <button
            type="button"
            onClick={onResetHandler}
            disabled={isSubmitting}
            className="form-reset-button"
          >
            {config.resetButtonText}
          </button>
        )}
      </div>
    </form>
  );
};
