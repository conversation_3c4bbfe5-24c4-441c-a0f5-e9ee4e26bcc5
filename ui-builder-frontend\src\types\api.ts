// API-related types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ApiError[];
  meta?: ResponseMeta;
}

export interface ApiError {
  field?: string;
  code: string;
  message: string;
}

export interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: ResponseMeta;
}

// Request types
export interface CreateUIConfigurationRequest {
  name: string;
  description?: string;
  templateId?: string;
  organizationId: string;
}

export interface UpdateUIConfigurationRequest {
  name?: string;
  description?: string;
  components?: any[];
  theme?: any;
  layout?: any;
  settings?: any;
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  tags: string[];
  configurationId: string;
  isPublic: boolean;
  isPremium: boolean;
  price?: number;
}

export interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  category?: string;
  tags?: string[];
  isPublic?: boolean;
  isPremium?: boolean;
  price?: number;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role: string;
  organizationId: string;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: any[];
  preferences?: any;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: any;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

// Query parameters
export interface UIConfigurationQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
  createdBy?: string;
  isPublished?: boolean;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  tags?: string[];
}

export interface TemplateQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  tags?: string[];
  isPublic?: boolean;
  isPremium?: boolean;
  minRating?: number;
  sortBy?: 'name' | 'rating' | 'downloads' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface UserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
  role?: string;
  isActive?: boolean;
  sortBy?: 'username' | 'email' | 'createdAt' | 'lastLoginAt';
  sortOrder?: 'asc' | 'desc';
}

// File upload types
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  metadata?: FileMetadata;
}

export interface FileMetadata {
  width?: number;
  height?: number;
  duration?: number;
  format?: string;
  colorSpace?: string;
  hasAlpha?: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

export interface WebSocketError {
  code: string;
  message: string;
  details?: any;
}

// Error types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface NetworkError {
  status: number;
  statusText: string;
  message: string;
  url: string;
  method: string;
}

export interface ApplicationError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
  timestamp: string;
}

// Cache types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheOptions {
  ttl?: number;
  maxAge?: number;
  staleWhileRevalidate?: boolean;
}

// Analytics types
export interface AnalyticsEvent {
  name: string;
  properties?: { [key: string]: any };
  userId?: string;
  sessionId?: string;
  timestamp: string;
}

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  bundleSize: number;
}

// Feature flags
export interface FeatureFlag {
  name: string;
  enabled: boolean;
  rolloutPercentage?: number;
  conditions?: FeatureFlagCondition[];
}

export interface FeatureFlagCondition {
  property: string;
  operator: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'greaterThan' | 'lessThan';
  value: any;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  actions?: NotificationAction[];
  persistent?: boolean;
  timestamp: string;
  read: boolean;
}

export interface NotificationAction {
  label: string;
  action: string;
  style?: 'primary' | 'secondary' | 'danger';
}

// Search types
export interface SearchResult<T> {
  items: T[];
  total: number;
  facets?: SearchFacet[];
  suggestions?: string[];
  query: string;
  took: number;
}

export interface SearchFacet {
  name: string;
  values: SearchFacetValue[];
}

export interface SearchFacetValue {
  value: string;
  count: number;
  selected?: boolean;
}

export interface SearchQuery {
  q: string;
  filters?: { [key: string]: any };
  sort?: string;
  page?: number;
  limit?: number;
  facets?: string[];
}

// Export types
export interface ExportOptions {
  format: 'json' | 'xml' | 'csv' | 'pdf' | 'html' | 'react' | 'vue' | 'angular';
  includeAssets?: boolean;
  includeTheme?: boolean;
  includeMetadata?: boolean;
  minify?: boolean;
  compression?: 'none' | 'gzip' | 'brotli';
}

export interface ExportResult {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  downloadUrl?: string;
  error?: string;
  createdAt: string;
  expiresAt: string;
}
