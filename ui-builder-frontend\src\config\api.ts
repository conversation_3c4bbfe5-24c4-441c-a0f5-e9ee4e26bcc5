// API Configuration for UI Builder Frontend

export const API_CONFIG = {
  // Base URLs from environment variables
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  WEBSOCKET_URL: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:8080/ws',
  
  // Timeout configuration
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  
  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  
  // Authentication
  JWT_STORAGE_KEY: import.meta.env.VITE_JWT_STORAGE_KEY || 'ui_builder_token',
  REFRESH_TOKEN_KEY: import.meta.env.VITE_REFRESH_TOKEN_KEY || 'ui_builder_refresh_token',
  
  // API Endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      LOGIN: '/auth/login',
      LOGOUT: '/auth/logout',
      REFRESH: '/auth/refresh',
      REGISTER: '/auth/register',
      FORGOT_PASSWORD: '/auth/forgot-password',
      RESET_PASSWORD: '/auth/reset-password',
    },
    
    // UI Configurations
    CONFIGURATIONS: {
      LIST: '/configurations',
      CREATE: '/configurations',
      GET: (id: string) => `/configurations/${id}`,
      UPDATE: (id: string) => `/configurations/${id}`,
      DELETE: (id: string) => `/configurations/${id}`,
      DUPLICATE: (id: string) => `/configurations/${id}/duplicate`,
      VERSIONS: (id: string) => `/configurations/${id}/versions`,
    },
    
    // Components
    COMPONENTS: {
      LIST: '/components',
      CREATE: '/components',
      GET: (id: string) => `/components/${id}`,
      UPDATE: (id: string) => `/components/${id}`,
      DELETE: (id: string) => `/components/${id}`,
      SEARCH: '/components/search',
    },
    
    // Templates
    TEMPLATES: {
      LIST: '/templates',
      CREATE: '/templates',
      GET: (id: string) => `/templates/${id}`,
      UPDATE: (id: string) => `/templates/${id}`,
      DELETE: (id: string) => `/templates/${id}`,
      PUBLISH: (id: string) => `/templates/${id}/publish`,
    },
    
    // Themes
    THEMES: {
      LIST: '/themes',
      CREATE: '/themes',
      GET: (id: string) => `/themes/${id}`,
      UPDATE: (id: string) => `/themes/${id}`,
      DELETE: (id: string) => `/themes/${id}`,
    },
    
    // Collaboration
    COLLABORATION: {
      SESSIONS: '/collaboration/sessions',
      JOIN: (sessionId: string) => `/collaboration/sessions/${sessionId}/join`,
      LEAVE: (sessionId: string) => `/collaboration/sessions/${sessionId}/leave`,
      CURSORS: (sessionId: string) => `/collaboration/sessions/${sessionId}/cursors`,
      COMMENTS: (sessionId: string) => `/collaboration/sessions/${sessionId}/comments`,
    },
    
    // File Upload
    FILES: {
      UPLOAD: '/files/upload',
      GET: (id: string) => `/files/${id}`,
      DELETE: (id: string) => `/files/${id}`,
    },
    
    // User Management
    USERS: {
      PROFILE: '/users/profile',
      UPDATE_PROFILE: '/users/profile',
      CHANGE_PASSWORD: '/users/change-password',
      PREFERENCES: '/users/preferences',
    },
    
    // Organizations
    ORGANIZATIONS: {
      LIST: '/organizations',
      GET: (id: string) => `/organizations/${id}`,
      MEMBERS: (id: string) => `/organizations/${id}/members`,
      INVITE: (id: string) => `/organizations/${id}/invite`,
    },
  },
  
  // WebSocket Events
  WS_EVENTS: {
    // Connection
    CONNECT: 'connect',
    DISCONNECT: 'disconnect',
    ERROR: 'error',
    
    // Collaboration
    USER_JOINED: 'user_joined',
    USER_LEFT: 'user_left',
    CURSOR_MOVED: 'cursor_moved',
    SELECTION_CHANGED: 'selection_changed',
    
    // Configuration Updates
    CONFIG_UPDATED: 'config_updated',
    COMPONENT_ADDED: 'component_added',
    COMPONENT_UPDATED: 'component_updated',
    COMPONENT_DELETED: 'component_deleted',
    
    // Comments
    COMMENT_ADDED: 'comment_added',
    COMMENT_UPDATED: 'comment_updated',
    COMMENT_DELETED: 'comment_deleted',
  },
  
  // HTTP Headers
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Feature Flags
  FEATURES: {
    COLLABORATION: import.meta.env.VITE_ENABLE_COLLABORATION === 'true',
    REAL_TIME: import.meta.env.VITE_ENABLE_REAL_TIME === 'true',
    ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    OFFLINE_MODE: import.meta.env.VITE_ENABLE_OFFLINE_MODE === 'true',
  },
} as const;

// Type definitions for better TypeScript support
export type ApiEndpoint = typeof API_CONFIG.ENDPOINTS;
export type WebSocketEvent = typeof API_CONFIG.WS_EVENTS;

// Helper function to build full URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Helper function to get auth headers
export const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem(API_CONFIG.JWT_STORAGE_KEY);
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Environment validation
export const validateApiConfig = (): boolean => {
  const requiredEnvVars = [
    'VITE_API_BASE_URL',
    'VITE_WEBSOCKET_URL',
  ];
  
  const missing = requiredEnvVars.filter(envVar => !import.meta.env[envVar]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    return false;
  }
  
  return true;
};

// Initialize configuration validation
if (import.meta.env.NODE_ENV === 'development') {
  validateApiConfig();
}
