package com.uiplatform.service;

import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import com.uiplatform.entity.Comment;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.repository.CommentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Service for managing collaborative comments.
 */
@Service
@Transactional
public class CommentService {

    private static final Logger logger = LoggerFactory.getLogger(CommentService.class);

    private final CommentRepository commentRepository;
    private final SimpMessagingTemplate messagingTemplate;

    @Autowired
    public CommentService(CommentRepository commentRepository,
                         SimpMessagingTemplate messagingTemplate) {
        this.commentRepository = commentRepository;
        this.messagingTemplate = messagingTemplate;
    }

    /**
     * Create a new comment.
     */
    @CacheEvict(value = "comments", key = "#uiConfigurationId")
    public Comment createComment(UUID uiConfigurationId, String elementId, UUID authorId, 
                               String authorUsername, String text, Double positionX, Double positionY) {
        logger.info("Creating comment for element {} in config {} by user {}", 
                   elementId, uiConfigurationId, authorUsername);
        
        try {
            Comment comment = new Comment(uiConfigurationId, elementId, authorId, authorUsername, text);
            comment.setPositionX(positionX);
            comment.setPositionY(positionY);
            
            comment = commentRepository.save(comment);
            
            // Broadcast comment creation
            broadcastCommentEvent(CollaborationEventType.COMMENT_ADDED, comment);
            
            logger.info("Successfully created comment with ID: {}", comment.getId());
            return comment;
            
        } catch (Exception e) {
            logger.error("Error creating comment", e);
            throw new RuntimeException("Failed to create comment: " + e.getMessage());
        }
    }

    /**
     * Create a reply to an existing comment.
     */
    @CacheEvict(value = "comments", key = "#parentComment.uiConfigurationId")
    public Comment createReply(UUID parentCommentId, UUID authorId, String authorUsername, String text) {
        logger.info("Creating reply to comment {} by user {}", parentCommentId, authorUsername);
        
        try {
            Comment parentComment = getCommentById(parentCommentId);
            
            Comment reply = new Comment(
                parentComment.getUiConfigurationId(),
                parentComment.getElementId(),
                authorId,
                authorUsername,
                text
            );
            reply.setParentCommentId(parentCommentId);
            reply.setThreadId(parentComment.getThreadId());
            
            reply = commentRepository.save(reply);
            
            // Broadcast reply creation
            broadcastCommentEvent(CollaborationEventType.COMMENT_ADDED, reply);
            
            logger.info("Successfully created reply with ID: {}", reply.getId());
            return reply;
            
        } catch (Exception e) {
            logger.error("Error creating reply", e);
            throw new RuntimeException("Failed to create reply: " + e.getMessage());
        }
    }

    /**
     * Update an existing comment.
     */
    @CacheEvict(value = "comments", key = "#result.uiConfigurationId")
    public Comment updateComment(UUID commentId, String newText, UUID updatedBy) {
        logger.info("Updating comment {} by user {}", commentId, updatedBy);
        
        try {
            Comment comment = getCommentById(commentId);
            
            // Check if user can update this comment (author or admin)
            if (!comment.getAuthorId().equals(updatedBy)) {
                throw new IllegalArgumentException("User not authorized to update this comment");
            }
            
            comment.setText(newText);
            comment = commentRepository.save(comment);
            
            // Broadcast comment update
            broadcastCommentEvent(CollaborationEventType.COMMENT_UPDATED, comment);
            
            logger.info("Successfully updated comment with ID: {}", commentId);
            return comment;
            
        } catch (Exception e) {
            logger.error("Error updating comment", e);
            throw new RuntimeException("Failed to update comment: " + e.getMessage());
        }
    }

    /**
     * Delete a comment (soft delete).
     */
    @CacheEvict(value = "comments", key = "#result.uiConfigurationId")
    public Comment deleteComment(UUID commentId, UUID deletedBy) {
        logger.info("Deleting comment {} by user {}", commentId, deletedBy);
        
        try {
            Comment comment = getCommentById(commentId);
            
            // Check if user can delete this comment (author or admin)
            if (!comment.getAuthorId().equals(deletedBy)) {
                throw new IllegalArgumentException("User not authorized to delete this comment");
            }
            
            comment.markAsDeleted(deletedBy);
            comment = commentRepository.save(comment);
            
            // Broadcast comment deletion
            broadcastCommentEvent(CollaborationEventType.COMMENT_DELETED, comment);
            
            logger.info("Successfully deleted comment with ID: {}", commentId);
            return comment;
            
        } catch (Exception e) {
            logger.error("Error deleting comment", e);
            throw new RuntimeException("Failed to delete comment: " + e.getMessage());
        }
    }

    /**
     * Resolve a comment.
     */
    @CacheEvict(value = "comments", key = "#result.uiConfigurationId")
    public Comment resolveComment(UUID commentId, UUID resolvedBy, String resolution) {
        logger.info("Resolving comment {} by user {}", commentId, resolvedBy);
        
        try {
            Comment comment = getCommentById(commentId);
            comment.resolve(resolvedBy, resolution);
            comment = commentRepository.save(comment);
            
            // Broadcast comment resolution
            broadcastCommentEvent(CollaborationEventType.COMMENT_RESOLVED, comment);
            
            logger.info("Successfully resolved comment with ID: {}", commentId);
            return comment;
            
        } catch (Exception e) {
            logger.error("Error resolving comment", e);
            throw new RuntimeException("Failed to resolve comment: " + e.getMessage());
        }
    }

    /**
     * Get comment by ID.
     */
    @Transactional(readOnly = true)
    public Comment getCommentById(UUID commentId) {
        return commentRepository.findByIdAndDeletedFalse(commentId)
                .orElseThrow(() -> new ResourceNotFoundException("Comment not found with ID: " + commentId));
    }

    /**
     * Get all comments for a UI configuration.
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "comments", key = "#configId")
    public List<Comment> getCommentsByUIConfiguration(UUID configId) {
        logger.debug("Fetching comments for UI configuration: {}", configId);
        return commentRepository.findByUIConfigurationIdAndDeletedFalse(configId);
    }

    /**
     * Get comments for a specific element.
     */
    @Transactional(readOnly = true)
    public List<Comment> getCommentsByElement(UUID configId, String elementId) {
        logger.debug("Fetching comments for element {} in config {}", elementId, configId);
        return commentRepository.findByUIConfigurationIdAndElementIdAndDeletedFalse(configId, elementId);
    }

    /**
     * Get comments by thread.
     */
    @Transactional(readOnly = true)
    public List<Comment> getCommentsByThread(UUID threadId) {
        logger.debug("Fetching comments for thread: {}", threadId);
        return commentRepository.findByThreadIdAndDeletedFalse(threadId);
    }

    /**
     * Get top-level comments (no parent).
     */
    @Transactional(readOnly = true)
    public List<Comment> getTopLevelComments(UUID configId) {
        logger.debug("Fetching top-level comments for config: {}", configId);
        return commentRepository.findTopLevelCommentsByUIConfigurationId(configId);
    }

    /**
     * Get replies to a comment.
     */
    @Transactional(readOnly = true)
    public List<Comment> getReplies(UUID parentCommentId) {
        logger.debug("Fetching replies for comment: {}", parentCommentId);
        return commentRepository.findRepliesByParentCommentId(parentCommentId);
    }

    /**
     * Get unresolved comments.
     */
    @Transactional(readOnly = true)
    public List<Comment> getUnresolvedComments(UUID configId) {
        logger.debug("Fetching unresolved comments for config: {}", configId);
        return commentRepository.findUnresolvedCommentsByUIConfigurationId(configId);
    }

    /**
     * Get resolved comments.
     */
    @Transactional(readOnly = true)
    public List<Comment> getResolvedComments(UUID configId) {
        logger.debug("Fetching resolved comments for config: {}", configId);
        return commentRepository.findResolvedCommentsByUIConfigurationId(configId);
    }

    /**
     * Get comments by author.
     */
    @Transactional(readOnly = true)
    public Page<Comment> getCommentsByAuthor(UUID authorId, Pageable pageable) {
        logger.debug("Fetching comments by author: {}", authorId);
        return commentRepository.findByAuthorIdAndDeletedFalse(authorId, pageable);
    }

    /**
     * Search comments by text.
     */
    @Transactional(readOnly = true)
    public List<Comment> searchComments(UUID configId, String searchText) {
        logger.debug("Searching comments in config {} for text: {}", configId, searchText);
        return commentRepository.searchCommentsByText(configId, searchText);
    }

    /**
     * Get comments by priority.
     */
    @Transactional(readOnly = true)
    public List<Comment> getCommentsByPriority(UUID configId, Comment.CommentPriority priority) {
        logger.debug("Fetching comments with priority {} for config: {}", priority, configId);
        return commentRepository.findByUIConfigurationIdAndPriorityAndDeletedFalse(configId, priority);
    }

    /**
     * Get recent comments.
     */
    @Transactional(readOnly = true)
    public List<Comment> getRecentComments(UUID configId, LocalDateTime since) {
        logger.debug("Fetching recent comments for config {} since: {}", configId, since);
        return commentRepository.findByUIConfigurationIdAndCreatedAtAfterAndDeletedFalse(configId, since);
    }

    /**
     * Get comment statistics.
     */
    @Transactional(readOnly = true)
    public CommentRepository.CommentStatistics getCommentStatistics(UUID configId) {
        logger.debug("Fetching comment statistics for config: {}", configId);
        return commentRepository.getCommentStatistics(configId);
    }

    /**
     * Count comments for a UI configuration.
     */
    @Transactional(readOnly = true)
    public long countComments(UUID configId) {
        return commentRepository.countByUIConfigurationIdAndDeletedFalse(configId);
    }

    /**
     * Count unresolved comments.
     */
    @Transactional(readOnly = true)
    public long countUnresolvedComments(UUID configId) {
        return commentRepository.countUnresolvedByUIConfigurationId(configId);
    }

    /**
     * Add mention to a comment.
     */
    @CacheEvict(value = "comments", key = "#result.uiConfigurationId")
    public Comment addMention(UUID commentId, UUID mentionedUserId) {
        logger.info("Adding mention of user {} to comment {}", mentionedUserId, commentId);
        
        try {
            Comment comment = getCommentById(commentId);
            
            // Add mentioned user ID to the JSON array
            String currentMentions = comment.getMentionUserIds();
            String newMentions;
            
            if (currentMentions == null || currentMentions.isEmpty()) {
                newMentions = "[\"" + mentionedUserId + "\"]";
            } else {
                // Simple append - in real implementation, would parse JSON properly
                newMentions = currentMentions.replace("]", ",\"" + mentionedUserId + "\"]");
            }
            
            comment.setMentionUserIds(newMentions);
            comment = commentRepository.save(comment);
            
            // Broadcast mention event
            broadcastCommentEvent(CollaborationEventType.COMMENT_UPDATED, comment);
            
            return comment;
            
        } catch (Exception e) {
            logger.error("Error adding mention", e);
            throw new RuntimeException("Failed to add mention: " + e.getMessage());
        }
    }

    // Private helper methods

    private void broadcastCommentEvent(CollaborationEventType eventType, Comment comment) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(eventType);
            event.setUserId(comment.getAuthorId());
            event.setUsername(comment.getAuthorUsername());
            event.setConfigId(comment.getUiConfigurationId());
            event.setElementId(comment.getElementId());
            event.setData(comment);
            event.setTimestamp(LocalDateTime.now());
            
            // Broadcast to UI configuration topic
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + comment.getUiConfigurationId() + "/comments", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting comment event", e);
        }
    }
}
