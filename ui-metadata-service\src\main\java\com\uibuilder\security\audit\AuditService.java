package com.uibuilder.security.audit;

import com.uibuilder.entity.AuditLog;
import com.uibuilder.entity.SecurityEvent;
import com.uibuilder.repository.AuditLogRepository;
import com.uibuilder.repository.SecurityEventRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final AuditLogRepository auditLogRepository;
    private final SecurityEventRepository securityEventRepository;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final ObjectMapper objectMapper;
    private final HttpServletRequest request;

    /**
     * Log general audit events
     */
    @Async
    @Transactional
    public void logAuditEvent(String userId, String action, String resource, 
                             String resourceId, Map<String, Object> details) {
        try {
            AuditLog auditLog = AuditLog.builder()
                .id(UUID.randomUUID().toString())
                .userId(userId)
                .action(action)
                .resource(resource)
                .resourceId(resourceId)
                .details(objectMapper.writeValueAsString(details))
                .ipAddress(getClientIpAddress())
                .userAgent(getUserAgent())
                .timestamp(LocalDateTime.now())
                .build();

            auditLogRepository.save(auditLog);

            // Send to Kafka for real-time processing
            kafkaTemplate.send("audit-events", auditLog);

            log.debug("Audit event logged: {} by user {} on resource {}", 
                     action, userId, resource);

        } catch (Exception e) {
            log.error("Failed to log audit event", e);
        }
    }

    /**
     * Log security-related events
     */
    @Async
    @Transactional
    public void logSecurityEvent(String userId, String eventType, String description, 
                                Map<String, Object> details) {
        try {
            SecurityEvent securityEvent = SecurityEvent.builder()
                .id(UUID.randomUUID().toString())
                .userId(userId)
                .eventType(eventType)
                .description(description)
                .details(objectMapper.writeValueAsString(details))
                .ipAddress(getClientIpAddress())
                .userAgent(getUserAgent())
                .severity(determineSeverity(eventType))
                .timestamp(LocalDateTime.now())
                .build();

            securityEventRepository.save(securityEvent);

            // Send to Kafka for security monitoring
            kafkaTemplate.send("security-events", securityEvent);

            // Send alerts for high-severity events
            if (securityEvent.getSeverity() == SecuritySeverity.HIGH || 
                securityEvent.getSeverity() == SecuritySeverity.CRITICAL) {
                sendSecurityAlert(securityEvent);
            }

            log.info("Security event logged: {} for user {}", eventType, userId);

        } catch (Exception e) {
            log.error("Failed to log security event", e);
        }
    }

    /**
     * Log access control decisions
     */
    @Async
    public void logAccessControl(String userId, String resource, String action, 
                                String workspaceId, String decision) {
        Map<String, Object> details = new HashMap<>();
        details.put("resource", resource);
        details.put("action", action);
        details.put("workspaceId", workspaceId);
        details.put("decision", decision);
        details.put("timestamp", LocalDateTime.now());

        logAuditEvent(userId, "ACCESS_CONTROL", "permission", null, details);

        // Log failed access attempts as security events
        if ("DENIED".equals(decision)) {
            logSecurityEvent(userId, "ACCESS_DENIED", 
                           String.format("Access denied for %s:%s", resource, action), details);
        }
    }

    /**
     * Log role assignments and changes
     */
    @Async
    public void logRoleAssignment(String assignedBy, String userId, String roleId, 
                                 String workspaceId, String action) {
        Map<String, Object> details = new HashMap<>();
        details.put("assignedBy", assignedBy);
        details.put("targetUserId", userId);
        details.put("roleId", roleId);
        details.put("workspaceId", workspaceId);
        details.put("action", action);

        logAuditEvent(assignedBy, "ROLE_" + action, "role_assignment", roleId, details);
    }

    /**
     * Log role management activities
     */
    @Async
    public void logRoleManagement(String userId, String roleId, String action, 
                                 Map<String, Object> details) {
        logAuditEvent(userId, action, "role", roleId, details);
    }

    /**
     * Log authentication events
     */
    @Async
    public void logAuthenticationEvent(String userId, String eventType, boolean success, 
                                     Map<String, Object> details) {
        String action = success ? eventType + "_SUCCESS" : eventType + "_FAILURE";
        
        if (success) {
            logAuditEvent(userId, action, "authentication", null, details);
        } else {
            logSecurityEvent(userId, action, 
                           String.format("Authentication failed: %s", eventType), details);
        }
    }

    /**
     * Log data access events for compliance
     */
    @Async
    public void logDataAccess(String userId, String dataType, String operation, 
                             String recordId, Map<String, Object> details) {
        details.put("dataType", dataType);
        details.put("operation", operation);
        details.put("recordId", recordId);
        details.put("compliance", true);

        logAuditEvent(userId, "DATA_ACCESS", dataType, recordId, details);
    }

    /**
     * Log configuration changes
     */
    @Async
    public void logConfigurationChange(String userId, String configType, String configId, 
                                     Object oldValue, Object newValue) {
        Map<String, Object> details = new HashMap<>();
        details.put("configType", configType);
        details.put("oldValue", oldValue);
        details.put("newValue", newValue);
        details.put("changeType", "CONFIGURATION_UPDATE");

        logAuditEvent(userId, "CONFIG_CHANGE", configType, configId, details);
    }

    /**
     * Log API usage for rate limiting and monitoring
     */
    @Async
    public void logApiUsage(String userId, String endpoint, String method, 
                           int responseCode, long responseTime) {
        Map<String, Object> details = new HashMap<>();
        details.put("endpoint", endpoint);
        details.put("method", method);
        details.put("responseCode", responseCode);
        details.put("responseTime", responseTime);
        details.put("timestamp", LocalDateTime.now());

        logAuditEvent(userId, "API_CALL", "api", endpoint, details);

        // Log suspicious API usage
        if (responseCode == 429) { // Too Many Requests
            logSecurityEvent(userId, "RATE_LIMIT_EXCEEDED", 
                           "Rate limit exceeded for endpoint: " + endpoint, details);
        } else if (responseCode >= 400 && responseCode < 500) {
            logSecurityEvent(userId, "API_CLIENT_ERROR", 
                           String.format("Client error %d for endpoint: %s", responseCode, endpoint), 
                           details);
        }
    }

    /**
     * Log file operations for security monitoring
     */
    @Async
    public void logFileOperation(String userId, String operation, String fileName, 
                                String fileType, long fileSize) {
        Map<String, Object> details = new HashMap<>();
        details.put("operation", operation);
        details.put("fileName", fileName);
        details.put("fileType", fileType);
        details.put("fileSize", fileSize);

        logAuditEvent(userId, "FILE_" + operation.toUpperCase(), "file", fileName, details);

        // Log potentially suspicious file operations
        if (fileSize > 100 * 1024 * 1024) { // Files larger than 100MB
            logSecurityEvent(userId, "LARGE_FILE_OPERATION", 
                           String.format("Large file %s: %s (%d bytes)", operation, fileName, fileSize), 
                           details);
        }
    }

    /**
     * Create audit trail for compliance reporting
     */
    public AuditTrail createAuditTrail(String userId, LocalDateTime startDate, 
                                      LocalDateTime endDate, String resourceType) {
        List<AuditLog> auditLogs = auditLogRepository.findByUserIdAndTimestampBetween(
            userId, startDate, endDate);

        if (resourceType != null) {
            auditLogs = auditLogs.stream()
                .filter(log -> resourceType.equals(log.getResource()))
                .collect(Collectors.toList());
        }

        return AuditTrail.builder()
            .userId(userId)
            .startDate(startDate)
            .endDate(endDate)
            .resourceType(resourceType)
            .events(auditLogs)
            .generatedAt(LocalDateTime.now())
            .build();
    }

    private SecuritySeverity determineSeverity(String eventType) {
        switch (eventType) {
            case "LOGIN_FAILURE":
            case "MFA_VERIFICATION_FAILED":
            case "ACCESS_DENIED":
                return SecuritySeverity.MEDIUM;
            
            case "ACCOUNT_LOCKED":
            case "SUSPICIOUS_ACTIVITY":
            case "RATE_LIMIT_EXCEEDED":
                return SecuritySeverity.HIGH;
            
            case "SECURITY_BREACH":
            case "UNAUTHORIZED_ACCESS":
            case "DATA_EXFILTRATION":
                return SecuritySeverity.CRITICAL;
            
            default:
                return SecuritySeverity.LOW;
        }
    }

    private void sendSecurityAlert(SecurityEvent event) {
        try {
            SecurityAlert alert = SecurityAlert.builder()
                .eventId(event.getId())
                .eventType(event.getEventType())
                .severity(event.getSeverity())
                .userId(event.getUserId())
                .description(event.getDescription())
                .timestamp(event.getTimestamp())
                .ipAddress(event.getIpAddress())
                .build();

            kafkaTemplate.send("security-alerts", alert);
            
            // Send to external monitoring systems
            if (event.getSeverity() == SecuritySeverity.CRITICAL) {
                notificationService.sendCriticalSecurityAlert(alert);
            }

        } catch (Exception e) {
            log.error("Failed to send security alert", e);
        }
    }

    private String getClientIpAddress() {
        if (request == null) {
            return "unknown";
        }

        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    private String getUserAgent() {
        return request != null ? request.getHeader("User-Agent") : "unknown";
    }
}

@Data
@Builder
class AuditTrail {
    private String userId;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private String resourceType;
    private List<AuditLog> events;
    private LocalDateTime generatedAt;
}

@Data
@Builder
class SecurityAlert {
    private String eventId;
    private String eventType;
    private SecuritySeverity severity;
    private String userId;
    private String description;
    private LocalDateTime timestamp;
    private String ipAddress;
}

enum SecuritySeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}
