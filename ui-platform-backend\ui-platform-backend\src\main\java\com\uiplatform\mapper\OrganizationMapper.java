package com.uiplatform.mapper;

import com.uiplatform.dto.OrganizationDTO;
import com.uiplatform.entity.Organization;
import org.mapstruct.*;

/**
 * Mapper interface for Organization entity and DTO conversions.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrganizationMapper {

    /**
     * Convert Organization entity to DTO.
     */
    @Mapping(target = "userCount", ignore = true)
    @Mapping(target = "projectCount", ignore = true)
    @Mapping(target = "templateCount", ignore = true)
    OrganizationDTO toDTO(Organization organization);

    /**
     * Convert CreateDTO to Organization entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "ACTIVE")
    @Mapping(target = "subscriptionPlan", constant = "FREE")
    @Mapping(target = "maxUsers", constant = "10")
    @Mapping(target = "maxProjects", constant = "5")
    @Mapping(target = "maxStorageMb", constant = "1000")
    @Mapping(target = "users", ignore = true)
    @Mapping(target = "themes", ignore = true)
    @Mapping(target = "layouts", ignore = true)
    @Mapping(target = "uiConfigurations", ignore = true)
    @Mapping(target = "templates", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    Organization toEntity(OrganizationDTO.CreateDTO createDTO);

    /**
     * Update Organization entity from UpdateDTO.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "slug", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "subscriptionPlan", ignore = true)
    @Mapping(target = "maxUsers", ignore = true)
    @Mapping(target = "maxProjects", ignore = true)
    @Mapping(target = "maxStorageMb", ignore = true)
    @Mapping(target = "users", ignore = true)
    @Mapping(target = "themes", ignore = true)
    @Mapping(target = "layouts", ignore = true)
    @Mapping(target = "uiConfigurations", ignore = true)
    @Mapping(target = "templates", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void updateEntityFromDTO(OrganizationDTO.UpdateDTO updateDTO, @MappingTarget Organization organization);
}
