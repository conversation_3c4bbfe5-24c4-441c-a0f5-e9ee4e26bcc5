import 'package:flutter/material.dart';

/// Size variants for UI components
enum UISize {
  xs,
  sm,
  md,
  lg,
  xl,
  xxl,
}

/// Button variants
enum UIButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  link,
  destructive,
}

/// Card variants
enum UICardVariant {
  defaultVariant,
  elevated,
  outlined,
  filled,
}

/// Alert variants
enum UIAlertVariant {
  info,
  success,
  warning,
  error,
}

/// Badge variants
enum UIBadgeVariant {
  primary,
  secondary,
  success,
  warning,
  error,
  info,
  outline,
}

/// Input variants
enum UIInputVariant {
  outlined,
  filled,
  underlined,
}

/// Text variants
enum UITextVariant {
  display,
  headline,
  title,
  body,
  label,
  caption,
}

/// Color variants for semantic meaning
enum UIColorVariant {
  primary,
  secondary,
  success,
  warning,
  error,
  info,
  neutral,
}

/// Animation variants
enum UIAnimationVariant {
  none,
  fade,
  slide,
  scale,
  bounce,
  elastic,
}

/// Border variants
enum UIBorderVariant {
  none,
  solid,
  dashed,
  dotted,
}

/// Shadow variants
enum UIShadowVariant {
  none,
  sm,
  md,
  lg,
  xl,
}

/// Alignment variants
enum UIAlignmentVariant {
  topLeft,
  topCenter,
  topRight,
  centerLeft,
  center,
  centerRight,
  bottomLeft,
  bottomCenter,
  bottomRight,
}

/// Direction variants
enum UIDirectionVariant {
  horizontal,
  vertical,
}

/// Flex alignment variants
enum UIFlexAlignment {
  start,
  center,
  end,
  spaceBetween,
  spaceAround,
  spaceEvenly,
}

/// Cross axis alignment variants
enum UICrossAxisAlignment {
  start,
  center,
  end,
  stretch,
  baseline,
}

/// Main axis size variants
enum UIMainAxisSize {
  min,
  max,
}

/// Text alignment variants
enum UITextAlignment {
  left,
  center,
  right,
  justify,
  start,
  end,
}

/// Icon position variants
enum UIIconPosition {
  left,
  right,
  top,
  bottom,
}

/// Loading state variants
enum UILoadingVariant {
  spinner,
  dots,
  pulse,
  skeleton,
}

/// Responsive breakpoint variants
enum UIBreakpoint {
  xs, // < 576px
  sm, // >= 576px
  md, // >= 768px
  lg, // >= 992px
  xl, // >= 1200px
  xxl, // >= 1400px
}

/// Extension methods for variant enums
extension UISizeExtension on UISize {
  double get multiplier {
    switch (this) {
      case UISize.xs: return 0.75;
      case UISize.sm: return 0.875;
      case UISize.md: return 1.0;
      case UISize.lg: return 1.125;
      case UISize.xl: return 1.25;
      case UISize.xxl: return 1.5;
    }
  }

  String get name {
    switch (this) {
      case UISize.xs: return 'xs';
      case UISize.sm: return 'sm';
      case UISize.md: return 'md';
      case UISize.lg: return 'lg';
      case UISize.xl: return 'xl';
      case UISize.xxl: return 'xxl';
    }
  }
}

extension UIColorVariantExtension on UIColorVariant {
  Color getColor(ColorScheme colorScheme) {
    switch (this) {
      case UIColorVariant.primary: return colorScheme.primary;
      case UIColorVariant.secondary: return colorScheme.secondary;
      case UIColorVariant.success: return const Color(0xFF10B981);
      case UIColorVariant.warning: return const Color(0xFFF59E0B);
      case UIColorVariant.error: return colorScheme.error;
      case UIColorVariant.info: return const Color(0xFF3B82F6);
      case UIColorVariant.neutral: return colorScheme.outline;
    }
  }

  String get name {
    switch (this) {
      case UIColorVariant.primary: return 'primary';
      case UIColorVariant.secondary: return 'secondary';
      case UIColorVariant.success: return 'success';
      case UIColorVariant.warning: return 'warning';
      case UIColorVariant.error: return 'error';
      case UIColorVariant.info: return 'info';
      case UIColorVariant.neutral: return 'neutral';
    }
  }
}

extension UIAlignmentVariantExtension on UIAlignmentVariant {
  Alignment get alignment {
    switch (this) {
      case UIAlignmentVariant.topLeft: return Alignment.topLeft;
      case UIAlignmentVariant.topCenter: return Alignment.topCenter;
      case UIAlignmentVariant.topRight: return Alignment.topRight;
      case UIAlignmentVariant.centerLeft: return Alignment.centerLeft;
      case UIAlignmentVariant.center: return Alignment.center;
      case UIAlignmentVariant.centerRight: return Alignment.centerRight;
      case UIAlignmentVariant.bottomLeft: return Alignment.bottomLeft;
      case UIAlignmentVariant.bottomCenter: return Alignment.bottomCenter;
      case UIAlignmentVariant.bottomRight: return Alignment.bottomRight;
    }
  }
}

extension UIFlexAlignmentExtension on UIFlexAlignment {
  MainAxisAlignment get mainAxisAlignment {
    switch (this) {
      case UIFlexAlignment.start: return MainAxisAlignment.start;
      case UIFlexAlignment.center: return MainAxisAlignment.center;
      case UIFlexAlignment.end: return MainAxisAlignment.end;
      case UIFlexAlignment.spaceBetween: return MainAxisAlignment.spaceBetween;
      case UIFlexAlignment.spaceAround: return MainAxisAlignment.spaceAround;
      case UIFlexAlignment.spaceEvenly: return MainAxisAlignment.spaceEvenly;
    }
  }
}

extension UICrossAxisAlignmentExtension on UICrossAxisAlignment {
  CrossAxisAlignment get crossAxisAlignment {
    switch (this) {
      case UICrossAxisAlignment.start: return CrossAxisAlignment.start;
      case UICrossAxisAlignment.center: return CrossAxisAlignment.center;
      case UICrossAxisAlignment.end: return CrossAxisAlignment.end;
      case UICrossAxisAlignment.stretch: return CrossAxisAlignment.stretch;
      case UICrossAxisAlignment.baseline: return CrossAxisAlignment.baseline;
    }
  }
}

extension UIMainAxisSizeExtension on UIMainAxisSize {
  MainAxisSize get mainAxisSize {
    switch (this) {
      case UIMainAxisSize.min: return MainAxisSize.min;
      case UIMainAxisSize.max: return MainAxisSize.max;
    }
  }
}

extension UITextAlignmentExtension on UITextAlignment {
  TextAlign get textAlign {
    switch (this) {
      case UITextAlignment.left: return TextAlign.left;
      case UITextAlignment.center: return TextAlign.center;
      case UITextAlignment.right: return TextAlign.right;
      case UITextAlignment.justify: return TextAlign.justify;
      case UITextAlignment.start: return TextAlign.start;
      case UITextAlignment.end: return TextAlign.end;
    }
  }
}

extension UIBreakpointExtension on UIBreakpoint {
  double get minWidth {
    switch (this) {
      case UIBreakpoint.xs: return 0;
      case UIBreakpoint.sm: return 576;
      case UIBreakpoint.md: return 768;
      case UIBreakpoint.lg: return 992;
      case UIBreakpoint.xl: return 1200;
      case UIBreakpoint.xxl: return 1400;
    }
  }

  bool matches(double screenWidth) {
    switch (this) {
      case UIBreakpoint.xs: return screenWidth < 576;
      case UIBreakpoint.sm: return screenWidth >= 576 && screenWidth < 768;
      case UIBreakpoint.md: return screenWidth >= 768 && screenWidth < 992;
      case UIBreakpoint.lg: return screenWidth >= 992 && screenWidth < 1200;
      case UIBreakpoint.xl: return screenWidth >= 1200 && screenWidth < 1400;
      case UIBreakpoint.xxl: return screenWidth >= 1400;
    }
  }
}
