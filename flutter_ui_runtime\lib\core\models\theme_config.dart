import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'theme_config.g.dart';

/// Theme configuration model for dynamic theming
@JsonSerializable()
@HiveType(typeId: 10)
class ThemeConfig {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final ColorPalette colors;
  
  @HiveField(3)
  final TypographyConfig typography;
  
  @HiveField(4)
  final SpacingConfig spacing;
  
  @HiveField(5)
  final ShadowConfig shadows;
  
  @HiveField(6)
  final BorderConfig borders;
  
  @HiveField(7)
  final AnimationConfig animations;

  const ThemeConfig({
    required this.id,
    required this.name,
    required this.colors,
    required this.typography,
    required this.spacing,
    required this.shadows,
    required this.borders,
    required this.animations,
  });

  factory ThemeConfig.fromJson(Map<String, dynamic> json) =>
      _$ThemeConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ThemeConfigToJson(this);

  /// Convert to Flutter ThemeData
  ThemeData toThemeData({bool isDark = false}) {
    final colorScheme = isDark
        ? ColorScheme.dark(
            primary: Color(colors.primary.shade500),
            secondary: Color(colors.secondary.shade500),
            surface: Color(colors.surface.shade900),
            background: Color(colors.surface.shade950),
            error: Color(colors.error.shade500),
          )
        : ColorScheme.light(
            primary: Color(colors.primary.shade500),
            secondary: Color(colors.secondary.shade500),
            surface: Color(colors.surface.shade50),
            background: Color(colors.surface.shade25),
            error: Color(colors.error.shade500),
          );

    return ThemeData(
      colorScheme: colorScheme,
      fontFamily: typography.fontFamilies.sans,
      textTheme: _buildTextTheme(),
      elevatedButtonTheme: _buildElevatedButtonTheme(),
      outlinedButtonTheme: _buildOutlinedButtonTheme(),
      textButtonTheme: _buildTextButtonTheme(),
      inputDecorationTheme: _buildInputDecorationTheme(),
      cardTheme: _buildCardTheme(),
      appBarTheme: _buildAppBarTheme(isDark),
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(isDark),
      useMaterial3: true,
    );
  }

  TextTheme _buildTextTheme() {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: typography.fontSizes.size6xl,
        fontWeight: FontWeight.w700,
        fontFamily: typography.fontFamilies.sans,
      ),
      displayMedium: TextStyle(
        fontSize: typography.fontSizes.size5xl,
        fontWeight: FontWeight.w600,
        fontFamily: typography.fontFamilies.sans,
      ),
      displaySmall: TextStyle(
        fontSize: typography.fontSizes.size4xl,
        fontWeight: FontWeight.w600,
        fontFamily: typography.fontFamilies.sans,
      ),
      headlineLarge: TextStyle(
        fontSize: typography.fontSizes.size3xl,
        fontWeight: FontWeight.w600,
        fontFamily: typography.fontFamilies.sans,
      ),
      headlineMedium: TextStyle(
        fontSize: typography.fontSizes.size2xl,
        fontWeight: FontWeight.w600,
        fontFamily: typography.fontFamilies.sans,
      ),
      headlineSmall: TextStyle(
        fontSize: typography.fontSizes.sizeXl,
        fontWeight: FontWeight.w500,
        fontFamily: typography.fontFamilies.sans,
      ),
      titleLarge: TextStyle(
        fontSize: typography.fontSizes.sizeLg,
        fontWeight: FontWeight.w500,
        fontFamily: typography.fontFamilies.sans,
      ),
      titleMedium: TextStyle(
        fontSize: typography.fontSizes.sizeBase,
        fontWeight: FontWeight.w500,
        fontFamily: typography.fontFamilies.sans,
      ),
      titleSmall: TextStyle(
        fontSize: typography.fontSizes.sizeSm,
        fontWeight: FontWeight.w500,
        fontFamily: typography.fontFamilies.sans,
      ),
      bodyLarge: TextStyle(
        fontSize: typography.fontSizes.sizeBase,
        fontWeight: FontWeight.w400,
        fontFamily: typography.fontFamilies.sans,
      ),
      bodyMedium: TextStyle(
        fontSize: typography.fontSizes.sizeSm,
        fontWeight: FontWeight.w400,
        fontFamily: typography.fontFamilies.sans,
      ),
      bodySmall: TextStyle(
        fontSize: typography.fontSizes.sizeXs,
        fontWeight: FontWeight.w400,
        fontFamily: typography.fontFamilies.sans,
      ),
    );
  }

  ElevatedButtonThemeData _buildElevatedButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Color(colors.primary.shade500),
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: spacing.sizeLg,
          vertical: spacing.sizeMd,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borders.sizeMd),
        ),
      ),
    );
  }

  OutlinedButtonThemeData _buildOutlinedButtonTheme() {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: Color(colors.primary.shade500),
        side: BorderSide(color: Color(colors.primary.shade500)),
        padding: EdgeInsets.symmetric(
          horizontal: spacing.sizeLg,
          vertical: spacing.sizeMd,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borders.sizeMd),
        ),
      ),
    );
  }

  TextButtonThemeData _buildTextButtonTheme() {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: Color(colors.primary.shade500),
        padding: EdgeInsets.symmetric(
          horizontal: spacing.sizeLg,
          vertical: spacing.sizeMd,
        ),
      ),
    );
  }

  InputDecorationTheme _buildInputDecorationTheme() {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borders.sizeMd),
        borderSide: BorderSide(color: Color(colors.surface.shade300)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borders.sizeMd),
        borderSide: BorderSide(color: Color(colors.surface.shade300)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borders.sizeMd),
        borderSide: BorderSide(color: Color(colors.primary.shade500)),
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: spacing.sizeMd,
        vertical: spacing.sizeSm,
      ),
    );
  }

  CardTheme _buildCardTheme() {
    return CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borders.sizeLg),
      ),
      margin: EdgeInsets.all(spacing.sizeSm),
    );
  }

  AppBarTheme _buildAppBarTheme(bool isDark) {
    return AppBarTheme(
      backgroundColor: isDark
          ? Color(colors.surface.shade900)
          : Color(colors.surface.shade50),
      foregroundColor: isDark
          ? Color(colors.surface.shade50)
          : Color(colors.surface.shade900),
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontSize: typography.fontSizes.sizeLg,
        fontWeight: FontWeight.w600,
        fontFamily: typography.fontFamilies.sans,
        color: isDark
            ? Color(colors.surface.shade50)
            : Color(colors.surface.shade900),
      ),
    );
  }

  BottomNavigationBarThemeData _buildBottomNavigationBarTheme(bool isDark) {
    return BottomNavigationBarThemeData(
      backgroundColor: isDark
          ? Color(colors.surface.shade900)
          : Color(colors.surface.shade50),
      selectedItemColor: Color(colors.primary.shade500),
      unselectedItemColor: isDark
          ? Color(colors.surface.shade400)
          : Color(colors.surface.shade600),
      type: BottomNavigationBarType.fixed,
    );
  }
}

/// Color palette configuration
@JsonSerializable()
@HiveType(typeId: 11)
class ColorPalette {
  @HiveField(0)
  final ColorScale primary;
  
  @HiveField(1)
  final ColorScale secondary;
  
  @HiveField(2)
  final ColorScale accent;
  
  @HiveField(3)
  final ColorScale surface;
  
  @HiveField(4)
  final ColorScale success;
  
  @HiveField(5)
  final ColorScale warning;
  
  @HiveField(6)
  final ColorScale error;
  
  @HiveField(7)
  final ColorScale info;

  const ColorPalette({
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.surface,
    required this.success,
    required this.warning,
    required this.error,
    required this.info,
  });

  factory ColorPalette.fromJson(Map<String, dynamic> json) =>
      _$ColorPaletteFromJson(json);

  Map<String, dynamic> toJson() => _$ColorPaletteToJson(this);
}

/// Color scale with different shades
@JsonSerializable()
@HiveType(typeId: 12)
class ColorScale {
  @HiveField(0)
  final int shade25;
  
  @HiveField(1)
  final int shade50;
  
  @HiveField(2)
  final int shade100;
  
  @HiveField(3)
  final int shade200;
  
  @HiveField(4)
  final int shade300;
  
  @HiveField(5)
  final int shade400;
  
  @HiveField(6)
  final int shade500;
  
  @HiveField(7)
  final int shade600;
  
  @HiveField(8)
  final int shade700;
  
  @HiveField(9)
  final int shade800;
  
  @HiveField(10)
  final int shade900;
  
  @HiveField(11)
  final int shade950;

  const ColorScale({
    required this.shade25,
    required this.shade50,
    required this.shade100,
    required this.shade200,
    required this.shade300,
    required this.shade400,
    required this.shade500,
    required this.shade600,
    required this.shade700,
    required this.shade800,
    required this.shade900,
    required this.shade950,
  });

  factory ColorScale.fromJson(Map<String, dynamic> json) =>
      _$ColorScaleFromJson(json);

  Map<String, dynamic> toJson() => _$ColorScaleToJson(this);
}

/// Typography configuration
@JsonSerializable()
@HiveType(typeId: 13)
class TypographyConfig {
  @HiveField(0)
  final FontFamilies fontFamilies;
  
  @HiveField(1)
  final FontSizes fontSizes;
  
  @HiveField(2)
  final FontWeights fontWeights;

  const TypographyConfig({
    required this.fontFamilies,
    required this.fontSizes,
    required this.fontWeights,
  });

  factory TypographyConfig.fromJson(Map<String, dynamic> json) =>
      _$TypographyConfigFromJson(json);

  Map<String, dynamic> toJson() => _$TypographyConfigToJson(this);
}

/// Font families configuration
@JsonSerializable()
@HiveType(typeId: 14)
class FontFamilies {
  @HiveField(0)
  final String sans;
  
  @HiveField(1)
  final String serif;
  
  @HiveField(2)
  final String mono;

  const FontFamilies({
    required this.sans,
    required this.serif,
    required this.mono,
  });

  factory FontFamilies.fromJson(Map<String, dynamic> json) =>
      _$FontFamiliesFromJson(json);

  Map<String, dynamic> toJson() => _$FontFamiliesToJson(this);
}

/// Font sizes configuration
@JsonSerializable()
@HiveType(typeId: 15)
class FontSizes {
  @HiveField(0)
  final double sizeXs;
  
  @HiveField(1)
  final double sizeSm;
  
  @HiveField(2)
  final double sizeBase;
  
  @HiveField(3)
  final double sizeLg;
  
  @HiveField(4)
  final double sizeXl;
  
  @HiveField(5)
  final double size2xl;
  
  @HiveField(6)
  final double size3xl;
  
  @HiveField(7)
  final double size4xl;
  
  @HiveField(8)
  final double size5xl;
  
  @HiveField(9)
  final double size6xl;

  const FontSizes({
    required this.sizeXs,
    required this.sizeSm,
    required this.sizeBase,
    required this.sizeLg,
    required this.sizeXl,
    required this.size2xl,
    required this.size3xl,
    required this.size4xl,
    required this.size5xl,
    required this.size6xl,
  });

  factory FontSizes.fromJson(Map<String, dynamic> json) =>
      _$FontSizesFromJson(json);

  Map<String, dynamic> toJson() => _$FontSizesToJson(this);
}

/// Font weights configuration
@JsonSerializable()
@HiveType(typeId: 16)
class FontWeights {
  @HiveField(0)
  final int light;
  
  @HiveField(1)
  final int normal;
  
  @HiveField(2)
  final int medium;
  
  @HiveField(3)
  final int semibold;
  
  @HiveField(4)
  final int bold;

  const FontWeights({
    required this.light,
    required this.normal,
    required this.medium,
    required this.semibold,
    required this.bold,
  });

  factory FontWeights.fromJson(Map<String, dynamic> json) =>
      _$FontWeightsFromJson(json);

  Map<String, dynamic> toJson() => _$FontWeightsToJson(this);
}

/// Spacing configuration
@JsonSerializable()
@HiveType(typeId: 17)
class SpacingConfig {
  @HiveField(0)
  final double sizeXs;
  
  @HiveField(1)
  final double sizeSm;
  
  @HiveField(2)
  final double sizeMd;
  
  @HiveField(3)
  final double sizeLg;
  
  @HiveField(4)
  final double sizeXl;
  
  @HiveField(5)
  final double size2xl;

  const SpacingConfig({
    required this.sizeXs,
    required this.sizeSm,
    required this.sizeMd,
    required this.sizeLg,
    required this.sizeXl,
    required this.size2xl,
  });

  factory SpacingConfig.fromJson(Map<String, dynamic> json) =>
      _$SpacingConfigFromJson(json);

  Map<String, dynamic> toJson() => _$SpacingConfigToJson(this);
}

/// Shadow configuration
@JsonSerializable()
@HiveType(typeId: 18)
class ShadowConfig {
  @HiveField(0)
  final String sizeXs;
  
  @HiveField(1)
  final String sizeSm;
  
  @HiveField(2)
  final String sizeMd;
  
  @HiveField(3)
  final String sizeLg;
  
  @HiveField(4)
  final String sizeXl;

  const ShadowConfig({
    required this.sizeXs,
    required this.sizeSm,
    required this.sizeMd,
    required this.sizeLg,
    required this.sizeXl,
  });

  factory ShadowConfig.fromJson(Map<String, dynamic> json) =>
      _$ShadowConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ShadowConfigToJson(this);
}

/// Border configuration
@JsonSerializable()
@HiveType(typeId: 19)
class BorderConfig {
  @HiveField(0)
  final double sizeXs;
  
  @HiveField(1)
  final double sizeSm;
  
  @HiveField(2)
  final double sizeMd;
  
  @HiveField(3)
  final double sizeLg;
  
  @HiveField(4)
  final double sizeXl;

  const BorderConfig({
    required this.sizeXs,
    required this.sizeSm,
    required this.sizeMd,
    required this.sizeLg,
    required this.sizeXl,
  });

  factory BorderConfig.fromJson(Map<String, dynamic> json) =>
      _$BorderConfigFromJson(json);

  Map<String, dynamic> toJson() => _$BorderConfigToJson(this);
}

/// Animation configuration
@JsonSerializable()
@HiveType(typeId: 21)
class AnimationConfig {
  @HiveField(0)
  final int durationFast;
  
  @HiveField(1)
  final int durationNormal;
  
  @HiveField(2)
  final int durationSlow;

  const AnimationConfig({
    required this.durationFast,
    required this.durationNormal,
    required this.durationSlow,
  });

  factory AnimationConfig.fromJson(Map<String, dynamic> json) =>
      _$AnimationConfigFromJson(json);

  Map<String, dynamic> toJson() => _$AnimationConfigToJson(this);
}
