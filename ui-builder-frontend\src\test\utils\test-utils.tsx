import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { B<PERSON>erRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { configureStore } from '@reduxjs/toolkit';
import { vi } from 'vitest';

// Import reducers
import authReducer from '@store/slices/authSlice';
import uiBuilderReducer from '@store/slices/uiBuilderSlice';
import themeReducer from '@store/slices/themeSlice';
import collaborationReducer from '@store/slices/collaborationSlice';
import notificationReducer from '@store/slices/notificationSlice';
import settingsReducer from '@store/slices/settingsSlice';

// Mock API services
const mockApiSlice = {
  reducer: (state = {}, action: any) => state,
  middleware: () => (next: any) => (action: any) => next(action),
  reducerPath: 'api',
};

// Create a test store
export function createTestStore(preloadedState?: any) {
  return configureStore({
    reducer: {
      auth: authReducer,
      uiBuilder: uiBuilderReducer,
      theme: themeReducer,
      collaboration: collaborationReducer,
      notifications: notificationReducer,
      settings: settingsReducer,
      [mockApiSlice.reducerPath]: mockApiSlice.reducer,
    },
    preloadedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }).concat(mockApiSlice.middleware),
  });
}

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode;
  store?: ReturnType<typeof createTestStore>;
  initialEntries?: string[];
}

const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  store = createTestStore(),
  initialEntries = ['/'],
}) => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ConfigProvider>
          {children}
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  store?: ReturnType<typeof createTestStore>;
  initialEntries?: string[];
}

export function renderWithProviders(
  ui: ReactElement,
  {
    store = createTestStore(),
    initialEntries = ['/'],
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper store={store} initialEntries={initialEntries}>
      {children}
    </TestWrapper>
  );

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
}

// Mock data factories
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'designer',
  organizationId: 'org-1',
  permissions: [],
  preferences: {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
    notifications: {
      email: true,
      push: true,
      collaboration: true,
      updates: true,
      marketing: false,
    },
    editor: {
      autoSave: true,
      autoSaveInterval: 30000,
      showGrid: true,
      snapToGrid: true,
      showRulers: true,
      showGuides: true,
      keyboardShortcuts: {},
    },
  },
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides,
});

export const createMockUIComponent = (overrides = {}) => ({
  id: 'comp-1',
  type: 'text',
  name: 'text',
  displayName: 'Text',
  properties: {
    text: 'Sample text',
  },
  position: { x: 0, y: 0, z: 1 },
  size: { width: 100, height: 30 },
  styles: {
    fontSize: 14,
    color: '#000000',
  },
  constraints: {
    resizable: true,
    draggable: true,
    deletable: true,
    duplicatable: true,
  },
  metadata: {
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user-1',
    version: 1,
  },
  ...overrides,
});

export const createMockUIConfiguration = (overrides = {}) => ({
  id: 'config-1',
  name: 'Test Configuration',
  description: 'A test configuration',
  version: '1.0.0',
  organizationId: 'org-1',
  createdBy: 'user-1',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  isPublished: false,
  components: [createMockUIComponent()],
  theme: {
    id: 'default',
    name: 'Default Theme',
    colors: {},
    typography: {},
    spacing: {},
    shadows: {},
    borders: {},
    animations: {},
  },
  layout: {
    type: 'fluid',
  },
  settings: {
    enableGrid: true,
    snapToGrid: true,
    gridSize: 20,
    showRulers: true,
    showGuides: true,
    enableCollaboration: true,
    autoSave: true,
    autoSaveInterval: 30000,
  },
  ...overrides,
});

export const createMockTemplate = (overrides = {}) => ({
  id: 'template-1',
  name: 'Test Template',
  description: 'A test template',
  category: 'dashboard',
  tags: ['test', 'dashboard'],
  thumbnail: 'https://example.com/thumbnail.jpg',
  screenshots: ['https://example.com/screenshot1.jpg'],
  configuration: createMockUIConfiguration(),
  isPublic: true,
  isPremium: false,
  rating: 4.5,
  downloads: 100,
  createdBy: 'user-1',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides,
});

export const createMockFormField = (overrides = {}) => ({
  id: 'field-1',
  type: 'text',
  name: 'firstName',
  label: 'First Name',
  placeholder: 'Enter your first name',
  required: true,
  validation: [],
  properties: {},
  styles: {},
  position: { x: 0, y: 0 },
  size: { width: 200, height: 40 },
  ...overrides,
});

export const createMockFormConfiguration = (overrides = {}) => ({
  id: 'form-1',
  name: 'Test Form',
  description: 'A test form',
  fields: [createMockFormField()],
  layout: {
    type: 'single-column',
    spacing: 16,
  },
  validation: {
    validateOnChange: true,
    validateOnBlur: true,
    showErrorsInline: true,
    showErrorSummary: false,
  },
  submission: {
    method: 'POST',
    action: '/submit',
    successMessage: 'Form submitted successfully!',
    errorMessage: 'There was an error submitting the form.',
  },
  styling: {
    theme: 'default',
    fieldSpacing: 16,
    labelPosition: 'top',
    buttonAlignment: 'left',
  },
  settings: {
    allowDrafts: false,
    showProgress: false,
    enableAutosave: false,
    autosaveInterval: 30,
    captcha: {
      enabled: false,
      provider: 'recaptcha',
      siteKey: '',
    },
    fileUpload: {
      maxFileSize: 10,
      allowedTypes: ['image/*'],
      maxFiles: 5,
      storage: 'local',
    },
  },
  ...overrides,
});

// Mock event handlers
export const createMockEventHandlers = () => ({
  onClick: vi.fn(),
  onChange: vi.fn(),
  onSubmit: vi.fn(),
  onFocus: vi.fn(),
  onBlur: vi.fn(),
  onMouseEnter: vi.fn(),
  onMouseLeave: vi.fn(),
  onDragStart: vi.fn(),
  onDragEnd: vi.fn(),
  onDrop: vi.fn(),
});

// Mock API responses
export const createMockApiResponse = (data: any, success = true) => ({
  success,
  data,
  message: success ? 'Success' : 'Error',
  errors: success ? [] : [{ code: 'ERROR', message: 'Something went wrong' }],
});

export const createMockPaginatedResponse = (data: any[], page = 1, limit = 10) => ({
  success: true,
  data,
  meta: {
    page,
    limit,
    total: data.length,
    totalPages: Math.ceil(data.length / limit),
  },
});

// Test utilities
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const mockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
  };
};

export const mockFetch = (response: any, ok = true) => {
  return vi.fn().mockResolvedValue({
    ok,
    json: vi.fn().mockResolvedValue(response),
    text: vi.fn().mockResolvedValue(JSON.stringify(response)),
    status: ok ? 200 : 400,
    statusText: ok ? 'OK' : 'Bad Request',
  });
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
