import React from 'react';
import { cn } from '@utils/cn';
import { ComponentProps } from '@types/index';

interface ContainerProps extends ComponentProps {
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full' | 'none';
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  centered?: boolean;
  fluid?: boolean;
}

const maxWidthClasses = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
  '4xl': 'max-w-4xl',
  '5xl': 'max-w-5xl',
  '6xl': 'max-w-6xl',
  '7xl': 'max-w-7xl',
  full: 'max-w-full',
  none: 'max-w-none',
};

const paddingClasses = {
  none: '',
  xs: 'px-2 py-1',
  sm: 'px-3 py-2',
  md: 'px-4 py-3',
  lg: 'px-6 py-4',
  xl: 'px-8 py-6',
  '2xl': 'px-12 py-8',
};

const Container: React.FC<ContainerProps> = ({
  maxWidth = 'full',
  padding = 'md',
  centered = true,
  fluid = false,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'w-full',
        !fluid && maxWidthClasses[maxWidth],
        paddingClasses[padding],
        centered && 'mx-auto',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default Container;
