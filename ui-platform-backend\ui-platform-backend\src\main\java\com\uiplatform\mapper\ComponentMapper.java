package com.uiplatform.mapper;

import com.uiplatform.dto.ComponentDTO;
import com.uiplatform.entity.Component;
import org.mapstruct.*;

/**
 * Mapper interface for Component entity and DTO conversions.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ComponentMapper {

    /**
     * Convert Component entity to DTO.
     */
    @Mapping(target = "uiConfigurationId", source = "uiConfiguration.id")
    @Mapping(target = "uiConfigurationName", source = "uiConfiguration.name")
    @Mapping(target = "parentId", source = "parent.id")
    @Mapping(target = "parentName", source = "parent.name")
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "childrenCount", ignore = true)
    @Mapping(target = "depth", ignore = true)
    ComponentDTO toDTO(Component component);

    /**
     * Convert CreateDTO to Component entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "uiConfiguration", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "eventHandlers", ignore = true)
    @Mapping(target = "validationRules", ignore = true)
    @Mapping(target = "responsiveSettings", ignore = true)
    @Mapping(target = "cssClasses", ignore = true)
    @Mapping(target = "customCss", ignore = true)
    @Mapping(target = "dataSource", ignore = true)
    @Mapping(target = "dataBinding", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    Component toEntity(ComponentDTO.CreateDTO createDTO);

    /**
     * Update Component entity from DTO.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "uiConfiguration", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void updateEntityFromDTO(ComponentDTO updateDTO, @MappingTarget Component component);
}
