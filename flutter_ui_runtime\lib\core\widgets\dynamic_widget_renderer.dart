import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/ui_metadata.dart';
import '../providers/theme_provider.dart';
import '../providers/platform_provider.dart';
import '../providers/data_provider.dart';
import '../providers/permissions_provider.dart';
import '../utils/logger.dart';
import 'widget_registry.dart';
import 'error_boundary_widget.dart';

/// Core widget renderer that converts JSON metadata to Flutter widgets
class DynamicWidgetRenderer extends ConsumerWidget {
  final WidgetConfiguration config;
  final String? parentId;
  final int index;

  const DynamicWidgetRenderer({
    super.key,
    required this.config,
    this.parentId,
    this.index = 0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get providers
    final themeConfig = ref.watch(themeConfigProvider);
    final platformInfo = ref.watch(platformInfoProvider);
    final dataState = ref.watch(dataProvider);
    final permissions = ref.watch(permissionsProvider);

    // Check permissions
    if (!_hasPermission(permissions)) {
      return _buildPermissionDeniedWidget();
    }

    // Evaluate conditions
    if (!_evaluateConditions(dataState.data)) {
      return const SizedBox.shrink();
    }

    // Build the widget with error boundary
    return ErrorBoundaryWidget(
      onError: (error, stackTrace) {
        AppLogger.error(
          'Widget render error',
          error: error,
          stackTrace: stackTrace,
          extra: {
            'widgetId': config.id,
            'widgetType': config.type,
            'parentId': parentId,
          },
        );
      },
      child: _buildWidget(context, ref, platformInfo.isIOS),
    );
  }

  /// Build the actual widget
  Widget _buildWidget(BuildContext context, WidgetRef ref, bool useCupertino) {
    try {
      // Get the widget from registry
      final widget = WidgetRegistry.instance.build(
        config,
        useCupertino: useCupertino,
      );

      // Apply positioning if specified
      Widget positionedWidget = _applyPositioning(widget);

      // Apply sizing if specified
      positionedWidget = _applySizing(positionedWidget);

      // Apply styling if specified
      positionedWidget = _applyStyling(positionedWidget);

      // Apply animations if specified
      positionedWidget = _applyAnimations(positionedWidget);

      // Apply gestures and interactions
      positionedWidget = _applyInteractions(positionedWidget, ref);

      return positionedWidget;
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to build widget',
        error: error,
        stackTrace: stackTrace,
        extra: {
          'widgetId': config.id,
          'widgetType': config.type,
        },
      );
      return _buildErrorWidget(error);
    }
  }

  /// Check if user has required permissions
  bool _hasPermission(List<String> userPermissions) {
    if (config.permissions == null || config.permissions!.isEmpty) {
      return true;
    }

    return config.permissions!.every(
      (permission) => userPermissions.contains(permission),
    );
  }

  /// Evaluate conditional rendering
  bool _evaluateConditions(Map<String, dynamic> data) {
    if (config.conditions == null || config.conditions!.isEmpty) {
      return true;
    }

    // Simple condition evaluation - in a real app, this would be more sophisticated
    for (final condition in config.conditions!) {
      if (!_evaluateCondition(condition, data)) {
        return false;
      }
    }

    return true;
  }

  /// Evaluate a single condition
  bool _evaluateCondition(ConditionConfiguration condition, Map<String, dynamic> data) {
    // This is a simplified implementation
    // In a real app, you'd have a proper expression evaluator
    try {
      switch (condition.type) {
        case 'show':
          return _evaluateExpression(condition.expression, data);
        case 'hide':
          return !_evaluateExpression(condition.expression, data);
        default:
          return true;
      }
    } catch (error) {
      AppLogger.warning('Failed to evaluate condition: $error');
      return true; // Default to showing the widget
    }
  }

  /// Simple expression evaluator
  bool _evaluateExpression(String expression, Map<String, dynamic> data) {
    // This is a very basic implementation
    // In production, you'd use a proper expression parser
    
    // Handle simple data path checks
    if (expression.startsWith('data.')) {
      final path = expression.substring(5);
      final value = _getNestedValue(data, path);
      return value != null && value != false && value != '';
    }

    // Handle simple equality checks
    if (expression.contains('==')) {
      final parts = expression.split('==');
      if (parts.length == 2) {
        final left = parts[0].trim();
        final right = parts[1].trim().replaceAll('"', '').replaceAll("'", '');
        
        if (left.startsWith('data.')) {
          final path = left.substring(5);
          final value = _getNestedValue(data, path);
          return value?.toString() == right;
        }
      }
    }

    return true;
  }

  /// Get nested value from data object
  dynamic _getNestedValue(Map<String, dynamic> data, String path) {
    final keys = path.split('.');
    dynamic current = data;
    
    for (final key in keys) {
      if (current is Map<String, dynamic>) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }

  /// Apply positioning to widget
  Widget _applyPositioning(Widget child) {
    final position = config.position;
    if (position == null) return child;

    // Handle absolute positioning
    if (position.x != null || position.y != null) {
      return Positioned(
        left: position.x,
        top: position.y,
        child: child,
      );
    }

    // Handle grid positioning (would be handled by parent grid)
    return child;
  }

  /// Apply sizing to widget
  Widget _applySizing(Widget child) {
    final size = config.size;
    if (size == null) return child;

    return SizedBox(
      width: size.width,
      height: size.height,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: size.minWidth ?? 0,
          minHeight: size.minHeight ?? 0,
          maxWidth: size.maxWidth ?? double.infinity,
          maxHeight: size.maxHeight ?? double.infinity,
        ),
        child: child,
      ),
    );
  }

  /// Apply styling to widget
  Widget _applyStyling(Widget child) {
    final style = config.style;
    if (style?.style == null) return child;

    final styleMap = style!.style!;
    
    // Apply container styling if needed
    if (styleMap.containsKey('backgroundColor') ||
        styleMap.containsKey('border') ||
        styleMap.containsKey('borderRadius') ||
        styleMap.containsKey('padding') ||
        styleMap.containsKey('margin')) {
      
      return Container(
        padding: _buildEdgeInsets(styleMap['padding']),
        margin: _buildEdgeInsets(styleMap['margin']),
        decoration: BoxDecoration(
          color: _buildColor(styleMap['backgroundColor']),
          border: _buildBorder(styleMap['border']),
          borderRadius: _buildBorderRadius(styleMap['borderRadius']),
          boxShadow: _buildBoxShadow(styleMap['boxShadow']),
        ),
        child: child,
      );
    }

    return child;
  }

  /// Apply animations to widget
  Widget _applyAnimations(Widget child) {
    // Check if widget has animation configuration
    final animations = config.props['animations'] as Map<String, dynamic>?;
    if (animations == null) return child;

    // Apply entrance animation
    if (animations.containsKey('entrance')) {
      return _buildEntranceAnimation(child, animations['entrance']);
    }

    return child;
  }

  /// Apply interactions and gestures
  Widget _applyInteractions(Widget child, WidgetRef ref) {
    final events = config.events;
    if (events == null || events.isEmpty) return child;

    return GestureDetector(
      onTap: () => _handleEvent('onTap', ref),
      onDoubleTap: () => _handleEvent('onDoubleTap', ref),
      onLongPress: () => _handleEvent('onLongPress', ref),
      child: child,
    );
  }

  /// Handle widget events
  void _handleEvent(String eventType, WidgetRef ref) {
    final event = config.events?.firstWhere(
      (e) => e.type == eventType,
      orElse: () => null,
    );

    if (event != null) {
      _executeAction(event.action, ref);
    }
  }

  /// Execute an action
  void _executeAction(ActionConfiguration action, WidgetRef ref) {
    try {
      switch (action.type) {
        case 'navigate':
          _handleNavigationAction(action);
          break;
        case 'api':
          _handleApiAction(action, ref);
          break;
        case 'data':
          _handleDataAction(action, ref);
          break;
        case 'custom':
          _handleCustomAction(action, ref);
          break;
        default:
          AppLogger.warning('Unknown action type: ${action.type}');
      }
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to execute action',
        error: error,
        stackTrace: stackTrace,
        extra: {
          'actionType': action.type,
          'widgetId': config.id,
        },
      );
    }
  }

  /// Handle navigation actions
  void _handleNavigationAction(ActionConfiguration action) {
    // Implementation would depend on your navigation setup
    AppLogger.info('Navigation action: ${action.target}');
  }

  /// Handle API actions
  void _handleApiAction(ActionConfiguration action, WidgetRef ref) {
    // Implementation would trigger API calls through providers
    AppLogger.info('API action: ${action.target}');
  }

  /// Handle data actions
  void _handleDataAction(ActionConfiguration action, WidgetRef ref) {
    // Implementation would update data through providers
    AppLogger.info('Data action: ${action.params}');
  }

  /// Handle custom actions
  void _handleCustomAction(ActionConfiguration action, WidgetRef ref) {
    // Implementation would execute custom logic
    AppLogger.info('Custom action: ${action.params}');
  }

  /// Build entrance animation
  Widget _buildEntranceAnimation(Widget child, Map<String, dynamic> config) {
    final type = config['type'] as String? ?? 'fadeIn';
    final duration = Duration(milliseconds: config['duration'] as int? ?? 300);

    switch (type) {
      case 'fadeIn':
        return AnimatedOpacity(
          opacity: 1.0,
          duration: duration,
          child: child,
        );
      case 'slideIn':
        return AnimatedSlide(
          offset: Offset.zero,
          duration: duration,
          child: child,
        );
      default:
        return child;
    }
  }

  /// Helper methods for building styles
  EdgeInsets? _buildEdgeInsets(dynamic value) {
    if (value == null) return null;
    if (value is num) return EdgeInsets.all(value.toDouble());
    if (value is Map<String, dynamic>) {
      return EdgeInsets.only(
        top: (value['top'] as num?)?.toDouble() ?? 0,
        right: (value['right'] as num?)?.toDouble() ?? 0,
        bottom: (value['bottom'] as num?)?.toDouble() ?? 0,
        left: (value['left'] as num?)?.toDouble() ?? 0,
      );
    }
    return null;
  }

  Color? _buildColor(dynamic value) {
    if (value == null) return null;
    if (value is int) return Color(value);
    if (value is String && value.startsWith('#')) {
      final hex = value.substring(1);
      if (hex.length == 6) {
        return Color(int.parse('FF$hex', radix: 16));
      } else if (hex.length == 8) {
        return Color(int.parse(hex, radix: 16));
      }
    }
    return null;
  }

  Border? _buildBorder(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      final width = (value['width'] as num?)?.toDouble() ?? 1.0;
      final color = _buildColor(value['color']) ?? Colors.grey;
      return Border.all(width: width, color: color);
    }
    return null;
  }

  BorderRadius? _buildBorderRadius(dynamic value) {
    if (value == null) return null;
    if (value is num) return BorderRadius.circular(value.toDouble());
    return null;
  }

  List<BoxShadow>? _buildBoxShadow(dynamic value) {
    if (value == null) return null;
    // Simplified implementation
    return [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ];
  }

  /// Build permission denied widget
  Widget _buildPermissionDeniedWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text(
        'Access Denied',
        style: TextStyle(
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(dynamic error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error, color: Colors.red),
          const SizedBox(height: 8),
          Text(
            'Widget Error',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          Text(
            'Type: ${config.type}',
            style: const TextStyle(fontSize: 12, color: Colors.red),
          ),
          Text(
            'Error: ${error.toString()}',
            style: const TextStyle(fontSize: 10, color: Colors.red),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// Placeholder classes for missing models
class ConditionConfiguration {
  final String type;
  final String expression;
  
  const ConditionConfiguration({
    required this.type,
    required this.expression,
  });
}

class EventConfiguration {
  final String type;
  final ActionConfiguration action;
  
  const EventConfiguration({
    required this.type,
    required this.action,
  });
}

class ActionConfiguration {
  final String type;
  final String? target;
  final Map<String, dynamic>? params;
  
  const ActionConfiguration({
    required this.type,
    this.target,
    this.params,
  });
}
