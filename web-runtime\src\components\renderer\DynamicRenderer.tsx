import React, { Suspense, useMemo, useCallback } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { componentRegistry } from '../registry/ComponentRegistry';
import { 
  ComponentConfiguration, 
  StyleConfiguration, 
  ConditionConfiguration,
  EventConfiguration,
  ComponentProps 
} from '@types/index';
import { useRuntimeStore } from '@stores/runtimeStore';
import { useDataBinding } from '@hooks/useDataBinding';
import { useConditions } from '@hooks/useConditions';
import { useEventHandlers } from '@hooks/useEventHandlers';
import { cn } from '@utils/cn';
import LoadingSpinner from '../ui/LoadingSpinner';
import ErrorFallback from '../ui/ErrorFallback';

interface DynamicRendererProps {
  configuration: ComponentConfiguration;
  parentId?: string;
  index?: number;
}

const DynamicRenderer: React.FC<DynamicRendererProps> = ({
  configuration,
  parentId,
  index = 0,
}) => {
  const { data, permissions, user } = useRuntimeStore();
  
  // Resolve data bindings for component props
  const resolvedProps = useDataBinding(configuration.props, configuration.data);
  
  // Evaluate conditions
  const conditionResults = useConditions(configuration.conditions || [], {
    data,
    props: resolvedProps,
    user,
  });
  
  // Setup event handlers
  const eventHandlers = useEventHandlers(configuration.events || []);

  // Check permissions
  const hasPermission = useMemo(() => {
    if (!configuration.permissions || configuration.permissions.length === 0) {
      return true;
    }
    
    return configuration.permissions.every(permission => 
      permissions.includes(permission)
    );
  }, [configuration.permissions, permissions]);

  // Apply conditions to determine visibility and state
  const shouldRender = useMemo(() => {
    const showConditions = conditionResults.filter(result => 
      result.condition.type === 'show'
    );
    const hideConditions = conditionResults.filter(result => 
      result.condition.type === 'hide'
    );

    // If any hide condition is true, don't render
    if (hideConditions.some(result => result.value)) {
      return false;
    }

    // If there are show conditions, at least one must be true
    if (showConditions.length > 0) {
      return showConditions.some(result => result.value);
    }

    return true;
  }, [conditionResults]);

  // Apply conditional styles
  const conditionalStyles = useMemo(() => {
    const styleConditions = conditionResults.filter(result => 
      result.condition.type === 'style' && result.value
    );
    
    return styleConditions.reduce((styles, result) => ({
      ...styles,
      ...result.condition.style,
    }), {});
  }, [conditionResults]);

  // Get component from registry
  const Component = useMemo(() => {
    return componentRegistry.get(configuration.type);
  }, [configuration.type]);

  // Build final props
  const finalProps = useMemo(() => {
    const baseProps: ComponentProps = {
      id: configuration.id,
      ...resolvedProps,
      ...eventHandlers,
    };

    // Apply conditional disabled state
    const disableConditions = conditionResults.filter(result => 
      result.condition.type === 'disable' && result.value
    );
    if (disableConditions.length > 0) {
      baseProps.disabled = true;
    }

    // Apply conditional enabled state
    const enableConditions = conditionResults.filter(result => 
      result.condition.type === 'enable'
    );
    if (enableConditions.length > 0) {
      baseProps.disabled = !enableConditions.some(result => result.value);
    }

    return baseProps;
  }, [configuration.id, resolvedProps, eventHandlers, conditionResults]);

  // Build styles
  const styles = useMemo(() => {
    const baseStyles = buildStyles(configuration.style);
    return {
      ...baseStyles,
      ...conditionalStyles,
    };
  }, [configuration.style, conditionalStyles]);

  // Build className
  const className = useMemo(() => {
    return cn(
      configuration.style?.className,
      // Add responsive classes if needed
      buildResponsiveClasses(configuration.style),
      // Add position classes
      buildPositionClasses(configuration.position),
      // Add size classes
      buildSizeClasses(configuration.size)
    );
  }, [configuration.style, configuration.position, configuration.size]);

  // Render children
  const renderChildren = useCallback(() => {
    if (!configuration.children || configuration.children.length === 0) {
      return null;
    }

    return configuration.children.map((childConfig, childIndex) => (
      <DynamicRenderer
        key={childConfig.id || `child-${childIndex}`}
        configuration={childConfig}
        parentId={configuration.id}
        index={childIndex}
      />
    ));
  }, [configuration.children, configuration.id]);

  // Handle permission fallback
  if (!hasPermission) {
    if (configuration.permissions) {
      // Render permission fallback if defined
      const fallbackConfig = (configuration as any).permissionFallback;
      if (fallbackConfig) {
        return (
          <DynamicRenderer
            configuration={fallbackConfig}
            parentId={parentId}
            index={index}
          />
        );
      }
    }
    return null;
  }

  // Don't render if conditions say so
  if (!shouldRender) {
    return null;
  }

  // Handle unknown component type
  if (!Component) {
    console.warn(`Unknown component type: ${configuration.type}`);
    return (
      <div className="p-4 border-2 border-dashed border-red-300 bg-red-50 text-red-700 rounded">
        <p className="font-medium">Unknown Component</p>
        <p className="text-sm">Type: {configuration.type}</p>
        <p className="text-sm">ID: {configuration.id}</p>
      </div>
    );
  }

  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <ErrorFallback
          error={error}
          resetErrorBoundary={resetErrorBoundary}
          componentId={configuration.id}
          componentType={configuration.type}
        />
      )}
      onError={(error, errorInfo) => {
        console.error('Component render error:', {
          componentId: configuration.id,
          componentType: configuration.type,
          error,
          errorInfo,
        });
      }}
    >
      <Suspense
        fallback={
          <LoadingSpinner 
            size="sm" 
            className="inline-block"
            aria-label={`Loading ${configuration.type} component`}
          />
        }
      >
        <Component
          {...finalProps}
          className={className}
          style={styles}
          data-component-id={configuration.id}
          data-component-type={configuration.type}
        >
          {renderChildren()}
        </Component>
      </Suspense>
    </ErrorBoundary>
  );
};

// Helper functions for building styles and classes
function buildStyles(styleConfig?: StyleConfiguration): React.CSSProperties {
  if (!styleConfig) return {};
  
  return {
    ...styleConfig.style,
    // Add any computed styles here
  };
}

function buildResponsiveClasses(styleConfig?: StyleConfiguration): string {
  if (!styleConfig?.responsive) return '';
  
  const classes: string[] = [];
  
  Object.entries(styleConfig.responsive).forEach(([breakpoint, styles]) => {
    // Convert CSS properties to Tailwind classes for each breakpoint
    // This is a simplified implementation - in practice, you'd have a more
    // sophisticated mapping system
    if (breakpoint === 'sm') {
      classes.push('sm:responsive-styles');
    } else if (breakpoint === 'md') {
      classes.push('md:responsive-styles');
    }
    // Add more breakpoints as needed
  });
  
  return classes.join(' ');
}

function buildPositionClasses(position?: ComponentConfiguration['position']): string {
  if (!position) return '';
  
  const classes: string[] = [];
  
  // Grid positioning
  if (position.col !== undefined) {
    classes.push(`col-start-${position.col}`);
  }
  if (position.span !== undefined) {
    classes.push(`col-span-${position.span}`);
  }
  if (position.row !== undefined) {
    classes.push(`row-start-${position.row}`);
  }
  if (position.rowSpan !== undefined) {
    classes.push(`row-span-${position.rowSpan}`);
  }
  
  // Absolute positioning
  if (position.x !== undefined || position.y !== undefined) {
    classes.push('absolute');
    if (position.x !== undefined) {
      classes.push(`left-[${position.x}px]`);
    }
    if (position.y !== undefined) {
      classes.push(`top-[${position.y}px]`);
    }
  }
  
  // Z-index
  if (position.z !== undefined) {
    classes.push(`z-${position.z}`);
  }
  
  return classes.join(' ');
}

function buildSizeClasses(size?: ComponentConfiguration['size']): string {
  if (!size) return '';
  
  const classes: string[] = [];
  
  if (size.width) {
    if (typeof size.width === 'number') {
      classes.push(`w-[${size.width}px]`);
    } else {
      classes.push(`w-${size.width}`);
    }
  }
  
  if (size.height) {
    if (typeof size.height === 'number') {
      classes.push(`h-[${size.height}px]`);
    } else {
      classes.push(`h-${size.height}`);
    }
  }
  
  if (size.minWidth) {
    if (typeof size.minWidth === 'number') {
      classes.push(`min-w-[${size.minWidth}px]`);
    } else {
      classes.push(`min-w-${size.minWidth}`);
    }
  }
  
  if (size.minHeight) {
    if (typeof size.minHeight === 'number') {
      classes.push(`min-h-[${size.minHeight}px]`);
    } else {
      classes.push(`min-h-${size.minHeight}`);
    }
  }
  
  if (size.maxWidth) {
    if (typeof size.maxWidth === 'number') {
      classes.push(`max-w-[${size.maxWidth}px]`);
    } else {
      classes.push(`max-w-${size.maxWidth}`);
    }
  }
  
  if (size.maxHeight) {
    if (typeof size.maxHeight === 'number') {
      classes.push(`max-h-[${size.maxHeight}px]`);
    } else {
      classes.push(`max-h-${size.maxHeight}`);
    }
  }
  
  return classes.join(' ');
}

export default DynamicRenderer;
