@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
@layer base {
  html {
    font-family: var(--font-family-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-surface-50 text-surface-900;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid rgb(var(--color-primary-500));
    outline-offset: 2px;
  }

  *:focus:not(:focus-visible) {
    outline: none;
  }

  /* Selection styles */
  ::selection {
    background-color: rgb(var(--color-primary-200));
    color: rgb(var(--color-primary-900));
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgb(var(--color-surface-100));
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(var(--color-surface-300));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--color-surface-400));
  }

  /* Dark mode scrollbar */
  @media (prefers-color-scheme: dark) {
    ::-webkit-scrollbar-track {
      background: rgb(var(--color-surface-800));
    }

    ::-webkit-scrollbar-thumb {
      background: rgb(var(--color-surface-600));
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgb(var(--color-surface-500));
    }
  }
}

/* Component styles */
@layer components {
  /* Loading spinner */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-current border-t-transparent;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-ghost {
    @apply text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-md {
    @apply px-4 py-2 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Input styles */
  .input {
    @apply block w-full rounded-md border border-surface-300 px-3 py-2 text-sm placeholder-surface-400 focus:border-primary-500 focus:ring-primary-500 disabled:bg-surface-50 disabled:text-surface-500;
  }

  .input-error {
    @apply border-error-500 focus:border-error-500 focus:ring-error-500;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-surface-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-surface-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-surface-200 bg-surface-50;
  }

  /* Alert styles */
  .alert {
    @apply rounded-md p-4 border;
  }

  .alert-info {
    @apply bg-info-50 border-info-200 text-info-800;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-error {
    @apply bg-error-50 border-error-200 text-error-800;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  /* Form group styles */
  .form-group {
    @apply space-y-1;
  }

  .form-label {
    @apply block text-sm font-medium text-surface-700;
  }

  .form-help {
    @apply text-xs text-surface-500;
  }

  .form-error {
    @apply text-xs text-error-600;
  }

  /* Grid system */
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .grid-cols-auto {
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  }

  /* Responsive utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-photo {
    aspect-ratio: 4 / 3;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--animation-duration-normal) var(--animation-easing-ease-out);
  }

  .animate-slide-up {
    animation: slideUp var(--animation-duration-normal) var(--animation-easing-ease-out);
  }

  .animate-scale-in {
    animation: scaleIn var(--animation-duration-fast) var(--animation-easing-ease-out);
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-surface-200 rounded;
  }

  .skeleton-text {
    @apply h-4 bg-surface-200 rounded;
  }

  .skeleton-avatar {
    @apply w-10 h-10 bg-surface-200 rounded-full;
  }

  /* Accessibility */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  .not-sr-only {
    @apply static w-auto h-auto p-0 m-0 overflow-visible whitespace-normal;
    clip: auto;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    .print-only {
      display: block !important;
    }

    * {
      color-adjust: exact;
    }
  }
}

/* Utility styles */
@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Layout utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Interaction utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .touch-pan-x {
    touch-action: pan-x;
  }

  .touch-pan-y {
    touch-action: pan-y;
  }

  /* Performance utilities */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  .contain-size {
    contain: size;
  }

  .contain-style {
    contain: style;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-spin {
    animation: none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }

  .input {
    border-width: 2px;
  }

  .card {
    border-width: 2px;
  }
}
