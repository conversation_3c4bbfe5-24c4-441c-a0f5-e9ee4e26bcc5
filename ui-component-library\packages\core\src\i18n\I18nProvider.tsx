import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Locale = string;
export type TranslationKey = string;
export type TranslationValues = Record<string, string | number>;

export interface Translation {
  [key: string]: string | Translation;
}

export interface I18nConfig {
  defaultLocale: Locale;
  fallbackLocale: Locale;
  supportedLocales: Locale[];
  translations: Record<Locale, Translation>;
  interpolation?: {
    prefix?: string;
    suffix?: string;
  };
  pluralization?: {
    rules?: Record<Locale, (count: number) => string>;
  };
}

interface I18nContextValue {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: TranslationKey, values?: TranslationValues, options?: TranslationOptions) => string;
  formatNumber: (value: number, options?: Intl.NumberFormatOptions) => string;
  formatDate: (value: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatCurrency: (value: number, currency: string, options?: Intl.NumberFormatOptions) => string;
  isRTL: boolean;
  supportedLocales: Locale[];
  loadTranslations: (locale: Locale, translations: Translation) => void;
}

interface TranslationOptions {
  count?: number;
  fallback?: string;
  interpolation?: boolean;
}

const I18nContext = createContext<I18nContextValue | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
  config: I18nConfig;
  storageKey?: string;
  detectBrowserLanguage?: boolean;
}

const RTL_LOCALES = ['ar', 'he', 'fa', 'ur', 'ku', 'dv'];

export const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  config,
  storageKey = 'ui-builder-locale',
  detectBrowserLanguage = true,
}) => {
  const [locale, setLocaleState] = useState<Locale>(() => {
    // Try to get locale from localStorage
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(storageKey);
      if (stored && config.supportedLocales.includes(stored)) {
        return stored;
      }
    }

    // Try to detect browser language
    if (detectBrowserLanguage && typeof navigator !== 'undefined') {
      const browserLang = navigator.language.split('-')[0];
      if (config.supportedLocales.includes(browserLang)) {
        return browserLang;
      }
    }

    return config.defaultLocale;
  });

  const [translations, setTranslations] = useState<Record<Locale, Translation>>(config.translations);

  // Save locale to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, locale);
    }
  }, [locale, storageKey]);

  // Apply RTL/LTR direction
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const isRTL = RTL_LOCALES.includes(locale);
      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
      document.documentElement.lang = locale;
    }
  }, [locale]);

  const setLocale = (newLocale: Locale) => {
    if (config.supportedLocales.includes(newLocale)) {
      setLocaleState(newLocale);
    } else {
      console.warn(`Locale "${newLocale}" is not supported. Supported locales: ${config.supportedLocales.join(', ')}`);
    }
  };

  const loadTranslations = (locale: Locale, newTranslations: Translation) => {
    setTranslations(prev => ({
      ...prev,
      [locale]: {
        ...prev[locale],
        ...newTranslations,
      },
    }));
  };

  const getNestedValue = (obj: Translation, path: string): string | undefined => {
    return path.split('.').reduce((current: any, key: string) => {
      return current && typeof current === 'object' ? current[key] : undefined;
    }, obj) as string | undefined;
  };

  const interpolate = (text: string, values: TranslationValues = {}): string => {
    const prefix = config.interpolation?.prefix || '{{';
    const suffix = config.interpolation?.suffix || '}}';
    
    return text.replace(
      new RegExp(`${prefix}\\s*(\\w+)\\s*${suffix}`, 'g'),
      (match, key) => {
        const value = values[key];
        return value !== undefined ? String(value) : match;
      }
    );
  };

  const pluralize = (key: string, count: number, currentTranslations: Translation): string => {
    const pluralRules = config.pluralization?.rules?.[locale];
    
    if (pluralRules) {
      const pluralForm = pluralRules(count);
      const pluralKey = `${key}.${pluralForm}`;
      const pluralValue = getNestedValue(currentTranslations, pluralKey);
      if (pluralValue) return pluralValue;
    }

    // Fallback to simple plural rules
    if (count === 0) {
      const zeroValue = getNestedValue(currentTranslations, `${key}.zero`);
      if (zeroValue) return zeroValue;
    }
    
    if (count === 1) {
      const oneValue = getNestedValue(currentTranslations, `${key}.one`);
      if (oneValue) return oneValue;
    }
    
    const otherValue = getNestedValue(currentTranslations, `${key}.other`);
    if (otherValue) return otherValue;
    
    // Fallback to base key
    return getNestedValue(currentTranslations, key) || key;
  };

  const t = (key: TranslationKey, values: TranslationValues = {}, options: TranslationOptions = {}): string => {
    const currentTranslations = translations[locale] || {};
    const fallbackTranslations = translations[config.fallbackLocale] || {};
    
    let translation: string | undefined;

    // Handle pluralization
    if (options.count !== undefined) {
      translation = pluralize(key, options.count, currentTranslations);
      if (translation === key && locale !== config.fallbackLocale) {
        translation = pluralize(key, options.count, fallbackTranslations);
      }
    } else {
      // Regular translation
      translation = getNestedValue(currentTranslations, key);
      
      // Fallback to fallback locale
      if (!translation && locale !== config.fallbackLocale) {
        translation = getNestedValue(fallbackTranslations, key);
      }
    }

    // Use fallback if provided
    if (!translation && options.fallback) {
      translation = options.fallback;
    }

    // Use key as fallback
    if (!translation) {
      translation = key;
      console.warn(`Translation missing for key: "${key}" in locale: "${locale}"`);
    }

    // Interpolate values
    if (options.interpolation !== false && Object.keys(values).length > 0) {
      translation = interpolate(translation, values);
    }

    return translation;
  };

  const formatNumber = (value: number, options: Intl.NumberFormatOptions = {}): string => {
    try {
      return new Intl.NumberFormat(locale, options).format(value);
    } catch (error) {
      console.warn(`Failed to format number for locale "${locale}":`, error);
      return value.toString();
    }
  };

  const formatDate = (value: Date, options: Intl.DateTimeFormatOptions = {}): string => {
    try {
      return new Intl.DateTimeFormat(locale, options).format(value);
    } catch (error) {
      console.warn(`Failed to format date for locale "${locale}":`, error);
      return value.toLocaleDateString();
    }
  };

  const formatCurrency = (value: number, currency: string, options: Intl.NumberFormatOptions = {}): string => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
        ...options,
      }).format(value);
    } catch (error) {
      console.warn(`Failed to format currency for locale "${locale}":`, error);
      return `${value} ${currency}`;
    }
  };

  const isRTL = RTL_LOCALES.includes(locale);

  const contextValue: I18nContextValue = {
    locale,
    setLocale,
    t,
    formatNumber,
    formatDate,
    formatCurrency,
    isRTL,
    supportedLocales: config.supportedLocales,
    loadTranslations,
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

export const useI18n = (): I18nContextValue => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// Hook for translation only
export const useTranslation = () => {
  const { t, locale } = useI18n();
  return { t, locale };
};

// Hook for formatting
export const useFormatting = () => {
  const { formatNumber, formatDate, formatCurrency, locale } = useI18n();
  return { formatNumber, formatDate, formatCurrency, locale };
};

// Hook for locale management
export const useLocale = () => {
  const { locale, setLocale, supportedLocales, isRTL } = useI18n();
  return { locale, setLocale, supportedLocales, isRTL };
};

// Higher-order component for i18n
export const withI18n = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const I18nComponent = (props: P) => {
    const i18n = useI18n();
    return <Component {...props} i18n={i18n} />;
  };

  I18nComponent.displayName = `withI18n(${Component.displayName || Component.name})`;
  return I18nComponent;
};

// Translation component for declarative usage
interface TransProps {
  i18nKey: TranslationKey;
  values?: TranslationValues;
  options?: TranslationOptions;
  component?: React.ElementType;
  children?: (translation: string) => ReactNode;
}

export const Trans: React.FC<TransProps> = ({
  i18nKey,
  values,
  options,
  component: Component = 'span',
  children,
}) => {
  const { t } = useI18n();
  const translation = t(i18nKey, values, options);

  if (children) {
    return <>{children(translation)}</>;
  }

  return <Component>{translation}</Component>;
};

// Locale selector component
interface LocaleSelectorProps {
  className?: string;
  showFlags?: boolean;
  showNativeNames?: boolean;
}

export const LocaleSelector: React.FC<LocaleSelectorProps> = ({
  className = '',
  showFlags = false,
  showNativeNames = true,
}) => {
  const { locale, setLocale, supportedLocales } = useI18n();

  const localeNames: Record<string, { native: string; english: string; flag?: string }> = {
    en: { native: 'English', english: 'English', flag: '🇺🇸' },
    es: { native: 'Español', english: 'Spanish', flag: '🇪🇸' },
    fr: { native: 'Français', english: 'French', flag: '🇫🇷' },
    de: { native: 'Deutsch', english: 'German', flag: '🇩🇪' },
    it: { native: 'Italiano', english: 'Italian', flag: '🇮🇹' },
    pt: { native: 'Português', english: 'Portuguese', flag: '🇵🇹' },
    ru: { native: 'Русский', english: 'Russian', flag: '🇷🇺' },
    ja: { native: '日本語', english: 'Japanese', flag: '🇯🇵' },
    ko: { native: '한국어', english: 'Korean', flag: '🇰🇷' },
    zh: { native: '中文', english: 'Chinese', flag: '🇨🇳' },
    ar: { native: 'العربية', english: 'Arabic', flag: '🇸🇦' },
    he: { native: 'עברית', english: 'Hebrew', flag: '🇮🇱' },
  };

  return (
    <select
      value={locale}
      onChange={(e) => setLocale(e.target.value)}
      className={`locale-selector ${className}`.trim()}
    >
      {supportedLocales.map((loc) => {
        const localeInfo = localeNames[loc] || { native: loc, english: loc };
        const displayName = showNativeNames ? localeInfo.native : localeInfo.english;
        const flag = showFlags && localeInfo.flag ? `${localeInfo.flag} ` : '';
        
        return (
          <option key={loc} value={loc}>
            {flag}{displayName}
          </option>
        );
      })}
    </select>
  );
};

// Utility functions
export const createI18nConfig = (config: Partial<I18nConfig>): I18nConfig => ({
  defaultLocale: 'en',
  fallbackLocale: 'en',
  supportedLocales: ['en'],
  translations: {},
  interpolation: {
    prefix: '{{',
    suffix: '}}',
  },
  ...config,
});

export const loadTranslationsAsync = async (
  locale: Locale,
  loader: () => Promise<Translation>
): Promise<Translation> => {
  try {
    return await loader();
  } catch (error) {
    console.error(`Failed to load translations for locale "${locale}":`, error);
    return {};
  }
};

export default I18nProvider;
