import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import '../models/ui_metadata.dart';

/// Real-time Service for Flutter UI Runtime
/// 
/// Provides WebSocket connectivity for:
/// - Live UI configuration updates
/// - Real-time collaboration
/// - User presence tracking
/// - Live data synchronization
/// - Event broadcasting

enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

class RealtimeEvent {
  final String type;
  final Map<String, dynamic> data;
  final String? userId;
  final DateTime timestamp;

  RealtimeEvent({
    required this.type,
    required this.data,
    this.userId,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory RealtimeEvent.fromJson(Map<String, dynamic> json) {
    return RealtimeEvent(
      type: json['type'],
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      userId: json['userId'],
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() => {
    'type': type,
    'data': data,
    'userId': userId,
    'timestamp': timestamp.toIso8601String(),
  };
}

class UserPresence {
  final String userId;
  final String name;
  final String? avatar;
  final String? currentConfigId;
  final String? currentComponentId;
  final Map<String, dynamic> metadata;
  final DateTime lastSeen;

  UserPresence({
    required this.userId,
    required this.name,
    this.avatar,
    this.currentConfigId,
    this.currentComponentId,
    this.metadata = const {},
    DateTime? lastSeen,
  }) : lastSeen = lastSeen ?? DateTime.now();

  factory UserPresence.fromJson(Map<String, dynamic> json) {
    return UserPresence(
      userId: json['userId'],
      name: json['name'],
      avatar: json['avatar'],
      currentConfigId: json['currentConfigId'],
      currentComponentId: json['currentComponentId'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      lastSeen: json['lastSeen'] != null 
          ? DateTime.parse(json['lastSeen'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'name': name,
    'avatar': avatar,
    'currentConfigId': currentConfigId,
    'currentComponentId': currentComponentId,
    'metadata': metadata,
    'lastSeen': lastSeen.toIso8601String(),
  };
}

class RealtimeService extends StateNotifier<ConnectionStatus> {
  RealtimeService() : super(ConnectionStatus.disconnected);

  late IO.Socket _socket;
  final Map<String, StreamController<RealtimeEvent>> _eventStreams = {};
  final Map<String, UserPresence> _activeUsers = {};
  final List<RealtimeEvent> _eventHistory = [];
  
  String? _currentUserId;
  String? _currentConfigId;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration heartbeatInterval = Duration(seconds: 30);

  /// Initialize connection
  Future<void> connect({
    required String serverUrl,
    required String userId,
    String? authToken,
    Map<String, dynamic>? options,
  }) async {
    if (state == ConnectionStatus.connected) {
      return;
    }

    state = ConnectionStatus.connecting;
    _currentUserId = userId;

    try {
      _socket = IO.io(serverUrl, IO.OptionBuilder()
          .setTransports(['websocket'])
          .setAuth({
            'userId': userId,
            if (authToken != null) 'token': authToken,
            ...?options,
          })
          .enableAutoConnect()
          .enableReconnection()
          .setReconnectionAttempts(maxReconnectAttempts)
          .setReconnectionDelay(1000)
          .build());

      _setupEventHandlers();
      _socket.connect();
    } catch (e) {
      state = ConnectionStatus.error;
      throw RealtimeException('Failed to connect: $e');
    }
  }

  /// Disconnect from server
  void disconnect() {
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    _socket.disconnect();
    _socket.dispose();
    state = ConnectionStatus.disconnected;
    _activeUsers.clear();
    _eventHistory.clear();
  }

  /// Join a configuration room for collaboration
  Future<void> joinConfiguration(String configId) async {
    if (state != ConnectionStatus.connected) {
      throw RealtimeException('Not connected to server');
    }

    _currentConfigId = configId;
    _socket.emit('join-configuration', {
      'configId': configId,
      'userId': _currentUserId,
    });
  }

  /// Leave current configuration room
  void leaveConfiguration() {
    if (_currentConfigId != null) {
      _socket.emit('leave-configuration', {
        'configId': _currentConfigId,
        'userId': _currentUserId,
      });
      _currentConfigId = null;
    }
  }

  /// Send configuration update
  void sendConfigurationUpdate(UIConfiguration config) {
    if (state != ConnectionStatus.connected) return;

    final event = RealtimeEvent(
      type: 'configuration-update',
      data: {
        'configId': config.id,
        'configuration': config.toJson(),
      },
      userId: _currentUserId,
    );

    _socket.emit('configuration-update', event.toJson());
  }

  /// Send component update
  void sendComponentUpdate(ComponentDefinition component, String configId) {
    if (state != ConnectionStatus.connected) return;

    final event = RealtimeEvent(
      type: 'component-update',
      data: {
        'configId': configId,
        'component': component.toJson(),
      },
      userId: _currentUserId,
    );

    _socket.emit('component-update', event.toJson());
  }

  /// Send user presence update
  void updatePresence({
    String? currentConfigId,
    String? currentComponentId,
    Map<String, dynamic>? metadata,
  }) {
    if (state != ConnectionStatus.connected) return;

    final presence = UserPresence(
      userId: _currentUserId!,
      name: 'Current User', // This would come from user profile
      currentConfigId: currentConfigId,
      currentComponentId: currentComponentId,
      metadata: metadata ?? {},
    );

    _socket.emit('presence-update', presence.toJson());
  }

  /// Send custom event
  void sendEvent(String eventType, Map<String, dynamic> data) {
    if (state != ConnectionStatus.connected) return;

    final event = RealtimeEvent(
      type: eventType,
      data: data,
      userId: _currentUserId,
    );

    _socket.emit('custom-event', event.toJson());
  }

  /// Get stream for specific event type
  Stream<RealtimeEvent> getEventStream(String eventType) {
    if (!_eventStreams.containsKey(eventType)) {
      _eventStreams[eventType] = StreamController<RealtimeEvent>.broadcast();
    }
    return _eventStreams[eventType]!.stream;
  }

  /// Get active users in current configuration
  List<UserPresence> getActiveUsers() {
    return _activeUsers.values.toList();
  }

  /// Get event history
  List<RealtimeEvent> getEventHistory({int? limit}) {
    if (limit != null && limit < _eventHistory.length) {
      return _eventHistory.sublist(_eventHistory.length - limit);
    }
    return List.from(_eventHistory);
  }

  /// Setup socket event handlers
  void _setupEventHandlers() {
    _socket.onConnect((_) {
      print('Connected to realtime server');
      state = ConnectionStatus.connected;
      _reconnectAttempts = 0;
      _startHeartbeat();
    });

    _socket.onDisconnect((_) {
      print('Disconnected from realtime server');
      state = ConnectionStatus.disconnected;
      _heartbeatTimer?.cancel();
      _attemptReconnect();
    });

    _socket.onConnectError((error) {
      print('Connection error: $error');
      state = ConnectionStatus.error;
      _attemptReconnect();
    });

    _socket.onReconnect((_) {
      print('Reconnected to realtime server');
      state = ConnectionStatus.connected;
      _reconnectAttempts = 0;
      
      // Rejoin configuration if we were in one
      if (_currentConfigId != null) {
        joinConfiguration(_currentConfigId!);
      }
    });

    // Configuration events
    _socket.on('configuration-update', (data) {
      final event = RealtimeEvent.fromJson(data);
      _handleEvent(event);
    });

    _socket.on('component-update', (data) {
      final event = RealtimeEvent.fromJson(data);
      _handleEvent(event);
    });

    // Presence events
    _socket.on('user-joined', (data) {
      final presence = UserPresence.fromJson(data);
      _activeUsers[presence.userId] = presence;
      
      final event = RealtimeEvent(
        type: 'user-joined',
        data: presence.toJson(),
      );
      _handleEvent(event);
    });

    _socket.on('user-left', (data) {
      final userId = data['userId'] as String;
      _activeUsers.remove(userId);
      
      final event = RealtimeEvent(
        type: 'user-left',
        data: {'userId': userId},
      );
      _handleEvent(event);
    });

    _socket.on('presence-update', (data) {
      final presence = UserPresence.fromJson(data);
      _activeUsers[presence.userId] = presence;
      
      final event = RealtimeEvent(
        type: 'presence-update',
        data: presence.toJson(),
      );
      _handleEvent(event);
    });

    // Custom events
    _socket.on('custom-event', (data) {
      final event = RealtimeEvent.fromJson(data);
      _handleEvent(event);
    });

    // System events
    _socket.on('error', (error) {
      print('Socket error: $error');
      final event = RealtimeEvent(
        type: 'error',
        data: {'error': error.toString()},
      );
      _handleEvent(event);
    });
  }

  /// Handle incoming events
  void _handleEvent(RealtimeEvent event) {
    // Add to history
    _eventHistory.add(event);
    
    // Keep history limited
    if (_eventHistory.length > 1000) {
      _eventHistory.removeAt(0);
    }

    // Emit to specific event stream
    if (_eventStreams.containsKey(event.type)) {
      _eventStreams[event.type]!.add(event);
    }

    // Emit to general event stream
    if (_eventStreams.containsKey('*')) {
      _eventStreams['*']!.add(event);
    }
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (state == ConnectionStatus.connected) {
        _socket.emit('heartbeat', {
          'timestamp': DateTime.now().toIso8601String(),
          'userId': _currentUserId,
        });
      }
    });
  }

  /// Attempt to reconnect
  void _attemptReconnect() {
    if (_reconnectAttempts >= maxReconnectAttempts) {
      print('Max reconnection attempts reached');
      state = ConnectionStatus.error;
      return;
    }

    _reconnectAttempts++;
    state = ConnectionStatus.reconnecting;
    
    final delay = Duration(seconds: _reconnectAttempts * 2);
    _reconnectTimer = Timer(delay, () {
      if (state == ConnectionStatus.reconnecting) {
        _socket.connect();
      }
    });
  }

  @override
  void dispose() {
    disconnect();
    for (final controller in _eventStreams.values) {
      controller.close();
    }
    _eventStreams.clear();
    super.dispose();
  }
}

/// Realtime Exception
class RealtimeException implements Exception {
  final String message;
  final String? code;

  RealtimeException(this.message, {this.code});

  @override
  String toString() => 'RealtimeException: $message';
}

/// Providers
final realtimeServiceProvider = StateNotifierProvider<RealtimeService, ConnectionStatus>((ref) {
  return RealtimeService();
});

final connectionStatusProvider = Provider<ConnectionStatus>((ref) {
  return ref.watch(realtimeServiceProvider);
});

final activeUsersProvider = Provider<List<UserPresence>>((ref) {
  final service = ref.read(realtimeServiceProvider.notifier);
  return service.getActiveUsers();
});

final configurationUpdatesProvider = StreamProvider<RealtimeEvent>((ref) {
  final service = ref.read(realtimeServiceProvider.notifier);
  return service.getEventStream('configuration-update');
});

final componentUpdatesProvider = StreamProvider<RealtimeEvent>((ref) {
  final service = ref.read(realtimeServiceProvider.notifier);
  return service.getEventStream('component-update');
});

final presenceUpdatesProvider = StreamProvider<RealtimeEvent>((ref) {
  final service = ref.read(realtimeServiceProvider.notifier);
  return service.getEventStream('presence-update');
});

/// Helper extensions
extension RealtimeServiceExtension on WidgetRef {
  RealtimeService get realtime => read(realtimeServiceProvider.notifier);
  
  ConnectionStatus get connectionStatus => read(connectionStatusProvider);
  
  bool get isConnected => connectionStatus == ConnectionStatus.connected;
  
  void connectRealtime({
    required String serverUrl,
    required String userId,
    String? authToken,
  }) {
    realtime.connect(
      serverUrl: serverUrl,
      userId: userId,
      authToken: authToken,
    );
  }
  
  void joinConfig(String configId) {
    realtime.joinConfiguration(configId);
  }
  
  void sendConfigUpdate(UIConfiguration config) {
    realtime.sendConfigurationUpdate(config);
  }
}
