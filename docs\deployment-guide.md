# UI Builder Platform - Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the UI Builder platform to production environments. The platform consists of multiple microservices, databases, and supporting infrastructure components.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │   Web Frontend  │
│    (ALB/NLB)    │────│   (Kong/Nginx)  │────│    (React)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌────────┴────────┐
                       │                 │
            ┌─────────────────┐    ┌─────────────────┐
            │  UI Metadata    │    │  Collaboration  │
            │    Service      │    │    Service      │
            └─────────────────┘    └─────────────────┘
                       │                 │
                ┌──────┴──────┐   ┌──────┴──────┐
                │             │   │             │
         ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
         │ PostgreSQL  │ │    Redis    │ │   Kafka     │
         │  Database   │ │   Cache     │ │ Messaging   │
         └─────────────┘ └─────────────┘ └─────────────┘
```

## Prerequisites

### Infrastructure Requirements

- **Kubernetes Cluster**: v1.28+ with at least 3 worker nodes
- **Node Specifications**: 
  - Minimum: 4 vCPU, 16GB RAM per node
  - Recommended: 8 vCPU, 32GB RAM per node
- **Storage**: 500GB+ persistent storage with backup capabilities
- **Network**: Load balancer with SSL termination
- **DNS**: Domain name with SSL certificates

### Software Requirements

- **kubectl**: v1.28+
- **Helm**: v3.12+
- **Terraform**: v1.5+ (for infrastructure provisioning)
- **Docker**: v24.0+ (for building images)
- **AWS CLI**: v2.0+ (if using AWS)

### Access Requirements

- Kubernetes cluster admin access
- Container registry push/pull access
- DNS management access
- SSL certificate management access

## Pre-Deployment Setup

### 1. Infrastructure Provisioning

```bash
# Clone the repository
git clone https://github.com/ui-builder/platform.git
cd platform

# Initialize Terraform
cd infrastructure/terraform
terraform init

# Review and customize variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values

# Plan and apply infrastructure
terraform plan
terraform apply
```

### 2. DNS and SSL Configuration

```bash
# Create DNS records
# A record: api.yourdomain.com -> Load Balancer IP
# A record: app.yourdomain.com -> Load Balancer IP
# A record: components.yourdomain.com -> Load Balancer IP

# SSL Certificate (using cert-manager)
kubectl apply -f infrastructure/ssl/cert-manager.yaml
kubectl apply -f infrastructure/ssl/cluster-issuer.yaml
```

### 3. Container Registry Setup

```bash
# Build and push images
docker build -t your-registry/ui-metadata-service:latest ./ui-metadata-service
docker build -t your-registry/collaboration-service:latest ./collaboration-service
docker build -t your-registry/ui-builder-frontend:latest ./ui-builder-frontend

docker push your-registry/ui-metadata-service:latest
docker push your-registry/collaboration-service:latest
docker push your-registry/ui-builder-frontend:latest
```

## Deployment Steps

### 1. Create Kubernetes Namespace

```bash
kubectl create namespace ui-builder
kubectl label namespace ui-builder istio-injection=enabled
```

### 2. Deploy Secrets and ConfigMaps

```bash
# Database credentials
kubectl create secret generic postgres-credentials \
  --from-literal=username=uibuilder \
  --from-literal=password=your-secure-password \
  --namespace=ui-builder

# Redis credentials
kubectl create secret generic redis-credentials \
  --from-literal=password=your-redis-password \
  --namespace=ui-builder

# JWT secrets
kubectl create secret generic jwt-secrets \
  --from-literal=secret=your-jwt-secret-key \
  --namespace=ui-builder

# AWS credentials (for S3, etc.)
kubectl create secret generic aws-credentials \
  --from-literal=access-key-id=your-access-key \
  --from-literal=secret-access-key=your-secret-key \
  --namespace=ui-builder
```

### 3. Deploy Database Layer

```bash
# PostgreSQL
kubectl apply -f infrastructure/kubernetes/postgres.yaml

# Redis
kubectl apply -f infrastructure/kubernetes/redis.yaml

# Kafka
kubectl apply -f infrastructure/kubernetes/kafka.yaml

# Wait for databases to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n ui-builder --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n ui-builder --timeout=300s
kubectl wait --for=condition=ready pod -l app=kafka -n ui-builder --timeout=300s
```

### 4. Deploy Application Services

```bash
# Update image tags in manifests
sed -i 's|ui-builder/ui-metadata-service:latest|your-registry/ui-metadata-service:latest|g' \
  infrastructure/kubernetes/ui-metadata-service.yaml

# Deploy services
kubectl apply -f infrastructure/kubernetes/ui-metadata-service.yaml
kubectl apply -f infrastructure/kubernetes/collaboration-service.yaml
kubectl apply -f infrastructure/kubernetes/notification-service.yaml

# Wait for services to be ready
kubectl wait --for=condition=ready pod -l app=ui-metadata-service -n ui-builder --timeout=300s
kubectl wait --for=condition=ready pod -l app=collaboration-service -n ui-builder --timeout=300s
```

### 5. Deploy Frontend Applications

```bash
# UI Builder Frontend
kubectl apply -f infrastructure/kubernetes/ui-builder-frontend.yaml

# Web Runtime
kubectl apply -f infrastructure/kubernetes/web-runtime.yaml

# Wait for frontend to be ready
kubectl wait --for=condition=ready pod -l app=ui-builder-frontend -n ui-builder --timeout=300s
```

### 6. Deploy Ingress and Load Balancer

```bash
# Deploy ingress controller
kubectl apply -f infrastructure/kubernetes/ingress.yaml

# Verify ingress is working
kubectl get ingress -n ui-builder
```

### 7. Deploy Monitoring Stack

```bash
# Prometheus
kubectl apply -f monitoring/prometheus/

# Grafana
kubectl apply -f monitoring/grafana/

# Alertmanager
kubectl apply -f monitoring/alertmanager/
```

## Post-Deployment Configuration

### 1. Database Initialization

```bash
# Run database migrations
kubectl exec -it deployment/ui-metadata-service -n ui-builder -- \
  java -jar app.jar --spring.profiles.active=migration

# Verify database schema
kubectl exec -it deployment/postgres -n ui-builder -- \
  psql -U uibuilder -d uibuilder -c "\dt"
```

### 2. Health Checks

```bash
# Check all pods are running
kubectl get pods -n ui-builder

# Check service health endpoints
curl https://api.yourdomain.com/health
curl https://api.yourdomain.com/actuator/health

# Check frontend accessibility
curl https://app.yourdomain.com
```

### 3. Load Testing

```bash
# Install k6 for load testing
kubectl apply -f testing/load-tests/k6-job.yaml

# Monitor during load test
kubectl logs -f job/load-test -n ui-builder
```

## Backup and Disaster Recovery

### 1. Enable Automated Backups

```bash
# Deploy backup jobs
kubectl apply -f infrastructure/backup/backup-strategy.yaml

# Verify backup jobs are scheduled
kubectl get cronjobs -n ui-builder
```

### 2. Test Backup Restoration

```bash
# Test database restore
kubectl create job --from=cronjob/postgres-backup test-backup -n ui-builder

# Verify backup files
aws s3 ls s3://ui-builder-backups/database/postgres/
```

## Monitoring and Alerting

### 1. Configure Alerts

```bash
# Deploy alert rules
kubectl apply -f monitoring/prometheus/rules/

# Verify alerts are loaded
kubectl exec -it prometheus-0 -n monitoring -- \
  promtool query instant 'up{job="ui-metadata-service"}'
```

### 2. Set Up Dashboards

```bash
# Import Grafana dashboards
kubectl apply -f monitoring/grafana/dashboards/

# Access Grafana
kubectl port-forward svc/grafana 3000:3000 -n monitoring
# Open http://localhost:3000
```

## Security Hardening

### 1. Network Policies

```bash
# Apply network policies
kubectl apply -f infrastructure/security/network-policies.yaml
```

### 2. Pod Security Standards

```bash
# Apply pod security policies
kubectl apply -f infrastructure/security/pod-security.yaml
```

### 3. RBAC Configuration

```bash
# Apply RBAC rules
kubectl apply -f infrastructure/security/rbac.yaml
```

## Scaling Configuration

### 1. Horizontal Pod Autoscaling

```bash
# HPA is already configured in deployment manifests
# Verify HPA is working
kubectl get hpa -n ui-builder

# Monitor scaling events
kubectl describe hpa ui-metadata-service-hpa -n ui-builder
```

### 2. Cluster Autoscaling

```bash
# Configure cluster autoscaler (AWS EKS example)
kubectl apply -f infrastructure/autoscaling/cluster-autoscaler.yaml
```

## Troubleshooting

### Common Issues

1. **Pods not starting**
   ```bash
   kubectl describe pod <pod-name> -n ui-builder
   kubectl logs <pod-name> -n ui-builder
   ```

2. **Database connection issues**
   ```bash
   kubectl exec -it deployment/ui-metadata-service -n ui-builder -- \
     nc -zv postgres-service 5432
   ```

3. **SSL certificate issues**
   ```bash
   kubectl describe certificate -n ui-builder
   kubectl logs -l app=cert-manager -n cert-manager
   ```

### Performance Tuning

1. **Database optimization**
   - Adjust connection pool sizes
   - Configure query optimization
   - Set up read replicas

2. **Cache optimization**
   - Tune Redis memory settings
   - Configure cache eviction policies
   - Monitor cache hit rates

3. **Application tuning**
   - Adjust JVM heap sizes
   - Configure garbage collection
   - Optimize thread pools

## Maintenance Procedures

### 1. Rolling Updates

```bash
# Update application image
kubectl set image deployment/ui-metadata-service \
  ui-metadata-service=your-registry/ui-metadata-service:v2.0.0 \
  -n ui-builder

# Monitor rollout
kubectl rollout status deployment/ui-metadata-service -n ui-builder
```

### 2. Database Maintenance

```bash
# Schedule maintenance window
kubectl scale deployment ui-metadata-service --replicas=0 -n ui-builder

# Perform database maintenance
kubectl exec -it deployment/postgres -n ui-builder -- \
  psql -U uibuilder -d uibuilder -c "VACUUM ANALYZE;"

# Scale back up
kubectl scale deployment ui-metadata-service --replicas=3 -n ui-builder
```

### 3. Certificate Renewal

```bash
# Certificates are auto-renewed by cert-manager
# Verify renewal status
kubectl describe certificate -n ui-builder
```

## Support and Documentation

- **Runbooks**: `/docs/runbooks/`
- **API Documentation**: `https://api.yourdomain.com/swagger-ui`
- **Monitoring Dashboards**: `https://grafana.yourdomain.com`
- **Support**: `<EMAIL>`

## Compliance and Auditing

### SOC 2 Compliance

- Audit logs are automatically collected
- Access controls are enforced via RBAC
- Data encryption is enabled at rest and in transit
- Backup and disaster recovery procedures are documented

### GDPR Compliance

- Data retention policies are configured
- User data deletion procedures are implemented
- Privacy controls are enforced
- Data processing logs are maintained
