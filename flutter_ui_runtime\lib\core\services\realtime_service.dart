import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

import '../config/app_config.dart';
import '../models/ui_metadata.dart';
import '../models/theme_config.dart';
import '../utils/logger.dart';
import '../providers/app_state_provider.dart';
import 'auth_service.dart';

part 'realtime_service.g.dart';

/// Real-time service provider
@riverpod
RealtimeService realtimeService(RealtimeServiceRef ref) {
  return RealtimeService(ref);
}

/// Real-time service for WebSocket communication
class RealtimeService {
  final Ref ref;
  io.Socket? _socket;
  bool _isConnected = false;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final _connectionController = StreamController<bool>.broadcast();
  final _messageController = StreamController<RealtimeMessage>.broadcast();

  RealtimeService(this.ref);

  /// Connection status stream
  Stream<bool> get connectionStream => _connectionController.stream;

  /// Message stream
  Stream<RealtimeMessage> get messageStream => _messageController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect() async {
    if (_socket != null && _isConnected) {
      AppLogger.debug('Already connected to WebSocket');
      return;
    }

    try {
      final authService = ref.read(authServiceProvider);
      final token = authService.getAccessToken();

      _socket = io.io(
        AppConfig.websocketUrl,
        io.OptionBuilder()
            .setTransports(['websocket'])
            .enableAutoConnect()
            .enableReconnection()
            .setReconnectionAttempts(AppConfig.maxReconnectAttempts)
            .setReconnectionDelay(AppConfig.reconnectDelay.inMilliseconds)
            .setAuth({'token': token})
            .build(),
      );

      _setupEventHandlers();
      _socket!.connect();

      AppLogger.info('Connecting to WebSocket: ${AppConfig.websocketUrl}');
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to connect to WebSocket',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Disconnect from WebSocket server
  void disconnect() {
    _reconnectTimer?.cancel();
    _socket?.disconnect();
    _socket?.dispose();
    _socket = null;
    _isConnected = false;
    _reconnectAttempts = 0;
    _connectionController.add(false);
    AppLogger.info('Disconnected from WebSocket');
  }

  /// Send message to server
  void sendMessage(String event, Map<String, dynamic> data) {
    if (_socket != null && _isConnected) {
      _socket!.emit(event, data);
      AppLogger.debug('Sent message: $event');
    } else {
      AppLogger.warning('Cannot send message - not connected');
    }
  }

  /// Join a specific page room
  void joinPage(String pageId) {
    sendMessage('join-page', {'pageId': pageId});
  }

  /// Leave a page room
  void leavePage(String pageId) {
    sendMessage('leave-page', {'pageId': pageId});
  }

  /// Send user presence update
  void sendPresence(Map<String, dynamic> presence) {
    sendMessage('user-presence', presence);
  }

  /// Send cursor position
  void sendCursorPosition(double x, double y) {
    sendMessage('cursor-update', {
      'x': x,
      'y': y,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Setup WebSocket event handlers
  void _setupEventHandlers() {
    if (_socket == null) return;

    // Connection events
    _socket!.onConnect((_) {
      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
      AppLogger.info('Connected to WebSocket');
      
      // Send initial presence
      _sendInitialPresence();
    });

    _socket!.onDisconnect((_) {
      _isConnected = false;
      _connectionController.add(false);
      AppLogger.warning('Disconnected from WebSocket');
      
      // Attempt reconnection
      _scheduleReconnect();
    });

    _socket!.onConnectError((error) {
      _isConnected = false;
      _connectionController.add(false);
      AppLogger.error('WebSocket connection error: $error');
      
      // Attempt reconnection
      _scheduleReconnect();
    });

    // Real-time update events
    _socket!.on('ui-metadata-update', (data) {
      _handleUIMetadataUpdate(data);
    });

    _socket!.on('theme-update', (data) {
      _handleThemeUpdate(data);
    });

    _socket!.on('data-update', (data) {
      _handleDataUpdate(data);
    });

    _socket!.on('component-update', (data) {
      _handleComponentUpdate(data);
    });

    // Collaboration events
    _socket!.on('user-joined', (data) {
      _handleUserJoined(data);
    });

    _socket!.on('user-left', (data) {
      _handleUserLeft(data);
    });

    _socket!.on('cursor-update', (data) {
      _handleCursorUpdate(data);
    });

    _socket!.on('user-presence', (data) {
      _handleUserPresence(data);
    });

    // Error handling
    _socket!.on('error', (error) {
      AppLogger.error('WebSocket error: $error');
      _messageController.add(RealtimeMessage(
        type: 'error',
        data: {'error': error.toString()},
        timestamp: DateTime.now(),
      ));
    });

    // Custom message handler
    _socket!.on('message', (data) {
      _handleCustomMessage(data);
    });
  }

  /// Handle UI metadata updates
  void _handleUIMetadataUpdate(dynamic data) {
    try {
      final metadata = UIMetadata.fromJson(data as Map<String, dynamic>);
      ref.read(appStateProvider.notifier).updateUIMetadata(metadata);
      
      _messageController.add(RealtimeMessage(
        type: 'ui-metadata-update',
        data: data,
        timestamp: DateTime.now(),
      ));
      
      AppLogger.debug('UI metadata updated via WebSocket');
    } catch (error) {
      AppLogger.error('Failed to handle UI metadata update: $error');
    }
  }

  /// Handle theme updates
  void _handleThemeUpdate(dynamic data) {
    try {
      final themeConfig = ThemeConfig.fromJson(data as Map<String, dynamic>);
      ref.read(themeStateProvider.notifier).updateTheme(themeConfig);
      
      _messageController.add(RealtimeMessage(
        type: 'theme-update',
        data: data,
        timestamp: DateTime.now(),
      ));
      
      AppLogger.debug('Theme updated via WebSocket');
    } catch (error) {
      AppLogger.error('Failed to handle theme update: $error');
    }
  }

  /// Handle data updates
  void _handleDataUpdate(dynamic data) {
    try {
      final updates = data as Map<String, dynamic>;
      ref.read(dataStateProvider.notifier).updateData(updates);
      
      _messageController.add(RealtimeMessage(
        type: 'data-update',
        data: data,
        timestamp: DateTime.now(),
      ));
      
      AppLogger.debug('Data updated via WebSocket');
    } catch (error) {
      AppLogger.error('Failed to handle data update: $error');
    }
  }

  /// Handle component updates
  void _handleComponentUpdate(dynamic data) {
    try {
      _messageController.add(RealtimeMessage(
        type: 'component-update',
        data: data,
        timestamp: DateTime.now(),
      ));
      
      AppLogger.debug('Component updated via WebSocket');
    } catch (error) {
      AppLogger.error('Failed to handle component update: $error');
    }
  }

  /// Handle user joined events
  void _handleUserJoined(dynamic data) {
    _messageController.add(RealtimeMessage(
      type: 'user-joined',
      data: data,
      timestamp: DateTime.now(),
    ));
    
    AppLogger.debug('User joined: ${data['username']}');
  }

  /// Handle user left events
  void _handleUserLeft(dynamic data) {
    _messageController.add(RealtimeMessage(
      type: 'user-left',
      data: data,
      timestamp: DateTime.now(),
    ));
    
    AppLogger.debug('User left: ${data['username']}');
  }

  /// Handle cursor updates
  void _handleCursorUpdate(dynamic data) {
    _messageController.add(RealtimeMessage(
      type: 'cursor-update',
      data: data,
      timestamp: DateTime.now(),
    ));
  }

  /// Handle user presence updates
  void _handleUserPresence(dynamic data) {
    _messageController.add(RealtimeMessage(
      type: 'user-presence',
      data: data,
      timestamp: DateTime.now(),
    ));
  }

  /// Handle custom messages
  void _handleCustomMessage(dynamic data) {
    _messageController.add(RealtimeMessage(
      type: 'custom',
      data: data,
      timestamp: DateTime.now(),
    ));
  }

  /// Send initial presence information
  void _sendInitialPresence() {
    final userState = ref.read(userStateProvider);
    if (userState.user != null) {
      sendPresence({
        'userId': userState.user!.id,
        'username': userState.user!.username,
        'status': 'online',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= AppConfig.maxReconnectAttempts) {
      AppLogger.error('Max reconnection attempts reached');
      return;
    }

    _reconnectAttempts++;
    final delay = Duration(
      milliseconds: AppConfig.reconnectDelay.inMilliseconds * _reconnectAttempts,
    );

    AppLogger.info('Scheduling reconnection attempt $_reconnectAttempts in ${delay.inSeconds}s');

    _reconnectTimer = Timer(delay, () {
      if (!_isConnected) {
        connect();
      }
    });
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _connectionController.close();
    _messageController.close();
  }
}

/// Real-time message model
class RealtimeMessage {
  final String type;
  final dynamic data;
  final DateTime timestamp;

  RealtimeMessage({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory RealtimeMessage.fromJson(Map<String, dynamic> json) {
    return RealtimeMessage(
      type: json['type'],
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Real-time connection state provider
@riverpod
Stream<bool> realtimeConnection(RealtimeConnectionRef ref) {
  final service = ref.watch(realtimeServiceProvider);
  return service.connectionStream;
}

/// Real-time messages provider
@riverpod
Stream<RealtimeMessage> realtimeMessages(RealtimeMessagesRef ref) {
  final service = ref.watch(realtimeServiceProvider);
  return service.messageStream;
}
