package com.uiplatform.repository;

import com.uiplatform.entity.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Comment entity operations.
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, UUID> {

    /**
     * Find all comments for a UI configuration (excluding deleted).
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByUIConfigurationIdAndDeletedFalse(@Param("configId") UUID configId);

    /**
     * Find comments for a specific element in a UI configuration.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.elementId = :elementId AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByUIConfigurationIdAndElementIdAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("elementId") String elementId);

    /**
     * Find comments by thread ID.
     */
    @Query("SELECT c FROM Comment c WHERE c.threadId = :threadId AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByThreadIdAndDeletedFalse(@Param("threadId") UUID threadId);

    /**
     * Find top-level comments (no parent) for a UI configuration.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.parentCommentId IS NULL AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findTopLevelCommentsByUIConfigurationId(@Param("configId") UUID configId);

    /**
     * Find replies to a specific comment.
     */
    @Query("SELECT c FROM Comment c WHERE c.parentCommentId = :parentId AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findRepliesByParentCommentId(@Param("parentId") UUID parentId);

    /**
     * Find comments by author.
     */
    @Query("SELECT c FROM Comment c WHERE c.authorId = :authorId AND c.deleted = false ORDER BY c.createdAt DESC")
    Page<Comment> findByAuthorIdAndDeletedFalse(@Param("authorId") UUID authorId, Pageable pageable);

    /**
     * Find unresolved comments for a UI configuration.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.isResolved = false AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findUnresolvedCommentsByUIConfigurationId(@Param("configId") UUID configId);

    /**
     * Find resolved comments for a UI configuration.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.isResolved = true AND c.deleted = false ORDER BY c.resolvedAt DESC")
    List<Comment> findResolvedCommentsByUIConfigurationId(@Param("configId") UUID configId);

    /**
     * Find comments by status.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.status = :status AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByUIConfigurationIdAndStatusAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("status") Comment.CommentStatus status);

    /**
     * Find comments by priority.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.priority = :priority AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByUIConfigurationIdAndPriorityAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("priority") Comment.CommentPriority priority);

    /**
     * Find comments created after a specific date.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.createdAt > :since AND c.deleted = false ORDER BY c.createdAt ASC")
    List<Comment> findByUIConfigurationIdAndCreatedAtAfterAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("since") LocalDateTime since);

    /**
     * Find comments with mentions of a specific user.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.mentionUserIds LIKE %:userId% AND c.deleted = false ORDER BY c.createdAt DESC")
    List<Comment> findCommentsWithUserMention(
            @Param("configId") UUID configId, 
            @Param("userId") String userId);

    /**
     * Count comments for a UI configuration.
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.uiConfigurationId = :configId AND c.deleted = false")
    long countByUIConfigurationIdAndDeletedFalse(@Param("configId") UUID configId);

    /**
     * Count unresolved comments for a UI configuration.
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.uiConfigurationId = :configId AND c.isResolved = false AND c.deleted = false")
    long countUnresolvedByUIConfigurationId(@Param("configId") UUID configId);

    /**
     * Count comments by author for a UI configuration.
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.uiConfigurationId = :configId AND c.authorId = :authorId AND c.deleted = false")
    long countByUIConfigurationIdAndAuthorIdAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("authorId") UUID authorId);

    /**
     * Find comments by element with pagination.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.elementId = :elementId AND c.deleted = false ORDER BY c.createdAt ASC")
    Page<Comment> findByUIConfigurationIdAndElementIdAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("elementId") String elementId, 
            Pageable pageable);

    /**
     * Search comments by text content.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND LOWER(c.text) LIKE LOWER(CONCAT('%', :searchText, '%')) AND c.deleted = false ORDER BY c.createdAt DESC")
    List<Comment> searchCommentsByText(
            @Param("configId") UUID configId, 
            @Param("searchText") String searchText);

    /**
     * Find comments by tags.
     */
    @Query("SELECT c FROM Comment c WHERE c.uiConfigurationId = :configId AND c.tags LIKE %:tag% AND c.deleted = false ORDER BY c.createdAt DESC")
    List<Comment> findByUIConfigurationIdAndTagsContainingAndDeletedFalse(
            @Param("configId") UUID configId, 
            @Param("tag") String tag);

    /**
     * Find recent comments across all UI configurations for an organization.
     */
    @Query("SELECT c FROM Comment c JOIN UIConfiguration ui ON c.uiConfigurationId = ui.id " +
           "WHERE ui.organizationId = :orgId AND c.createdAt > :since AND c.deleted = false " +
           "ORDER BY c.createdAt DESC")
    List<Comment> findRecentCommentsByOrganization(
            @Param("orgId") UUID organizationId, 
            @Param("since") LocalDateTime since);

    /**
     * Find comment by ID (excluding deleted).
     */
    @Query("SELECT c FROM Comment c WHERE c.id = :id AND c.deleted = false")
    Optional<Comment> findByIdAndDeletedFalse(@Param("id") UUID id);

    /**
     * Get comment statistics for a UI configuration.
     */
    @Query("SELECT " +
           "COUNT(c) as totalComments, " +
           "SUM(CASE WHEN c.isResolved = false THEN 1 ELSE 0 END) as unresolvedComments, " +
           "SUM(CASE WHEN c.isResolved = true THEN 1 ELSE 0 END) as resolvedComments, " +
           "COUNT(DISTINCT c.authorId) as uniqueAuthors " +
           "FROM Comment c WHERE c.uiConfigurationId = :configId AND c.deleted = false")
    CommentStatistics getCommentStatistics(@Param("configId") UUID configId);

    /**
     * Interface for comment statistics projection.
     */
    interface CommentStatistics {
        long getTotalComments();
        long getUnresolvedComments();
        long getResolvedComments();
        long getUniqueAuthors();
    }
}
