import { describe, it, expect, beforeEach } from 'vitest';
import { configureStore } from '@reduxjs/toolkit';
import uiBuilderReducer, {
  setCurrentConfiguration,
  addComponent,
  updateComponent,
  deleteComponent,
  selectComponent,
  clearSelection,
  undo,
  redo,
  setCanvasZoom,
  toggleGrid,
} from '@store/slices/uiBuilderSlice';
import { createMockUIConfiguration, createMockUIComponent } from '@test/utils/test-utils';

describe('uiBuilderSlice', () => {
  let store: ReturnType<typeof configureStore>;
  const mockConfiguration = createMockUIConfiguration();
  const mockComponent = createMockUIComponent();

  beforeEach(() => {
    store = configureStore({
      reducer: {
        uiBuilder: uiBuilderReducer,
      },
    });
  });

  describe('configuration management', () => {
    it('should set current configuration', () => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration).toEqual(mockConfiguration);
      expect(state.history.present).toEqual(mockConfiguration);
      expect(state.history.past).toHaveLength(0);
      expect(state.history.future).toHaveLength(0);
    });

    it('should clear current configuration', () => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
      store.dispatch({ type: 'uiBuilder/clearCurrentConfiguration' });
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration).toBeNull();
      expect(state.history.present).toBeNull();
      expect(state.selection.selectedComponentIds).toHaveLength(0);
    });
  });

  describe('component management', () => {
    beforeEach(() => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
    });

    it('should add component to root level', () => {
      const newComponent = createMockUIComponent({ id: 'new-comp' });
      
      store.dispatch(addComponent({ component: newComponent }));
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration?.components).toHaveLength(2);
      expect(state.currentConfiguration?.components[1]).toEqual(newComponent);
      expect(state.selection.selectedComponentIds).toEqual([newComponent.id]);
    });

    it('should add component to specific parent', () => {
      const parentComponent = createMockUIComponent({ 
        id: 'parent-comp',
        children: [],
      });
      const childComponent = createMockUIComponent({ id: 'child-comp' });
      
      // First add parent
      store.dispatch(addComponent({ component: parentComponent }));
      
      // Then add child to parent
      store.dispatch(addComponent({ 
        component: childComponent, 
        parentId: parentComponent.id 
      }));
      
      const state = store.getState().uiBuilder;
      const parent = state.currentConfiguration?.components.find(c => c.id === parentComponent.id);
      expect(parent?.children).toHaveLength(1);
      expect(parent?.children?.[0]).toEqual({
        ...childComponent,
        parentId: parentComponent.id,
      });
    });

    it('should update component properties', () => {
      const componentId = mockConfiguration.components[0].id;
      const updates = {
        properties: { text: 'Updated text' },
        styles: { color: '#ff0000' },
      };
      
      store.dispatch(updateComponent({ id: componentId, updates }));
      
      const state = store.getState().uiBuilder;
      const updatedComponent = state.currentConfiguration?.components[0];
      expect(updatedComponent?.properties.text).toBe('Updated text');
      expect(updatedComponent?.styles.color).toBe('#ff0000');
    });

    it('should delete component', () => {
      const componentId = mockConfiguration.components[0].id;
      
      store.dispatch(deleteComponent(componentId));
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration?.components).toHaveLength(0);
      expect(state.selection.selectedComponentIds).not.toContain(componentId);
    });

    it('should move component to new parent', () => {
      const parentComponent = createMockUIComponent({ 
        id: 'parent-comp',
        children: [],
      });
      const componentToMove = mockConfiguration.components[0];
      
      // Add parent component
      store.dispatch(addComponent({ component: parentComponent }));
      
      // Move existing component to parent
      store.dispatch({
        type: 'uiBuilder/moveComponent',
        payload: {
          componentId: componentToMove.id,
          newParentId: parentComponent.id,
          newIndex: 0,
        },
      });
      
      const state = store.getState().uiBuilder;
      const parent = state.currentConfiguration?.components.find(c => c.id === parentComponent.id);
      expect(parent?.children).toHaveLength(1);
      expect(parent?.children?.[0].id).toBe(componentToMove.id);
    });

    it('should duplicate component', () => {
      const componentId = mockConfiguration.components[0].id;
      
      store.dispatch({ type: 'uiBuilder/duplicateComponent', payload: componentId });
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration?.components).toHaveLength(2);
      
      const original = state.currentConfiguration?.components[0];
      const duplicate = state.currentConfiguration?.components[1];
      
      expect(duplicate?.id).not.toBe(original?.id);
      expect(duplicate?.properties).toEqual(original?.properties);
      expect(duplicate?.styles).toEqual(original?.styles);
    });
  });

  describe('selection management', () => {
    beforeEach(() => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
    });

    it('should select single component', () => {
      const componentId = mockConfiguration.components[0].id;
      
      store.dispatch(selectComponent(componentId));
      
      const state = store.getState().uiBuilder;
      expect(state.selection.selectedComponentIds).toEqual([componentId]);
    });

    it('should handle multi-select mode', () => {
      const componentId1 = mockConfiguration.components[0].id;
      const componentId2 = 'comp-2';
      
      // Add second component
      store.dispatch(addComponent({ 
        component: createMockUIComponent({ id: componentId2 }) 
      }));
      
      // Enable multi-select mode
      store.dispatch({ type: 'uiBuilder/setMultiSelectMode', payload: true });
      
      // Select first component
      store.dispatch(selectComponent(componentId1));
      
      // Select second component (should add to selection)
      store.dispatch(selectComponent(componentId2));
      
      const state = store.getState().uiBuilder;
      expect(state.selection.selectedComponentIds).toContain(componentId1);
      expect(state.selection.selectedComponentIds).toContain(componentId2);
    });

    it('should clear selection', () => {
      const componentId = mockConfiguration.components[0].id;
      
      store.dispatch(selectComponent(componentId));
      store.dispatch(clearSelection());
      
      const state = store.getState().uiBuilder;
      expect(state.selection.selectedComponentIds).toHaveLength(0);
    });

    it('should set hovered component', () => {
      const componentId = mockConfiguration.components[0].id;
      
      store.dispatch({ 
        type: 'uiBuilder/setHoveredComponent', 
        payload: componentId 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.selection.hoveredComponentId).toBe(componentId);
    });
  });

  describe('clipboard operations', () => {
    beforeEach(() => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
    });

    it('should copy components to clipboard', () => {
      const componentIds = [mockConfiguration.components[0].id];
      
      store.dispatch({ 
        type: 'uiBuilder/copyComponents', 
        payload: componentIds 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.clipboard.components).toHaveLength(1);
      expect(state.clipboard.operation).toBe('copy');
    });

    it('should cut components to clipboard', () => {
      const componentIds = [mockConfiguration.components[0].id];
      
      store.dispatch({ 
        type: 'uiBuilder/cutComponents', 
        payload: componentIds 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.clipboard.components).toHaveLength(1);
      expect(state.clipboard.operation).toBe('cut');
      expect(state.currentConfiguration?.components).toHaveLength(0);
    });

    it('should paste components from clipboard', () => {
      const componentIds = [mockConfiguration.components[0].id];
      
      // Copy component
      store.dispatch({ 
        type: 'uiBuilder/copyComponents', 
        payload: componentIds 
      });
      
      // Paste component
      store.dispatch({ 
        type: 'uiBuilder/pasteComponents', 
        payload: {} 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration?.components).toHaveLength(2);
    });
  });

  describe('canvas controls', () => {
    it('should set canvas zoom', () => {
      store.dispatch(setCanvasZoom(1.5));
      
      const state = store.getState().uiBuilder;
      expect(state.canvas.zoom).toBe(1.5);
    });

    it('should clamp zoom values', () => {
      store.dispatch(setCanvasZoom(10)); // Above max
      expect(store.getState().uiBuilder.canvas.zoom).toBe(5);
      
      store.dispatch(setCanvasZoom(0.01)); // Below min
      expect(store.getState().uiBuilder.canvas.zoom).toBe(0.1);
    });

    it('should set canvas pan', () => {
      const panPosition = { x: 100, y: 200 };
      
      store.dispatch({ 
        type: 'uiBuilder/setCanvasPan', 
        payload: panPosition 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.canvas.pan).toEqual(panPosition);
    });

    it('should toggle grid visibility', () => {
      const initialGridState = store.getState().uiBuilder.canvas.showGrid;
      
      store.dispatch(toggleGrid());
      
      const state = store.getState().uiBuilder;
      expect(state.canvas.showGrid).toBe(!initialGridState);
    });

    it('should set grid size', () => {
      store.dispatch({ 
        type: 'uiBuilder/setGridSize', 
        payload: 25 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.canvas.gridSize).toBe(25);
    });

    it('should clamp grid size values', () => {
      store.dispatch({ 
        type: 'uiBuilder/setGridSize', 
        payload: 200 
      }); // Above max
      expect(store.getState().uiBuilder.canvas.gridSize).toBe(100);
      
      store.dispatch({ 
        type: 'uiBuilder/setGridSize', 
        payload: 1 
      }); // Below min
      expect(store.getState().uiBuilder.canvas.gridSize).toBe(5);
    });
  });

  describe('history management', () => {
    beforeEach(() => {
      store.dispatch(setCurrentConfiguration(mockConfiguration));
    });

    it('should support undo operation', () => {
      const newComponent = createMockUIComponent({ id: 'new-comp' });
      
      // Make a change
      store.dispatch(addComponent({ component: newComponent }));
      expect(store.getState().uiBuilder.currentConfiguration?.components).toHaveLength(2);
      
      // Undo the change
      store.dispatch(undo());
      expect(store.getState().uiBuilder.currentConfiguration?.components).toHaveLength(1);
    });

    it('should support redo operation', () => {
      const newComponent = createMockUIComponent({ id: 'new-comp' });
      
      // Make a change
      store.dispatch(addComponent({ component: newComponent }));
      
      // Undo the change
      store.dispatch(undo());
      expect(store.getState().uiBuilder.currentConfiguration?.components).toHaveLength(1);
      
      // Redo the change
      store.dispatch(redo());
      expect(store.getState().uiBuilder.currentConfiguration?.components).toHaveLength(2);
    });

    it('should not undo when no history exists', () => {
      const initialState = store.getState().uiBuilder;
      
      store.dispatch(undo());
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration).toEqual(initialState.currentConfiguration);
    });

    it('should not redo when no future history exists', () => {
      const initialState = store.getState().uiBuilder;
      
      store.dispatch(redo());
      
      const state = store.getState().uiBuilder;
      expect(state.currentConfiguration).toEqual(initialState.currentConfiguration);
    });

    it('should limit history size', () => {
      // Add many components to exceed history limit
      for (let i = 0; i < 60; i++) {
        store.dispatch(addComponent({ 
          component: createMockUIComponent({ id: `comp-${i}` }) 
        }));
      }
      
      const state = store.getState().uiBuilder;
      expect(state.history.past.length).toBeLessThanOrEqual(50);
    });
  });

  describe('UI state management', () => {
    it('should toggle preview mode', () => {
      const initialPreviewMode = store.getState().uiBuilder.ui.previewMode;
      
      store.dispatch({ type: 'uiBuilder/togglePreviewMode' });
      
      const state = store.getState().uiBuilder;
      expect(state.ui.previewMode).toBe(!initialPreviewMode);
    });

    it('should set device preview', () => {
      store.dispatch({ 
        type: 'uiBuilder/setDevicePreview', 
        payload: 'mobile' 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.ui.devicePreview).toBe('mobile');
    });

    it('should toggle panel visibility', () => {
      const initialLeftPanelState = store.getState().uiBuilder.ui.leftPanelCollapsed;
      
      store.dispatch({ type: 'uiBuilder/toggleLeftPanel' });
      
      const state = store.getState().uiBuilder;
      expect(state.ui.leftPanelCollapsed).toBe(!initialLeftPanelState);
    });

    it('should set panel widths', () => {
      store.dispatch({ 
        type: 'uiBuilder/setLeftPanelWidth', 
        payload: 400 
      });
      
      const state = store.getState().uiBuilder;
      expect(state.ui.leftPanelWidth).toBe(400);
    });

    it('should clamp panel widths', () => {
      store.dispatch({ 
        type: 'uiBuilder/setLeftPanelWidth', 
        payload: 100 
      }); // Below min
      expect(store.getState().uiBuilder.ui.leftPanelWidth).toBe(200);
      
      store.dispatch({ 
        type: 'uiBuilder/setLeftPanelWidth', 
        payload: 800 
      }); // Above max
      expect(store.getState().uiBuilder.ui.leftPanelWidth).toBe(600);
    });
  });
});
