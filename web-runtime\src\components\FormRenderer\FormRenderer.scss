.form-renderer {
  width: 100%;
  max-width: 100%;
  
  &.form-layout-vertical {
    .form-fields {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md, 1rem);
    }
  }
  
  &.form-layout-horizontal {
    .form-fields {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: var(--spacing-md, 1rem);
      
      .form-field {
        flex: 1;
        min-width: 200px;
      }
    }
  }
  
  &.form-layout-inline {
    .form-fields {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: end;
      gap: var(--spacing-sm, 0.5rem);
      
      .form-field {
        flex: 0 0 auto;
        
        .form-label {
          margin-bottom: var(--spacing-xs, 0.25rem);
        }
      }
    }
  }
  
  &.form-layout-grid {
    .form-fields {
      display: grid;
      gap: var(--spacing-md, 1rem);
      
      &.form-grid-2 {
        grid-template-columns: repeat(2, 1fr);
      }
      
      &.form-grid-3 {
        grid-template-columns: repeat(3, 1fr);
      }
      
      &.form-grid-4 {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }
  
  &.form-debug {
    border: 2px dashed rgba(59, 130, 246, 0.5);
    padding: var(--spacing-md, 1rem);
    
    .form-debug-info {
      background: rgba(59, 130, 246, 0.1);
      padding: var(--spacing-sm, 0.5rem);
      margin-bottom: var(--spacing-md, 1rem);
      border-radius: var(--border-radius-md, 0.375rem);
      font-size: var(--font-size-sm, 0.875rem);
      font-family: var(--font-family-mono, monospace);
      
      div {
        margin-bottom: 2px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.form-field {
  display: flex;
  flex-direction: column;
  
  &.form-field-error {
    .form-label {
      color: var(--color-error, #ef4444);
    }
    
    .form-input,
    .form-textarea,
    .form-select,
    .form-multiselect {
      border-color: var(--color-error, #ef4444);
      
      &:focus {
        border-color: var(--color-error, #ef4444);
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
      }
    }
  }
}

.form-label {
  font-weight: var(--font-weight-medium, 500);
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-text-primary, #1e293b);
  margin-bottom: var(--spacing-xs, 0.25rem);
  
  .required-indicator {
    color: var(--color-error, #ef4444);
    margin-left: 2px;
  }
}

.form-input-wrapper {
  position: relative;
}

.form-input,
.form-textarea,
.form-select,
.form-multiselect {
  width: 100%;
  padding: var(--spacing-sm, 0.5rem) var(--spacing-md, 1rem);
  border: 1px solid var(--color-border-medium, #e2e8f0);
  border-radius: var(--border-radius-md, 0.375rem);
  font-size: var(--font-size-base, 1rem);
  line-height: var(--line-height-normal, 1.5);
  background-color: var(--color-background, #ffffff);
  color: var(--color-text-primary, #1e293b);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--color-primary, #3b82f6);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  
  &:disabled {
    background-color: var(--color-surface, #f8fafc);
    color: var(--color-text-muted, #94a3b8);
    cursor: not-allowed;
  }
  
  &:read-only {
    background-color: var(--color-surface, #f8fafc);
  }
  
  &::placeholder {
    color: var(--color-text-muted, #94a3b8);
  }
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-select,
.form-multiselect {
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
}

.form-multiselect {
  min-height: 80px;
}

.form-checkbox-wrapper,
.form-radio-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 0.5rem);
  cursor: pointer;
  
  &:hover {
    .form-checkbox-label,
    .form-radio-label {
      color: var(--color-primary, #3b82f6);
    }
  }
}

.form-checkbox,
.form-radio {
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary, #3b82f6);
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
}

.form-checkbox-label,
.form-radio-label {
  font-size: var(--font-size-base, 1rem);
  color: var(--color-text-primary, #1e293b);
  cursor: pointer;
  transition: color 0.2s ease;
}

.form-radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm, 0.5rem);
}

.form-file {
  padding: var(--spacing-sm, 0.5rem);
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
}

.form-help-text {
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-text-secondary, #64748b);
  margin-top: var(--spacing-xs, 0.25rem);
}

.form-error-message {
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--color-error, #ef4444);
  margin-top: var(--spacing-xs, 0.25rem);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs, 0.25rem);
  
  &::before {
    content: "⚠";
    font-size: 12px;
  }
}

.form-error-summary {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--spacing-md, 1rem);
  margin-bottom: var(--spacing-lg, 1.5rem);
  
  h4 {
    margin: 0 0 var(--spacing-sm, 0.5rem) 0;
    color: var(--color-error, #ef4444);
    font-size: var(--font-size-base, 1rem);
  }
  
  ul {
    margin: 0;
    padding-left: var(--spacing-lg, 1.5rem);
    
    li {
      color: var(--color-error, #ef4444);
      font-size: var(--font-size-sm, 0.875rem);
      margin-bottom: var(--spacing-xs, 0.25rem);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.form-submit-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--spacing-md, 1rem);
  margin-bottom: var(--spacing-lg, 1.5rem);
  color: var(--color-error, #ef4444);
  font-size: var(--font-size-sm, 0.875rem);
}

.form-submit-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--spacing-md, 1rem);
  margin-bottom: var(--spacing-lg, 1.5rem);
  color: var(--color-success, #10b981);
  font-size: var(--font-size-sm, 0.875rem);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md, 1rem);
  margin-top: var(--spacing-lg, 1.5rem);
  padding-top: var(--spacing-lg, 1.5rem);
  border-top: 1px solid var(--color-border-light, #f1f5f9);
}

.form-submit-button,
.form-reset-button {
  padding: var(--spacing-sm, 0.5rem) var(--spacing-lg, 1.5rem);
  border-radius: var(--border-radius-md, 0.375rem);
  font-size: var(--font-size-base, 1rem);
  font-weight: var(--font-weight-medium, 500);
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.form-submit-button {
  background-color: var(--color-primary, #3b82f6);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--color-primary-dark, #2563eb);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
}

.form-reset-button {
  background-color: transparent;
  color: var(--color-text-secondary, #64748b);
  border: 1px solid var(--color-border-medium, #e2e8f0);
  
  &:hover:not(:disabled) {
    background-color: var(--color-surface, #f8fafc);
    border-color: var(--color-border-strong, #cbd5e1);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(100, 116, 139, 0.2);
  }
}

// Responsive design
@media (max-width: 768px) {
  .form-renderer {
    &.form-layout-horizontal,
    &.form-layout-grid {
      .form-fields {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md, 1rem);
      }
    }
    
    &.form-layout-inline {
      .form-fields {
        flex-direction: column;
        align-items: stretch;
        
        .form-field {
          flex: 1;
        }
      }
    }
  }
  
  .form-actions {
    flex-direction: column;
    
    .form-submit-button,
    .form-reset-button {
      width: 100%;
    }
  }
}

// Dark theme support
.dark-theme {
  .form-input,
  .form-textarea,
  .form-select,
  .form-multiselect {
    background-color: var(--color-surface, #1e293b);
    border-color: var(--color-border-medium, #475569);
    color: var(--color-text-primary, #f8fafc);
    
    &::placeholder {
      color: var(--color-text-muted, #64748b);
    }
  }
  
  .form-checkbox-label,
  .form-radio-label {
    color: var(--color-text-primary, #f8fafc);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .form-input,
  .form-textarea,
  .form-select,
  .form-multiselect {
    border-width: 2px;
    
    &:focus {
      border-width: 3px;
    }
  }
  
  .form-submit-button {
    border: 2px solid transparent;
    
    &:focus {
      border-color: white;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .form-textarea,
  .form-select,
  .form-multiselect,
  .form-submit-button,
  .form-reset-button,
  .form-checkbox-label,
  .form-radio-label {
    transition: none;
  }
}

// Print styles
@media print {
  .form-renderer {
    .form-actions {
      display: none;
    }
    
    .form-input,
    .form-textarea,
    .form-select {
      border: 1px solid #000;
      background: transparent;
    }
  }
}
