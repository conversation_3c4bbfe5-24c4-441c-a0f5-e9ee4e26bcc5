<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>UI Builder - Advanced Interface Designer</title>
    <meta name="description" content="Professional drag-and-drop UI builder for creating dynamic interfaces" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Critical CSS for loading state -->
    <style>
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        font-weight: 500;
        margin-top: 20px;
        opacity: 0.9;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading screen -->
      <div id="loading">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading UI Builder...</div>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Remove loading screen once app is loaded -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => loading.remove(), 300);
          }
        }, 500);
      });
    </script>
  </body>
</html>
