package com.uiplatform;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for the Dynamic UI Platform Backend.
 * 
 * This application provides a comprehensive backend service for a drag-and-drop
 * UI customization platform similar to Salesforce Lightning App Builder.
 * 
 * Features:
 * - Dynamic UI metadata management
 * - Component library and template marketplace
 * - Multi-tenant architecture with RBAC
 * - Real-time collaboration support
 * - GraphQL and REST APIs
 * - Caching and performance optimization
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EnableKafka
public class UiPlatformBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(UiPlatformBackendApplication.class, args);
    }
}
