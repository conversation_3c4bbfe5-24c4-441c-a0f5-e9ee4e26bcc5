import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';
import 'state_management.dart';

/// Action execution context
class ActionContext {
  final BuildContext buildContext;
  final StateManager stateManager;
  final Map<String, dynamic> componentProps;
  final Map<String, dynamic> globalData;
  final String componentId;

  ActionContext({
    required this.buildContext,
    required this.stateManager,
    required this.componentProps,
    required this.globalData,
    required this.componentId,
  });
}

/// Action execution result
class ActionResult {
  final bool success;
  final dynamic data;
  final String? error;
  final Map<String, dynamic>? stateChanges;

  ActionResult({
    required this.success,
    this.data,
    this.error,
    this.stateChanges,
  });

  factory ActionResult.success({dynamic data, Map<String, dynamic>? stateChanges}) {
    return ActionResult(
      success: true,
      data: data,
      stateChanges: stateChanges,
    );
  }

  factory ActionResult.error(String error) {
    return ActionResult(
      success: false,
      error: error,
    );
  }
}

/// Action handler function type
typedef ActionHandler = Future<ActionResult> Function(ActionContext context, Map<String, dynamic> params);

/// Action middleware function type
typedef ActionMiddleware = Future<ActionResult?> Function(ActionContext context, Map<String, dynamic> params, ActionHandler next);

/// Action Handler System for Flutter UI Runtime
/// 
/// Manages action execution, middleware, validation, and error handling
/// for component interactions and business logic.
class ActionHandlerSystem {
  static final ActionHandlerSystem _instance = ActionHandlerSystem._internal();
  factory ActionHandlerSystem() => _instance;
  ActionHandlerSystem._internal();

  final Map<String, ActionHandler> _handlers = {};
  final List<ActionMiddleware> _middleware = [];
  final Map<String, List<String>> _actionValidators = {};
  final Map<String, Map<String, dynamic>> _actionDefaults = {};
  bool _debugMode = false;

  /// Register an action handler
  void registerHandler(String actionType, ActionHandler handler) {
    _handlers[actionType] = handler;
    if (_debugMode) {
      print('Registered action handler: $actionType');
    }
  }

  /// Register middleware
  void registerMiddleware(ActionMiddleware middleware) {
    _middleware.add(middleware);
  }

  /// Execute an action
  Future<ActionResult> executeAction(
    String actionType,
    ActionContext context,
    Map<String, dynamic> params,
  ) async {
    try {
      if (_debugMode) {
        print('Executing action: $actionType with params: $params');
      }

      // Apply default parameters
      final finalParams = _applyDefaults(actionType, params);

      // Validate parameters
      final validationResult = _validateParams(actionType, finalParams);
      if (!validationResult.success) {
        return validationResult;
      }

      // Get handler
      final handler = _handlers[actionType];
      if (handler == null) {
        return ActionResult.error('Unknown action type: $actionType');
      }

      // Execute with middleware chain
      final result = await _executeWithMiddleware(context, finalParams, handler);

      // Apply state changes if any
      if (result.success && result.stateChanges != null) {
        _applyStateChanges(context.stateManager, result.stateChanges!);
      }

      if (_debugMode) {
        print('Action $actionType completed: ${result.success ? 'success' : 'error'}');
      }

      return result;
    } catch (e) {
      if (_debugMode) {
        print('Action $actionType failed with exception: $e');
      }
      return ActionResult.error('Action execution failed: $e');
    }
  }

  /// Execute action with middleware chain
  Future<ActionResult> _executeWithMiddleware(
    ActionContext context,
    Map<String, dynamic> params,
    ActionHandler handler,
  ) async {
    int index = 0;

    Future<ActionResult> next() async {
      if (index < _middleware.length) {
        final middleware = _middleware[index++];
        return await middleware(context, params, next) ?? await next();
      } else {
        return await handler(context, params);
      }
    }

    return await next();
  }

  /// Apply default parameters
  Map<String, dynamic> _applyDefaults(String actionType, Map<String, dynamic> params) {
    final defaults = _actionDefaults[actionType] ?? {};
    return {...defaults, ...params};
  }

  /// Validate action parameters
  ActionResult _validateParams(String actionType, Map<String, dynamic> params) {
    final validators = _actionValidators[actionType] ?? [];
    
    for (final validator in validators) {
      if (!params.containsKey(validator)) {
        return ActionResult.error('Missing required parameter: $validator');
      }
    }
    
    return ActionResult.success();
  }

  /// Apply state changes
  void _applyStateChanges(StateManager stateManager, Map<String, dynamic> changes) {
    for (final entry in changes.entries) {
      stateManager.set(entry.key, entry.value, source: 'action');
    }
  }

  /// Set action parameter defaults
  void setActionDefaults(String actionType, Map<String, dynamic> defaults) {
    _actionDefaults[actionType] = defaults;
  }

  /// Set required parameters for action
  void setRequiredParams(String actionType, List<String> requiredParams) {
    _actionValidators[actionType] = requiredParams;
  }

  /// Enable/disable debug mode
  void setDebugMode(bool enabled) {
    _debugMode = enabled;
  }

  /// Get registered action types
  Set<String> getRegisteredActions() {
    return _handlers.keys.toSet();
  }

  /// Clear all handlers and middleware
  void clear() {
    _handlers.clear();
    _middleware.clear();
    _actionValidators.clear();
    _actionDefaults.clear();
  }
}

/// Initialize default action handlers
void initializeDefaultActions() {
  final actionSystem = ActionHandlerSystem();

  // Navigation actions
  actionSystem.registerHandler('navigate', (context, params) async {
    final route = params['route'] as String?;
    final arguments = params['arguments'] as Map<String, dynamic>?;

    if (route == null) {
      return ActionResult.error('Route parameter is required');
    }

    try {
      Navigator.of(context.buildContext).pushNamed(route, arguments: arguments);
      return ActionResult.success();
    } catch (e) {
      return ActionResult.error('Navigation failed: $e');
    }
  });

  actionSystem.registerHandler('navigate_back', (context, params) async {
    try {
      Navigator.of(context.buildContext).pop(params['result']);
      return ActionResult.success();
    } catch (e) {
      return ActionResult.error('Navigation back failed: $e');
    }
  });

  actionSystem.registerHandler('navigate_replace', (context, params) async {
    final route = params['route'] as String?;
    final arguments = params['arguments'] as Map<String, dynamic>?;

    if (route == null) {
      return ActionResult.error('Route parameter is required');
    }

    try {
      Navigator.of(context.buildContext).pushReplacementNamed(route, arguments: arguments);
      return ActionResult.success();
    } catch (e) {
      return ActionResult.error('Navigation replace failed: $e');
    }
  });

  // State actions
  actionSystem.registerHandler('set_state', (context, params) async {
    final key = params['key'] as String?;
    final value = params['value'];

    if (key == null) {
      return ActionResult.error('Key parameter is required');
    }

    context.stateManager.set(key, value, source: 'action');
    return ActionResult.success();
  });

  actionSystem.registerHandler('update_state', (context, params) async {
    final updates = params['updates'] as Map<String, dynamic>?;

    if (updates == null) {
      return ActionResult.error('Updates parameter is required');
    }

    for (final entry in updates.entries) {
      context.stateManager.set(entry.key, entry.value, source: 'action');
    }

    return ActionResult.success();
  });

  actionSystem.registerHandler('toggle_state', (context, params) async {
    final key = params['key'] as String?;

    if (key == null) {
      return ActionResult.error('Key parameter is required');
    }

    final currentValue = context.stateManager.get<bool>(key) ?? false;
    context.stateManager.set(key, !currentValue, source: 'action');
    
    return ActionResult.success();
  });

  // UI actions
  actionSystem.registerHandler('show_dialog', (context, params) async {
    final title = params['title'] as String?;
    final content = params['content'] as String?;
    final actions = params['actions'] as List<Map<String, dynamic>>?;

    try {
      final result = await showDialog<dynamic>(
        context: context.buildContext,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: title != null ? Text(title) : null,
            content: content != null ? Text(content) : null,
            actions: actions?.map((action) {
              return TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop(action['value']);
                },
                child: Text(action['label'] as String? ?? 'OK'),
              );
            }).toList(),
          );
        },
      );

      return ActionResult.success(data: result);
    } catch (e) {
      return ActionResult.error('Show dialog failed: $e');
    }
  });

  actionSystem.registerHandler('show_snackbar', (context, params) async {
    final message = params['message'] as String?;
    final duration = params['duration'] as int? ?? 3000;

    if (message == null) {
      return ActionResult.error('Message parameter is required');
    }

    try {
      ScaffoldMessenger.of(context.buildContext).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: Duration(milliseconds: duration),
        ),
      );

      return ActionResult.success();
    } catch (e) {
      return ActionResult.error('Show snackbar failed: $e');
    }
  });

  actionSystem.registerHandler('show_bottom_sheet', (context, params) async {
    final content = params['content'] as Map<String, dynamic>?;

    if (content == null) {
      return ActionResult.error('Content parameter is required');
    }

    try {
      final result = await showModalBottomSheet<dynamic>(
        context: context.buildContext,
        builder: (BuildContext sheetContext) {
          // This would render the content using the widget registry
          return Container(
            padding: const EdgeInsets.all(16),
            child: Text(content['text'] as String? ?? 'Bottom Sheet'),
          );
        },
      );

      return ActionResult.success(data: result);
    } catch (e) {
      return ActionResult.error('Show bottom sheet failed: $e');
    }
  });

  // HTTP actions
  actionSystem.registerHandler('http_request', (context, params) async {
    final url = params['url'] as String?;
    final method = params['method'] as String? ?? 'GET';
    final headers = params['headers'] as Map<String, String>?;
    final body = params['body'];

    if (url == null) {
      return ActionResult.error('URL parameter is required');
    }

    try {
      // This would use an HTTP client like Dio
      // For now, just simulate the request
      await Future.delayed(const Duration(milliseconds: 500));
      
      final responseData = {
        'status': 200,
        'data': {'message': 'Request successful'},
      };

      return ActionResult.success(data: responseData);
    } catch (e) {
      return ActionResult.error('HTTP request failed: $e');
    }
  });

  // Form actions
  actionSystem.registerHandler('submit_form', (context, params) async {
    final formKey = params['formKey'] as String?;
    final endpoint = params['endpoint'] as String?;

    if (formKey == null) {
      return ActionResult.error('FormKey parameter is required');
    }

    try {
      // Get form data from state
      final formData = <String, dynamic>{};
      for (final key in context.stateManager.keys) {
        if (key.startsWith('${formKey}_')) {
          final field = key.substring(formKey.length + 1);
          formData[field] = context.stateManager.get(key);
        }
      }

      // Submit form data
      if (endpoint != null) {
        // This would make an HTTP request
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      return ActionResult.success(data: formData);
    } catch (e) {
      return ActionResult.error('Form submission failed: $e');
    }
  });

  actionSystem.registerHandler('reset_form', (context, params) async {
    final formKey = params['formKey'] as String?;
    final defaultValues = params['defaultValues'] as Map<String, dynamic>? ?? {};

    if (formKey == null) {
      return ActionResult.error('FormKey parameter is required');
    }

    try {
      // Clear existing form data
      for (final key in context.stateManager.keys.toList()) {
        if (key.startsWith('${formKey}_')) {
          context.stateManager.remove(key);
        }
      }

      // Set default values
      for (final entry in defaultValues.entries) {
        context.stateManager.set('${formKey}_${entry.key}', entry.value, source: 'action');
      }

      return ActionResult.success();
    } catch (e) {
      return ActionResult.error('Form reset failed: $e');
    }
  });

  // Validation actions
  actionSystem.registerHandler('validate_field', (context, params) async {
    final field = params['field'] as String?;
    final rules = params['rules'] as List<Map<String, dynamic>>?;

    if (field == null) {
      return ActionResult.error('Field parameter is required');
    }

    final value = context.stateManager.get(field);
    final errors = <String>[];

    if (rules != null) {
      for (final rule in rules) {
        final type = rule['type'] as String?;
        final message = rule['message'] as String? ?? 'Validation failed';

        switch (type) {
          case 'required':
            if (value == null || value.toString().isEmpty) {
              errors.add(message);
            }
            break;
          case 'min_length':
            final minLength = rule['value'] as int? ?? 0;
            if (value.toString().length < minLength) {
              errors.add(message);
            }
            break;
          case 'max_length':
            final maxLength = rule['value'] as int? ?? 100;
            if (value.toString().length > maxLength) {
              errors.add(message);
            }
            break;
          case 'email':
            final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
            if (!emailRegex.hasMatch(value.toString())) {
              errors.add(message);
            }
            break;
        }
      }
    }

    final isValid = errors.isEmpty;
    context.stateManager.set('${field}_errors', errors, source: 'validation');
    context.stateManager.set('${field}_valid', isValid, source: 'validation');

    return ActionResult.success(data: {'valid': isValid, 'errors': errors});
  });

  // Set required parameters
  actionSystem.setRequiredParams('navigate', ['route']);
  actionSystem.setRequiredParams('set_state', ['key']);
  actionSystem.setRequiredParams('show_snackbar', ['message']);
  actionSystem.setRequiredParams('http_request', ['url']);
  actionSystem.setRequiredParams('submit_form', ['formKey']);
  actionSystem.setRequiredParams('validate_field', ['field']);
}

/// Action execution widget
class ActionExecutor extends ConsumerWidget {
  final String actionType;
  final Map<String, dynamic> params;
  final String componentId;
  final Widget child;
  final VoidCallback? onSuccess;
  final void Function(String error)? onError;

  const ActionExecutor({
    Key? key,
    required this.actionType,
    required this.params,
    required this.componentId,
    required this.child,
    this.onSuccess,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stateManager = ref.watch(stateManagerProvider);

    return GestureDetector(
      onTap: () async {
        final actionContext = ActionContext(
          buildContext: context,
          stateManager: stateManager,
          componentProps: {},
          globalData: {},
          componentId: componentId,
        );

        final result = await ActionHandlerSystem().executeAction(
          actionType,
          actionContext,
          params,
        );

        if (result.success) {
          onSuccess?.call();
        } else {
          onError?.call(result.error ?? 'Unknown error');
        }
      },
      child: child,
    );
  }
}

/// Riverpod provider for action handler system
final actionHandlerProvider = Provider<ActionHandlerSystem>((ref) {
  return ActionHandlerSystem();
});
