package com.uiplatform.repository;

import com.uiplatform.entity.Theme;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Theme entity.
 * Provides CRUD operations and custom queries for theme management.
 */
@Repository
public interface ThemeRepository extends JpaRepository<Theme, UUID>, JpaSpecificationExecutor<Theme> {

    /**
     * Find theme by name and organization.
     */
    Optional<Theme> findByNameAndOrganizationIdAndDeletedFalse(String name, UUID organizationId);

    /**
     * Find themes by organization.
     */
    List<Theme> findByOrganizationIdAndDeletedFalse(UUID organizationId);

    /**
     * Find themes by organization with pagination.
     */
    Page<Theme> findByOrganizationIdAndDeletedFalse(UUID organizationId, Pageable pageable);

    /**
     * Find themes by author.
     */
    List<Theme> findByAuthorIdAndDeletedFalse(UUID authorId);

    /**
     * Find default theme by organization.
     */
    Optional<Theme> findByOrganizationIdAndIsDefaultTrueAndDeletedFalse(UUID organizationId);

    /**
     * Find public themes.
     */
    List<Theme> findByIsPublicTrueAndDeletedFalse();

    /**
     * Find public themes with pagination.
     */
    Page<Theme> findByIsPublicTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find system themes.
     */
    List<Theme> findByIsSystemTrueAndDeletedFalse();

    /**
     * Search themes by name.
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(:organizationId IS NULL OR t.organization.id = :organizationId OR t.isPublic = true) AND " +
           "LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%')) AND t.deleted = false")
    Page<Theme> searchByName(@Param("organizationId") UUID organizationId, 
                            @Param("name") String name, 
                            Pageable pageable);

    /**
     * Search themes by tags.
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(:organizationId IS NULL OR t.organization.id = :organizationId OR t.isPublic = true) AND " +
           "LOWER(t.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND t.deleted = false")
    Page<Theme> searchByTags(@Param("organizationId") UUID organizationId, 
                            @Param("tag") String tag, 
                            Pageable pageable);

    /**
     * Find themes by multiple criteria.
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(:organizationId IS NULL OR t.organization.id = :organizationId OR t.isPublic = true) AND " +
           "(:authorId IS NULL OR t.author.id = :authorId) AND " +
           "(:isPublic IS NULL OR t.isPublic = :isPublic) AND " +
           "(:isSystem IS NULL OR t.isSystem = :isSystem) AND " +
           "t.deleted = false")
    Page<Theme> findByCriteria(@Param("organizationId") UUID organizationId,
                              @Param("authorId") UUID authorId,
                              @Param("isPublic") Boolean isPublic,
                              @Param("isSystem") Boolean isSystem,
                              Pageable pageable);

    /**
     * Count themes by organization.
     */
    @Query("SELECT COUNT(t) FROM Theme t WHERE t.organization.id = :organizationId AND t.deleted = false")
    Long countByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Count public themes.
     */
    @Query("SELECT COUNT(t) FROM Theme t WHERE t.isPublic = true AND t.deleted = false")
    Long countPublicThemes();

    /**
     * Check if theme name exists in organization (excluding current theme).
     */
    @Query("SELECT COUNT(t) > 0 FROM Theme t WHERE t.name = :name AND t.organization.id = :organizationId AND t.id != :excludeId AND t.deleted = false")
    boolean existsByNameAndOrganizationIdAndIdNot(@Param("name") String name, 
                                                 @Param("organizationId") UUID organizationId, 
                                                 @Param("excludeId") UUID excludeId);

    /**
     * Find themes with usage count.
     */
    @Query("SELECT t, COUNT(u) as usageCount FROM Theme t LEFT JOIN t.uiConfigurations u " +
           "WHERE (:organizationId IS NULL OR t.organization.id = :organizationId) AND t.deleted = false GROUP BY t")
    List<Object[]> findWithUsageCount(@Param("organizationId") UUID organizationId);

    /**
     * Find most popular themes.
     */
    @Query("SELECT t, COUNT(u) as usageCount FROM Theme t LEFT JOIN t.uiConfigurations u " +
           "WHERE t.isPublic = true AND t.deleted = false GROUP BY t ORDER BY usageCount DESC")
    List<Object[]> findMostPopularThemes(Pageable pageable);

    /**
     * Set theme as default for organization.
     */
    @Modifying
    @Query("UPDATE Theme t SET t.isDefault = CASE WHEN t.id = :themeId THEN true ELSE false END WHERE t.organization.id = :organizationId")
    void setAsDefaultForOrganization(@Param("themeId") UUID themeId, @Param("organizationId") UUID organizationId);

    /**
     * Find recently created themes.
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(:organizationId IS NULL OR t.organization.id = :organizationId OR t.isPublic = true) AND " +
           "t.deleted = false ORDER BY t.createdAt DESC")
    List<Theme> findRecentThemes(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Find themes by version.
     */
    @Query("SELECT t FROM Theme t WHERE t.version = :version AND t.deleted = false")
    List<Theme> findByVersion(@Param("version") String version);

    /**
     * Find available themes for organization (own + public + system).
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(t.organization.id = :organizationId OR t.isPublic = true OR t.isSystem = true) AND " +
           "t.deleted = false ORDER BY t.isSystem DESC, t.isDefault DESC, t.name ASC")
    List<Theme> findAvailableForOrganization(@Param("organizationId") UUID organizationId);

    /**
     * Find available themes for organization with pagination.
     */
    @Query("SELECT t FROM Theme t WHERE " +
           "(t.organization.id = :organizationId OR t.isPublic = true OR t.isSystem = true) AND " +
           "t.deleted = false ORDER BY t.isSystem DESC, t.isDefault DESC, t.name ASC")
    Page<Theme> findAvailableForOrganization(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Update theme public status.
     */
    @Modifying
    @Query("UPDATE Theme t SET t.isPublic = :isPublic WHERE t.id = :id")
    void updatePublicStatus(@Param("id") UUID id, @Param("isPublic") Boolean isPublic);

    /**
     * Find themes by author with pagination.
     */
    Page<Theme> findByAuthorIdAndDeletedFalse(UUID authorId, Pageable pageable);
}
