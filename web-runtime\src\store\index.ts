import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import slices
import runtimeSlice from './slices/runtimeSlice';
import configSlice from './slices/configSlice';
import themeSlice from './slices/themeSlice';
import userSlice from './slices/userSlice';
import analyticsSlice from './slices/analyticsSlice';

// Import API slices
import { apiSlice } from './api/apiSlice';

// Persist configuration
const persistConfig = {
  key: 'ui-runtime',
  version: 1,
  storage,
  whitelist: ['theme', 'user'], // Only persist theme and user data
  blacklist: ['api'], // Don't persist API cache
};

// Root reducer
const rootReducer = combineReducers({
  runtime: runtimeSlice,
  config: configSlice,
  theme: themeSlice,
  user: userSlice,
  analytics: analyticsSlice,
  api: apiSlice.reducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
          'persist/FLUSH',
        ],
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['items.dates'],
      },
      immutableCheck: {
        ignoredPaths: ['items.dates'],
      },
    }).concat(apiSlice.middleware),
  devTools: process.env.NODE_ENV !== 'production',
  preloadedState: undefined,
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export store actions
export * from './slices/runtimeSlice';
export * from './slices/configSlice';
export * from './slices/themeSlice';
export * from './slices/userSlice';
export * from './slices/analyticsSlice';
export * from './api/apiSlice';

// Hot module replacement for development
if (process.env.NODE_ENV === 'development' && module.hot) {
  module.hot.accept('./slices/runtimeSlice', () => {
    store.replaceReducer(persistedReducer);
  });
}
