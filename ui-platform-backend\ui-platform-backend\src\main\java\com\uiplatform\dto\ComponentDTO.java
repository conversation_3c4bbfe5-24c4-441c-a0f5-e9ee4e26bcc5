package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * DTO for Component entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ComponentDTO extends BaseDTO {

    @NotBlank(message = "Component name is required")
    @Size(max = 100, message = "Component name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @NotBlank(message = "Component type is required")
    @Size(max = 50, message = "Component type must not exceed 50 characters")
    private String componentType;

    @Size(max = 50, message = "Category must not exceed 50 characters")
    private String category;

    private Map<String, Object> properties;
    private Map<String, Object> styleProperties;
    private Map<String, Object> eventHandlers;
    private Map<String, Object> validationRules;
    private Map<String, Object> responsiveSettings;

    @Min(value = 0, message = "Sort order must be non-negative")
    private Integer sortOrder;

    private Boolean isVisible;
    private Boolean isEnabled;
    private Boolean isRequired;
    private Boolean isReusable;

    private String cssClasses;
    private String customCss;
    private String dataSource;
    private Map<String, Object> dataBinding;

    // Related entities
    @NotNull(message = "UI Configuration ID is required")
    private UUID uiConfigurationId;
    private String uiConfigurationName;

    private UUID parentId;
    private String parentName;

    // Collections
    private List<ComponentDTO> children;

    // Statistics
    private Long childrenCount;
    private Integer depth;

    // Constructors
    public ComponentDTO() {}

    public ComponentDTO(String name, String componentType) {
        this.name = name;
        this.componentType = componentType;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public Map<String, Object> getStyleProperties() {
        return styleProperties;
    }

    public void setStyleProperties(Map<String, Object> styleProperties) {
        this.styleProperties = styleProperties;
    }

    public Map<String, Object> getEventHandlers() {
        return eventHandlers;
    }

    public void setEventHandlers(Map<String, Object> eventHandlers) {
        this.eventHandlers = eventHandlers;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Map<String, Object> getResponsiveSettings() {
        return responsiveSettings;
    }

    public void setResponsiveSettings(Map<String, Object> responsiveSettings) {
        this.responsiveSettings = responsiveSettings;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Boolean getIsReusable() {
        return isReusable;
    }

    public void setIsReusable(Boolean isReusable) {
        this.isReusable = isReusable;
    }

    public String getCssClasses() {
        return cssClasses;
    }

    public void setCssClasses(String cssClasses) {
        this.cssClasses = cssClasses;
    }

    public String getCustomCss() {
        return customCss;
    }

    public void setCustomCss(String customCss) {
        this.customCss = customCss;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Map<String, Object> getDataBinding() {
        return dataBinding;
    }

    public void setDataBinding(Map<String, Object> dataBinding) {
        this.dataBinding = dataBinding;
    }

    public UUID getUiConfigurationId() {
        return uiConfigurationId;
    }

    public void setUiConfigurationId(UUID uiConfigurationId) {
        this.uiConfigurationId = uiConfigurationId;
    }

    public String getUiConfigurationName() {
        return uiConfigurationName;
    }

    public void setUiConfigurationName(String uiConfigurationName) {
        this.uiConfigurationName = uiConfigurationName;
    }

    public UUID getParentId() {
        return parentId;
    }

    public void setParentId(UUID parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<ComponentDTO> getChildren() {
        return children;
    }

    public void setChildren(List<ComponentDTO> children) {
        this.children = children;
    }

    public Long getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(Long childrenCount) {
        this.childrenCount = childrenCount;
    }

    public Integer getDepth() {
        return depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    /**
     * Create DTO for component creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Component name is required")
        @Size(max = 100, message = "Component name must not exceed 100 characters")
        private String name;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @NotBlank(message = "Component type is required")
        @Size(max = 50, message = "Component type must not exceed 50 characters")
        private String componentType;

        @Size(max = 50, message = "Category must not exceed 50 characters")
        private String category;

        private Map<String, Object> properties;
        private Map<String, Object> styleProperties;

        @Min(value = 0, message = "Sort order must be non-negative")
        private Integer sortOrder = 0;

        private Boolean isVisible = true;
        private Boolean isEnabled = true;
        private Boolean isRequired = false;
        private Boolean isReusable = false;

        private UUID parentId;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getComponentType() {
            return componentType;
        }

        public void setComponentType(String componentType) {
            this.componentType = componentType;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }

        public Map<String, Object> getStyleProperties() {
            return styleProperties;
        }

        public void setStyleProperties(Map<String, Object> styleProperties) {
            this.styleProperties = styleProperties;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        public Boolean getIsVisible() {
            return isVisible;
        }

        public void setIsVisible(Boolean isVisible) {
            this.isVisible = isVisible;
        }

        public Boolean getIsEnabled() {
            return isEnabled;
        }

        public void setIsEnabled(Boolean isEnabled) {
            this.isEnabled = isEnabled;
        }

        public Boolean getIsRequired() {
            return isRequired;
        }

        public void setIsRequired(Boolean isRequired) {
            this.isRequired = isRequired;
        }

        public Boolean getIsReusable() {
            return isReusable;
        }

        public void setIsReusable(Boolean isReusable) {
            this.isReusable = isReusable;
        }

        public UUID getParentId() {
            return parentId;
        }

        public void setParentId(UUID parentId) {
            this.parentId = parentId;
        }
    }
}
