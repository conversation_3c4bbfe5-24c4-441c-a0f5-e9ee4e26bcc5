package com.uiplatform.service;

import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Service for handling Kafka-based collaboration messaging.
 */
@Service
public class KafkaCollaborationService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaCollaborationService.class);

    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final SimpMessagingTemplate messagingTemplate;
    private final ActivityLogService activityLogService;

    @Autowired
    public KafkaCollaborationService(KafkaTemplate<String, Object> kafkaTemplate,
                                   SimpMessagingTemplate messagingTemplate,
                                   ActivityLogService activityLogService) {
        this.kafkaTemplate = kafkaTemplate;
        this.messagingTemplate = messagingTemplate;
        this.activityLogService = activityLogService;
    }

    /**
     * Publish UI configuration event to Kafka.
     */
    public void publishUIConfigurationEvent(CollaborationEvent event) {
        try {
            String key = event.getConfigId() != null ? event.getConfigId().toString() : "global";
            
            kafkaTemplate.send("ui-configuration-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published UI configuration event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish UI configuration event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing UI configuration event", e);
        }
    }

    /**
     * Publish collaboration event to Kafka.
     */
    public void publishCollaborationEvent(CollaborationEvent event) {
        try {
            String key = event.getConfigId() != null ? event.getConfigId().toString() : 
                        event.getUserId() != null ? event.getUserId().toString() : "global";
            
            kafkaTemplate.send("collaboration-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published collaboration event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish collaboration event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing collaboration event", e);
        }
    }

    /**
     * Publish user presence event to Kafka.
     */
    public void publishPresenceEvent(CollaborationEvent event) {
        try {
            String key = event.getUserId() != null ? event.getUserId().toString() : "anonymous";
            
            kafkaTemplate.send("user-presence-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published presence event: {} for user: {}", 
                                       event.getType(), event.getUsername());
                        } else {
                            logger.error("Failed to publish presence event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing presence event", e);
        }
    }

    /**
     * Publish comment event to Kafka.
     */
    public void publishCommentEvent(CollaborationEvent event) {
        try {
            String key = event.getConfigId() != null ? event.getConfigId().toString() : "global";
            
            kafkaTemplate.send("comment-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published comment event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish comment event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing comment event", e);
        }
    }

    /**
     * Publish activity event to Kafka.
     */
    public void publishActivityEvent(CollaborationEvent event) {
        try {
            String key = event.getConfigId() != null ? event.getConfigId().toString() : 
                        event.getUserId() != null ? event.getUserId().toString() : "system";
            
            kafkaTemplate.send("activity-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published activity event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish activity event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing activity event", e);
        }
    }

    /**
     * Publish operation event to Kafka.
     */
    public void publishOperationEvent(CollaborationEvent event) {
        try {
            String key = event.getConfigId() != null ? event.getConfigId().toString() : "global";
            
            kafkaTemplate.send("operation-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published operation event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish operation event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing operation event", e);
        }
    }

    /**
     * Publish notification event to Kafka.
     */
    public void publishNotificationEvent(CollaborationEvent event) {
        try {
            String key = event.getUserId() != null ? event.getUserId().toString() : "broadcast";
            
            kafkaTemplate.send("notification-events", key, event)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.debug("Published notification event: {} with key: {}", 
                                       event.getType(), key);
                        } else {
                            logger.error("Failed to publish notification event", ex);
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("Error publishing notification event", e);
        }
    }

    // Kafka Listeners for consuming events

    /**
     * Listen to UI configuration events.
     */
    @KafkaListener(topics = "ui-configuration-events", groupId = "ui-platform-collaboration")
    public void handleUIConfigurationEvent(@Payload CollaborationEvent event,
                                         @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                         @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                         @Header(KafkaHeaders.OFFSET) long offset,
                                         Acknowledgment acknowledgment) {
        try {
            logger.debug("Received UI configuration event: {} from partition: {}, offset: {}", 
                       event.getType(), partition, offset);
            
            // Process the event
            processUIConfigurationEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing UI configuration event", e);
            // Don't acknowledge on error - message will be retried
        }
    }

    /**
     * Listen to collaboration events.
     */
    @KafkaListener(topics = "collaboration-events", groupId = "ui-platform-collaboration")
    public void handleCollaborationEvent(@Payload CollaborationEvent event,
                                       @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                       @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                       @Header(KafkaHeaders.OFFSET) long offset,
                                       Acknowledgment acknowledgment) {
        try {
            logger.debug("Received collaboration event: {} from partition: {}, offset: {}", 
                       event.getType(), partition, offset);
            
            // Process the event
            processCollaborationEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing collaboration event", e);
        }
    }

    /**
     * Listen to presence events.
     */
    @KafkaListener(topics = "user-presence-events", groupId = "ui-platform-collaboration")
    public void handlePresenceEvent(@Payload CollaborationEvent event,
                                  @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                  Acknowledgment acknowledgment) {
        try {
            logger.debug("Received presence event: {} for user: {}", 
                       event.getType(), event.getUsername());
            
            // Process the event
            processPresenceEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing presence event", e);
        }
    }

    /**
     * Listen to comment events.
     */
    @KafkaListener(topics = "comment-events", groupId = "ui-platform-collaboration")
    public void handleCommentEvent(@Payload CollaborationEvent event,
                                 @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                 Acknowledgment acknowledgment) {
        try {
            logger.debug("Received comment event: {} from key: {}", event.getType(), key);
            
            // Process the event
            processCommentEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing comment event", e);
        }
    }

    /**
     * Listen to activity events.
     */
    @KafkaListener(topics = "activity-events", groupId = "ui-platform-collaboration")
    public void handleActivityEvent(@Payload CollaborationEvent event,
                                  @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                  Acknowledgment acknowledgment) {
        try {
            logger.debug("Received activity event: {} from key: {}", event.getType(), key);
            
            // Process the event
            processActivityEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing activity event", e);
        }
    }

    /**
     * Listen to operation events.
     */
    @KafkaListener(topics = "operation-events", groupId = "ui-platform-collaboration")
    public void handleOperationEvent(@Payload CollaborationEvent event,
                                   @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                   Acknowledgment acknowledgment) {
        try {
            logger.debug("Received operation event: {} from key: {}", event.getType(), key);
            
            // Process the event
            processOperationEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing operation event", e);
        }
    }

    /**
     * Listen to notification events.
     */
    @KafkaListener(topics = "notification-events", groupId = "ui-platform-collaboration")
    public void handleNotificationEvent(@Payload CollaborationEvent event,
                                      @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                      Acknowledgment acknowledgment) {
        try {
            logger.debug("Received notification event: {} from key: {}", event.getType(), key);
            
            // Process the event
            processNotificationEvent(event);
            
            // Acknowledge the message
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            logger.error("Error processing notification event", e);
        }
    }

    // Event processing methods

    private void processUIConfigurationEvent(CollaborationEvent event) {
        // Broadcast to WebSocket subscribers
        if (event.getConfigId() != null) {
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + event.getConfigId() + "/updates", 
                event
            );
        }
        
        // Log activity if needed
        if (shouldLogActivity(event.getType())) {
            logEventActivity(event);
        }
    }

    private void processCollaborationEvent(CollaborationEvent event) {
        // Broadcast to appropriate WebSocket topics
        if (event.getConfigId() != null) {
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + event.getConfigId() + "/collaboration", 
                event
            );
        }
    }

    private void processPresenceEvent(CollaborationEvent event) {
        // Broadcast presence updates
        messagingTemplate.convertAndSend("/topic/presence", event);
    }

    private void processCommentEvent(CollaborationEvent event) {
        // Broadcast comment updates
        if (event.getConfigId() != null) {
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + event.getConfigId() + "/comments", 
                event
            );
        }
    }

    private void processActivityEvent(CollaborationEvent event) {
        // Broadcast to activity feeds
        messagingTemplate.convertAndSend("/topic/activity", event);
    }

    private void processOperationEvent(CollaborationEvent event) {
        // Broadcast operation updates
        if (event.getConfigId() != null) {
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + event.getConfigId() + "/operations", 
                event
            );
        }
    }

    private void processNotificationEvent(CollaborationEvent event) {
        // Broadcast notifications
        messagingTemplate.convertAndSend("/topic/notifications", event);
    }

    private boolean shouldLogActivity(CollaborationEventType eventType) {
        return eventType == CollaborationEventType.UI_CONFIG_UPDATED ||
               eventType == CollaborationEventType.COMPONENT_ADDED ||
               eventType == CollaborationEventType.COMPONENT_UPDATED ||
               eventType == CollaborationEventType.COMPONENT_DELETED ||
               eventType == CollaborationEventType.COMMENT_ADDED;
    }

    private void logEventActivity(CollaborationEvent event) {
        try {
            // Convert collaboration event to activity log
            // This is a simplified implementation
            if (event.getUserId() != null) {
                activityLogService.logActivity(
                    null, // organizationId would need to be determined
                    event.getUserId(),
                    event.getUsername(),
                    mapToActivityType(event.getType()),
                    event.getType().toString(),
                    "Collaboration event: " + event.getType(),
                    event.getConfigId(),
                    "collaboration_event",
                    event.getElementId()
                );
            }
        } catch (Exception e) {
            logger.error("Error logging event activity", e);
        }
    }

    private com.uiplatform.entity.ActivityLog.ActivityType mapToActivityType(CollaborationEventType eventType) {
        switch (eventType) {
            case UI_CONFIG_UPDATED:
                return com.uiplatform.entity.ActivityLog.ActivityType.UI_CONFIG_UPDATED;
            case COMPONENT_ADDED:
                return com.uiplatform.entity.ActivityLog.ActivityType.COMPONENT_CREATED;
            case COMPONENT_UPDATED:
                return com.uiplatform.entity.ActivityLog.ActivityType.COMPONENT_UPDATED;
            case COMPONENT_DELETED:
                return com.uiplatform.entity.ActivityLog.ActivityType.COMPONENT_DELETED;
            case COMMENT_ADDED:
                return com.uiplatform.entity.ActivityLog.ActivityType.COMMENT_CREATED;
            default:
                return com.uiplatform.entity.ActivityLog.ActivityType.API_CALL;
        }
    }
}
