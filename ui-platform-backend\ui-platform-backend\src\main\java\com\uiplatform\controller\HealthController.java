package com.uiplatform.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * Health Check Controller for monitoring service health
 * Provides endpoints for Kubernetes readiness and liveness probes
 */
@RestController
@RequestMapping("/api/v1/health")
public class HealthController {

    @Autowired
    private DataSource dataSource;

    /**
     * Basic health check endpoint
     * @return Simple OK response
     */
    @GetMapping
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "ui-platform-backend");
        response.put("timestamp", java.time.Instant.now().toString());
        return ResponseEntity.ok(response);
    }

    /**
     * Readiness probe - checks if service is ready to accept traffic
     * @return Detailed health status including dependencies
     */
    @GetMapping("/ready")
    public ResponseEntity<Map<String, Object>> readiness() {
        Map<String, Object> response = new HashMap<>();
        Map<String, String> checks = new HashMap<>();
        
        boolean isReady = true;
        
        // Check database connectivity
        try {
            checkDatabaseConnection();
            checks.put("database", "UP");
        } catch (Exception e) {
            checks.put("database", "DOWN - " + e.getMessage());
            isReady = false;
        }
        
        // Add more dependency checks here
        // Redis, Kafka, external services, etc.
        
        response.put("status", isReady ? "UP" : "DOWN");
        response.put("checks", checks);
        response.put("timestamp", java.time.Instant.now().toString());
        
        return isReady ? 
            ResponseEntity.ok(response) : 
            ResponseEntity.status(503).body(response);
    }

    /**
     * Liveness probe - checks if service is alive
     * @return Simple alive status
     */
    @GetMapping("/live")
    public ResponseEntity<Map<String, String>> liveness() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Service is alive");
        response.put("timestamp", java.time.Instant.now().toString());
        return ResponseEntity.ok(response);
    }

    /**
     * Detailed health information
     * @return Comprehensive health status
     */
    @GetMapping("/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> details = new HashMap<>();
        
        // System information
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> system = new HashMap<>();
        system.put("totalMemory", runtime.totalMemory());
        system.put("freeMemory", runtime.freeMemory());
        system.put("maxMemory", runtime.maxMemory());
        system.put("availableProcessors", runtime.availableProcessors());
        details.put("system", system);
        
        // Database health
        try {
            checkDatabaseConnection();
            details.put("database", Map.of("status", "UP", "type", "PostgreSQL"));
        } catch (Exception e) {
            details.put("database", Map.of("status", "DOWN", "error", e.getMessage()));
        }
        
        // Application info
        Map<String, String> app = new HashMap<>();
        app.put("name", "UI Platform Backend");
        app.put("version", "1.0.0");
        app.put("profile", System.getProperty("spring.profiles.active", "default"));
        details.put("application", app);
        
        response.put("status", "UP");
        response.put("details", details);
        response.put("timestamp", java.time.Instant.now().toString());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Check database connection
     * @throws SQLException if database is not accessible
     */
    private void checkDatabaseConnection() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (!connection.isValid(5)) {
                throw new SQLException("Database connection is not valid");
            }
        }
    }
}
