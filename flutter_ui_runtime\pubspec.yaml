name: flutter_ui_runtime
description: Dynamic UI Runtime for Flutter - Renders UI configurations from JSON metadata with real-time sync and native platform features.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Networking & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Real-time Communication
  socket_io_client: ^2.0.3+1
  
  # Local Storage & Caching
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Navigation
  go_router: ^12.1.3
  
  # UI & Widgets
  flutter_hooks: ^0.20.3
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.9
  lottie: ^2.7.0
  
  # Platform Integration
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.2
  
  # Camera & Media
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  video_player: ^2.8.1
  
  # Location & Maps
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  
  # Push Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  
  # Haptics & Sensors
  vibration: ^1.8.4
  sensors_plus: ^4.0.2
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.2.1
  path_provider: ^2.1.1
  share_plus: ^7.2.1
  url_launcher: ^6.2.1
  
  # Security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  
  # Performance & Monitoring
  sentry_flutter: ^7.13.2
  
  # Development
  logger: ^2.0.2+1
  
  # Platform-specific
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
  
  # Linting & Analysis
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter
  patrol: ^3.15.2
  
  # Tools
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.6

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
    
    - family: SF Pro Display
      fonts:
        - asset: assets/fonts/SFProDisplay-Regular.ttf
        - asset: assets/fonts/SFProDisplay-Medium.ttf
          weight: 500
        - asset: assets/fonts/SFProDisplay-Semibold.ttf
          weight: 600
        - asset: assets/fonts/SFProDisplay-Bold.ttf
          weight: 700

# Flutter Launcher Icons Configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"

# Native Splash Screen Configuration
flutter_native_splash:
  color: "#ffffff"
  image: assets/images/splash_logo.png
  color_dark: "#121212"
  image_dark: assets/images/splash_logo_dark.png
  android_12:
    image: assets/images/splash_logo_android12.png
    icon_background_color: "#ffffff"
    image_dark: assets/images/splash_logo_android12_dark.png
    icon_background_color_dark: "#121212"
  web: false
