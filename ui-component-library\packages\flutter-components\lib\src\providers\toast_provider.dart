import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';
import '../utils/ui_utils.dart';

/// Toast message data
class ToastMessage {
  const ToastMessage({
    required this.id,
    required this.message,
    this.title,
    this.variant = UIColorVariant.info,
    this.duration = const Duration(seconds: 4),
    this.action,
    this.dismissible = true,
    this.icon,
  });

  final String id;
  final String message;
  final String? title;
  final UIColorVariant variant;
  final Duration duration;
  final ToastAction? action;
  final bool dismissible;
  final IconData? icon;

  ToastMessage copyWith({
    String? id,
    String? message,
    String? title,
    UIColorVariant? variant,
    Duration? duration,
    ToastAction? action,
    bool? dismissible,
    IconData? icon,
  }) {
    return ToastMessage(
      id: id ?? this.id,
      message: message ?? this.message,
      title: title ?? this.title,
      variant: variant ?? this.variant,
      duration: duration ?? this.duration,
      action: action ?? this.action,
      dismissible: dismissible ?? this.dismissible,
      icon: icon ?? this.icon,
    );
  }
}

/// Toast action data
class ToastAction {
  const ToastAction({
    required this.label,
    required this.onPressed,
  });

  final String label;
  final VoidCallback onPressed;
}

/// Toast state
class ToastState {
  const ToastState({
    this.messages = const [],
  });

  final List<ToastMessage> messages;

  ToastState copyWith({
    List<ToastMessage>? messages,
  }) {
    return ToastState(
      messages: messages ?? this.messages,
    );
  }
}

/// Toast provider
class ToastNotifier extends StateNotifier<ToastState> {
  ToastNotifier() : super(const ToastState());

  /// Show a toast message
  void showToast({
    required String message,
    String? title,
    UIColorVariant variant = UIColorVariant.info,
    Duration duration = const Duration(seconds: 4),
    ToastAction? action,
    bool dismissible = true,
    IconData? icon,
  }) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final toast = ToastMessage(
      id: id,
      message: message,
      title: title,
      variant: variant,
      duration: duration,
      action: action,
      dismissible: dismissible,
      icon: icon,
    );

    state = state.copyWith(
      messages: [...state.messages, toast],
    );

    // Auto-dismiss after duration
    if (duration != Duration.zero) {
      Future.delayed(duration, () {
        dismissToast(id);
      });
    }
  }

  /// Show success toast
  void showSuccess(String message, {String? title, ToastAction? action}) {
    showToast(
      message: message,
      title: title,
      variant: UIColorVariant.success,
      action: action,
      icon: Icons.check_circle,
    );
  }

  /// Show error toast
  void showError(String message, {String? title, ToastAction? action}) {
    showToast(
      message: message,
      title: title,
      variant: UIColorVariant.error,
      action: action,
      icon: Icons.error,
    );
  }

  /// Show warning toast
  void showWarning(String message, {String? title, ToastAction? action}) {
    showToast(
      message: message,
      title: title,
      variant: UIColorVariant.warning,
      action: action,
      icon: Icons.warning,
    );
  }

  /// Show info toast
  void showInfo(String message, {String? title, ToastAction? action}) {
    showToast(
      message: message,
      title: title,
      variant: UIColorVariant.info,
      action: action,
      icon: Icons.info,
    );
  }

  /// Dismiss a specific toast
  void dismissToast(String id) {
    state = state.copyWith(
      messages: state.messages.where((toast) => toast.id != id).toList(),
    );
  }

  /// Dismiss all toasts
  void dismissAll() {
    state = state.copyWith(messages: []);
  }
}

/// Toast provider instance
final toastProvider = StateNotifierProvider<ToastNotifier, ToastState>((ref) {
  return ToastNotifier();
});

/// Toast overlay widget
class ToastOverlay extends ConsumerWidget {
  const ToastOverlay({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final toastState = ref.watch(toastProvider);

    return Stack(
      children: [
        child,
        if (toastState.messages.isNotEmpty)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: Column(
              children: toastState.messages
                  .map((toast) => ToastWidget(
                        key: ValueKey(toast.id),
                        toast: toast,
                        onDismiss: () => ref.read(toastProvider.notifier).dismissToast(toast.id),
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }
}

/// Individual toast widget
class ToastWidget extends StatefulWidget {
  const ToastWidget({
    super.key,
    required this.toast,
    required this.onDismiss,
  });

  final ToastMessage toast;
  final VoidCallback onDismiss;

  @override
  State<ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<ToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final tokens = DesignTokens.instance;

    final backgroundColor = widget.toast.variant.getColor(colorScheme);
    final textColor = UIUtils.getContrastColor(backgroundColor);

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: EdgeInsets.only(bottom: tokens.spacing.size2),
          child: Material(
            elevation: 4,
            borderRadius: tokens.borderRadius.lg,
            color: backgroundColor,
            child: Container(
              padding: EdgeInsets.all(tokens.spacing.size4),
              child: Row(
                children: [
                  if (widget.toast.icon != null) ...[
                    Icon(
                      widget.toast.icon,
                      color: textColor,
                      size: 20,
                    ),
                    SizedBox(width: tokens.spacing.size3),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.toast.title != null) ...[
                          Text(
                            widget.toast.title!,
                            style: textTheme.titleSmall?.copyWith(
                              color: textColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: tokens.spacing.size1),
                        ],
                        Text(
                          widget.toast.message,
                          style: textTheme.bodyMedium?.copyWith(
                            color: textColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (widget.toast.action != null) ...[
                    SizedBox(width: tokens.spacing.size3),
                    TextButton(
                      onPressed: widget.toast.action!.onPressed,
                      style: TextButton.styleFrom(
                        foregroundColor: textColor,
                        padding: EdgeInsets.symmetric(
                          horizontal: tokens.spacing.size3,
                          vertical: tokens.spacing.size1,
                        ),
                      ),
                      child: Text(widget.toast.action!.label),
                    ),
                  ],
                  if (widget.toast.dismissible) ...[
                    SizedBox(width: tokens.spacing.size2),
                    IconButton(
                      onPressed: widget.onDismiss,
                      icon: Icon(
                        Icons.close,
                        color: textColor,
                        size: 18,
                      ),
                      padding: EdgeInsets.all(tokens.spacing.size1),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
