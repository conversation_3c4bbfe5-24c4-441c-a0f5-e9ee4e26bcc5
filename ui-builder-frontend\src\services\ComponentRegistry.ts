import { ComponentConfig, ComponentProps, ComponentValidation } from '../types/component';

export interface ComponentBuilder {
  (props: ComponentProps): React.ReactElement | null;
}

export interface ComponentRegistryEntry {
  config: ComponentConfig;
  builder: ComponentBuilder;
  validator?: ComponentValidation;
  dependencies?: string[];
  loadedAt?: Date;
  version?: string;
}

export interface ComponentCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  order?: number;
  components: string[];
}

export interface ComponentSearchOptions {
  query?: string;
  category?: string;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
}

export interface ComponentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Component Registry for managing UI components
 * 
 * This registry manages all available components, their configurations,
 * builders, and validation rules. It provides a centralized way to
 * register, discover, and instantiate components.
 */
export class ComponentRegistry {
  private components = new Map<string, ComponentRegistryEntry>();
  private categories = new Map<string, ComponentCategory>();
  private aliases = new Map<string, string>();
  private loadingPromises = new Map<string, Promise<ComponentRegistryEntry>>();

  /**
   * Register a component with the registry
   */
  register(
    type: string,
    config: ComponentConfig,
    builder: ComponentBuilder,
    validator?: ComponentValidation
  ): void {
    if (this.components.has(type)) {
      console.warn(`Component ${type} is already registered. Overwriting.`);
    }

    const entry: ComponentRegistryEntry = {
      config,
      builder,
      validator,
      loadedAt: new Date(),
      version: config.version
    };

    this.components.set(type, entry);

    // Add to category
    this.addToCategory(config.category, type);

    // Register aliases if any
    if (config.aliases) {
      config.aliases.forEach(alias => {
        this.aliases.set(alias, type);
      });
    }

    console.log(`Registered component: ${type} (${config.displayName})`);
  }

  /**
   * Unregister a component from the registry
   */
  unregister(type: string): boolean {
    const entry = this.components.get(type);
    if (!entry) {
      return false;
    }

    // Remove from components
    this.components.delete(type);

    // Remove from category
    this.removeFromCategory(entry.config.category, type);

    // Remove aliases
    if (entry.config.aliases) {
      entry.config.aliases.forEach(alias => {
        this.aliases.delete(alias);
      });
    }

    console.log(`Unregistered component: ${type}`);
    return true;
  }

  /**
   * Get a component builder by type
   */
  getBuilder(type: string): ComponentBuilder | null {
    // Check aliases first
    const actualType = this.aliases.get(type) || type;
    const entry = this.components.get(actualType);
    return entry?.builder || null;
  }

  /**
   * Get component configuration by type
   */
  getConfig(type: string): ComponentConfig | null {
    const actualType = this.aliases.get(type) || type;
    const entry = this.components.get(actualType);
    return entry?.config || null;
  }

  /**
   * Get component validator by type
   */
  getValidator(type: string): ComponentValidation | null {
    const actualType = this.aliases.get(type) || type;
    const entry = this.components.get(actualType);
    return entry?.validator || null;
  }

  /**
   * Check if a component is registered
   */
  hasComponent(type: string): boolean {
    const actualType = this.aliases.get(type) || type;
    return this.components.has(actualType);
  }

  /**
   * Get all registered component types
   */
  getComponentTypes(): string[] {
    return Array.from(this.components.keys());
  }

  /**
   * Get all registered components
   */
  getAllComponents(): ComponentRegistryEntry[] {
    return Array.from(this.components.values());
  }

  /**
   * Search components by criteria
   */
  searchComponents(options: ComponentSearchOptions = {}): ComponentConfig[] {
    let results = Array.from(this.components.values()).map(entry => entry.config);

    // Filter by query
    if (options.query) {
      const query = options.query.toLowerCase();
      results = results.filter(config =>
        config.displayName.toLowerCase().includes(query) ||
        config.description?.toLowerCase().includes(query) ||
        config.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (options.category) {
      results = results.filter(config => config.category === options.category);
    }

    // Filter by tags
    if (options.tags && options.tags.length > 0) {
      results = results.filter(config =>
        options.tags!.some(tag => config.tags.includes(tag))
      );
    }

    // Filter deprecated
    if (options.deprecated === false) {
      results = results.filter(config => !config.deprecated);
    }

    // Filter experimental
    if (options.experimental === false) {
      results = results.filter(config => !config.experimental);
    }

    return results;
  }

  /**
   * Get components by category
   */
  getComponentsByCategory(category: string): ComponentConfig[] {
    return Array.from(this.components.values())
      .map(entry => entry.config)
      .filter(config => config.category === category);
  }

  /**
   * Get all categories
   */
  getCategories(): ComponentCategory[] {
    return Array.from(this.categories.values()).sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  /**
   * Register a category
   */
  registerCategory(category: ComponentCategory): void {
    this.categories.set(category.id, category);
  }

  /**
   * Validate component configuration
   */
  validateConfig(type: string, config: any): ComponentValidationResult {
    const validator = this.getValidator(type);
    const componentConfig = this.getConfig(type);

    const result: ComponentValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!componentConfig) {
      result.isValid = false;
      result.errors.push(`Unknown component type: ${type}`);
      return result;
    }

    // Validate required properties
    if (componentConfig.requiredProperties) {
      for (const prop of componentConfig.requiredProperties) {
        if (!(prop in config) || config[prop] === undefined || config[prop] === null) {
          result.isValid = false;
          result.errors.push(`Required property '${prop}' is missing`);
        }
      }
    }

    // Validate property types
    if (componentConfig.properties) {
      for (const [propName, propDef] of Object.entries(componentConfig.properties)) {
        if (propName in config) {
          const value = config[propName];
          const validationError = this.validateProperty(propName, value, propDef);
          if (validationError) {
            result.isValid = false;
            result.errors.push(validationError);
          }
        }
      }
    }

    // Run custom validator if available
    if (validator && validator.custom) {
      for (const customValidator of validator.custom) {
        try {
          const isValid = customValidator(config);
          if (typeof isValid === 'string') {
            result.isValid = false;
            result.errors.push(isValid);
          } else if (!isValid) {
            result.isValid = false;
            result.errors.push('Custom validation failed');
          }
        } catch (error) {
          result.isValid = false;
          result.errors.push(`Validation error: ${error}`);
        }
      }
    }

    // Check for deprecated usage
    if (componentConfig.deprecated) {
      result.warnings.push(`Component '${type}' is deprecated`);
    }

    return result;
  }

  /**
   * Load component dynamically
   */
  async loadComponent(type: string, loader: () => Promise<ComponentRegistryEntry>): Promise<ComponentRegistryEntry> {
    // Check if already loaded
    if (this.components.has(type)) {
      return this.components.get(type)!;
    }

    // Check if already loading
    if (this.loadingPromises.has(type)) {
      return this.loadingPromises.get(type)!;
    }

    // Start loading
    const loadingPromise = loader().then(entry => {
      this.components.set(type, entry);
      this.loadingPromises.delete(type);
      return entry;
    }).catch(error => {
      this.loadingPromises.delete(type);
      throw error;
    });

    this.loadingPromises.set(type, loadingPromise);
    return loadingPromise;
  }

  /**
   * Get component dependencies
   */
  getDependencies(type: string): string[] {
    const entry = this.components.get(type);
    return entry?.dependencies || [];
  }

  /**
   * Check if all dependencies are loaded
   */
  areDependenciesLoaded(type: string): boolean {
    const dependencies = this.getDependencies(type);
    return dependencies.every(dep => this.hasComponent(dep));
  }

  /**
   * Get component statistics
   */
  getStats(): {
    totalComponents: number;
    categoriesCount: number;
    deprecatedCount: number;
    experimentalCount: number;
  } {
    const components = this.getAllComponents();
    return {
      totalComponents: components.length,
      categoriesCount: this.categories.size,
      deprecatedCount: components.filter(entry => entry.config.deprecated).length,
      experimentalCount: components.filter(entry => entry.config.experimental).length
    };
  }

  /**
   * Clear all registered components
   */
  clear(): void {
    this.components.clear();
    this.categories.clear();
    this.aliases.clear();
    this.loadingPromises.clear();
  }

  // Private helper methods

  private addToCategory(categoryId: string, componentType: string): void {
    let category = this.categories.get(categoryId);
    if (!category) {
      category = {
        id: categoryId,
        name: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
        components: []
      };
      this.categories.set(categoryId, category);
    }

    if (!category.components.includes(componentType)) {
      category.components.push(componentType);
    }
  }

  private removeFromCategory(categoryId: string, componentType: string): void {
    const category = this.categories.get(categoryId);
    if (category) {
      const index = category.components.indexOf(componentType);
      if (index > -1) {
        category.components.splice(index, 1);
      }

      // Remove category if empty
      if (category.components.length === 0) {
        this.categories.delete(categoryId);
      }
    }
  }

  private validateProperty(name: string, value: any, definition: any): string | null {
    // Basic type validation
    const expectedType = definition.type;
    const actualType = typeof value;

    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          return `Property '${name}' must be a string`;
        }
        break;
      case 'number':
        if (actualType !== 'number' || isNaN(value)) {
          return `Property '${name}' must be a number`;
        }
        break;
      case 'boolean':
        if (actualType !== 'boolean') {
          return `Property '${name}' must be a boolean`;
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          return `Property '${name}' must be an array`;
        }
        break;
      case 'object':
        if (actualType !== 'object' || Array.isArray(value) || value === null) {
          return `Property '${name}' must be an object`;
        }
        break;
    }

    // Validation rules
    if (definition.validation) {
      const validation = definition.validation;

      // String validation
      if (expectedType === 'string' && typeof value === 'string') {
        if (validation.minLength && value.length < validation.minLength) {
          return `Property '${name}' must be at least ${validation.minLength} characters`;
        }
        if (validation.maxLength && value.length > validation.maxLength) {
          return `Property '${name}' must be at most ${validation.maxLength} characters`;
        }
        if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
          return `Property '${name}' does not match required pattern`;
        }
      }

      // Number validation
      if (expectedType === 'number' && typeof value === 'number') {
        if (validation.min !== undefined && value < validation.min) {
          return `Property '${name}' must be at least ${validation.min}`;
        }
        if (validation.max !== undefined && value > validation.max) {
          return `Property '${name}' must be at most ${validation.max}`;
        }
      }

      // Array validation
      if (expectedType === 'array' && Array.isArray(value)) {
        if (validation.minItems && value.length < validation.minItems) {
          return `Property '${name}' must have at least ${validation.minItems} items`;
        }
        if (validation.maxItems && value.length > validation.maxItems) {
          return `Property '${name}' must have at most ${validation.maxItems} items`;
        }
      }

      // Select validation
      if (validation.options && Array.isArray(validation.options)) {
        const validValues = validation.options.map((opt: any) => opt.value || opt);
        if (!validValues.includes(value)) {
          return `Property '${name}' must be one of: ${validValues.join(', ')}`;
        }
      }
    }

    return null;
  }
}

// Global component registry instance
export const componentRegistry = new ComponentRegistry();
