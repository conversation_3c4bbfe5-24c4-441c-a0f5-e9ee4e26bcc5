import 'package:flutter/material.dart';

/// Base component properties that all components should extend
abstract class BaseComponentProps {
  const BaseComponentProps({
    this.key,
    this.testId,
    this.semanticLabel,
  });

  /// Widget key
  final Key? key;

  /// Test identifier for testing
  final String? testId;

  /// Semantic label for accessibility
  final String? semanticLabel;
}

/// Size variants used across components
enum UISize {
  xs,
  sm,
  md,
  lg,
  xl,
  xxl,
}

/// Color variants used across components
enum UIColorVariant {
  primary,
  secondary,
  accent,
  success,
  warning,
  error,
  info,
  neutral,
}

/// Button variants
enum UIButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  link,
  destructive,
}

/// Input variants
enum UIInputVariant {
  defaultVariant,
  filled,
  outline,
  underline,
}

/// Alert variants
enum UIAlertVariant {
  info,
  success,
  warning,
  error,
}

/// Badge variants
enum UIBadgeVariant {
  defaultVariant,
  secondary,
  success,
  warning,
  error,
  outline,
}

/// Loading state mixin
mixin LoadingState {
  bool get loading => false;
  String? get loadingText => null;
}

/// Disabled state mixin
mixin DisabledState {
  bool get disabled => false;
}

/// Form field state mixin
mixin FormFieldState {
  bool get hasError => false;
  String? get errorMessage => null;
  String? get helperText => null;
  bool get required => false;
}

/// Animation properties
class UIAnimationProps {
  const UIAnimationProps({
    this.duration = const Duration(milliseconds: 300),
    this.delay = Duration.zero,
    this.curve = Curves.easeInOut,
    this.animateOnMount = false,
  });

  /// Animation duration
  final Duration duration;

  /// Animation delay
  final Duration delay;

  /// Animation curve
  final Curve curve;

  /// Whether to animate on mount
  final bool animateOnMount;
}

/// Responsive properties
class UIResponsiveProps<T> {
  const UIResponsiveProps({
    this.base,
    this.sm,
    this.md,
    this.lg,
    this.xl,
    this.xxl,
  });

  /// Base value
  final T? base;

  /// Small screens and up
  final T? sm;

  /// Medium screens and up
  final T? md;

  /// Large screens and up
  final T? lg;

  /// Extra large screens and up
  final T? xl;

  /// 2x extra large screens and up
  final T? xxl;

  /// Get value for current screen size
  T? getValueForSize(UISize size) {
    switch (size) {
      case UISize.xs:
        return base;
      case UISize.sm:
        return sm ?? base;
      case UISize.md:
        return md ?? sm ?? base;
      case UISize.lg:
        return lg ?? md ?? sm ?? base;
      case UISize.xl:
        return xl ?? lg ?? md ?? sm ?? base;
      case UISize.xxl:
        return xxl ?? xl ?? lg ?? md ?? sm ?? base;
    }
  }
}

/// Spacing properties
class UISpacingProps {
  const UISpacingProps({
    this.margin,
    this.marginTop,
    this.marginRight,
    this.marginBottom,
    this.marginLeft,
    this.marginHorizontal,
    this.marginVertical,
    this.padding,
    this.paddingTop,
    this.paddingRight,
    this.paddingBottom,
    this.paddingLeft,
    this.paddingHorizontal,
    this.paddingVertical,
  });

  /// Margin
  final double? margin;
  final double? marginTop;
  final double? marginRight;
  final double? marginBottom;
  final double? marginLeft;
  final double? marginHorizontal;
  final double? marginVertical;

  /// Padding
  final double? padding;
  final double? paddingTop;
  final double? paddingRight;
  final double? paddingBottom;
  final double? paddingLeft;
  final double? paddingHorizontal;
  final double? paddingVertical;

  /// Convert to EdgeInsets for margin
  EdgeInsets get marginEdgeInsets {
    return EdgeInsets.only(
      top: marginTop ?? marginVertical ?? margin ?? 0,
      right: marginRight ?? marginHorizontal ?? margin ?? 0,
      bottom: marginBottom ?? marginVertical ?? margin ?? 0,
      left: marginLeft ?? marginHorizontal ?? margin ?? 0,
    );
  }

  /// Convert to EdgeInsets for padding
  EdgeInsets get paddingEdgeInsets {
    return EdgeInsets.only(
      top: paddingTop ?? paddingVertical ?? padding ?? 0,
      right: paddingRight ?? paddingHorizontal ?? padding ?? 0,
      bottom: paddingBottom ?? paddingVertical ?? padding ?? 0,
      left: paddingLeft ?? paddingHorizontal ?? padding ?? 0,
    );
  }
}

/// Layout properties
class UILayoutProps {
  const UILayoutProps({
    this.width,
    this.height,
    this.minWidth,
    this.minHeight,
    this.maxWidth,
    this.maxHeight,
  });

  final double? width;
  final double? height;
  final double? minWidth;
  final double? minHeight;
  final double? maxWidth;
  final double? maxHeight;

  /// Convert to BoxConstraints
  BoxConstraints get constraints {
    return BoxConstraints(
      minWidth: minWidth ?? 0,
      minHeight: minHeight ?? 0,
      maxWidth: maxWidth ?? double.infinity,
      maxHeight: maxHeight ?? double.infinity,
    );
  }
}

/// Flex properties
class UIFlexProps {
  const UIFlexProps({
    this.direction = Axis.horizontal,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.textBaseline,
  });

  final Axis direction;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final TextBaseline? textBaseline;
}

/// Event handler types
typedef UIClickHandler = void Function();
typedef UIChangeHandler<T> = void Function(T value);
typedef UIFocusHandler = void Function();
typedef UIBlurHandler = void Function();

/// Component state
enum UIComponentState {
  normal,
  hover,
  focus,
  active,
  disabled,
  loading,
  error,
  success,
}

/// Breakpoint definitions
enum UIBreakpoint {
  xs(475),
  sm(640),
  md(768),
  lg(1024),
  xl(1280),
  xxl(1536);

  const UIBreakpoint(this.value);
  final double value;

  /// Get breakpoint from width
  static UIBreakpoint fromWidth(double width) {
    if (width >= UIBreakpoint.xxl.value) return UIBreakpoint.xxl;
    if (width >= UIBreakpoint.xl.value) return UIBreakpoint.xl;
    if (width >= UIBreakpoint.lg.value) return UIBreakpoint.lg;
    if (width >= UIBreakpoint.md.value) return UIBreakpoint.md;
    if (width >= UIBreakpoint.sm.value) return UIBreakpoint.sm;
    return UIBreakpoint.xs;
  }
}

/// Platform-specific behavior
enum UIPlatformBehavior {
  auto,
  material,
  cupertino,
}

/// Component density
enum UIComponentDensity {
  compact,
  comfortable,
  standard,
}
