# Production Environment Configuration

# Database Configuration
spring.datasource.url=${DATABASE_URL:****************************************************}
spring.datasource.username=${DATABASE_USERNAME:ui_platform_prod_user}
spring.datasource.password=${DATABASE_PASSWORD:ui_platform_prod_pass}
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=10

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Redis Configuration
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:}

# Kafka Configuration
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}

# Security Configuration
app.jwt.secret=${JWT_SECRET:}
app.jwt.expiration=${JWT_EXPIRATION:86400000}
app.jwt.refresh-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# CORS Configuration
app.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:https://yourdomain.com}

# Logging Configuration
logging.level.com.uiplatform=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# GraphQL Configuration
spring.graphql.graphiql.enabled=false

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized

# Cache Configuration
spring.cache.redis.time-to-live=3600000

# SSL Configuration
server.ssl.enabled=${SSL_ENABLED:false}
server.ssl.key-store=${SSL_KEYSTORE:}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD:}
server.ssl.key-store-type=${SSL_KEYSTORE_TYPE:PKCS12}

# Production specific settings
app.prod.enable-monitoring=true
app.prod.enable-alerting=true
