import '@testing-library/jest-dom';
import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';

// Extend Vitest's expect with jest-dom matchers
expect.extend({});

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock environment variables
vi.mock('import.meta.env', () => ({
  VITE_API_URL: 'http://localhost:8080/api/v1',
  VITE_WS_URL: 'ws://localhost:8080/ws',
  MODE: 'test',
  DEV: false,
  PROD: false,
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock fetch
global.fetch = vi.fn();

// Mock performance API
global.performance = {
  ...global.performance,
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000,
  },
};

// Mock PerformanceObserver
global.PerformanceObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock Socket.IO
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    connected: true,
  })),
}));

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
    useSearchParams: () => [new URLSearchParams(), vi.fn()],
  };
});

// Mock Framer Motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Headless UI
vi.mock('@headlessui/react', () => ({
  Dialog: ({ children }: any) => <div role="dialog">{children}</div>,
  Transition: ({ children }: any) => children,
  Menu: ({ children }: any) => <div role="menu">{children}</div>,
  Listbox: ({ children }: any) => <div role="listbox">{children}</div>,
}));

// Mock Heroicons
vi.mock('@heroicons/react/24/outline', () => ({
  ExclamationTriangleIcon: () => <svg data-testid="exclamation-triangle-icon" />,
  ArrowPathIcon: () => <svg data-testid="arrow-path-icon" />,
  HomeIcon: () => <svg data-testid="home-icon" />,
  ArrowLeftIcon: () => <svg data-testid="arrow-left-icon" />,
}));

// Global test utilities
export const createMockUIMetadata = (overrides = {}) => ({
  pageId: 'test-page',
  version: '1.0.0',
  title: 'Test Page',
  theme: 'default',
  layout: {
    type: 'grid',
    columns: 12,
    components: [],
  },
  ...overrides,
});

export const createMockComponent = (overrides = {}) => ({
  id: 'test-component',
  type: 'Text',
  props: {
    content: 'Test content',
  },
  position: { col: 1, row: 1, span: 12 },
  ...overrides,
});

export const createMockTheme = (overrides = {}) => ({
  id: 'test-theme',
  name: 'Test Theme',
  colors: {
    primary: {
      500: '#3b82f6',
      600: '#2563eb',
    },
  },
  typography: {
    fontFamilies: {
      sans: 'Inter, sans-serif',
    },
  },
  spacing: {
    md: '1rem',
  },
  shadows: {
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
  },
  borders: {
    md: '0.375rem',
  },
  animations: {
    durations: {
      normal: '300ms',
    },
    easings: {
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    },
  },
  ...overrides,
});

export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));
