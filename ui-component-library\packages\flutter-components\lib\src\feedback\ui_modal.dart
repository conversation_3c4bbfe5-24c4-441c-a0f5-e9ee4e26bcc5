import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Modal component
class UIModal extends StatelessWidget {
  const UIModal({
    super.key,
    required this.child,
    this.title,
    this.showCloseButton = true,
    this.onClose,
    this.size = UISize.md,
    this.padding,
  });

  final Widget child;
  final String? title;
  final bool showCloseButton;
  final VoidCallback? onClose;
  final UISize size;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final modalWidth = _getModalWidth(context);
    final effectivePadding = padding ?? EdgeInsets.all(tokens.spacing.size6);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: tokens.borderRadius.xl,
      ),
      child: Container(
        width: modalWidth,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null || showCloseButton) ...[
              Container(
                padding: EdgeInsets.all(tokens.spacing.size4),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: colorScheme.outline),
                  ),
                ),
                child: Row(
                  children: [
                    if (title != null)
                      Expanded(
                        child: Text(
                          title!,
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                    if (showCloseButton)
                      IconButton(
                        onPressed: onClose ?? () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close),
                      ),
                  ],
                ),
              ),
            ],
            Flexible(
              child: Padding(
                padding: effectivePadding,
                child: child,
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _getModalWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    switch (size) {
      case UISize.xs:
        return (screenWidth * 0.3).clamp(300, 400);
      case UISize.sm:
        return (screenWidth * 0.4).clamp(400, 500);
      case UISize.md:
        return (screenWidth * 0.5).clamp(500, 600);
      case UISize.lg:
        return (screenWidth * 0.6).clamp(600, 800);
      case UISize.xl:
        return (screenWidth * 0.7).clamp(700, 900);
      case UISize.xxl:
        return (screenWidth * 0.8).clamp(800, 1000);
    }
  }

  /// Show modal dialog
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showCloseButton = true,
    VoidCallback? onClose,
    UISize size = UISize.md,
    EdgeInsets? padding,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => UIModal(
        title: title,
        showCloseButton: showCloseButton,
        onClose: onClose,
        size: size,
        padding: padding,
        child: child,
      ),
    );
  }
}
