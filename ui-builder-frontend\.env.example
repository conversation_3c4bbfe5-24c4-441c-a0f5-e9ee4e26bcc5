# UI Builder Frontend Environment Configuration
# Copy this file to .env.local and update the values

# Application Configuration
VITE_APP_NAME=UI Builder
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Visual UI Builder Platform

# Environment
VITE_NODE_ENV=development

# API Configuration
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_WEBSOCKET_URL=ws://localhost:8080/ws
VITE_API_TIMEOUT=30000

# Authentication
VITE_JWT_STORAGE_KEY=ui_builder_token
VITE_REFRESH_TOKEN_KEY=ui_builder_refresh_token
VITE_SESSION_TIMEOUT=1800000

# Features Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_COLLABORATION=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_PWA=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_MULTI_LANGUAGE=false

# File Upload
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,svg,pdf,doc,docx

# External Services
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=
VITE_HOTJAR_ID=

# CDN Configuration
VITE_CDN_BASE_URL=https://cdn.uibuilder.dev
VITE_ASSETS_BASE_URL=https://assets.uibuilder.dev

# Social Authentication (if enabled)
VITE_GOOGLE_CLIENT_ID=
VITE_GITHUB_CLIENT_ID=
VITE_MICROSOFT_CLIENT_ID=

# Development Tools
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_STORYBOOK=true

# Performance
VITE_ENABLE_BUNDLE_ANALYZER=false
VITE_ENABLE_SOURCE_MAPS=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# Collaboration
VITE_MAX_CONCURRENT_USERS=10
VITE_COLLABORATION_TIMEOUT=300000
VITE_AUTO_SAVE_INTERVAL=30000

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_ANIMATIONS=true
VITE_ANIMATION_DURATION=300
VITE_SIDEBAR_WIDTH=280
VITE_TOOLBAR_HEIGHT=60

# Component Library
VITE_COMPONENT_LIBRARY_URL=https://components.uibuilder.dev
VITE_TEMPLATE_LIBRARY_URL=https://templates.uibuilder.dev

# Monitoring
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_LOG_LEVEL=info

# Security
VITE_ENABLE_CSP=true
VITE_TRUSTED_DOMAINS=localhost,uibuilder.dev,*.uibuilder.dev

# Experimental Features
VITE_ENABLE_AI_SUGGESTIONS=false
VITE_ENABLE_VOICE_COMMANDS=false
VITE_ENABLE_GESTURE_CONTROLS=false
