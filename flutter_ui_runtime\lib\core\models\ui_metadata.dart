import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'ui_metadata.g.dart';

/// Core UI metadata model that defines a complete UI configuration
@JsonSerializable()
@HiveType(typeId: 0)
class UIMetadata {
  @HiveField(0)
  final String pageId;
  
  @HiveField(1)
  final String version;
  
  @HiveField(2)
  final String? title;
  
  @HiveField(3)
  final String? description;
  
  @HiveField(4)
  final String theme;
  
  @HiveField(5)
  final LayoutConfiguration layout;
  
  @HiveField(6)
  final DataConfiguration? data;
  
  @HiveField(7)
  final PermissionConfiguration? permissions;
  
  @HiveField(8)
  final MetadataInfo? metadata;

  const UIMetadata({
    required this.pageId,
    required this.version,
    this.title,
    this.description,
    required this.theme,
    required this.layout,
    this.data,
    this.permissions,
    this.metadata,
  });

  factory UIMetadata.fromJson(Map<String, dynamic> json) =>
      _$UIMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$UIMetadataToJson(this);

  UIMetadata copyWith({
    String? pageId,
    String? version,
    String? title,
    String? description,
    String? theme,
    LayoutConfiguration? layout,
    DataConfiguration? data,
    PermissionConfiguration? permissions,
    MetadataInfo? metadata,
  }) {
    return UIMetadata(
      pageId: pageId ?? this.pageId,
      version: version ?? this.version,
      title: title ?? this.title,
      description: description ?? this.description,
      theme: theme ?? this.theme,
      layout: layout ?? this.layout,
      data: data ?? this.data,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Layout configuration for the UI
@JsonSerializable()
@HiveType(typeId: 1)
class LayoutConfiguration {
  @HiveField(0)
  final LayoutType type;
  
  @HiveField(1)
  final int? columns;
  
  @HiveField(2)
  final int? rows;
  
  @HiveField(3)
  final double? gap;
  
  @HiveField(4)
  final EdgeInsetsData? padding;
  
  @HiveField(5)
  final EdgeInsetsData? margin;
  
  @HiveField(6)
  final List<WidgetConfiguration> components;
  
  @HiveField(7)
  final ResponsiveConfiguration? responsive;

  const LayoutConfiguration({
    required this.type,
    this.columns,
    this.rows,
    this.gap,
    this.padding,
    this.margin,
    required this.components,
    this.responsive,
  });

  factory LayoutConfiguration.fromJson(Map<String, dynamic> json) =>
      _$LayoutConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$LayoutConfigurationToJson(this);
}

/// Widget configuration for individual components
@JsonSerializable()
@HiveType(typeId: 2)
class WidgetConfiguration {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String type;
  
  @HiveField(2)
  final Map<String, dynamic> props;
  
  @HiveField(3)
  final PositionConfiguration? position;
  
  @HiveField(4)
  final SizeConfiguration? size;
  
  @HiveField(5)
  final StyleConfiguration? style;
  
  @HiveField(6)
  final List<WidgetConfiguration>? children;
  
  @HiveField(7)
  final List<ConditionConfiguration>? conditions;
  
  @HiveField(8)
  final List<EventConfiguration>? events;
  
  @HiveField(9)
  final DataBindingConfiguration? data;
  
  @HiveField(10)
  final List<String>? permissions;

  const WidgetConfiguration({
    required this.id,
    required this.type,
    required this.props,
    this.position,
    this.size,
    this.style,
    this.children,
    this.conditions,
    this.events,
    this.data,
    this.permissions,
  });

  factory WidgetConfiguration.fromJson(Map<String, dynamic> json) =>
      _$WidgetConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$WidgetConfigurationToJson(this);
}

/// Position configuration for widgets
@JsonSerializable()
@HiveType(typeId: 3)
class PositionConfiguration {
  @HiveField(0)
  final int? col;
  
  @HiveField(1)
  final int? row;
  
  @HiveField(2)
  final int? span;
  
  @HiveField(3)
  final int? rowSpan;
  
  @HiveField(4)
  final double? x;
  
  @HiveField(5)
  final double? y;
  
  @HiveField(6)
  final int? z;

  const PositionConfiguration({
    this.col,
    this.row,
    this.span,
    this.rowSpan,
    this.x,
    this.y,
    this.z,
  });

  factory PositionConfiguration.fromJson(Map<String, dynamic> json) =>
      _$PositionConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$PositionConfigurationToJson(this);
}

/// Size configuration for widgets
@JsonSerializable()
@HiveType(typeId: 4)
class SizeConfiguration {
  @HiveField(0)
  final double? width;
  
  @HiveField(1)
  final double? height;
  
  @HiveField(2)
  final double? minWidth;
  
  @HiveField(3)
  final double? minHeight;
  
  @HiveField(4)
  final double? maxWidth;
  
  @HiveField(5)
  final double? maxHeight;

  const SizeConfiguration({
    this.width,
    this.height,
    this.minWidth,
    this.minHeight,
    this.maxWidth,
    this.maxHeight,
  });

  factory SizeConfiguration.fromJson(Map<String, dynamic> json) =>
      _$SizeConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$SizeConfigurationToJson(this);
}

/// Style configuration for widgets
@JsonSerializable()
@HiveType(typeId: 5)
class StyleConfiguration {
  @HiveField(0)
  final Map<String, dynamic>? style;
  
  @HiveField(1)
  final Map<String, Map<String, dynamic>>? variants;
  
  @HiveField(2)
  final Map<String, Map<String, dynamic>>? responsive;

  const StyleConfiguration({
    this.style,
    this.variants,
    this.responsive,
  });

  factory StyleConfiguration.fromJson(Map<String, dynamic> json) =>
      _$StyleConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$StyleConfigurationToJson(this);
}

/// Edge insets data for padding and margin
@JsonSerializable()
@HiveType(typeId: 6)
class EdgeInsetsData {
  @HiveField(0)
  final double top;
  
  @HiveField(1)
  final double right;
  
  @HiveField(2)
  final double bottom;
  
  @HiveField(3)
  final double left;

  const EdgeInsetsData({
    required this.top,
    required this.right,
    required this.bottom,
    required this.left,
  });

  factory EdgeInsetsData.all(double value) => EdgeInsetsData(
        top: value,
        right: value,
        bottom: value,
        left: value,
      );

  factory EdgeInsetsData.symmetric({
    double vertical = 0.0,
    double horizontal = 0.0,
  }) =>
      EdgeInsetsData(
        top: vertical,
        right: horizontal,
        bottom: vertical,
        left: horizontal,
      );

  factory EdgeInsetsData.fromJson(Map<String, dynamic> json) =>
      _$EdgeInsetsDataFromJson(json);

  Map<String, dynamic> toJson() => _$EdgeInsetsDataToJson(this);
}

/// Layout types supported by the system
@HiveType(typeId: 20)
enum LayoutType {
  @HiveField(0)
  column,
  
  @HiveField(1)
  row,
  
  @HiveField(2)
  stack,
  
  @HiveField(3)
  grid,
  
  @HiveField(4)
  wrap,
  
  @HiveField(5)
  flex,
  
  @HiveField(6)
  absolute,
  
  @HiveField(7)
  scroll,
}

/// Additional model classes would be defined here:
/// - DataConfiguration
/// - PermissionConfiguration
/// - MetadataInfo
/// - ResponsiveConfiguration
/// - ConditionConfiguration
/// - EventConfiguration
/// - DataBindingConfiguration
