package com.uiplatform.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import com.uiplatform.dto.collaboration.CursorPosition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.simp.stomp.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for WebSocket collaboration features.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class WebSocketIntegrationTest {

    @LocalServerPort
    private int port;

    private WebSocketStompClient stompClient;
    private StompSession stompSession;
    private final BlockingQueue<CollaborationEvent> receivedEvents = new LinkedBlockingQueue<>();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() throws Exception {
        stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        // Connect to WebSocket
        String url = "ws://localhost:" + port + "/ws";
        StompHeaders connectHeaders = new StompHeaders();
        connectHeaders.add("Authorization", "Bearer test-token"); // Mock token

        stompSession = stompClient.connect(url, connectHeaders, new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("Connected to WebSocket");
            }

            @Override
            public void handleException(StompSession session, StompCommand command, 
                                      StompHeaders headers, byte[] payload, Throwable exception) {
                exception.printStackTrace();
            }
        }).get(5, TimeUnit.SECONDS);
    }

    @Test
    void testCursorPositionUpdate() throws Exception {
        // Arrange
        UUID configId = UUID.randomUUID();
        String topic = "/topic/ui-config/" + configId + "/cursors";

        // Subscribe to cursor updates
        stompSession.subscribe(topic, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return CollaborationEvent.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                receivedEvents.offer((CollaborationEvent) payload);
            }
        });

        // Create cursor position update
        CursorPosition position = new CursorPosition();
        position.setX(100.0);
        position.setY(200.0);
        position.setElementId("test-element");
        position.setTimestamp(LocalDateTime.now());

        // Send cursor position update
        stompSession.send("/app/cursor/position", position);

        // Wait for response
        CollaborationEvent receivedEvent = receivedEvents.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(receivedEvent);
        assertEquals(CollaborationEventType.CURSOR_MOVED, receivedEvent.getType());
        assertNotNull(receivedEvent.getData());
    }

    @Test
    void testEditingLockAcquisition() throws Exception {
        // Arrange
        UUID configId = UUID.randomUUID();
        String topic = "/topic/ui-config/" + configId + "/locks";

        // Subscribe to lock updates
        stompSession.subscribe(topic, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return CollaborationEvent.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                receivedEvents.offer((CollaborationEvent) payload);
            }
        });

        // Create lock request
        EditingLockRequest lockRequest = new EditingLockRequest();
        lockRequest.setElementId("test-element");
        lockRequest.setElementType("component");

        // Send lock acquisition request
        stompSession.send("/app/lock/acquire", lockRequest);

        // Wait for response
        CollaborationEvent receivedEvent = receivedEvents.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(receivedEvent);
        assertTrue(receivedEvent.getType() == CollaborationEventType.LOCK_ACQUIRED ||
                  receivedEvent.getType() == CollaborationEventType.LOCK_CONFLICT);
    }

    @Test
    void testUIConfigurationUpdate() throws Exception {
        // Arrange
        UUID configId = UUID.randomUUID();
        String topic = "/topic/ui-config/" + configId + "/updates";

        // Subscribe to UI config updates
        stompSession.subscribe(topic, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return CollaborationEvent.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                receivedEvents.offer((CollaborationEvent) payload);
            }
        });

        // Create UI config update
        UIConfigUpdateEvent updateEvent = new UIConfigUpdateEvent();
        updateEvent.setOperation("update");
        updateEvent.setElementId("test-element");
        updateEvent.setData("test-data");

        // Send UI config update
        stompSession.send("/app/ui-config/update", updateEvent);

        // Wait for response
        CollaborationEvent receivedEvent = receivedEvents.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(receivedEvent);
        assertEquals(CollaborationEventType.UI_CONFIG_UPDATED, receivedEvent.getType());
    }

    @Test
    void testPresenceUpdate() throws Exception {
        // Arrange
        UUID orgId = UUID.randomUUID();
        String topic = "/topic/organization/" + orgId + "/presence";

        // Subscribe to presence updates
        stompSession.subscribe(topic, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return CollaborationEvent.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                receivedEvents.offer((CollaborationEvent) payload);
            }
        });

        // Create presence update
        UserPresenceUpdate presenceUpdate = new UserPresenceUpdate();
        presenceUpdate.setStatus("active");
        presenceUpdate.setCurrentConfigId(UUID.randomUUID());

        // Send presence update
        stompSession.send("/app/presence/update", presenceUpdate);

        // Wait for response
        CollaborationEvent receivedEvent = receivedEvents.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(receivedEvent);
        // Presence updates might not always generate events, so we just verify no errors
    }

    @Test
    void testPingPong() throws Exception {
        // Arrange
        String userQueue = "/user/queue/pong";
        final BlockingQueue<String> pongResponses = new LinkedBlockingQueue<>();

        // Subscribe to pong responses
        stompSession.subscribe(userQueue, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                pongResponses.offer((String) payload);
            }
        });

        // Send ping
        stompSession.send("/app/ping", "ping");

        // Wait for pong response
        String pongResponse = pongResponses.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(pongResponse);
        assertEquals("pong", pongResponse);
    }

    @Test
    void testActiveUsersRequest() throws Exception {
        // Arrange
        String userQueue = "/user/queue/active-users";
        final BlockingQueue<Object> userResponses = new LinkedBlockingQueue<>();

        // Subscribe to active users responses
        stompSession.subscribe(userQueue, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return Object.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                userResponses.offer(payload);
            }
        });

        // Create active users request
        ActiveUsersRequest request = new ActiveUsersRequest();
        request.setConfigId(UUID.randomUUID());

        // Send active users request
        stompSession.send("/app/users/active", request);

        // Wait for response
        Object response = userResponses.poll(5, TimeUnit.SECONDS);

        // Assert
        assertNotNull(response);
        // Response should be a list of active users
    }

    // Helper classes for test data

    public static class EditingLockRequest {
        private String elementId;
        private String elementType;

        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public String getElementType() { return elementType; }
        public void setElementType(String elementType) { this.elementType = elementType; }
    }

    public static class UIConfigUpdateEvent {
        private String operation;
        private String elementId;
        private Object data;

        public String getOperation() { return operation; }
        public void setOperation(String operation) { this.operation = operation; }
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }

    public static class UserPresenceUpdate {
        private String status;
        private UUID currentConfigId;

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public UUID getCurrentConfigId() { return currentConfigId; }
        public void setCurrentConfigId(UUID currentConfigId) { this.currentConfigId = currentConfigId; }
    }

    public static class ActiveUsersRequest {
        private UUID configId;

        public UUID getConfigId() { return configId; }
        public void setConfigId(UUID configId) { this.configId = configId; }
    }
}
