{"name": "@ui-builder/design-tokens", "version": "1.0.0", "description": "Design tokens for UI Builder component library", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "tokens"], "scripts": {"build": "style-dictionary build && tsc", "clean": "rm -rf dist", "watch": "style-dictionary build --watch", "test": "jest", "lint": "eslint src/**/*.{ts,js}", "format": "prettier --write src/**/*.{ts,js,json}"}, "dependencies": {"chroma-js": "^2.4.2"}, "devDependencies": {"@types/chroma-js": "^2.4.3", "style-dictionary": "^3.9.0", "typescript": "^5.2.2"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/ui-builder/component-library.git", "directory": "packages/design-tokens"}, "keywords": ["design-tokens", "design-system", "ui-builder", "style-dictionary"], "author": "UI Builder Team", "license": "MIT"}