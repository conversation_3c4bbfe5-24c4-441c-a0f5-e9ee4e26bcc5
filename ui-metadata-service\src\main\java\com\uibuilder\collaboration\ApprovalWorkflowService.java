package com.uibuilder.collaboration;

import com.uibuilder.entity.ChangeRequest;
import com.uibuilder.entity.ApprovalWorkflow;
import com.uibuilder.entity.WorkflowStep;
import com.uibuilder.entity.User;
import com.uibuilder.entity.UIConfig;
import com.uibuilder.repository.ChangeRequestRepository;
import com.uibuilder.repository.ApprovalWorkflowRepository;
import com.uibuilder.repository.WorkflowStepRepository;
import com.uibuilder.repository.UserRepository;
import com.uibuilder.repository.UIConfigRepository;
import com.uibuilder.security.rbac.RoleBasedAccessControl;
import com.uibuilder.websocket.CollaborationWebSocketHandler;
import com.uibuilder.notification.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApprovalWorkflowService {

    private final ChangeRequestRepository changeRequestRepository;
    private final ApprovalWorkflowRepository workflowRepository;
    private final WorkflowStepRepository workflowStepRepository;
    private final UserRepository userRepository;
    private final UIConfigRepository uiConfigRepository;
    private final RoleBasedAccessControl rbac;
    private final CollaborationWebSocketHandler webSocketHandler;
    private final NotificationService notificationService;
    private final VersionControlService versionControlService;

    /**
     * Create a change request for UI config modifications
     */
    @Transactional
    public ChangeRequest createChangeRequest(CreateChangeRequestDto request, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "create_change_request", request.getWorkspaceId())) {
            throw new AccessDeniedException("Insufficient permissions to create change requests");
        }

        UIConfig config = uiConfigRepository.findById(request.getConfigId())
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        User creator = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("User not found"));

        // Get applicable workflow
        ApprovalWorkflow workflow = getApplicableWorkflow(request.getWorkspaceId(), request.getChangeType());

        // Create change request
        ChangeRequest changeRequest = ChangeRequest.builder()
            .id(UUID.randomUUID().toString())
            .title(request.getTitle())
            .description(request.getDescription())
            .changeType(request.getChangeType())
            .uiConfig(config)
            .proposedChanges(request.getProposedChanges())
            .creator(creator)
            .workflow(workflow)
            .status(ChangeRequestStatus.PENDING)
            .priority(request.getPriority())
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        changeRequest = changeRequestRepository.save(changeRequest);

        // Initialize workflow steps
        if (workflow != null) {
            initializeWorkflowSteps(changeRequest, workflow);
        } else {
            // Auto-approve if no workflow required
            approveChangeRequest(changeRequest.getId(), userId, "Auto-approved - no workflow required");
        }

        // Broadcast change request creation
        ChangeRequestEvent event = ChangeRequestEvent.builder()
            .type("CHANGE_REQUEST_CREATED")
            .changeRequest(toChangeRequestDto(changeRequest))
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToWorkspace(request.getWorkspaceId(), event);

        // Notify reviewers
        notifyReviewers(changeRequest);

        log.info("Change request {} created by user {}", changeRequest.getId(), userId);
        return changeRequest;
    }

    /**
     * Review a change request step
     */
    @Transactional
    public ChangeRequest reviewChangeRequest(String changeRequestId, ReviewDecisionDto decision, String userId) {
        ChangeRequest changeRequest = changeRequestRepository.findById(changeRequestId)
            .orElseThrow(() -> new EntityNotFoundException("Change request not found"));

        // Validate reviewer permissions
        if (!canReviewChangeRequest(changeRequest, userId)) {
            throw new AccessDeniedException("Insufficient permissions to review this change request");
        }

        // Get current workflow step
        WorkflowStep currentStep = getCurrentWorkflowStep(changeRequest);
        if (currentStep == null) {
            throw new IllegalStateException("No active workflow step found");
        }

        // Record the review
        currentStep.setReviewedBy(userId);
        currentStep.setReviewedAt(LocalDateTime.now());
        currentStep.setDecision(decision.getDecision());
        currentStep.setComments(decision.getComments());
        currentStep.setStatus(decision.getDecision() == ReviewDecision.APPROVED ? 
                             WorkflowStepStatus.COMPLETED : WorkflowStepStatus.REJECTED);

        workflowStepRepository.save(currentStep);

        // Update change request based on decision
        if (decision.getDecision() == ReviewDecision.APPROVED) {
            // Move to next step or complete
            WorkflowStep nextStep = getNextWorkflowStep(changeRequest, currentStep);
            if (nextStep != null) {
                nextStep.setStatus(WorkflowStepStatus.PENDING);
                nextStep.setStartedAt(LocalDateTime.now());
                workflowStepRepository.save(nextStep);
                
                changeRequest.setCurrentStep(nextStep.getStepOrder());
                
                // Notify next reviewers
                notifyStepReviewers(changeRequest, nextStep);
            } else {
                // All steps completed - approve the change request
                approveChangeRequest(changeRequestId, userId, "All workflow steps completed");
            }
        } else if (decision.getDecision() == ReviewDecision.REJECTED) {
            // Reject the entire change request
            rejectChangeRequest(changeRequestId, userId, decision.getComments());
        } else if (decision.getDecision() == ReviewDecision.CHANGES_REQUESTED) {
            // Request changes from the creator
            requestChanges(changeRequestId, userId, decision.getComments());
        }

        changeRequest.setUpdatedAt(LocalDateTime.now());
        changeRequest = changeRequestRepository.save(changeRequest);

        // Broadcast review decision
        ChangeRequestEvent event = ChangeRequestEvent.builder()
            .type("CHANGE_REQUEST_REVIEWED")
            .changeRequest(toChangeRequestDto(changeRequest))
            .reviewDecision(decision)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToWorkspace(changeRequest.getUiConfig().getWorkspace().getId(), event);

        return changeRequest;
    }

    /**
     * Approve a change request and apply changes
     */
    @Transactional
    public ChangeRequest approveChangeRequest(String changeRequestId, String userId, String comments) {
        ChangeRequest changeRequest = changeRequestRepository.findById(changeRequestId)
            .orElseThrow(() -> new EntityNotFoundException("Change request not found"));

        // Apply the proposed changes
        try {
            UIConfig updatedConfig = applyChanges(changeRequest);
            
            // Create a new version
            versionControlService.createVersion(
                updatedConfig.getId(),
                "Change request #" + changeRequest.getId() + " approved",
                userId,
                changeRequest.getProposedChanges()
            );

            changeRequest.setStatus(ChangeRequestStatus.APPROVED);
            changeRequest.setApprovedBy(userId);
            changeRequest.setApprovedAt(LocalDateTime.now());
            changeRequest.setApprovalComments(comments);
            changeRequest.setUpdatedAt(LocalDateTime.now());

            changeRequest = changeRequestRepository.save(changeRequest);

            // Notify creator and stakeholders
            notificationService.sendChangeRequestApprovedNotification(
                changeRequest.getCreator().getId(),
                changeRequest,
                userId
            );

            // Broadcast approval
            ChangeRequestEvent event = ChangeRequestEvent.builder()
                .type("CHANGE_REQUEST_APPROVED")
                .changeRequest(toChangeRequestDto(changeRequest))
                .userId(userId)
                .timestamp(LocalDateTime.now())
                .build();

            webSocketHandler.broadcastToWorkspace(changeRequest.getUiConfig().getWorkspace().getId(), event);

            log.info("Change request {} approved by user {}", changeRequestId, userId);

        } catch (Exception e) {
            log.error("Failed to apply changes for change request {}", changeRequestId, e);
            throw new RuntimeException("Failed to apply changes", e);
        }

        return changeRequest;
    }

    /**
     * Reject a change request
     */
    @Transactional
    public ChangeRequest rejectChangeRequest(String changeRequestId, String userId, String reason) {
        ChangeRequest changeRequest = changeRequestRepository.findById(changeRequestId)
            .orElseThrow(() -> new EntityNotFoundException("Change request not found"));

        changeRequest.setStatus(ChangeRequestStatus.REJECTED);
        changeRequest.setRejectedBy(userId);
        changeRequest.setRejectedAt(LocalDateTime.now());
        changeRequest.setRejectionReason(reason);
        changeRequest.setUpdatedAt(LocalDateTime.now());

        changeRequest = changeRequestRepository.save(changeRequest);

        // Notify creator
        notificationService.sendChangeRequestRejectedNotification(
            changeRequest.getCreator().getId(),
            changeRequest,
            userId,
            reason
        );

        // Broadcast rejection
        ChangeRequestEvent event = ChangeRequestEvent.builder()
            .type("CHANGE_REQUEST_REJECTED")
            .changeRequest(toChangeRequestDto(changeRequest))
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToWorkspace(changeRequest.getUiConfig().getWorkspace().getId(), event);

        log.info("Change request {} rejected by user {}", changeRequestId, userId);
        return changeRequest;
    }

    /**
     * Request changes to a change request
     */
    @Transactional
    public ChangeRequest requestChanges(String changeRequestId, String userId, String feedback) {
        ChangeRequest changeRequest = changeRequestRepository.findById(changeRequestId)
            .orElseThrow(() -> new EntityNotFoundException("Change request not found"));

        changeRequest.setStatus(ChangeRequestStatus.CHANGES_REQUESTED);
        changeRequest.setFeedback(feedback);
        changeRequest.setUpdatedAt(LocalDateTime.now());

        changeRequest = changeRequestRepository.save(changeRequest);

        // Notify creator
        notificationService.sendChangeRequestFeedbackNotification(
            changeRequest.getCreator().getId(),
            changeRequest,
            userId,
            feedback
        );

        return changeRequest;
    }

    /**
     * Update a change request with new changes
     */
    @Transactional
    public ChangeRequest updateChangeRequest(String changeRequestId, UpdateChangeRequestDto update, String userId) {
        ChangeRequest changeRequest = changeRequestRepository.findById(changeRequestId)
            .orElseThrow(() -> new EntityNotFoundException("Change request not found"));

        // Only creator can update
        if (!changeRequest.getCreator().getId().equals(userId)) {
            throw new AccessDeniedException("Only the creator can update a change request");
        }

        // Only allow updates if changes were requested
        if (changeRequest.getStatus() != ChangeRequestStatus.CHANGES_REQUESTED) {
            throw new IllegalStateException("Change request cannot be updated in current status");
        }

        changeRequest.setTitle(update.getTitle());
        changeRequest.setDescription(update.getDescription());
        changeRequest.setProposedChanges(update.getProposedChanges());
        changeRequest.setStatus(ChangeRequestStatus.PENDING);
        changeRequest.setUpdatedAt(LocalDateTime.now());

        // Reset workflow to first step
        resetWorkflowSteps(changeRequest);

        changeRequest = changeRequestRepository.save(changeRequest);

        // Notify reviewers
        notifyReviewers(changeRequest);

        return changeRequest;
    }

    /**
     * Get change requests for a workspace
     */
    public Page<ChangeRequest> getChangeRequests(String workspaceId, ChangeRequestFilter filter, 
                                                Pageable pageable, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_change_requests", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view change requests");
        }

        return changeRequestRepository.findByWorkspaceIdAndFilters(workspaceId, filter, pageable);
    }

    /**
     * Get change requests pending review by user
     */
    public Page<ChangeRequest> getPendingReviews(String userId, Pageable pageable) {
        return changeRequestRepository.findPendingReviewsByUser(userId, pageable);
    }

    private ApprovalWorkflow getApplicableWorkflow(String workspaceId, ChangeType changeType) {
        return workflowRepository.findByWorkspaceIdAndChangeTypeAndIsActive(workspaceId, changeType, true)
            .orElse(null);
    }

    private void initializeWorkflowSteps(ChangeRequest changeRequest, ApprovalWorkflow workflow) {
        List<WorkflowStep> steps = workflow.getSteps().stream()
            .map(template -> WorkflowStep.builder()
                .id(UUID.randomUUID().toString())
                .changeRequest(changeRequest)
                .stepOrder(template.getStepOrder())
                .stepType(template.getStepType())
                .requiredApprovers(template.getRequiredApprovers())
                .reviewers(template.getReviewers())
                .status(template.getStepOrder() == 1 ? WorkflowStepStatus.PENDING : WorkflowStepStatus.WAITING)
                .startedAt(template.getStepOrder() == 1 ? LocalDateTime.now() : null)
                .build())
            .toList();

        workflowStepRepository.saveAll(steps);
        
        if (!steps.isEmpty()) {
            changeRequest.setCurrentStep(1);
        }
    }

    private WorkflowStep getCurrentWorkflowStep(ChangeRequest changeRequest) {
        return workflowStepRepository.findByChangeRequestAndStepOrder(
            changeRequest, changeRequest.getCurrentStep()).orElse(null);
    }

    private WorkflowStep getNextWorkflowStep(ChangeRequest changeRequest, WorkflowStep currentStep) {
        return workflowStepRepository.findByChangeRequestAndStepOrder(
            changeRequest, currentStep.getStepOrder() + 1).orElse(null);
    }

    private boolean canReviewChangeRequest(ChangeRequest changeRequest, String userId) {
        WorkflowStep currentStep = getCurrentWorkflowStep(changeRequest);
        if (currentStep == null) {
            return false;
        }

        // Check if user is in the reviewers list for current step
        return currentStep.getReviewers().contains(userId) ||
               rbac.hasPermission(userId, "workspace", "approve_all_changes", 
                                changeRequest.getUiConfig().getWorkspace().getId());
    }

    private UIConfig applyChanges(ChangeRequest changeRequest) {
        // Implementation would apply the JSON changes to the UI config
        // This is a simplified version
        UIConfig config = changeRequest.getUiConfig();
        
        // Apply changes from changeRequest.getProposedChanges()
        // This would involve merging the changes into the existing config
        
        config.setUpdatedAt(LocalDateTime.now());
        return uiConfigRepository.save(config);
    }

    private void notifyReviewers(ChangeRequest changeRequest) {
        WorkflowStep currentStep = getCurrentWorkflowStep(changeRequest);
        if (currentStep != null) {
            notifyStepReviewers(changeRequest, currentStep);
        }
    }

    private void notifyStepReviewers(ChangeRequest changeRequest, WorkflowStep step) {
        for (String reviewerId : step.getReviewers()) {
            notificationService.sendChangeRequestReviewNotification(
                reviewerId,
                changeRequest,
                step
            );
        }
    }

    private void resetWorkflowSteps(ChangeRequest changeRequest) {
        List<WorkflowStep> steps = workflowStepRepository.findByChangeRequestOrderByStepOrder(changeRequest);
        
        for (WorkflowStep step : steps) {
            step.setStatus(step.getStepOrder() == 1 ? WorkflowStepStatus.PENDING : WorkflowStepStatus.WAITING);
            step.setReviewedBy(null);
            step.setReviewedAt(null);
            step.setDecision(null);
            step.setComments(null);
            step.setStartedAt(step.getStepOrder() == 1 ? LocalDateTime.now() : null);
        }
        
        workflowStepRepository.saveAll(steps);
        changeRequest.setCurrentStep(1);
    }

    private ChangeRequestDto toChangeRequestDto(ChangeRequest changeRequest) {
        return ChangeRequestDto.builder()
            .id(changeRequest.getId())
            .title(changeRequest.getTitle())
            .description(changeRequest.getDescription())
            .changeType(changeRequest.getChangeType())
            .status(changeRequest.getStatus())
            .priority(changeRequest.getPriority())
            .creator(toUserDto(changeRequest.getCreator()))
            .configId(changeRequest.getUiConfig().getId())
            .currentStep(changeRequest.getCurrentStep())
            .createdAt(changeRequest.getCreatedAt())
            .updatedAt(changeRequest.getUpdatedAt())
            .approvedAt(changeRequest.getApprovedAt())
            .rejectedAt(changeRequest.getRejectedAt())
            .build();
    }

    private UserDto toUserDto(User user) {
        return UserDto.builder()
            .id(user.getId())
            .name(user.getName())
            .email(user.getEmail())
            .avatar(user.getAvatar())
            .build();
    }
}
