#!/bin/bash

# UI Builder Deployment Script
# Automates deployment to different environments with health checks and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/deployment"

# Default values
ENVIRONMENT="staging"
IMAGE_TAG="latest"
NAMESPACE="ui-builder"
DRY_RUN=false
SKIP_TESTS=false
ROLLBACK=false
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
UI Builder Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENV    Target environment (staging, production) [default: staging]
    -t, --tag TAG           Docker image tag to deploy [default: latest]
    -n, --namespace NS      Kubernetes namespace [default: ui-builder]
    -d, --dry-run          Show what would be deployed without executing
    -s, --skip-tests       Skip pre-deployment tests
    -r, --rollback         Rollback to previous deployment
    -v, --verbose          Enable verbose output
    -h, --help             Show this help message

Examples:
    $0 --environment staging --tag v1.2.3
    $0 --environment production --tag v1.2.3 --verbose
    $0 --rollback --environment staging

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -r|--rollback)
                ROLLBACK=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required tools
    local tools=("kubectl" "docker" "helm")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check kubectl context
    local current_context
    current_context=$(kubectl config current-context)
    log_info "Current kubectl context: $current_context"
    
    # Verify cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "Namespace $NAMESPACE does not exist, creating..."
        kubectl create namespace "$NAMESPACE"
    fi
    
    log_success "Prerequisites check passed"
}

# Run pre-deployment tests
run_pre_deployment_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "Skipping pre-deployment tests"
        return 0
    fi
    
    log_info "Running pre-deployment tests..."
    
    # Run unit tests
    log_info "Running unit tests..."
    cd "$PROJECT_ROOT"
    npm run test:unit || {
        log_error "Unit tests failed"
        exit 1
    }
    
    # Run integration tests
    log_info "Running integration tests..."
    npm run test:integration || {
        log_error "Integration tests failed"
        exit 1
    }
    
    # Validate Kubernetes manifests
    log_info "Validating Kubernetes manifests..."
    kubectl apply --dry-run=client -k "$DEPLOYMENT_DIR/kubernetes/$ENVIRONMENT" || {
        log_error "Kubernetes manifest validation failed"
        exit 1
    }
    
    log_success "Pre-deployment tests passed"
}

# Build and push images
build_and_push_images() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would build and push images with tag: $IMAGE_TAG"
        return 0
    fi
    
    log_info "Building and pushing Docker images..."
    
    local services=("api-gateway" "auth-service" "config-service" "template-service" "analytics-service" "frontend" "web-runtime")
    
    for service in "${services[@]}"; do
        log_info "Building $service..."
        
        cd "$PROJECT_ROOT/$service"
        
        # Build image
        docker build -t "ui-builder/$service:$IMAGE_TAG" . || {
            log_error "Failed to build $service"
            exit 1
        }
        
        # Tag for registry
        docker tag "ui-builder/$service:$IMAGE_TAG" "ghcr.io/ui-builder/$service:$IMAGE_TAG"
        
        # Push to registry
        docker push "ghcr.io/ui-builder/$service:$IMAGE_TAG" || {
            log_error "Failed to push $service"
            exit 1
        }
        
        log_success "Built and pushed $service"
    done
}

# Deploy to Kubernetes
deploy_to_kubernetes() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would deploy to Kubernetes with the following changes:"
        kubectl diff -k "$DEPLOYMENT_DIR/kubernetes/$ENVIRONMENT" || true
        return 0
    fi
    
    log_info "Deploying to Kubernetes..."
    
    # Set image tag in kustomization
    cd "$DEPLOYMENT_DIR/kubernetes/$ENVIRONMENT"
    
    # Update image tags
    kustomize edit set image "ghcr.io/ui-builder/frontend:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/api-gateway:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/auth-service:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/config-service:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/template-service:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/analytics-service:$IMAGE_TAG"
    kustomize edit set image "ghcr.io/ui-builder/web-runtime:$IMAGE_TAG"
    
    # Apply manifests
    kubectl apply -k . || {
        log_error "Failed to apply Kubernetes manifests"
        exit 1
    }
    
    log_success "Deployed to Kubernetes"
}

# Wait for deployment to complete
wait_for_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would wait for deployment to complete"
        return 0
    fi
    
    log_info "Waiting for deployment to complete..."
    
    local deployments=("frontend" "api-gateway" "auth-service" "config-service" "template-service" "analytics-service" "web-runtime")
    
    for deployment in "${deployments[@]}"; do
        log_info "Waiting for $deployment to be ready..."
        
        kubectl rollout status "deployment/$deployment" -n "$NAMESPACE" --timeout=600s || {
            log_error "Deployment $deployment failed to become ready"
            return 1
        }
        
        log_success "$deployment is ready"
    done
    
    log_success "All deployments are ready"
}

# Run health checks
run_health_checks() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would run health checks"
        return 0
    fi
    
    log_info "Running health checks..."
    
    # Get service URLs
    local frontend_url
    if [[ "$ENVIRONMENT" == "production" ]]; then
        frontend_url="https://ui-builder.com"
    else
        frontend_url="https://staging.ui-builder.com"
    fi
    
    # Check frontend health
    log_info "Checking frontend health..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$frontend_url/health" > /dev/null; then
            log_success "Frontend health check passed"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Frontend health check failed after $max_attempts attempts"
            return 1
        fi
        
        log_info "Attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    # Check API health
    log_info "Checking API health..."
    if curl -f -s "$frontend_url/api/health" > /dev/null; then
        log_success "API health check passed"
    else
        log_error "API health check failed"
        return 1
    fi
    
    log_success "All health checks passed"
}

# Rollback deployment
rollback_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would rollback deployment"
        return 0
    fi
    
    log_info "Rolling back deployment..."
    
    local deployments=("frontend" "api-gateway" "auth-service" "config-service" "template-service" "analytics-service" "web-runtime")
    
    for deployment in "${deployments[@]}"; do
        log_info "Rolling back $deployment..."
        
        kubectl rollout undo "deployment/$deployment" -n "$NAMESPACE" || {
            log_error "Failed to rollback $deployment"
            exit 1
        }
        
        kubectl rollout status "deployment/$deployment" -n "$NAMESPACE" --timeout=300s || {
            log_error "Rollback of $deployment failed to complete"
            exit 1
        }
        
        log_success "Rolled back $deployment"
    done
    
    log_success "Rollback completed"
}

# Run post-deployment tests
run_post_deployment_tests() {
    if [[ "$SKIP_TESTS" == "true" ]] || [[ "$DRY_RUN" == "true" ]]; then
        log_warning "Skipping post-deployment tests"
        return 0
    fi
    
    log_info "Running post-deployment tests..."
    
    cd "$PROJECT_ROOT"
    
    # Run smoke tests
    npm run test:smoke -- --env="$ENVIRONMENT" || {
        log_error "Smoke tests failed"
        return 1
    }
    
    # Run E2E tests for critical paths
    npm run test:e2e:critical -- --env="$ENVIRONMENT" || {
        log_error "Critical E2E tests failed"
        return 1
    }
    
    log_success "Post-deployment tests passed"
}

# Send notifications
send_notifications() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would send deployment notifications"
        return 0
    fi
    
    log_info "Sending deployment notifications..."
    
    # Send Slack notification (if webhook is configured)
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local message="🚀 UI Builder deployed to $ENVIRONMENT (tag: $IMAGE_TAG)"
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK_URL" || log_warning "Failed to send Slack notification"
    fi
    
    log_success "Notifications sent"
}

# Main deployment function
main() {
    parse_args "$@"
    
    log_info "Starting UI Builder deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Image tag: $IMAGE_TAG"
    log_info "Namespace: $NAMESPACE"
    log_info "Dry run: $DRY_RUN"
    
    validate_environment
    check_prerequisites
    
    if [[ "$ROLLBACK" == "true" ]]; then
        rollback_deployment
        run_health_checks
        log_success "Rollback completed successfully"
        exit 0
    fi
    
    run_pre_deployment_tests
    build_and_push_images
    deploy_to_kubernetes
    
    if wait_for_deployment && run_health_checks; then
        run_post_deployment_tests
        send_notifications
        log_success "Deployment completed successfully"
    else
        log_error "Deployment failed, consider rolling back"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
