package com.uiplatform.repository;

import com.uiplatform.entity.Organization;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Organization entity.
 * Provides CRUD operations and custom queries for organization management.
 */
@Repository
public interface OrganizationRepository extends JpaRepository<Organization, UUID>, JpaSpecificationExecutor<Organization> {

    /**
     * Find organization by slug.
     */
    Optional<Organization> findBySlugAndDeletedFalse(String slug);

    /**
     * Find organization by domain.
     */
    Optional<Organization> findByDomainAndDeletedFalse(String domain);

    /**
     * Find organizations by status.
     */
    List<Organization> findByStatusAndDeletedFalse(Organization.OrganizationStatus status);

    /**
     * Find organizations by subscription plan.
     */
    List<Organization> findBySubscriptionPlanAndDeletedFalse(Organization.SubscriptionPlan subscriptionPlan);

    /**
     * Search organizations by name containing text (case-insensitive).
     */
    @Query("SELECT o FROM Organization o WHERE LOWER(o.name) LIKE LOWER(CONCAT('%', :name, '%')) AND o.deleted = false")
    Page<Organization> findByNameContainingIgnoreCase(@Param("name") String name, Pageable pageable);

    /**
     * Find active organizations with user count.
     */
    @Query("SELECT o FROM Organization o LEFT JOIN FETCH o.users WHERE o.status = 'ACTIVE' AND o.deleted = false")
    List<Organization> findActiveOrganizationsWithUsers();

    /**
     * Count organizations by subscription plan.
     */
    @Query("SELECT COUNT(o) FROM Organization o WHERE o.subscriptionPlan = :plan AND o.deleted = false")
    Long countBySubscriptionPlan(@Param("plan") Organization.SubscriptionPlan plan);

    /**
     * Find organizations exceeding user limit.
     */
    @Query("SELECT o FROM Organization o WHERE SIZE(o.users) > o.maxUsers AND o.deleted = false")
    List<Organization> findOrganizationsExceedingUserLimit();

    /**
     * Find organizations by creation date range.
     */
    @Query("SELECT o FROM Organization o WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate AND o.deleted = false")
    List<Organization> findByCreatedAtBetween(@Param("startDate") java.time.LocalDateTime startDate, 
                                            @Param("endDate") java.time.LocalDateTime endDate);

    /**
     * Check if slug exists (excluding current organization).
     */
    @Query("SELECT COUNT(o) > 0 FROM Organization o WHERE o.slug = :slug AND o.id != :excludeId AND o.deleted = false")
    boolean existsBySlugAndIdNot(@Param("slug") String slug, @Param("excludeId") UUID excludeId);

    /**
     * Check if domain exists (excluding current organization).
     */
    @Query("SELECT COUNT(o) > 0 FROM Organization o WHERE o.domain = :domain AND o.id != :excludeId AND o.deleted = false")
    boolean existsByDomainAndIdNot(@Param("domain") String domain, @Param("excludeId") UUID excludeId);

    /**
     * Find organizations with templates count.
     */
    @Query("SELECT o, COUNT(t) as templateCount FROM Organization o LEFT JOIN o.templates t " +
           "WHERE o.deleted = false GROUP BY o ORDER BY templateCount DESC")
    List<Object[]> findOrganizationsWithTemplateCount();

    /**
     * Find organizations by subscription plan with pagination.
     */
    Page<Organization> findBySubscriptionPlanAndDeletedFalse(Organization.SubscriptionPlan subscriptionPlan, Pageable pageable);

    /**
     * Find organizations by status with pagination.
     */
    Page<Organization> findByStatusAndDeletedFalse(Organization.OrganizationStatus status, Pageable pageable);

    /**
     * Search organizations by multiple criteria.
     */
    @Query("SELECT o FROM Organization o WHERE " +
           "(:name IS NULL OR LOWER(o.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:status IS NULL OR o.status = :status) AND " +
           "(:plan IS NULL OR o.subscriptionPlan = :plan) AND " +
           "o.deleted = false")
    Page<Organization> searchOrganizations(@Param("name") String name,
                                         @Param("status") Organization.OrganizationStatus status,
                                         @Param("plan") Organization.SubscriptionPlan plan,
                                         Pageable pageable);
}
