import type { StorybookConfig } from '@storybook/react-vite';
import { mergeConfig } from 'vite';
import path from 'path';

const config: StorybookConfig = {
  stories: [
    '../packages/*/src/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../packages/*/stories/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../docs/**/*.stories.@(js|jsx|ts|tsx|mdx)',
  ],
  
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-docs',
    '@storybook/addon-controls',
    '@storybook/addon-viewport',
    '@storybook/addon-backgrounds',
    '@storybook/addon-measure',
    '@storybook/addon-outline',
    '@storybook/addon-a11y',
    '@storybook/addon-design-tokens',
    'storybook-addon-pseudo-states',
    'storybook-addon-figma',
    '@chromatic-com/storybook',
  ],
  
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },
  
  docs: {
    autodocs: 'tag',
    defaultName: 'Documentation',
  },
  
  viteFinal: async (config) => {
    return mergeConfig(config, {
      resolve: {
        alias: {
          '@ui-builder/core': path.resolve(__dirname, '../packages/core/src'),
          '@ui-builder/react': path.resolve(__dirname, '../packages/react/src'),
          '@ui-builder/icons': path.resolve(__dirname, '../packages/icons/src'),
          '@ui-builder/tokens': path.resolve(__dirname, '../packages/tokens/src'),
          '@ui-builder/utils': path.resolve(__dirname, '../packages/utils/src'),
        },
      },
      define: {
        'process.env': {},
      },
      optimizeDeps: {
        include: ['@storybook/addon-docs'],
      },
    });
  },
  
  features: {
    buildStoriesJson: true,
    storyStoreV7: true,
  },
  
  staticDirs: [
    '../packages/tokens/assets',
    '../docs/assets',
  ],
  
  core: {
    disableTelemetry: true,
  },
  
  env: (config) => ({
    ...config,
    STORYBOOK_THEME: 'light',
  }),
};

export default config;
