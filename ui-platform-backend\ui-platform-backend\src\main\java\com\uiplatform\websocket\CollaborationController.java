package com.uiplatform.websocket;

import com.uiplatform.dto.collaboration.*;
import com.uiplatform.service.CollaborationService;
import com.uiplatform.service.CursorTrackingService;
import com.uiplatform.service.UIConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * WebSocket controller for handling real-time collaboration messages.
 */
@Controller
public class CollaborationController {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationController.class);

    private final CollaborationService collaborationService;
    private final UIConfigurationService uiConfigurationService;
    private final CursorTrackingService cursorTrackingService;

    @Autowired
    public CollaborationController(CollaborationService collaborationService,
                                  UIConfigurationService uiConfigurationService,
                                  CursorTrackingService cursorTrackingService) {
        this.collaborationService = collaborationService;
        this.uiConfigurationService = uiConfigurationService;
        this.cursorTrackingService = cursorTrackingService;
    }

    /**
     * Handle cursor position updates.
     */
    @MessageMapping("/cursor/position")
    @SendTo("/topic/ui-config/{configId}/cursors")
    public CollaborationEvent updateCursorPosition(@DestinationVariable UUID configId,
                                                  @Payload CursorPosition position,
                                                  Principal principal,
                                                  SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Cursor position update from user {} for config {}", username, configId);
        
        try {
            // Update cursor position using CursorTrackingService
            cursorTrackingService.updateCursorPosition(
                userId, username, configId, position.getElementId(),
                position.getX(), position.getY(),
                position.getSelectionStart(), position.getSelectionEnd()
            );

            // Create and return collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.CURSOR_MOVED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setElementId(position.getElementId());
            event.setData(position);
            event.setTimestamp(LocalDateTime.now());

            return event;
            
        } catch (Exception e) {
            logger.error("Error updating cursor position", e);
            return createErrorEvent(userId, username, configId, "Failed to update cursor position");
        }
    }

    /**
     * Handle editing lock acquisition requests.
     */
    @MessageMapping("/lock/acquire")
    @SendTo("/topic/ui-config/{configId}/locks")
    public CollaborationEvent acquireEditingLock(@DestinationVariable UUID configId,
                                                @Payload EditingLockRequest request,
                                                Principal principal,
                                                SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Lock acquisition request from user {} for element {} in config {}", 
                    username, request.getElementId(), configId);
        
        try {
            boolean acquired = collaborationService.acquireEditingLock(userId, configId, request.getElementId());
            
            CollaborationEvent event = new CollaborationEvent();
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setElementId(request.getElementId());
            event.setTimestamp(LocalDateTime.now());
            
            if (acquired) {
                event.setType(CollaborationEventType.LOCK_ACQUIRED);
                
                EditingLock lock = new EditingLock(userId, username, configId, request.getElementId());
                lock.setElementType(request.getElementType());
                event.setData(lock);
                
                logger.debug("Lock acquired successfully for user {} on element {}", username, request.getElementId());
            } else {
                event.setType(CollaborationEventType.LOCK_CONFLICT);
                event.setData("Element is already being edited by another user");
                
                logger.debug("Lock acquisition failed for user {} on element {} - conflict", username, request.getElementId());
            }
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error acquiring editing lock", e);
            return createErrorEvent(userId, username, configId, "Failed to acquire editing lock");
        }
    }

    /**
     * Handle editing lock release requests.
     */
    @MessageMapping("/lock/release")
    @SendTo("/topic/ui-config/{configId}/locks")
    public CollaborationEvent releaseEditingLock(@DestinationVariable UUID configId,
                                                @Payload EditingLockRequest request,
                                                Principal principal,
                                                SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Lock release request from user {} for element {} in config {}", 
                    username, request.getElementId(), configId);
        
        try {
            collaborationService.releaseEditingLock(userId, configId, request.getElementId());
            
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.LOCK_RELEASED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setElementId(request.getElementId());
            event.setTimestamp(LocalDateTime.now());
            
            EditingLock lock = new EditingLock(userId, username, configId, request.getElementId());
            lock.setReleasedAt(LocalDateTime.now());
            event.setData(lock);
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error releasing editing lock", e);
            return createErrorEvent(userId, username, configId, "Failed to release editing lock");
        }
    }

    /**
     * Handle UI configuration update events.
     */
    @MessageMapping("/ui-config/update")
    @SendTo("/topic/ui-config/{configId}/updates")
    public CollaborationEvent handleUIConfigUpdate(@DestinationVariable UUID configId,
                                                  @Payload UIConfigUpdateEvent updateEvent,
                                                  Principal principal,
                                                  SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("UI config update from user {} for config {}", username, configId);
        
        try {
            // Set user information
            updateEvent.setUserId(userId);
            updateEvent.setUsername(username);
            updateEvent.setConfigId(configId);
            updateEvent.setTimestamp(LocalDateTime.now());
            
            // Process the update through the UI configuration service
            // This would typically involve operational transformation
            
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.UI_CONFIG_UPDATED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setData(updateEvent);
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error handling UI config update", e);
            return createErrorEvent(userId, username, configId, "Failed to update UI configuration");
        }
    }

    /**
     * Handle user presence updates.
     */
    @MessageMapping("/presence/update")
    @SendTo("/topic/organization/{orgId}/presence")
    public UserPresence updateUserPresence(@DestinationVariable UUID orgId,
                                         @Payload UserPresence presence,
                                         Principal principal,
                                         SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Presence update from user {} for organization {}", username, orgId);
        
        try {
            // Set user information
            presence.setUserId(userId);
            presence.setUsername(username);
            presence.setOrganizationId(orgId);
            presence.setLastSeen(LocalDateTime.now());
            
            return presence;
            
        } catch (Exception e) {
            logger.error("Error updating user presence", e);
            return null;
        }
    }

    /**
     * Get active users for a UI configuration.
     */
    @MessageMapping("/users/active")
    @SendToUser("/queue/active-users")
    public List<ActiveUser> getActiveUsers(@Payload ActiveUsersRequest request,
                                          Principal principal) {
        
        UUID userId = UUID.fromString(principal.getName());
        
        logger.debug("Active users request from user {} for config {}", userId, request.getConfigId());
        
        try {
            return collaborationService.getActiveUsers(request.getConfigId());
            
        } catch (Exception e) {
            logger.error("Error getting active users", e);
            return List.of();
        }
    }

    /**
     * Handle ping/heartbeat messages to keep connection alive.
     */
    @MessageMapping("/ping")
    @SendToUser("/queue/pong")
    public String handlePing(@Payload String message, Principal principal) {
        return "pong";
    }

    // Helper methods

    private CollaborationEvent createErrorEvent(UUID userId, String username, UUID configId, String errorMessage) {
        CollaborationEvent event = new CollaborationEvent();
        event.setType(CollaborationEventType.ERROR_OCCURRED);
        event.setUserId(userId);
        event.setUsername(username);
        event.setConfigId(configId);
        event.setData(errorMessage);
        event.setTimestamp(LocalDateTime.now());
        return event;
    }

    // Inner classes for request DTOs

    public static class EditingLockRequest {
        private String elementId;
        private String elementType;
        
        // Getters and setters
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public String getElementType() { return elementType; }
        public void setElementType(String elementType) { this.elementType = elementType; }
    }

    public static class UIConfigUpdateEvent {
        private UUID userId;
        private String username;
        private UUID configId;
        private String operation; // "create", "update", "delete", "move"
        private String elementId;
        private Object data;
        private LocalDateTime timestamp;
        
        // Getters and setters
        public UUID getUserId() { return userId; }
        public void setUserId(UUID userId) { this.userId = userId; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public UUID getConfigId() { return configId; }
        public void setConfigId(UUID configId) { this.configId = configId; }
        public String getOperation() { return operation; }
        public void setOperation(String operation) { this.operation = operation; }
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }

    public static class ActiveUsersRequest {
        private UUID configId;
        
        // Getters and setters
        public UUID getConfigId() { return configId; }
        public void setConfigId(UUID configId) { this.configId = configId; }
    }
}
