# Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide covers common issues, diagnostic procedures, and resolution steps for the UI Builder Platform.

## Quick Diagnostic Commands

### System Health Check
```bash
# Overall system status
kubectl get pods -n ui-builder-production
kubectl get services -n ui-builder-production
kubectl get ingress -n ui-builder-production

# Application health endpoints
curl -f https://api.yourdomain.com/actuator/health
curl -f https://api.yourdomain.com/actuator/health/db
curl -f https://api.yourdomain.com/actuator/health/redis
```

### Resource Usage
```bash
# Pod resource usage
kubectl top pods -n ui-builder-production

# Node resource usage
kubectl top nodes

# Persistent volume usage
kubectl get pv
df -h /data
```

## Common Issues and Solutions

### 1. Application Startup Issues

#### Symptoms
- Pods stuck in `Pending` or `CrashLoopBackOff` state
- Application not responding to health checks
- Error messages in logs

#### Diagnostic Steps
```bash
# Check pod status and events
kubectl describe pod <pod-name> -n ui-builder-production

# Check logs
kubectl logs <pod-name> -n ui-builder-production --previous
kubectl logs <pod-name> -n ui-builder-production --follow

# Check resource constraints
kubectl describe node <node-name>
```

#### Common Causes and Solutions

**Insufficient Resources**
```bash
# Check resource requests vs limits
kubectl describe pod <pod-name> -n ui-builder-production | grep -A 5 "Requests\|Limits"

# Solution: Increase resource limits or add more nodes
kubectl patch deployment <deployment-name> -p '{"spec":{"template":{"spec":{"containers":[{"name":"<container-name>","resources":{"limits":{"memory":"2Gi","cpu":"1000m"}}}]}}}}'
```

**Configuration Issues**
```bash
# Check environment variables
kubectl exec -it <pod-name> -n ui-builder-production -- env | grep -E "(DATABASE|REDIS|KAFKA)"

# Check secrets and configmaps
kubectl get secrets -n ui-builder-production
kubectl get configmaps -n ui-builder-production
```

**Image Pull Issues**
```bash
# Check image pull secrets
kubectl get pods <pod-name> -n ui-builder-production -o yaml | grep -A 5 imagePullSecrets

# Solution: Update image pull secrets
kubectl create secret docker-registry regcred \
  --docker-server=<registry-url> \
  --docker-username=<username> \
  --docker-password=<password> \
  -n ui-builder-production
```

### 2. Database Connection Issues

#### Symptoms
- "Connection refused" errors
- Timeout errors when connecting to database
- Application unable to start due to database issues

#### Diagnostic Steps
```bash
# Check database pod status
kubectl get pods -l app=postgres -n ui-builder-production

# Test database connectivity
kubectl exec -it <backend-pod> -n ui-builder-production -- nc -zv postgres 5432

# Check database logs
kubectl logs <postgres-pod> -n ui-builder-production
```

#### Solutions

**Database Pod Not Running**
```bash
# Check database pod events
kubectl describe pod <postgres-pod> -n ui-builder-production

# Check persistent volume
kubectl get pv,pvc -n ui-builder-production

# Restart database if needed
kubectl delete pod <postgres-pod> -n ui-builder-production
```

**Connection Pool Exhaustion**
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity;

-- Check connection limits
SHOW max_connections;

-- Kill long-running queries
SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
WHERE state = 'active' AND query_start < now() - interval '5 minutes';
```

**Database Performance Issues**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check table sizes
SELECT schemaname,tablename,pg_size_pretty(size) as size
FROM (
  SELECT schemaname,tablename,pg_total_relation_size(schemaname||'.'||tablename) as size
  FROM pg_tables
) s
ORDER BY size DESC;
```

### 3. Performance Issues

#### Symptoms
- High response times
- Timeouts
- High CPU or memory usage
- Slow page loads

#### Diagnostic Steps
```bash
# Check application metrics
curl https://api.yourdomain.com/actuator/metrics/http.server.requests

# Check resource usage
kubectl top pods -n ui-builder-production
kubectl top nodes

# Check HPA status
kubectl get hpa -n ui-builder-production
```

#### Solutions

**High CPU Usage**
```bash
# Scale horizontally
kubectl scale deployment <deployment-name> --replicas=5 -n ui-builder-production

# Check for CPU-intensive processes
kubectl exec -it <pod-name> -n ui-builder-production -- top
```

**High Memory Usage**
```bash
# Check memory leaks
kubectl exec -it <pod-name> -n ui-builder-production -- cat /proc/meminfo

# Restart pods if memory leak suspected
kubectl rollout restart deployment/<deployment-name> -n ui-builder-production
```

**Database Performance**
```sql
-- Update table statistics
ANALYZE;

-- Reindex if needed
REINDEX DATABASE uibuilder;

-- Check for missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC;
```

### 4. Network and Connectivity Issues

#### Symptoms
- Services unable to communicate
- External API calls failing
- WebSocket connections dropping

#### Diagnostic Steps
```bash
# Check service endpoints
kubectl get endpoints -n ui-builder-production

# Test internal connectivity
kubectl exec -it <pod-name> -n ui-builder-production -- nslookup <service-name>
kubectl exec -it <pod-name> -n ui-builder-production -- curl -v http://<service-name>:8080/health

# Check network policies
kubectl get networkpolicies -n ui-builder-production
```

#### Solutions

**DNS Resolution Issues**
```bash
# Check CoreDNS
kubectl get pods -n kube-system -l k8s-app=kube-dns

# Test DNS resolution
kubectl exec -it <pod-name> -n ui-builder-production -- nslookup kubernetes.default.svc.cluster.local
```

**Service Discovery Issues**
```bash
# Check service labels and selectors
kubectl get service <service-name> -n ui-builder-production -o yaml
kubectl get pods -l app=<app-label> -n ui-builder-production

# Verify endpoints
kubectl describe endpoints <service-name> -n ui-builder-production
```

### 5. Storage Issues

#### Symptoms
- Pods stuck in `Pending` state due to volume mounting issues
- "No space left on device" errors
- Data persistence issues

#### Diagnostic Steps
```bash
# Check persistent volumes
kubectl get pv,pvc -n ui-builder-production

# Check storage class
kubectl get storageclass

# Check disk usage
kubectl exec -it <pod-name> -n ui-builder-production -- df -h
```

#### Solutions

**Volume Mounting Issues**
```bash
# Check PVC status
kubectl describe pvc <pvc-name> -n ui-builder-production

# Check storage class provisioner
kubectl describe storageclass <storage-class-name>
```

**Disk Space Issues**
```bash
# Clean up old logs
kubectl exec -it <pod-name> -n ui-builder-production -- find /var/log -name "*.log" -mtime +7 -delete

# Expand volume if supported
kubectl patch pvc <pvc-name> -p '{"spec":{"resources":{"requests":{"storage":"200Gi"}}}}'
```

### 6. Security and Authentication Issues

#### Symptoms
- Authentication failures
- Authorization errors
- SSL/TLS certificate issues

#### Diagnostic Steps
```bash
# Check certificate status
kubectl describe certificate <cert-name> -n ui-builder-production

# Test SSL connectivity
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Check JWT token validity
curl -H "Authorization: Bearer <token>" https://api.yourdomain.com/api/v1/users/profile
```

#### Solutions

**Certificate Issues**
```bash
# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager

# Manually trigger certificate renewal
kubectl delete certificate <cert-name> -n ui-builder-production
```

**Authentication Issues**
```bash
# Check JWT secret
kubectl get secret jwt-secret -n ui-builder-production -o yaml

# Verify token generation
kubectl exec -it <backend-pod> -n ui-builder-production -- curl -X POST http://localhost:8080/api/v1/auth/login
```

## Monitoring and Alerting

### Key Metrics to Monitor

```bash
# Application metrics
curl https://api.yourdomain.com/actuator/prometheus | grep -E "(http_requests|jvm_memory|database_connections)"

# Infrastructure metrics
kubectl get --raw /metrics | grep -E "(cpu|memory|disk)"
```

### Log Analysis

```bash
# Centralized logging with ELK stack
kubectl logs -f deployment/backend-deployment -n ui-builder-production | grep ERROR

# Search for specific errors
kubectl logs deployment/backend-deployment -n ui-builder-production --since=1h | grep -i "exception\|error\|failed"
```

## Emergency Procedures

### Service Degradation Response

1. **Immediate Actions**
   ```bash
   # Scale up critical services
   kubectl scale deployment backend-deployment --replicas=10 -n ui-builder-production
   
   # Enable maintenance mode if needed
   kubectl patch configmap frontend-config -p '{"data":{"maintenance_mode":"true"}}' -n ui-builder-production
   ```

2. **Investigation**
   ```bash
   # Collect diagnostic information
   kubectl get events --sort-by=.metadata.creationTimestamp -n ui-builder-production
   kubectl top pods -n ui-builder-production
   kubectl describe nodes
   ```

3. **Communication**
   - Update status page
   - Notify stakeholders
   - Document incident timeline

### Data Recovery Procedures

```bash
# Database recovery from backup
kubectl exec -it postgres-0 -n ui-builder-production -- pg_restore -d uibuilder /backups/latest_backup.sql

# File system recovery
kubectl exec -it <pod-name> -n ui-builder-production -- rsync -av /backups/files/ /app/uploads/
```

## Performance Tuning

### JVM Tuning
```yaml
env:
- name: JAVA_OPTS
  value: "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### Database Tuning
```sql
-- PostgreSQL configuration
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
SELECT pg_reload_conf();
```

### Redis Tuning
```yaml
redis:
  config:
    maxmemory: 1gb
    maxmemory-policy: allkeys-lru
    timeout: 300
```

## Preventive Measures

### Regular Maintenance Tasks

```bash
# Weekly database maintenance
kubectl create job --from=cronjob/database-maintenance manual-maintenance-$(date +%Y%m%d)

# Monthly security updates
kubectl patch deployment backend-deployment -p '{"spec":{"template":{"metadata":{"annotations":{"date":"'$(date +'%s')'"}}}}}'

# Quarterly disaster recovery testing
kubectl apply -f k8s/testing/dr-test.yaml
```

### Monitoring Setup

```yaml
# Prometheus alerts
groups:
- name: ui-builder-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: High error rate detected
      description: "Error rate is {{ $value }} errors per second"
```

## Contact Information

### Escalation Matrix

| Severity | Contact | Response Time |
|----------|---------|---------------|
| Critical | On-call engineer | 15 minutes |
| High | Team lead | 1 hour |
| Medium | Development team | 4 hours |
| Low | Support team | 24 hours |

### Emergency Contacts

- **On-call Engineer**: +1-555-ONCALL
- **Platform Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Infrastructure Team**: <EMAIL>
