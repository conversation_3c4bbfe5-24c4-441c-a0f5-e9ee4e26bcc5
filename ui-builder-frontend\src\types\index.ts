// Re-export all types for easy importing
export * from './components';
export * from './ui-configuration';
export * from './theme';
export * from './template';
export * from './user';
export * from './collaboration';
export * from './form';
export * from './api';

// Core UI Builder Types
export interface UIComponent {
  id: string;
  type: ComponentType;
  name: string;
  displayName: string;
  properties: ComponentProperties;
  children?: UIComponent[];
  parentId?: string;
  position: Position;
  size: Size;
  styles: ComponentStyles;
  constraints?: ComponentConstraints;
  metadata?: ComponentMetadata;
}

export interface ComponentProperties {
  [key: string]: any;
  text?: string;
  placeholder?: string;
  value?: any;
  disabled?: boolean;
  required?: boolean;
  validation?: ValidationRule[];
  events?: ComponentEvent[];
}

export interface Position {
  x: number;
  y: number;
  z?: number;
}

export interface Size {
  width: number | 'auto' | string;
  height: number | 'auto' | string;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface ComponentStyles {
  backgroundColor?: string;
  color?: string;
  fontSize?: number | string;
  fontFamily?: string;
  fontWeight?: string | number;
  padding?: Spacing;
  margin?: Spacing;
  border?: BorderStyle;
  borderRadius?: number | string;
  boxShadow?: string;
  opacity?: number;
  transform?: string;
  transition?: string;
  [key: string]: any;
}

export interface Spacing {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
  all?: number;
}

export interface BorderStyle {
  width?: number;
  style?: 'solid' | 'dashed' | 'dotted' | 'none';
  color?: string;
}

export interface ComponentConstraints {
  resizable?: boolean;
  draggable?: boolean;
  deletable?: boolean;
  duplicatable?: boolean;
  lockAspectRatio?: boolean;
  snapToGrid?: boolean;
}

export interface ComponentMetadata {
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  version: number;
  tags?: string[];
  description?: string;
}

export enum ComponentType {
  // Layout Components
  CONTAINER = 'container',
  GRID = 'grid',
  FLEX = 'flex',
  STACK = 'stack',
  DIVIDER = 'divider',
  SPACER = 'spacer',
  
  // Text Components
  TEXT = 'text',
  HEADING = 'heading',
  PARAGRAPH = 'paragraph',
  LINK = 'link',
  
  // Form Components
  INPUT = 'input',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SWITCH = 'switch',
  SLIDER = 'slider',
  DATE_PICKER = 'datePicker',
  TIME_PICKER = 'timePicker',
  FILE_UPLOAD = 'fileUpload',
  
  // Interactive Components
  BUTTON = 'button',
  ICON_BUTTON = 'iconButton',
  DROPDOWN = 'dropdown',
  MENU = 'menu',
  TABS = 'tabs',
  ACCORDION = 'accordion',
  MODAL = 'modal',
  DRAWER = 'drawer',
  TOOLTIP = 'tooltip',
  POPOVER = 'popover',
  
  // Display Components
  IMAGE = 'image',
  ICON = 'icon',
  AVATAR = 'avatar',
  BADGE = 'badge',
  TAG = 'tag',
  PROGRESS = 'progress',
  SPINNER = 'spinner',
  SKELETON = 'skeleton',
  
  // Data Components
  TABLE = 'table',
  LIST = 'list',
  CARD = 'card',
  TIMELINE = 'timeline',
  TREE = 'tree',
  
  // Chart Components
  LINE_CHART = 'lineChart',
  BAR_CHART = 'barChart',
  PIE_CHART = 'pieChart',
  AREA_CHART = 'areaChart',
  
  // Media Components
  VIDEO = 'video',
  AUDIO = 'audio',
  IFRAME = 'iframe',
  
  // Custom Components
  CUSTOM = 'custom',
}

export interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'email' | 'url' | 'number' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean | Promise<boolean>;
}

export interface ComponentEvent {
  type: EventType;
  handler: string;
  parameters?: { [key: string]: any };
}

export enum EventType {
  CLICK = 'click',
  DOUBLE_CLICK = 'doubleClick',
  MOUSE_ENTER = 'mouseEnter',
  MOUSE_LEAVE = 'mouseLeave',
  FOCUS = 'focus',
  BLUR = 'blur',
  CHANGE = 'change',
  INPUT = 'input',
  SUBMIT = 'submit',
  RESET = 'reset',
  SCROLL = 'scroll',
  RESIZE = 'resize',
  LOAD = 'load',
  ERROR = 'error',
}

// UI Configuration Types
export interface UIConfiguration {
  id: string;
  name: string;
  description?: string;
  version: string;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  isPublished: boolean;
  components: UIComponent[];
  theme: Theme;
  layout: LayoutConfiguration;
  settings: ConfigurationSettings;
  metadata?: ConfigurationMetadata;
}

export interface LayoutConfiguration {
  type: 'fixed' | 'fluid' | 'responsive';
  breakpoints?: Breakpoint[];
  gridSystem?: GridSystem;
  spacing?: number;
  maxWidth?: number;
}

export interface Breakpoint {
  name: string;
  minWidth: number;
  maxWidth?: number;
  columns?: number;
}

export interface GridSystem {
  columns: number;
  gutterWidth: number;
  marginWidth: number;
}

export interface ConfigurationSettings {
  enableGrid: boolean;
  snapToGrid: boolean;
  gridSize: number;
  showRulers: boolean;
  showGuides: boolean;
  enableCollaboration: boolean;
  autoSave: boolean;
  autoSaveInterval: number;
}

export interface ConfigurationMetadata {
  tags?: string[];
  category?: string;
  thumbnail?: string;
  screenshots?: string[];
  documentation?: string;
  changelog?: ChangelogEntry[];
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: string[];
  author: string;
}

// Theme Types
export interface Theme {
  id: string;
  name: string;
  description?: string;
  colors: ColorPalette;
  typography: Typography;
  spacing: SpacingScale;
  shadows: ShadowScale;
  borders: BorderScale;
  animations: AnimationSettings;
  customProperties?: { [key: string]: any };
}

export interface ColorPalette {
  primary: ColorScale;
  secondary: ColorScale;
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;
  neutral: ColorScale;
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  text: {
    primary: string;
    secondary: string;
    disabled: string;
    inverse: string;
  };
}

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface Typography {
  fontFamilies: {
    primary: string;
    secondary: string;
    monospace: string;
  };
  fontSizes: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
  };
  fontWeights: {
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
  lineHeights: {
    tight: number;
    normal: number;
    relaxed: number;
    loose: number;
  };
  letterSpacing: {
    tight: string;
    normal: string;
    wide: string;
  };
}

export interface SpacingScale {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
}

export interface ShadowScale {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
  none: string;
}

export interface BorderScale {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface AnimationSettings {
  durations: {
    fast: string;
    normal: string;
    slow: string;
  };
  easings: {
    linear: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
  };
}

// Template Types
export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  tags: string[];
  thumbnail: string;
  screenshots: string[];
  configuration: UIConfiguration;
  isPublic: boolean;
  isPremium: boolean;
  price?: number;
  rating: number;
  downloads: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export enum TemplateCategory {
  DASHBOARD = 'dashboard',
  LANDING_PAGE = 'landingPage',
  FORM = 'form',
  E_COMMERCE = 'eCommerce',
  BLOG = 'blog',
  PORTFOLIO = 'portfolio',
  ADMIN = 'admin',
  MOBILE = 'mobile',
  EMAIL = 'email',
  SOCIAL = 'social',
  EDUCATION = 'education',
  HEALTHCARE = 'healthcare',
  FINANCE = 'finance',
  TRAVEL = 'travel',
  FOOD = 'food',
  REAL_ESTATE = 'realEstate',
  OTHER = 'other',
}

// User and Organization Types
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  organizationId: string;
  permissions: Permission[];
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export enum UserRole {
  SUPER_ADMIN = 'superAdmin',
  ADMIN = 'admin',
  DESIGNER = 'designer',
  DEVELOPER = 'developer',
  VIEWER = 'viewer',
}

export interface Permission {
  resource: string;
  actions: string[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  editor: EditorSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  collaboration: boolean;
  updates: boolean;
  marketing: boolean;
}

export interface EditorSettings {
  autoSave: boolean;
  autoSaveInterval: number;
  showGrid: boolean;
  snapToGrid: boolean;
  showRulers: boolean;
  showGuides: boolean;
  keyboardShortcuts: { [key: string]: string };
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  plan: SubscriptionPlan;
  settings: OrganizationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPlan {
  name: string;
  tier: 'free' | 'pro' | 'enterprise';
  features: string[];
  limits: PlanLimits;
  price: number;
  billingCycle: 'monthly' | 'yearly';
}

export interface PlanLimits {
  maxProjects: number;
  maxUsers: number;
  maxStorage: number; // in MB
  maxTemplates: number;
  collaborationEnabled: boolean;
  advancedFeaturesEnabled: boolean;
}

export interface OrganizationSettings {
  allowPublicTemplates: boolean;
  requireApprovalForPublishing: boolean;
  defaultTheme: string;
  brandColors: ColorPalette;
  customDomain?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ApiError[];
  meta?: ResponseMeta;
}

export interface ApiError {
  field?: string;
  code: string;
  message: string;
}

export interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: ResponseMeta;
}

// Collaboration Types
export interface CollaborationSession {
  id: string;
  configurationId: string;
  users: CollaborationUser[];
  createdAt: string;
  updatedAt: string;
}

export interface CollaborationUser {
  id: string;
  username: string;
  avatar?: string;
  color: string;
  cursor?: CursorPosition;
  selection?: ComponentSelection;
  isActive: boolean;
  lastSeen: string;
}

export interface CursorPosition {
  x: number;
  y: number;
  elementId?: string;
}

export interface ComponentSelection {
  componentIds: string[];
  action?: 'select' | 'edit' | 'resize' | 'move';
}

export interface CollaborationEvent {
  type: CollaborationEventType;
  userId: string;
  data: any;
  timestamp: string;
}

export enum CollaborationEventType {
  USER_JOINED = 'userJoined',
  USER_LEFT = 'userLeft',
  CURSOR_MOVED = 'cursorMoved',
  COMPONENT_SELECTED = 'componentSelected',
  COMPONENT_UPDATED = 'componentUpdated',
  COMPONENT_ADDED = 'componentAdded',
  COMPONENT_DELETED = 'componentDeleted',
  COMPONENT_MOVED = 'componentMoved',
  COMMENT_ADDED = 'commentAdded',
  COMMENT_UPDATED = 'commentUpdated',
  COMMENT_DELETED = 'commentDeleted',
}

// Form Builder Types
export interface FormField {
  id: string;
  type: FormFieldType;
  name: string;
  label: string;
  placeholder?: string;
  defaultValue?: any;
  required: boolean;
  validation: ValidationRule[];
  options?: FormFieldOption[];
  properties: FormFieldProperties;
  styles: ComponentStyles;
  position: Position;
  size: Size;
}

export enum FormFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTI_SELECT = 'multiSelect',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  SWITCH = 'switch',
  SLIDER = 'slider',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  FILE = 'file',
  IMAGE = 'image',
  SIGNATURE = 'signature',
  RATING = 'rating',
  COLOR = 'color',
  URL = 'url',
  PHONE = 'phone',
  ADDRESS = 'address',
}

export interface FormFieldOption {
  label: string;
  value: any;
  disabled?: boolean;
  icon?: string;
}

export interface FormFieldProperties {
  [key: string]: any;
  min?: number;
  max?: number;
  step?: number;
  multiple?: boolean;
  accept?: string;
  pattern?: string;
  autocomplete?: string;
  readonly?: boolean;
}

export interface FormConfiguration {
  id: string;
  name: string;
  description?: string;
  fields: FormField[];
  layout: FormLayout;
  validation: FormValidation;
  submission: FormSubmission;
  styling: FormStyling;
  settings: FormSettings;
}

export interface FormLayout {
  type: 'single-column' | 'multi-column' | 'grid' | 'tabs' | 'steps';
  columns?: number;
  spacing?: number;
  sections?: FormSection[];
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: string[];
  collapsible?: boolean;
  collapsed?: boolean;
}

export interface FormValidation {
  validateOnChange: boolean;
  validateOnBlur: boolean;
  showErrorsInline: boolean;
  showErrorSummary: boolean;
  customValidators?: CustomValidator[];
}

export interface CustomValidator {
  name: string;
  function: string;
  message: string;
  fields: string[];
}

export interface FormSubmission {
  method: 'POST' | 'PUT' | 'PATCH';
  action: string;
  successMessage: string;
  errorMessage: string;
  redirectUrl?: string;
  emailNotification?: EmailNotification;
  webhooks?: Webhook[];
}

export interface EmailNotification {
  enabled: boolean;
  to: string[];
  subject: string;
  template: string;
  attachments?: boolean;
}

export interface Webhook {
  url: string;
  method: 'POST' | 'PUT' | 'PATCH';
  headers?: { [key: string]: string };
  retries: number;
  timeout: number;
}

export interface FormStyling {
  theme: string;
  customCss?: string;
  fieldSpacing: number;
  labelPosition: 'top' | 'left' | 'inside';
  buttonAlignment: 'left' | 'center' | 'right';
}

export interface FormSettings {
  allowDrafts: boolean;
  showProgress: boolean;
  enableAutosave: boolean;
  autosaveInterval: number;
  captcha: CaptchaSettings;
  fileUpload: FileUploadSettings;
}

export interface CaptchaSettings {
  enabled: boolean;
  provider: 'recaptcha' | 'hcaptcha' | 'turnstile';
  siteKey: string;
}

export interface FileUploadSettings {
  maxFileSize: number; // in MB
  allowedTypes: string[];
  maxFiles: number;
  storage: 'local' | 's3' | 'cloudinary';
  storageConfig?: { [key: string]: any };
}
