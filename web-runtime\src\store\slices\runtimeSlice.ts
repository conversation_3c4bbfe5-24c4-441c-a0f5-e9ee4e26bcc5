import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface RuntimeState {
  // Application state
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Current configuration
  currentConfigId: string | null;
  configData: any;
  configVersion: number;
  
  // Runtime settings
  mode: 'development' | 'production' | 'preview';
  debugMode: boolean;
  performanceMode: 'normal' | 'optimized' | 'minimal';
  
  // Device and viewport
  viewport: {
    width: number;
    height: number;
    deviceType: 'mobile' | 'tablet' | 'desktop';
    orientation: 'portrait' | 'landscape';
  };
  
  // Component state
  componentStates: Record<string, any>;
  componentErrors: Record<string, string>;
  
  // Performance metrics
  performance: {
    loadTime: number;
    renderTime: number;
    memoryUsage: number;
    componentCount: number;
    lastUpdate: string;
  };
  
  // Feature flags
  features: {
    enableAnalytics: boolean;
    enableErrorReporting: boolean;
    enablePerformanceMonitoring: boolean;
    enableHotReload: boolean;
    enableDevTools: boolean;
  };
  
  // Cache settings
  cache: {
    enabled: boolean;
    maxSize: number;
    ttl: number;
    strategy: 'memory' | 'localStorage' | 'sessionStorage';
  };
}

const initialState: RuntimeState = {
  isInitialized: false,
  isLoading: false,
  error: null,
  
  currentConfigId: null,
  configData: null,
  configVersion: 0,
  
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  debugMode: process.env.NODE_ENV === 'development',
  performanceMode: 'normal',
  
  viewport: {
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080,
    deviceType: 'desktop',
    orientation: 'landscape',
  },
  
  componentStates: {},
  componentErrors: {},
  
  performance: {
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    componentCount: 0,
    lastUpdate: new Date().toISOString(),
  },
  
  features: {
    enableAnalytics: true,
    enableErrorReporting: true,
    enablePerformanceMonitoring: true,
    enableHotReload: process.env.NODE_ENV === 'development',
    enableDevTools: process.env.NODE_ENV === 'development',
  },
  
  cache: {
    enabled: true,
    maxSize: 50, // MB
    ttl: 3600000, // 1 hour
    strategy: 'memory',
  },
};

const runtimeSlice = createSlice({
  name: 'runtime',
  initialState,
  reducers: {
    // Application lifecycle
    initializeRuntime: (state) => {
      state.isInitialized = true;
      state.isLoading = false;
      state.error = null;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    
    // Configuration management
    setCurrentConfig: (state, action: PayloadAction<{ id: string; data: any; version?: number }>) => {
      state.currentConfigId = action.payload.id;
      state.configData = action.payload.data;
      state.configVersion = action.payload.version || state.configVersion + 1;
    },
    
    updateConfigData: (state, action: PayloadAction<any>) => {
      state.configData = action.payload;
      state.configVersion += 1;
    },
    
    clearConfig: (state) => {
      state.currentConfigId = null;
      state.configData = null;
      state.configVersion = 0;
      state.componentStates = {};
      state.componentErrors = {};
    },
    
    // Runtime settings
    setMode: (state, action: PayloadAction<RuntimeState['mode']>) => {
      state.mode = action.payload;
    },
    
    setDebugMode: (state, action: PayloadAction<boolean>) => {
      state.debugMode = action.payload;
    },
    
    setPerformanceMode: (state, action: PayloadAction<RuntimeState['performanceMode']>) => {
      state.performanceMode = action.payload;
    },
    
    // Viewport management
    updateViewport: (state, action: PayloadAction<Partial<RuntimeState['viewport']>>) => {
      state.viewport = { ...state.viewport, ...action.payload };
    },
    
    setDeviceType: (state, action: PayloadAction<RuntimeState['viewport']['deviceType']>) => {
      state.viewport.deviceType = action.payload;
    },
    
    setOrientation: (state, action: PayloadAction<RuntimeState['viewport']['orientation']>) => {
      state.viewport.orientation = action.payload;
    },
    
    // Component state management
    setComponentState: (state, action: PayloadAction<{ componentId: string; state: any }>) => {
      state.componentStates[action.payload.componentId] = action.payload.state;
    },
    
    updateComponentState: (state, action: PayloadAction<{ componentId: string; updates: any }>) => {
      const currentState = state.componentStates[action.payload.componentId] || {};
      state.componentStates[action.payload.componentId] = {
        ...currentState,
        ...action.payload.updates,
      };
    },
    
    removeComponentState: (state, action: PayloadAction<string>) => {
      delete state.componentStates[action.payload];
    },
    
    clearComponentStates: (state) => {
      state.componentStates = {};
    },
    
    // Error management
    setComponentError: (state, action: PayloadAction<{ componentId: string; error: string }>) => {
      state.componentErrors[action.payload.componentId] = action.payload.error;
    },
    
    clearComponentError: (state, action: PayloadAction<string>) => {
      delete state.componentErrors[action.payload];
    },
    
    clearAllErrors: (state) => {
      state.componentErrors = {};
      state.error = null;
    },
    
    // Performance tracking
    updatePerformanceMetrics: (state, action: PayloadAction<Partial<RuntimeState['performance']>>) => {
      state.performance = {
        ...state.performance,
        ...action.payload,
        lastUpdate: new Date().toISOString(),
      };
    },
    
    incrementComponentCount: (state) => {
      state.performance.componentCount += 1;
    },
    
    decrementComponentCount: (state) => {
      state.performance.componentCount = Math.max(0, state.performance.componentCount - 1);
    },
    
    resetPerformanceMetrics: (state) => {
      state.performance = {
        loadTime: 0,
        renderTime: 0,
        memoryUsage: 0,
        componentCount: 0,
        lastUpdate: new Date().toISOString(),
      };
    },
    
    // Feature flags
    toggleFeature: (state, action: PayloadAction<keyof RuntimeState['features']>) => {
      state.features[action.payload] = !state.features[action.payload];
    },
    
    setFeature: (state, action: PayloadAction<{ feature: keyof RuntimeState['features']; enabled: boolean }>) => {
      state.features[action.payload.feature] = action.payload.enabled;
    },
    
    updateFeatures: (state, action: PayloadAction<Partial<RuntimeState['features']>>) => {
      state.features = { ...state.features, ...action.payload };
    },
    
    // Cache management
    updateCacheSettings: (state, action: PayloadAction<Partial<RuntimeState['cache']>>) => {
      state.cache = { ...state.cache, ...action.payload };
    },
    
    enableCache: (state) => {
      state.cache.enabled = true;
    },
    
    disableCache: (state) => {
      state.cache.enabled = false;
    },
    
    // Bulk updates
    updateRuntimeSettings: (state, action: PayloadAction<{
      mode?: RuntimeState['mode'];
      debugMode?: boolean;
      performanceMode?: RuntimeState['performanceMode'];
      features?: Partial<RuntimeState['features']>;
      cache?: Partial<RuntimeState['cache']>;
    }>) => {
      const { mode, debugMode, performanceMode, features, cache } = action.payload;
      
      if (mode !== undefined) state.mode = mode;
      if (debugMode !== undefined) state.debugMode = debugMode;
      if (performanceMode !== undefined) state.performanceMode = performanceMode;
      if (features) state.features = { ...state.features, ...features };
      if (cache) state.cache = { ...state.cache, ...cache };
    },
    
    // Reset state
    resetRuntime: (state) => {
      return {
        ...initialState,
        viewport: state.viewport, // Keep viewport settings
      };
    },
  },
});

export const {
  initializeRuntime,
  setLoading,
  setError,
  setCurrentConfig,
  updateConfigData,
  clearConfig,
  setMode,
  setDebugMode,
  setPerformanceMode,
  updateViewport,
  setDeviceType,
  setOrientation,
  setComponentState,
  updateComponentState,
  removeComponentState,
  clearComponentStates,
  setComponentError,
  clearComponentError,
  clearAllErrors,
  updatePerformanceMetrics,
  incrementComponentCount,
  decrementComponentCount,
  resetPerformanceMetrics,
  toggleFeature,
  setFeature,
  updateFeatures,
  updateCacheSettings,
  enableCache,
  disableCache,
  updateRuntimeSettings,
  resetRuntime,
} = runtimeSlice.actions;

export default runtimeSlice.reducer;
