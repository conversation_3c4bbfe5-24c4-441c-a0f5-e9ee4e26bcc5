package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Component entity representing reusable UI components.
 * Components can be nested to create complex UI structures.
 */
@Entity
@Table(name = "components", indexes = {
    @Index(name = "idx_component_name", columnList = "name"),
    @Index(name = "idx_component_type", columnList = "component_type"),
    @Index(name = "idx_component_category", columnList = "category"),
    @Index(name = "idx_component_ui_config", columnList = "ui_configuration_id"),
    @Index(name = "idx_component_parent", columnList = "parent_id"),
    @Index(name = "idx_component_order", columnList = "sort_order")
})
public class Component extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @NotBlank
    @Size(max = 50)
    @Column(name = "component_type", nullable = false, length = 50)
    private String componentType;

    @Size(max = 50)
    @Column(name = "category", length = 50)
    private String category;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private Map<String, Object> properties = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "style_properties", columnDefinition = "jsonb")
    private Map<String, Object> styleProperties = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_handlers", columnDefinition = "jsonb")
    private Map<String, Object> eventHandlers = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "jsonb")
    private Map<String, Object> validationRules = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "responsive_settings", columnDefinition = "jsonb")
    private Map<String, Object> responsiveSettings = new HashMap<>();

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible = true;

    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;

    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = false;

    @Column(name = "is_reusable", nullable = false)
    private Boolean isReusable = false;

    @Column(name = "css_classes")
    private String cssClasses;

    @Column(name = "custom_css", columnDefinition = "TEXT")
    private String customCss;

    @Column(name = "data_source")
    private String dataSource;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_binding", columnDefinition = "jsonb")
    private Map<String, Object> dataBinding = new HashMap<>();

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ui_configuration_id")
    @JsonIgnore
    private UIConfiguration uiConfiguration;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnore
    private Component parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private Set<Component> children = new HashSet<>();

    // Constructors
    public Component() {}

    public Component(String name, String componentType, UIConfiguration uiConfiguration) {
        this.name = name;
        this.componentType = componentType;
        this.uiConfiguration = uiConfiguration;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public Map<String, Object> getStyleProperties() {
        return styleProperties;
    }

    public void setStyleProperties(Map<String, Object> styleProperties) {
        this.styleProperties = styleProperties;
    }

    public Map<String, Object> getEventHandlers() {
        return eventHandlers;
    }

    public void setEventHandlers(Map<String, Object> eventHandlers) {
        this.eventHandlers = eventHandlers;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Map<String, Object> getResponsiveSettings() {
        return responsiveSettings;
    }

    public void setResponsiveSettings(Map<String, Object> responsiveSettings) {
        this.responsiveSettings = responsiveSettings;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Boolean getIsReusable() {
        return isReusable;
    }

    public void setIsReusable(Boolean isReusable) {
        this.isReusable = isReusable;
    }

    public String getCssClasses() {
        return cssClasses;
    }

    public void setCssClasses(String cssClasses) {
        this.cssClasses = cssClasses;
    }

    public String getCustomCss() {
        return customCss;
    }

    public void setCustomCss(String customCss) {
        this.customCss = customCss;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Map<String, Object> getDataBinding() {
        return dataBinding;
    }

    public void setDataBinding(Map<String, Object> dataBinding) {
        this.dataBinding = dataBinding;
    }

    public UIConfiguration getUiConfiguration() {
        return uiConfiguration;
    }

    public void setUiConfiguration(UIConfiguration uiConfiguration) {
        this.uiConfiguration = uiConfiguration;
    }

    public Component getParent() {
        return parent;
    }

    public void setParent(Component parent) {
        this.parent = parent;
    }

    public Set<Component> getChildren() {
        return children;
    }

    public void setChildren(Set<Component> children) {
        this.children = children;
    }

    // Utility methods
    public void addChild(Component child) {
        child.setParent(this);
        this.children.add(child);
    }

    public void removeChild(Component child) {
        child.setParent(null);
        this.children.remove(child);
    }

    public boolean hasChildren() {
        return !children.isEmpty();
    }

    public int getDepth() {
        int depth = 0;
        Component current = this.parent;
        while (current != null) {
            depth++;
            current = current.getParent();
        }
        return depth;
    }

    public void addProperty(String key, Object value) {
        this.properties.put(key, value);
    }

    public Object getProperty(String key) {
        return this.properties.get(key);
    }

    public void addStyleProperty(String key, Object value) {
        this.styleProperties.put(key, value);
    }

    public Object getStyleProperty(String key) {
        return this.styleProperties.get(key);
    }

    // Component type constants
    public static final class Types {
        public static final String TEXT = "TEXT";
        public static final String INPUT = "INPUT";
        public static final String BUTTON = "BUTTON";
        public static final String IMAGE = "IMAGE";
        public static final String CONTAINER = "CONTAINER";
        public static final String GRID = "GRID";
        public static final String FORM = "FORM";
        public static final String TABLE = "TABLE";
        public static final String LIST = "LIST";
        public static final String CHART = "CHART";
        public static final String CARD = "CARD";
        public static final String MODAL = "MODAL";
        public static final String TAB = "TAB";
        public static final String ACCORDION = "ACCORDION";
        public static final String DROPDOWN = "DROPDOWN";
        public static final String CHECKBOX = "CHECKBOX";
        public static final String RADIO = "RADIO";
        public static final String TEXTAREA = "TEXTAREA";
        public static final String SELECT = "SELECT";
        public static final String DATE_PICKER = "DATE_PICKER";
        public static final String FILE_UPLOAD = "FILE_UPLOAD";
    }

    // Component categories
    public static final class Categories {
        public static final String LAYOUT = "LAYOUT";
        public static final String INPUT = "INPUT";
        public static final String DISPLAY = "DISPLAY";
        public static final String NAVIGATION = "NAVIGATION";
        public static final String FEEDBACK = "FEEDBACK";
        public static final String DATA = "DATA";
        public static final String MEDIA = "MEDIA";
        public static final String CUSTOM = "CUSTOM";
    }
}
