import 'package:flutter/material.dart';
import '../models/ui_metadata.dart';
import '../utils/logger.dart';

/// Base class for all dynamic widgets
abstract class DynamicWidget {
  final WidgetConfiguration config;

  const DynamicWidget(this.config);

  /// Build the Flutter widget
  Widget build();

  /// Whether this widget can be cached
  bool get isCacheable => true;

  /// Get a property value with type safety
  T? getProp<T>(String key, [T? defaultValue]) {
    try {
      final value = config.props[key];
      if (value == null) return defaultValue;
      
      if (value is T) return value;
      
      // Try to convert common types
      if (T == String) return value.toString() as T?;
      if (T == int && value is num) return value.toInt() as T?;
      if (T == double && value is num) return value.toDouble() as T?;
      if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true') as T?;
        }
        return (value == true) as T?;
      }
      
      return defaultValue;
    } catch (error) {
      AppLogger.warning('Failed to get prop $key: $error');
      return defaultValue;
    }
  }

  /// Get required property with validation
  T getRequiredProp<T>(String key) {
    final value = getProp<T>(key);
    if (value == null) {
      throw ArgumentError('Required property "$key" is missing for widget ${config.type}');
    }
    return value;
  }

  /// Get children widgets
  List<WidgetConfiguration> get children => config.children ?? [];

  /// Check if widget has children
  bool get hasChildren => children.isNotEmpty;

  /// Get widget ID
  String get id => config.id;

  /// Get widget type
  String get type => config.type;

  /// Get position configuration
  PositionConfiguration? get position => config.position;

  /// Get size configuration
  SizeConfiguration? get size => config.size;

  /// Get style configuration
  StyleConfiguration? get style => config.style;

  /// Build EdgeInsets from configuration
  EdgeInsets buildEdgeInsets(dynamic value, [EdgeInsets? defaultValue]) {
    if (value == null) return defaultValue ?? EdgeInsets.zero;
    
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    }
    
    if (value is Map<String, dynamic>) {
      return EdgeInsets.only(
        top: (value['top'] as num?)?.toDouble() ?? 0,
        right: (value['right'] as num?)?.toDouble() ?? 0,
        bottom: (value['bottom'] as num?)?.toDouble() ?? 0,
        left: (value['left'] as num?)?.toDouble() ?? 0,
      );
    }
    
    if (value is List && value.length >= 4) {
      return EdgeInsets.fromLTRB(
        (value[3] as num).toDouble(), // left
        (value[0] as num).toDouble(), // top
        (value[1] as num).toDouble(), // right
        (value[2] as num).toDouble(), // bottom
      );
    }
    
    return defaultValue ?? EdgeInsets.zero;
  }

  /// Build BorderRadius from configuration
  BorderRadius buildBorderRadius(dynamic value, [BorderRadius? defaultValue]) {
    if (value == null) return defaultValue ?? BorderRadius.zero;
    
    if (value is num) {
      return BorderRadius.circular(value.toDouble());
    }
    
    if (value is Map<String, dynamic>) {
      return BorderRadius.only(
        topLeft: Radius.circular((value['topLeft'] as num?)?.toDouble() ?? 0),
        topRight: Radius.circular((value['topRight'] as num?)?.toDouble() ?? 0),
        bottomLeft: Radius.circular((value['bottomLeft'] as num?)?.toDouble() ?? 0),
        bottomRight: Radius.circular((value['bottomRight'] as num?)?.toDouble() ?? 0),
      );
    }
    
    return defaultValue ?? BorderRadius.zero;
  }

  /// Build Color from configuration
  Color buildColor(dynamic value, [Color? defaultValue]) {
    if (value == null) return defaultValue ?? Colors.transparent;
    
    if (value is int) {
      return Color(value);
    }
    
    if (value is String) {
      // Handle hex colors
      if (value.startsWith('#')) {
        final hex = value.substring(1);
        if (hex.length == 6) {
          return Color(int.parse('FF$hex', radix: 16));
        } else if (hex.length == 8) {
          return Color(int.parse(hex, radix: 16));
        }
      }
      
      // Handle named colors
      switch (value.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'cyan': return Colors.cyan;
        case 'teal': return Colors.teal;
        case 'indigo': return Colors.indigo;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'grey': case 'gray': return Colors.grey;
        case 'transparent': return Colors.transparent;
      }
    }
    
    return defaultValue ?? Colors.transparent;
  }

  /// Build TextStyle from configuration
  TextStyle buildTextStyle(dynamic value, [TextStyle? defaultValue]) {
    if (value == null) return defaultValue ?? const TextStyle();
    
    if (value is! Map<String, dynamic>) {
      return defaultValue ?? const TextStyle();
    }
    
    return TextStyle(
      fontSize: (value['fontSize'] as num?)?.toDouble(),
      fontWeight: _buildFontWeight(value['fontWeight']),
      fontStyle: _buildFontStyle(value['fontStyle']),
      color: buildColor(value['color']),
      letterSpacing: (value['letterSpacing'] as num?)?.toDouble(),
      wordSpacing: (value['wordSpacing'] as num?)?.toDouble(),
      height: (value['lineHeight'] as num?)?.toDouble(),
      decoration: _buildTextDecoration(value['decoration']),
      decorationColor: buildColor(value['decorationColor']),
      decorationStyle: _buildTextDecorationStyle(value['decorationStyle']),
      fontFamily: value['fontFamily'] as String?,
    );
  }

  /// Build Alignment from configuration
  Alignment buildAlignment(dynamic value, [Alignment? defaultValue]) {
    if (value == null) return defaultValue ?? Alignment.center;
    
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'topleft': return Alignment.topLeft;
        case 'topcenter': return Alignment.topCenter;
        case 'topright': return Alignment.topRight;
        case 'centerleft': return Alignment.centerLeft;
        case 'center': return Alignment.center;
        case 'centerright': return Alignment.centerRight;
        case 'bottomleft': return Alignment.bottomLeft;
        case 'bottomcenter': return Alignment.bottomCenter;
        case 'bottomright': return Alignment.bottomRight;
      }
    }
    
    if (value is Map<String, dynamic>) {
      final x = (value['x'] as num?)?.toDouble() ?? 0.0;
      final y = (value['y'] as num?)?.toDouble() ?? 0.0;
      return Alignment(x, y);
    }
    
    return defaultValue ?? Alignment.center;
  }

  /// Build MainAxisAlignment from configuration
  MainAxisAlignment buildMainAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'start': return MainAxisAlignment.start;
        case 'end': return MainAxisAlignment.end;
        case 'center': return MainAxisAlignment.center;
        case 'spacebetween': return MainAxisAlignment.spaceBetween;
        case 'spacearound': return MainAxisAlignment.spaceAround;
        case 'spaceevenly': return MainAxisAlignment.spaceEvenly;
      }
    }
    return MainAxisAlignment.start;
  }

  /// Build CrossAxisAlignment from configuration
  CrossAxisAlignment buildCrossAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'start': return CrossAxisAlignment.start;
        case 'end': return CrossAxisAlignment.end;
        case 'center': return CrossAxisAlignment.center;
        case 'stretch': return CrossAxisAlignment.stretch;
        case 'baseline': return CrossAxisAlignment.baseline;
      }
    }
    return CrossAxisAlignment.center;
  }

  /// Build MainAxisSize from configuration
  MainAxisSize buildMainAxisSize(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'min': return MainAxisSize.min;
        case 'max': return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  /// Helper methods for text styling
  FontWeight? _buildFontWeight(dynamic value) {
    if (value is int) {
      switch (value) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
      }
    }
    
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin': return FontWeight.w100;
        case 'extralight': return FontWeight.w200;
        case 'light': return FontWeight.w300;
        case 'normal': return FontWeight.w400;
        case 'medium': return FontWeight.w500;
        case 'semibold': return FontWeight.w600;
        case 'bold': return FontWeight.w700;
        case 'extrabold': return FontWeight.w800;
        case 'black': return FontWeight.w900;
      }
    }
    
    return null;
  }

  FontStyle? _buildFontStyle(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'normal': return FontStyle.normal;
        case 'italic': return FontStyle.italic;
      }
    }
    return null;
  }

  TextDecoration? _buildTextDecoration(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'none': return TextDecoration.none;
        case 'underline': return TextDecoration.underline;
        case 'overline': return TextDecoration.overline;
        case 'linethrough': return TextDecoration.lineThrough;
      }
    }
    return null;
  }

  TextDecorationStyle? _buildTextDecorationStyle(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'solid': return TextDecorationStyle.solid;
        case 'double': return TextDecorationStyle.double;
        case 'dotted': return TextDecorationStyle.dotted;
        case 'dashed': return TextDecorationStyle.dashed;
        case 'wavy': return TextDecorationStyle.wavy;
      }
    }
    return null;
  }
}
