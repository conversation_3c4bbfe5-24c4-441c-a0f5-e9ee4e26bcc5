package com.uiplatform.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * Custom Permission Evaluator for fine-grained access control.
 */
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    private static final Logger logger = LoggerFactory.getLogger(CustomPermissionEvaluator.class);

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String permissionString = permission.toString();
        logger.debug("Checking permission: {} for user: {}", permissionString, authentication.getName());

        // Check if user has the specific permission
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> authority.equals(permissionString));
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String permissionString = buildPermissionString(targetType, permission.toString());
        logger.debug("Checking permission: {} for user: {} on target: {}", 
                    permissionString, authentication.getName(), targetId);

        // Check if user has the specific permission
        boolean hasPermission = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> 
                    authority.equals(permissionString) || 
                    authority.equals("ROLE_ADMIN") || 
                    authority.equals("ROLE_SUPER_ADMIN")
                );

        logger.debug("Permission check result: {}", hasPermission);
        return hasPermission;
    }

    /**
     * Build permission string from resource and action.
     */
    private String buildPermissionString(String resource, String action) {
        return resource.toUpperCase() + "_" + action.toUpperCase();
    }
}
