package com.uiplatform.service;

import com.uiplatform.dto.UserDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.entity.Role;
import com.uiplatform.entity.User;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.exception.DuplicateResourceException;
import com.uiplatform.exception.BusinessException;
import com.uiplatform.mapper.UserMapper;
import com.uiplatform.repository.OrganizationRepository;
import com.uiplatform.repository.RoleRepository;
import com.uiplatform.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for User management.
 * Handles business logic for user operations including authentication and profile management.
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    private final UserRepository userRepository;
    private final OrganizationRepository organizationRepository;
    private final RoleRepository roleRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository,
                      OrganizationRepository organizationRepository,
                      RoleRepository roleRepository,
                      UserMapper userMapper,
                      PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.organizationRepository = organizationRepository;
        this.roleRepository = roleRepository;
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * Create a new user.
     */
    public UserDTO createUser(UserDTO.CreateDTO createDTO) {
        logger.info("Creating new user with username: {} for organization: {}", 
                   createDTO.getUsername(), createDTO.getOrganizationId());

        // Validate organization exists
        Organization organization = organizationRepository.findById(createDTO.getOrganizationId())
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + createDTO.getOrganizationId()));

        // Check organization user limits
        long currentUserCount = userRepository.countByOrganizationId(createDTO.getOrganizationId());
        if (currentUserCount >= organization.getMaxUsers()) {
            throw new BusinessException("Organization has reached maximum user limit of " + organization.getMaxUsers());
        }

        // Check username uniqueness within organization
        if (userRepository.findByUsernameAndOrganizationIdAndDeletedFalse(createDTO.getUsername(), createDTO.getOrganizationId()).isPresent()) {
            throw new DuplicateResourceException("Username '" + createDTO.getUsername() + "' already exists in this organization");
        }

        // Check email uniqueness globally
        if (userRepository.findByEmailAndDeletedFalse(createDTO.getEmail()).isPresent()) {
            throw new DuplicateResourceException("Email '" + createDTO.getEmail() + "' already exists");
        }

        User user = userMapper.toEntity(createDTO);
        user.setOrganization(organization);
        user.setPassword(passwordEncoder.encode(createDTO.getPassword()));

        // Assign default USER role
        Optional<Role> userRole = roleRepository.findByNameAndDeletedFalse("USER");
        if (userRole.isPresent()) {
            user.getRoles().add(userRole.get());
        }

        user = userRepository.save(user);

        logger.info("Successfully created user with ID: {}", user.getId());
        return userMapper.toDTO(user);
    }

    /**
     * Get user by ID.
     */
    @Transactional(readOnly = true)
    public UserDTO getUserById(UUID id) {
        logger.debug("Fetching user by ID: {}", id);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        return userMapper.toDTO(user);
    }

    /**
     * Get user by username.
     */
    @Transactional(readOnly = true)
    public UserDTO getUserByUsername(String username) {
        logger.debug("Fetching user by username: {}", username);

        User user = userRepository.findByUsernameAndDeletedFalse(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        return userMapper.toDTO(user);
    }

    /**
     * Get user by email.
     */
    @Transactional(readOnly = true)
    public UserDTO getUserByEmail(String email) {
        logger.debug("Fetching user by email: {}", email);

        User user = userRepository.findByEmailAndDeletedFalse(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

        return userMapper.toDTO(user);
    }

    /**
     * Update user profile.
     */
    public UserDTO updateUser(UUID id, UserDTO.UpdateDTO updateDTO) {
        logger.info("Updating user profile for ID: {}", id);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        userMapper.updateEntityFromDTO(updateDTO, user);
        user = userRepository.save(user);

        logger.info("Successfully updated user profile for ID: {}", id);
        return userMapper.toDTO(user);
    }

    /**
     * Change user password.
     */
    public void changePassword(UUID id, String currentPassword, String newPassword) {
        logger.info("Changing password for user ID: {}", id);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        // Verify current password
        if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
            throw new BusinessException("Current password is incorrect");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        logger.info("Successfully changed password for user ID: {}", id);
    }

    /**
     * Delete user (soft delete).
     */
    public void deleteUser(UUID id) {
        logger.info("Deleting user with ID: {}", id);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        user.markAsDeleted("SYSTEM"); // TODO: Get current user
        userRepository.save(user);

        logger.info("Successfully deleted user with ID: {}", id);
    }

    /**
     * Get users by organization.
     */
    @Transactional(readOnly = true)
    public Page<UserDTO> getUsersByOrganization(UUID organizationId, Pageable pageable) {
        logger.debug("Fetching users for organization ID: {}", organizationId);

        Page<User> users = userRepository.findByOrganizationIdAndDeletedFalse(organizationId, pageable);
        return users.map(userMapper::toDTO);
    }

    /**
     * Search users within organization.
     */
    @Transactional(readOnly = true)
    public Page<UserDTO> searchUsers(UUID organizationId, String searchTerm, Pageable pageable) {
        logger.debug("Searching users in organization {} with term: {}", organizationId, searchTerm);

        Page<User> users = userRepository.searchUsers(organizationId, searchTerm, pageable);
        return users.map(userMapper::toDTO);
    }

    /**
     * Get users by role.
     */
    @Transactional(readOnly = true)
    public List<UserDTO> getUsersByRole(String roleName) {
        logger.debug("Fetching users with role: {}", roleName);

        List<User> users = userRepository.findByRoleName(roleName);
        return users.stream()
                .map(userMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get users by organization and role.
     */
    @Transactional(readOnly = true)
    public List<UserDTO> getUsersByOrganizationAndRole(UUID organizationId, String roleName) {
        logger.debug("Fetching users in organization {} with role: {}", organizationId, roleName);

        List<User> users = userRepository.findByOrganizationAndRole(organizationId, roleName);
        return users.stream()
                .map(userMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Update user status.
     */
    public UserDTO updateUserStatus(UUID id, User.UserStatus newStatus) {
        logger.info("Updating status for user ID: {} to {}", id, newStatus);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        User.UserStatus oldStatus = user.getStatus();
        user.setStatus(newStatus);
        user = userRepository.save(user);

        logger.info("Successfully updated status for user ID: {} from {} to {}", id, oldStatus, newStatus);
        return userMapper.toDTO(user);
    }

    /**
     * Verify user email.
     */
    public void verifyEmail(String token) {
        logger.info("Verifying email with token: {}", token);

        User user = userRepository.findByEmailVerificationTokenAndDeletedFalse(token)
                .orElseThrow(() -> new ResourceNotFoundException("Invalid email verification token"));

        user.setEmailVerified(true);
        user.setEmailVerificationToken(null);
        userRepository.save(user);

        logger.info("Successfully verified email for user ID: {}", user.getId());
    }

    /**
     * Update last login time.
     */
    public void updateLastLogin(UUID userId) {
        logger.debug("Updating last login time for user ID: {}", userId);
        userRepository.updateLastLoginTime(userId, LocalDateTime.now());
    }

    /**
     * Increment login attempts.
     */
    public void incrementLoginAttempts(UUID userId) {
        logger.debug("Incrementing login attempts for user ID: {}", userId);
        userRepository.incrementLoginAttempts(userId);
    }

    /**
     * Lock user account.
     */
    public void lockUserAccount(UUID userId, int lockDurationMinutes) {
        logger.info("Locking user account ID: {} for {} minutes", userId, lockDurationMinutes);
        LocalDateTime lockUntil = LocalDateTime.now().plusMinutes(lockDurationMinutes);
        userRepository.lockUserAccount(userId, lockUntil);
    }

    /**
     * Check if username exists in organization.
     */
    @Transactional(readOnly = true)
    public boolean existsByUsernameInOrganization(String username, UUID organizationId) {
        return userRepository.findByUsernameAndOrganizationIdAndDeletedFalse(username, organizationId).isPresent();
    }

    /**
     * Check if email exists.
     */
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.findByEmailAndDeletedFalse(email).isPresent();
    }

    /**
     * Get user statistics.
     */
    @Transactional(readOnly = true)
    public UserDTO getUserWithStats(UUID id) {
        logger.debug("Fetching user with statistics for ID: {}", id);

        User user = userRepository.findById(id)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        UserDTO dto = userMapper.toDTO(user);
        
        // TODO: Add statistics when other services are implemented
        // dto.setOwnedConfigurationsCount(uiConfigurationRepository.countByOwnerId(id));
        // dto.setAuthoredTemplatesCount(templateRepository.countByAuthorId(id));
        
        return dto;
    }
}
