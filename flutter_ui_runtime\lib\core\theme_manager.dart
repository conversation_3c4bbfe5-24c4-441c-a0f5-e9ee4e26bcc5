import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// Theme mode enumeration
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Design system enumeration
enum DesignSystem {
  material,
  cupertino,
  custom,
}

/// Theme configuration
class ThemeConfiguration {
  final String id;
  final String name;
  final DesignSystem designSystem;
  final AppThemeMode mode;
  final Map<String, dynamic> colors;
  final Map<String, dynamic> typography;
  final Map<String, dynamic> spacing;
  final Map<String, dynamic> borderRadius;
  final Map<String, dynamic> shadows;
  final Map<String, dynamic> customProperties;

  ThemeConfiguration({
    required this.id,
    required this.name,
    required this.designSystem,
    required this.mode,
    required this.colors,
    required this.typography,
    required this.spacing,
    required this.borderRadius,
    required this.shadows,
    this.customProperties = const {},
  });

  factory ThemeConfiguration.fromJson(Map<String, dynamic> json) {
    return ThemeConfiguration(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      designSystem: DesignSystem.values.firstWhere(
        (e) => e.name == json['designSystem'],
        orElse: () => DesignSystem.material,
      ),
      mode: AppThemeMode.values.firstWhere(
        (e) => e.name == json['mode'],
        orElse: () => AppThemeMode.light,
      ),
      colors: Map<String, dynamic>.from(json['colors'] ?? {}),
      typography: Map<String, dynamic>.from(json['typography'] ?? {}),
      spacing: Map<String, dynamic>.from(json['spacing'] ?? {}),
      borderRadius: Map<String, dynamic>.from(json['borderRadius'] ?? {}),
      shadows: Map<String, dynamic>.from(json['shadows'] ?? {}),
      customProperties: Map<String, dynamic>.from(json['customProperties'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'designSystem': designSystem.name,
      'mode': mode.name,
      'colors': colors,
      'typography': typography,
      'spacing': spacing,
      'borderRadius': borderRadius,
      'shadows': shadows,
      'customProperties': customProperties,
    };
  }

  ThemeConfiguration copyWith({
    String? id,
    String? name,
    DesignSystem? designSystem,
    AppThemeMode? mode,
    Map<String, dynamic>? colors,
    Map<String, dynamic>? typography,
    Map<String, dynamic>? spacing,
    Map<String, dynamic>? borderRadius,
    Map<String, dynamic>? shadows,
    Map<String, dynamic>? customProperties,
  }) {
    return ThemeConfiguration(
      id: id ?? this.id,
      name: name ?? this.name,
      designSystem: designSystem ?? this.designSystem,
      mode: mode ?? this.mode,
      colors: colors ?? this.colors,
      typography: typography ?? this.typography,
      spacing: spacing ?? this.spacing,
      borderRadius: borderRadius ?? this.borderRadius,
      shadows: shadows ?? this.shadows,
      customProperties: customProperties ?? this.customProperties,
    );
  }
}

/// Theme state
class ThemeState {
  final ThemeConfiguration currentTheme;
  final List<ThemeConfiguration> availableThemes;
  final bool isLoading;
  final String? error;

  ThemeState({
    required this.currentTheme,
    required this.availableThemes,
    this.isLoading = false,
    this.error,
  });

  ThemeState copyWith({
    ThemeConfiguration? currentTheme,
    List<ThemeConfiguration>? availableThemes,
    bool? isLoading,
    String? error,
  }) {
    return ThemeState(
      currentTheme: currentTheme ?? this.currentTheme,
      availableThemes: availableThemes ?? this.availableThemes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Theme Manager for Flutter UI Runtime
///
/// Manages dynamic theme application supporting both Material and Cupertino
/// design systems with dark/light mode, custom themes, and accessibility support.
class ThemeManager extends StateNotifier<ThemeState> {
  ThemeManager()
      : super(ThemeState(
          currentTheme: _getDefaultTheme(),
          availableThemes: _getDefaultThemes(),
        ));

  /// Set current theme
  void setTheme(ThemeConfiguration theme) {
    state = state.copyWith(currentTheme: theme);
    _saveThemePreference(theme);
  }

  /// Set theme mode
  void setThemeMode(AppThemeMode mode) {
    final updatedTheme = state.currentTheme.copyWith(mode: mode);
    setTheme(updatedTheme);
  }

  /// Set design system
  void setDesignSystem(DesignSystem designSystem) {
    final updatedTheme = state.currentTheme.copyWith(designSystem: designSystem);
    setTheme(updatedTheme);
  }

  /// Add custom theme
  void addCustomTheme(ThemeConfiguration theme) {
    final updatedThemes = [...state.availableThemes, theme];
    state = state.copyWith(availableThemes: updatedThemes);
  }

  /// Remove custom theme
  void removeCustomTheme(String themeId) {
    final updatedThemes = state.availableThemes.where((theme) => theme.id != themeId).toList();
    state = state.copyWith(availableThemes: updatedThemes);
  }

  /// Update theme colors
  void updateColors(Map<String, dynamic> colors) {
    final updatedTheme = state.currentTheme.copyWith(
      colors: {...state.currentTheme.colors, ...colors},
    );
    setTheme(updatedTheme);
  }

  /// Update typography
  void updateTypography(Map<String, dynamic> typography) {
    final updatedTheme = state.currentTheme.copyWith(
      typography: {...state.currentTheme.typography, ...typography},
    );
    setTheme(updatedTheme);
  }

  /// Update spacing
  void updateSpacing(Map<String, dynamic> spacing) {
    final updatedTheme = state.currentTheme.copyWith(
      spacing: {...state.currentTheme.spacing, ...spacing},
    );
    setTheme(updatedTheme);
  }

  /// Load theme from configuration
  void loadThemeFromConfiguration(UIConfiguration configuration) {
    if (configuration.theme != null) {
      final themeConfig = ThemeConfiguration.fromJson(configuration.theme!);
      setTheme(themeConfig);
    }
  }

  /// Get Material ThemeData
  ThemeData getMaterialThemeData() {
    final theme = state.currentTheme;
    final isDark = _isDarkMode();

    return ThemeData(
      useMaterial3: true,
      brightness: isDark ? Brightness.dark : Brightness.light,
      colorScheme: _buildMaterialColorScheme(theme.colors, isDark),
      textTheme: _buildMaterialTextTheme(theme.typography),
      appBarTheme: _buildMaterialAppBarTheme(theme.colors, isDark),
      elevatedButtonTheme: _buildMaterialElevatedButtonTheme(theme.colors),
      outlinedButtonTheme: _buildMaterialOutlinedButtonTheme(theme.colors),
      textButtonTheme: _buildMaterialTextButtonTheme(theme.colors),
      inputDecorationTheme: _buildMaterialInputDecorationTheme(theme.colors),
      cardTheme: _buildMaterialCardTheme(theme.colors, theme.borderRadius, theme.shadows),
      dividerTheme: _buildMaterialDividerTheme(theme.colors),
      scaffoldBackgroundColor: _parseColor(theme.colors['background']),
      fontFamily: theme.typography['fontFamily'] as String?,
    );
  }

  /// Get Cupertino ThemeData
  CupertinoThemeData getCupertinoThemeData() {
    final theme = state.currentTheme;
    final isDark = _isDarkMode();

    return CupertinoThemeData(
      brightness: isDark ? Brightness.dark : Brightness.light,
      primaryColor: _parseColor(theme.colors['primary']) ?? CupertinoColors.systemBlue,
      primaryContrastingColor: _parseColor(theme.colors['onPrimary']) ?? CupertinoColors.white,
      scaffoldBackgroundColor: _parseColor(theme.colors['background']) ?? (isDark ? CupertinoColors.black : CupertinoColors.white),
      barBackgroundColor: _parseColor(theme.colors['surface']) ?? (isDark ? CupertinoColors.systemGrey6 : CupertinoColors.systemGrey6),
      textTheme: _buildCupertinoTextTheme(theme.typography, isDark),
    );
  }

  /// Check if current mode is dark
  bool _isDarkMode() {
    switch (state.currentTheme.mode) {
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.light:
        return false;
      case AppThemeMode.system:
        return WidgetsBinding.instance.window.platformBrightness == Brightness.dark;
    }
  }

  /// Build Material color scheme
  ColorScheme _buildMaterialColorScheme(Map<String, dynamic> colors, bool isDark) {
    if (isDark) {
      return ColorScheme.dark(
        primary: _parseColor(colors['primary']) ?? Colors.blue,
        secondary: _parseColor(colors['secondary']) ?? Colors.blueAccent,
        surface: _parseColor(colors['surface']) ?? Colors.grey[900]!,
        background: _parseColor(colors['background']) ?? Colors.black,
        error: _parseColor(colors['error']) ?? Colors.red,
        onPrimary: _parseColor(colors['onPrimary']) ?? Colors.white,
        onSecondary: _parseColor(colors['onSecondary']) ?? Colors.white,
        onSurface: _parseColor(colors['onSurface']) ?? Colors.white,
        onBackground: _parseColor(colors['onBackground']) ?? Colors.white,
        onError: _parseColor(colors['onError']) ?? Colors.white,
      );
    } else {
      return ColorScheme.light(
        primary: _parseColor(colors['primary']) ?? Colors.blue,
        secondary: _parseColor(colors['secondary']) ?? Colors.blueAccent,
        surface: _parseColor(colors['surface']) ?? Colors.white,
        background: _parseColor(colors['background']) ?? Colors.white,
        error: _parseColor(colors['error']) ?? Colors.red,
        onPrimary: _parseColor(colors['onPrimary']) ?? Colors.white,
        onSecondary: _parseColor(colors['onSecondary']) ?? Colors.white,
        onSurface: _parseColor(colors['onSurface']) ?? Colors.black,
        onBackground: _parseColor(colors['onBackground']) ?? Colors.black,
        onError: _parseColor(colors['onError']) ?? Colors.white,
      );
    }
  }

  /// Build Material text theme
  TextTheme _buildMaterialTextTheme(Map<String, dynamic> typography) {
    final fontFamily = typography['fontFamily'] as String?;
    final baseSize = _parseDouble(typography['fontSize']) ?? 16.0;

    return TextTheme(
      displayLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 3.5,
        fontWeight: FontWeight.w400,
      ),
      displayMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 2.8,
        fontWeight: FontWeight.w400,
      ),
      displaySmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 2.25,
        fontWeight: FontWeight.w400,
      ),
      headlineLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 2,
        fontWeight: FontWeight.w400,
      ),
      headlineMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 1.75,
        fontWeight: FontWeight.w400,
      ),
      headlineSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 1.5,
        fontWeight: FontWeight.w400,
      ),
      titleLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 1.375,
        fontWeight: FontWeight.w500,
      ),
      titleMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 1.125,
        fontWeight: FontWeight.w500,
      ),
      titleSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize,
        fontWeight: FontWeight.w400,
      ),
      bodyMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 0.875,
        fontWeight: FontWeight.w400,
      ),
      bodySmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 0.75,
        fontWeight: FontWeight.w400,
      ),
      labelLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 0.875,
        fontWeight: FontWeight.w500,
      ),
      labelMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 0.75,
        fontWeight: FontWeight.w500,
      ),
      labelSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: baseSize * 0.6875,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Build Material AppBar theme
  AppBarTheme _buildMaterialAppBarTheme(Map<String, dynamic> colors, bool isDark) {
    return AppBarTheme(
      backgroundColor: _parseColor(colors['surface']),
      foregroundColor: _parseColor(colors['onSurface']),
      elevation: 0,
      centerTitle: false,
      titleTextStyle: TextStyle(
        color: _parseColor(colors['onSurface']),
        fontSize: 20,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Build Material ElevatedButton theme
  ElevatedButtonThemeData _buildMaterialElevatedButtonTheme(Map<String, dynamic> colors) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: _parseColor(colors['primary']),
        foregroundColor: _parseColor(colors['onPrimary']),
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Build Material OutlinedButton theme
  OutlinedButtonThemeData _buildMaterialOutlinedButtonTheme(Map<String, dynamic> colors) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: _parseColor(colors['primary']),
        side: BorderSide(color: _parseColor(colors['primary']) ?? Colors.blue),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Build Material TextButton theme
  TextButtonThemeData _buildMaterialTextButtonTheme(Map<String, dynamic> colors) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: _parseColor(colors['primary']),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Build Material InputDecoration theme
  InputDecorationTheme _buildMaterialInputDecorationTheme(Map<String, dynamic> colors) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: _parseColor(colors['outline']) ?? Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: _parseColor(colors['outline']) ?? Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: _parseColor(colors['primary']) ?? Colors.blue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: _parseColor(colors['error']) ?? Colors.red),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// Build Material Card theme
  CardTheme _buildMaterialCardTheme(
    Map<String, dynamic> colors,
    Map<String, dynamic> borderRadius,
    Map<String, dynamic> shadows,
  ) {
    return CardTheme(
      color: _parseColor(colors['surface']),
      elevation: _parseDouble(shadows['elevation']) ?? 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_parseDouble(borderRadius['card']) ?? 12),
      ),
      margin: const EdgeInsets.all(8),
    );
  }

  /// Build Material Divider theme
  DividerThemeData _buildMaterialDividerTheme(Map<String, dynamic> colors) {
    return DividerThemeData(
      color: _parseColor(colors['outline']) ?? Colors.grey.shade300,
      thickness: 1,
      space: 1,
    );
  }

  /// Build Cupertino text theme
  CupertinoTextThemeData _buildCupertinoTextTheme(Map<String, dynamic> typography, bool isDark) {
    final fontFamily = typography['fontFamily'] as String?;
    final baseColor = isDark ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoTextThemeData(
      primaryColor: baseColor,
      textStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
      ),
      actionTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: CupertinoColors.systemBlue,
      ),
      tabLabelTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
      ),
      navTitleTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
        fontWeight: FontWeight.w600,
      ),
      navLargeTitleTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
        fontWeight: FontWeight.bold,
        fontSize: 34,
      ),
      navActionTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: CupertinoColors.systemBlue,
      ),
      pickerTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
      ),
      dateTimePickerTextStyle: TextStyle(
        fontFamily: fontFamily,
        color: baseColor,
      ),
    );
  }

  /// Parse color from string or return null
  Color? _parseColor(dynamic value) {
    if (value is String && value.startsWith('#')) {
      return Color(int.parse(value.replaceFirst('#', '0xff')));
    }
    return null;
  }

  /// Parse double from dynamic value
  double? _parseDouble(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  /// Save theme preference to storage
  void _saveThemePreference(ThemeConfiguration theme) {
    // This would save to SharedPreferences or similar
    // For now, just log the save operation
    print('Saving theme preference: ${theme.name}');
  }

  /// Get default theme configuration
  static ThemeConfiguration _getDefaultTheme() {
    return ThemeConfiguration(
      id: 'default-light',
      name: 'Default Light',
      designSystem: DesignSystem.material,
      mode: AppThemeMode.light,
      colors: {
        'primary': '#2196F3',
        'secondary': '#03DAC6',
        'surface': '#FFFFFF',
        'background': '#FAFAFA',
        'error': '#F44336',
        'onPrimary': '#FFFFFF',
        'onSecondary': '#000000',
        'onSurface': '#000000',
        'onBackground': '#000000',
        'onError': '#FFFFFF',
        'outline': '#E0E0E0',
      },
      typography: {
        'fontFamily': 'Roboto',
        'fontSize': 16.0,
      },
      spacing: {
        'xs': 4.0,
        'sm': 8.0,
        'md': 16.0,
        'lg': 24.0,
        'xl': 32.0,
      },
      borderRadius: {
        'sm': 4.0,
        'md': 8.0,
        'lg': 12.0,
        'xl': 16.0,
        'card': 12.0,
      },
      shadows: {
        'elevation': 2.0,
      },
    );
  }

  /// Get default themes list
  static List<ThemeConfiguration> _getDefaultThemes() {
    return [
      _getDefaultTheme(),
      ThemeConfiguration(
        id: 'default-dark',
        name: 'Default Dark',
        designSystem: DesignSystem.material,
        mode: AppThemeMode.dark,
        colors: {
          'primary': '#BB86FC',
          'secondary': '#03DAC6',
          'surface': '#121212',
          'background': '#000000',
          'error': '#CF6679',
          'onPrimary': '#000000',
          'onSecondary': '#000000',
          'onSurface': '#FFFFFF',
          'onBackground': '#FFFFFF',
          'onError': '#000000',
          'outline': '#424242',
        },
        typography: {
          'fontFamily': 'Roboto',
          'fontSize': 16.0,
        },
        spacing: {
          'xs': 4.0,
          'sm': 8.0,
          'md': 16.0,
          'lg': 24.0,
          'xl': 32.0,
        },
        borderRadius: {
          'sm': 4.0,
          'md': 8.0,
          'lg': 12.0,
          'xl': 16.0,
          'card': 12.0,
        },
        shadows: {
          'elevation': 8.0,
        },
      ),
      ThemeConfiguration(
        id: 'cupertino-light',
        name: 'Cupertino Light',
        designSystem: DesignSystem.cupertino,
        mode: AppThemeMode.light,
        colors: {
          'primary': '#007AFF',
          'secondary': '#5AC8FA',
          'surface': '#FFFFFF',
          'background': '#F2F2F7',
          'error': '#FF3B30',
          'onPrimary': '#FFFFFF',
          'onSecondary': '#FFFFFF',
          'onSurface': '#000000',
          'onBackground': '#000000',
          'onError': '#FFFFFF',
          'outline': '#C7C7CC',
        },
        typography: {
          'fontFamily': 'SF Pro Text',
          'fontSize': 17.0,
        },
        spacing: {
          'xs': 4.0,
          'sm': 8.0,
          'md': 16.0,
          'lg': 24.0,
          'xl': 32.0,
        },
        borderRadius: {
          'sm': 8.0,
          'md': 10.0,
          'lg': 12.0,
          'xl': 16.0,
          'card': 10.0,
        },
        shadows: {
          'elevation': 1.0,
        },
      ),
    ];
  }
}

/// Theme provider
final themeManagerProvider = StateNotifierProvider<ThemeManager, ThemeState>((ref) {
  return ThemeManager();
});

/// Current theme provider
final currentThemeProvider = Provider<ThemeConfiguration>((ref) {
  return ref.watch(themeManagerProvider).currentTheme;
});

/// Material theme data provider
final materialThemeProvider = Provider<ThemeData>((ref) {
  return ref.watch(themeManagerProvider.notifier).getMaterialThemeData();
});

/// Cupertino theme data provider
final cupertinoThemeProvider = Provider<CupertinoThemeData>((ref) {
  return ref.watch(themeManagerProvider.notifier).getCupertinoThemeData();
});

/// Theme mode provider
final themeModeProvider = Provider<AppThemeMode>((ref) {
  return ref.watch(themeManagerProvider).currentTheme.mode;
});

/// Design system provider
final designSystemProvider = Provider<DesignSystem>((ref) {
  return ref.watch(themeManagerProvider).currentTheme.designSystem;
});

/// Theme helper extensions
extension ThemeHelpers on WidgetRef {
  ThemeManager get themeManager => read(themeManagerProvider.notifier);
  ThemeConfiguration get currentTheme => read(currentThemeProvider);

  void setTheme(ThemeConfiguration theme) {
    themeManager.setTheme(theme);
  }

  void setThemeMode(AppThemeMode mode) {
    themeManager.setThemeMode(mode);
  }

  void setDesignSystem(DesignSystem designSystem) {
    themeManager.setDesignSystem(designSystem);
  }
}
