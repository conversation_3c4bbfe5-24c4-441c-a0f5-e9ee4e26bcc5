# UI Platform Backend Configuration

server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: ui-platform-backend
  
  profiles:
    active: development
  
  # Database Configuration
  datasource:
    url: ********************************************
    username: ui_platform_user
    password: ui_platform_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
  
  # Liquibase Configuration
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1 hour in milliseconds
      cache-null-values: false

  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      acks: all
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        "[linger.ms]": 5
    consumer:
      group-id: ui-platform-collaboration
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        "[spring.json.trusted.packages]": "com.uiplatform.dto.collaboration"
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${app.jwt.issuer-uri:http://localhost:8080}

# Application Specific Configuration
app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey1234567890123456789012345678901234567890}
    issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080}
    access-token-expiration-ms: 86400000 # 24 hours
    refresh-token-expiration-ms: 604800000 # 7 days
    password-reset-token-expiration-ms: 3600000 # 1 hour
  
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
    allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  file-upload:
    max-file-size: 10MB
    max-request-size: 50MB
    upload-dir: ${UPLOAD_DIR:./uploads}
  
  email:
    enabled: ${EMAIL_ENABLED:false}
    smtp:
      host: ${SMTP_HOST:localhost}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
      auth: true
      starttls: true
    from: ${EMAIL_FROM:<EMAIL>}
  
  rate-limiting:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100

# Logging Configuration
logging:
  level:
    com.uiplatform: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    graphql: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ui-platform-backend.log
    max-size: 10MB
    max-history: 30

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    redis:
      enabled: true
    db:
      enabled: true

# GraphQL Configuration
graphql:
  servlet:
    mapping: /graphql
    enabled: true
    corsEnabled: true
  tools:
    schema-location-pattern: "**/*.graphqls"
  playground:
    enabled: true
    mapping: /graphiql
    endpoint: /graphql
    subscriptionEndpoint: /subscriptions
    settings:
      editor.theme: dark
      editor.fontSize: 14

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operationsSorter: method
    tagsSorter: alpha
  show-actuator: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: development
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
  
  h2:
    console:
      enabled: true
      path: /h2-console

logging:
  level:
    com.uiplatform: DEBUG
    org.springframework.security: DEBUG

app:
  cors:
    allowed-origins: "*"

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  liquibase:
    enabled: false
  
  data:
    redis:
      host: localhost
      port: 6370 # Different port for testing

logging:
  level:
    com.uiplatform: INFO
    org.springframework.security: WARN

---
# Production Profile
spring:
  config:
    activate:
      on-profile: production
  
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
  
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}

logging:
  level:
    com.uiplatform: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/ui-platform-backend/application.log

app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com}
  
  jwt:
    secret: ${JWT_SECRET}
  
  email:
    enabled: true
