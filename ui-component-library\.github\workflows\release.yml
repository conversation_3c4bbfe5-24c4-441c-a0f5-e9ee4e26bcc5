name: Release

on:
  push:
    branches: [main]
    paths:
      - 'packages/*/package.json'
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease

env:
  NODE_VERSION: '18'
  FLUTTER_VERSION: '3.16.0'

jobs:
  # Check if release is needed
  check-release:
    runs-on: ubuntu-latest
    outputs:
      should-release: ${{ steps.check.outputs.should-release }}
      packages: ${{ steps.check.outputs.packages }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Check for changes
        id: check
        run: |
          # Check if any package.json files have changed
          if git diff --name-only HEAD~1 HEAD | grep -q "packages/.*/package.json"; then
            echo "should-release=true" >> $GITHUB_OUTPUT
            
            # Get list of changed packages
            PACKAGES=$(git diff --name-only HEAD~1 HEAD | grep "packages/.*/package.json" | sed 's|packages/\([^/]*\)/.*|\1|' | sort -u | jq -R -s -c 'split("\n")[:-1]')
            echo "packages=$PACKAGES" >> $GITHUB_OUTPUT
          else
            echo "should-release=false" >> $GITHUB_OUTPUT
          fi

  # Build all packages
  build:
    runs-on: ubuntu-latest
    needs: check-release
    if: needs.check-release.outputs.should-release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://registry.npmjs.org'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'

      - name: Install dependencies
        run: npm ci

      - name: Build design tokens
        run: npm run tokens:build

      - name: Build React components
        run: npm run react:build

      - name: Build Flutter components
        run: |
          cd packages/flutter-components
          flutter pub get
          flutter analyze
          flutter test

      - name: Build icons
        run: npm run icons:build

      - name: Build testing utilities
        run: npm run testing-utilities:build

      - name: Build documentation
        run: npm run docs:build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            packages/*/dist/
            packages/flutter-components/lib/

  # Run tests
  test:
    runs-on: ubuntu-latest
    needs: [check-release, build]
    if: needs.check-release.outputs.should-release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Test React components
        run: npm run react:test

      - name: Test Flutter components
        run: |
          cd packages/flutter-components
          flutter test

      - name: Test icons
        run: npm run icons:test

      - name: Test testing utilities
        run: npm run testing-utilities:test

  # Create GitHub release
  create-release:
    runs-on: ubuntu-latest
    needs: [check-release, build, test]
    if: needs.check-release.outputs.should-release == 'true'
    outputs:
      release-tag: ${{ steps.release.outputs.tag }}
      release-url: ${{ steps.release.outputs.url }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog using conventional commits
          npx conventional-changelog-cli -p angular -i CHANGELOG.md -s
          
          # Extract latest version changes
          CHANGELOG_CONTENT=$(sed -n '/^## \[/,/^## \[/p' CHANGELOG.md | head -n -1)
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG_CONTENT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Get version
        id: version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Create GitHub release
        id: release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.version }}
          release_name: Release v${{ steps.version.outputs.version }}
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: ${{ contains(steps.version.outputs.version, '-') }}

  # Publish to NPM
  publish-npm:
    runs-on: ubuntu-latest
    needs: [check-release, create-release]
    if: needs.check-release.outputs.should-release == 'true'
    strategy:
      matrix:
        package: ${{ fromJson(needs.check-release.outputs.packages) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Publish ${{ matrix.package }} to NPM
        run: |
          cd packages/${{ matrix.package }}
          
          # Check if package should be published
          if [ -f "package.json" ] && [ -d "dist" ]; then
            # Get current version
            CURRENT_VERSION=$(node -p "require('./package.json').version")
            
            # Check if version exists on NPM
            if npm view @ui-builder/${{ matrix.package }}@$CURRENT_VERSION > /dev/null 2>&1; then
              echo "Version $CURRENT_VERSION already exists on NPM, skipping..."
            else
              echo "Publishing @ui-builder/${{ matrix.package }}@$CURRENT_VERSION to NPM..."
              npm publish --access public
            fi
          else
            echo "No dist folder found for ${{ matrix.package }}, skipping NPM publish..."
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  # Publish Flutter package
  publish-flutter:
    runs-on: ubuntu-latest
    needs: [check-release, create-release]
    if: needs.check-release.outputs.should-release == 'true' && contains(fromJson(needs.check-release.outputs.packages), 'flutter-components')
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'

      - name: Setup pub credentials
        run: |
          mkdir -p ~/.pub-cache
          echo '${{ secrets.PUB_CREDENTIALS }}' > ~/.pub-cache/credentials.json

      - name: Publish Flutter package
        run: |
          cd packages/flutter-components
          flutter pub get
          flutter pub publish --force

  # Deploy Storybook
  deploy-storybook:
    runs-on: ubuntu-latest
    needs: [check-release, create-release]
    if: needs.check-release.outputs.should-release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Build Storybook
        run: npm run react:build-storybook

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist/storybook
          cname: components.uibuilder.dev

  # Update documentation
  update-docs:
    runs-on: ubuntu-latest
    needs: [check-release, create-release]
    if: needs.check-release.outputs.should-release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Build documentation
        run: npm run docs:build

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist/documentation
          destination_dir: docs
          cname: docs.uibuilder.dev

  # Notify on success
  notify-success:
    runs-on: ubuntu-latest
    needs: [create-release, publish-npm, deploy-storybook, update-docs]
    if: always() && needs.create-release.result == 'success'
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#releases'
          text: |
            🎉 New release published!
            
            **Version:** ${{ needs.create-release.outputs.release-tag }}
            **Release:** ${{ needs.create-release.outputs.release-url }}
            **Storybook:** https://components.uibuilder.dev
            **Documentation:** https://docs.uibuilder.dev
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
