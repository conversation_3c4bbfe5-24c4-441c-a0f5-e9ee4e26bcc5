import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_ui_runtime/core/theme_manager.dart';
import 'package:flutter_ui_runtime/core/state_management.dart';
import 'package:flutter_ui_runtime/models/ui_metadata.dart';

/// Test utilities for Flutter UI Runtime
/// 
/// Provides helper functions and utilities for testing widgets,
/// state management, and accessibility compliance.

/// Custom test wrapper that provides necessary providers
class TestWrapper extends StatelessWidget {
  final Widget child;
  final ThemeConfiguration? theme;
  final Map<String, dynamic>? initialState;
  final bool enableAccessibility;

  const TestWrapper({
    Key? key,
    required this.child,
    this.theme,
    this.initialState,
    this.enableAccessibility = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      overrides: [
        if (theme != null)
          themeManagerProvider.overrideWith((ref) {
            final manager = ThemeManager();
            manager.setTheme(theme!);
            return manager;
          }),
        if (initialState != null)
          stateManagerProvider.overrideWith((ref) {
            final manager = StateManager();
            initialState!.forEach((key, value) {
              manager.setState(key, value);
            });
            return manager;
          }),
      ],
      child: Consumer(
        builder: (context, ref, _) {
          final themeManager = ref.watch(themeManagerProvider.notifier);
          final currentTheme = ref.watch(themeManagerProvider).currentTheme;
          
          return MaterialApp(
            theme: themeManager.getMaterialThemeData(),
            home: Scaffold(
              body: enableAccessibility
                  ? Semantics(
                      enabled: true,
                      child: child,
                    )
                  : child,
            ),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

/// Pump widget with test wrapper
Future<void> pumpWidgetWithWrapper(
  WidgetTester tester,
  Widget widget, {
  ThemeConfiguration? theme,
  Map<String, dynamic>? initialState,
  bool enableAccessibility = true,
  Duration? duration,
}) async {
  await tester.pumpWidget(
    TestWrapper(
      theme: theme,
      initialState: initialState,
      enableAccessibility: enableAccessibility,
      child: widget,
    ),
  );
  
  if (duration != null) {
    await tester.pump(duration);
  }
}

/// Test widget in both light and dark themes
Future<void> testThemes(
  WidgetTester tester,
  Widget widget,
  Future<void> Function(WidgetTester, ThemeConfiguration) testFunction,
) async {
  // Test light theme
  final lightTheme = ThemeConfiguration(
    id: 'test-light',
    name: 'Test Light',
    designSystem: DesignSystem.material,
    mode: AppThemeMode.light,
    colors: {
      'primary': '#2196F3',
      'surface': '#FFFFFF',
      'onSurface': '#000000',
    },
    typography: {'fontFamily': 'Roboto'},
    spacing: {},
    borderRadius: {},
    shadows: {},
  );

  await pumpWidgetWithWrapper(tester, widget, theme: lightTheme);
  await testFunction(tester, lightTheme);

  // Test dark theme
  final darkTheme = ThemeConfiguration(
    id: 'test-dark',
    name: 'Test Dark',
    designSystem: DesignSystem.material,
    mode: AppThemeMode.dark,
    colors: {
      'primary': '#BB86FC',
      'surface': '#121212',
      'onSurface': '#FFFFFF',
    },
    typography: {'fontFamily': 'Roboto'},
    spacing: {},
    borderRadius: {},
    shadows: {},
  );

  await pumpWidgetWithWrapper(tester, widget, theme: darkTheme);
  await testFunction(tester, darkTheme);
}

/// Test widget accessibility
Future<void> testAccessibility(
  WidgetTester tester,
  Widget widget, {
  ThemeConfiguration? theme,
}) async {
  await pumpWidgetWithWrapper(
    tester,
    widget,
    theme: theme,
    enableAccessibility: true,
  );

  // Test semantic properties
  final SemanticsHandle handle = tester.ensureSemantics();
  
  try {
    // Verify no semantic issues
    expect(tester.getSemantics(find.byWidget(widget)), isNotNull);
    
    // Test focus traversal
    await testFocusTraversal(tester);
    
    // Test screen reader announcements
    await testScreenReaderAnnouncements(tester);
    
  } finally {
    handle.dispose();
  }
}

/// Test focus traversal order
Future<void> testFocusTraversal(WidgetTester tester) async {
  final focusableWidgets = find.byWidgetPredicate((widget) {
    return widget is Focus ||
           widget is FocusableActionDetector ||
           widget is TextField ||
           widget is Button ||
           widget is InkWell ||
           widget is GestureDetector;
  });

  if (focusableWidgets.evaluate().isNotEmpty) {
    // Test tab navigation
    await tester.sendKeyEvent(LogicalKeyboardKey.tab);
    await tester.pump();
    
    // Verify focus is visible
    final focusedWidget = FocusManager.instance.primaryFocus;
    expect(focusedWidget, isNotNull);
  }
}

/// Test screen reader announcements
Future<void> testScreenReaderAnnouncements(WidgetTester tester) async {
  final announcements = <String>[];
  
  tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
    SystemChannels.accessibility,
    (MethodCall methodCall) async {
      if (methodCall.method == 'announce') {
        announcements.add(methodCall.arguments as String);
      }
      return null;
    },
  );

  // Trigger any announcements
  await tester.pump();
  
  // Verify announcements are meaningful
  for (final announcement in announcements) {
    expect(announcement.trim(), isNotEmpty);
  }
}

/// Test widget performance
Future<void> testPerformance(
  WidgetTester tester,
  Widget widget, {
  int maxBuildTime = 16, // 16ms for 60fps
  int iterations = 10,
}) async {
  final buildTimes = <int>[];
  
  for (int i = 0; i < iterations; i++) {
    final stopwatch = Stopwatch()..start();
    
    await pumpWidgetWithWrapper(tester, widget);
    await tester.pump();
    
    stopwatch.stop();
    buildTimes.add(stopwatch.elapsedMilliseconds);
    
    // Clean up for next iteration
    await tester.pumpWidget(Container());
  }
  
  final averageBuildTime = buildTimes.reduce((a, b) => a + b) / buildTimes.length;
  expect(averageBuildTime, lessThan(maxBuildTime));
}

/// Test widget memory usage
Future<void> testMemoryUsage(
  WidgetTester tester,
  Widget widget, {
  int iterations = 100,
}) async {
  // Force garbage collection before test
  await tester.binding.reassembleApplication();
  
  for (int i = 0; i < iterations; i++) {
    await pumpWidgetWithWrapper(tester, widget);
    await tester.pump();
    await tester.pumpWidget(Container());
  }
  
  // Force garbage collection after test
  await tester.binding.reassembleApplication();
  
  // Memory usage should be stable (no significant leaks)
  // This is more of a smoke test - detailed memory analysis
  // would require additional tooling
}

/// Test widget responsiveness across different screen sizes
Future<void> testResponsiveness(
  WidgetTester tester,
  Widget widget,
  Map<String, Size> screenSizes,
) async {
  for (final entry in screenSizes.entries) {
    final name = entry.key;
    final size = entry.value;
    
    // Set screen size
    tester.binding.window.physicalSizeTestValue = size;
    tester.binding.window.devicePixelRatioTestValue = 1.0;
    
    await pumpWidgetWithWrapper(tester, widget);
    await tester.pump();
    
    // Verify widget renders correctly at this size
    expect(find.byWidget(widget), findsOneWidget);
    
    // Add specific assertions based on screen size
    if (size.width < 600) {
      // Mobile-specific tests
    } else if (size.width < 1024) {
      // Tablet-specific tests
    } else {
      // Desktop-specific tests
    }
  }
  
  // Reset to default size
  tester.binding.window.clearPhysicalSizeTestValue();
  tester.binding.window.clearDevicePixelRatioTestValue();
}

/// Create mock component definition for testing
ComponentDefinition createMockComponent({
  String id = 'test-component',
  String type = 'text',
  Map<String, dynamic>? properties,
  Map<String, dynamic>? style,
  List<ComponentDefinition>? children,
  Map<String, dynamic>? actions,
}) {
  return ComponentDefinition(
    id: id,
    type: type,
    properties: properties ?? {'text': 'Test Component'},
    style: style,
    children: children,
    actions: actions,
  );
}

/// Create mock UI configuration for testing
UIConfiguration createMockConfiguration({
  String id = 'test-config',
  String name = 'Test Configuration',
  List<ComponentDefinition>? components,
  Map<String, dynamic>? theme,
  LayoutDefinition? layout,
}) {
  return UIConfiguration(
    id: id,
    name: name,
    version: '1.0.0',
    components: components ?? [createMockComponent()],
    theme: theme,
    layout: layout ?? LayoutDefinition(
      type: 'container',
      properties: {},
    ),
    metadata: {
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    },
  );
}

/// Test state management operations
Future<void> testStateManagement(
  WidgetTester tester,
  Widget widget,
  Map<String, dynamic> initialState,
  Map<String, dynamic> expectedState,
) async {
  await pumpWidgetWithWrapper(
    tester,
    widget,
    initialState: initialState,
  );

  // Verify initial state
  final container = ProviderScope.containerOf(
    tester.element(find.byType(TestWrapper)),
  );
  
  final stateManager = container.read(stateManagerProvider.notifier);
  
  for (final entry in expectedState.entries) {
    expect(stateManager.getState(entry.key), equals(entry.value));
  }
}

/// Common screen sizes for responsive testing
final Map<String, Size> commonScreenSizes = {
  'mobile_portrait': const Size(375, 667),
  'mobile_landscape': const Size(667, 375),
  'tablet_portrait': const Size(768, 1024),
  'tablet_landscape': const Size(1024, 768),
  'desktop_small': const Size(1280, 720),
  'desktop_large': const Size(1920, 1080),
};

/// Custom matchers for testing
class WidgetMatchers {
  /// Matcher for checking if widget has specific theme color
  static Matcher hasThemeColor(String colorKey, String expectedColor) {
    return predicate<Widget>((widget) {
      // Implementation would check if widget uses the specified theme color
      return true; // Placeholder
    }, 'has theme color $colorKey with value $expectedColor');
  }
  
  /// Matcher for checking if widget is accessible
  static Matcher isAccessible() {
    return predicate<Widget>((widget) {
      // Implementation would check accessibility properties
      return true; // Placeholder
    }, 'is accessible');
  }
  
  /// Matcher for checking if widget is responsive
  static Matcher isResponsive() {
    return predicate<Widget>((widget) {
      // Implementation would check responsive behavior
      return true; // Placeholder
    }, 'is responsive');
  }
}

/// Test group helpers
void testWidgetGroup(
  String description,
  Widget widget,
  void Function() tests, {
  ThemeConfiguration? theme,
  Map<String, dynamic>? initialState,
}) {
  group(description, () {
    late WidgetTester tester;
    
    setUp(() async {
      // Setup code here
    });
    
    tearDown(() async {
      // Cleanup code here
    });
    
    tests();
  });
}
