/**
 * Test setup for Design Tokens package
 * Configures testing environment for Node.js based tests
 */

// Mock file system operations if needed
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn(),
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
}));

// Mock path operations
jest.mock('path', () => ({
  ...jest.requireActual('path'),
  resolve: jest.fn().mockImplementation((...args) => args.join('/')),
  join: jest.fn().mockImplementation((...args) => args.join('/')),
}));

// Global test utilities for design tokens
global.mockTokens = {
  color: {
    primary: {
      value: '#007bff',
      type: 'color',
      attributes: {
        category: 'color',
        type: 'primary',
      },
    },
    secondary: {
      value: '#6c757d',
      type: 'color',
      attributes: {
        category: 'color',
        type: 'secondary',
      },
    },
  },
  spacing: {
    small: {
      value: 8,
      type: 'dimension',
      attributes: {
        category: 'spacing',
        type: 'small',
      },
    },
    medium: {
      value: 16,
      type: 'dimension',
      attributes: {
        category: 'spacing',
        type: 'medium',
      },
    },
  },
  typography: {
    fontSize: {
      small: {
        value: 12,
        type: 'dimension',
        attributes: {
          category: 'typography',
          type: 'fontSize',
          item: 'small',
        },
      },
      medium: {
        value: 16,
        type: 'dimension',
        attributes: {
          category: 'typography',
          type: 'fontSize',
          item: 'medium',
        },
      },
    },
  },
};

// Helper function to create mock token
global.createMockToken = (category: string, type: string, value: any) => ({
  value,
  type: typeof value === 'number' ? 'dimension' : 'color',
  attributes: {
    category,
    type,
  },
  name: `${category}-${type}`,
  path: [category, type],
});

// Helper function to create mock dictionary
global.createMockDictionary = (tokens: any) => ({
  allTokens: Object.values(tokens).flat(),
  tokens,
  getToken: (path: string[]) => {
    let current = tokens;
    for (const segment of path) {
      current = current[segment];
      if (!current) return undefined;
    }
    return current;
  },
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Set up console spies
beforeAll(() => {
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  jest.restoreAllMocks();
});
