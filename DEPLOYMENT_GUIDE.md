# UI Builder Platform - Deployment Guide

This guide provides comprehensive instructions for deploying the UI Builder Platform in various environments.

## 📋 Prerequisites

### System Requirements
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **Memory**: Minimum 16GB RAM, Recommended 32GB+ RAM
- **Storage**: Minimum 100GB SSD, Recommended 500GB+ SSD
- **Network**: Stable internet connection with sufficient bandwidth

### Software Requirements
- **Docker**: Version 20.10+ with Docker Compose
- **Kubernetes**: Version 1.25+ (for production deployment)
- **Helm**: Version 3.10+ (for Kubernetes deployment)
- **kubectl**: Compatible with your Kubernetes cluster

### Infrastructure Requirements
- **Database**: PostgreSQL 15+ with sufficient storage
- **Cache**: Redis 7+ with persistence enabled
- **Message Queue**: Apache Kafka 3.0+ for real-time features
- **Load Balancer**: Nginx, HAProxy, or cloud load balancer
- **SSL Certificates**: Valid SSL certificates for HTTPS

## 🚀 Quick Start Deployment

### 1. Local Development Environment

```bash
# Clone the repository
git clone https://github.com/ui-builder/platform.git
cd platform

# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

**Access Points:**
- UI Builder: http://localhost:3000
- Web Runtime: http://localhost:3001
- API Documentation: http://localhost:8080/swagger-ui.html
- Grafana Dashboard: http://localhost:3002 (admin/admin)

### 2. Production-Ready Docker Deployment

```bash
# Create production environment file
cp .env.example .env.production

# Edit production configuration
nano .env.production

# Deploy with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Verify deployment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
```

## 🏗️ Kubernetes Deployment

### 1. Prepare Kubernetes Cluster

```bash
# Verify cluster access
kubectl cluster-info

# Create namespace
kubectl create namespace ui-builder

# Set default namespace
kubectl config set-context --current --namespace=ui-builder
```

### 2. Configure Secrets and ConfigMaps

```bash
# Create database secret
kubectl create secret generic postgres-secret \
  --from-literal=username=uiplatform \
  --from-literal=password=your-secure-password \
  --from-literal=database=uiplatform

# Create JWT secret
kubectl create secret generic jwt-secret \
  --from-literal=secret=your-jwt-secret-key

# Create Redis secret
kubectl create secret generic redis-secret \
  --from-literal=password=your-redis-password

# Apply ConfigMaps
kubectl apply -f infrastructure/kubernetes/configmaps/
```

### 3. Deploy Infrastructure Services

```bash
# Deploy PostgreSQL
kubectl apply -f infrastructure/kubernetes/postgres/

# Deploy Redis
kubectl apply -f infrastructure/kubernetes/redis/

# Deploy Kafka
kubectl apply -f infrastructure/kubernetes/kafka/

# Wait for services to be ready
kubectl wait --for=condition=ready pod -l app=postgres --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis --timeout=300s
kubectl wait --for=condition=ready pod -l app=kafka --timeout=300s
```

### 4. Deploy Application Services

```bash
# Deploy backend services
kubectl apply -f infrastructure/kubernetes/backend/

# Deploy frontend applications
kubectl apply -f infrastructure/kubernetes/frontend/

# Deploy ingress controller
kubectl apply -f infrastructure/kubernetes/ingress/

# Check deployment status
kubectl get deployments
kubectl get services
kubectl get ingress
```

### 5. Configure Monitoring

```bash
# Deploy Prometheus
kubectl apply -f monitoring/kubernetes/prometheus/

# Deploy Grafana
kubectl apply -f monitoring/kubernetes/grafana/

# Import dashboards
kubectl apply -f monitoring/kubernetes/dashboards/
```

## 🔧 Configuration Management

### Environment Variables

#### Backend Services Configuration
```yaml
# ui-platform-backend
DATABASE_URL: ******************************************
DATABASE_USERNAME: uiplatform
DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
REDIS_HOST: redis
REDIS_PORT: 6379
KAFKA_BOOTSTRAP_SERVERS: kafka:9092
JWT_SECRET: ${JWT_SECRET}
CORS_ALLOWED_ORIGINS: https://app.yourdomain.com
LOG_LEVEL: INFO
SPRING_PROFILES_ACTIVE: production
```

#### Frontend Applications Configuration
```yaml
# ui-builder-frontend
VITE_API_BASE_URL: https://api.yourdomain.com/api/v1
VITE_WEBSOCKET_URL: wss://api.yourdomain.com/ws
VITE_NODE_ENV: production
VITE_ENABLE_ANALYTICS: true
VITE_ENABLE_COLLABORATION: true
```

### SSL/TLS Configuration

#### Let's Encrypt with Cert-Manager
```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer
kubectl apply -f infrastructure/kubernetes/cert-manager/cluster-issuer.yaml

# Certificates will be automatically provisioned via ingress annotations
```

#### Custom SSL Certificates
```bash
# Create TLS secret
kubectl create secret tls ui-builder-tls \
  --cert=path/to/certificate.crt \
  --key=path/to/private.key
```

## 📊 Monitoring and Observability

### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'ui-platform-backend'
    static_configs:
      - targets: ['ui-platform-backend:8080']
    metrics_path: '/actuator/prometheus'
    
  - job_name: 'ui-metadata-service'
    static_configs:
      - targets: ['ui-metadata-service:8080']
    metrics_path: '/actuator/prometheus'
```

### Grafana Dashboards

Pre-configured dashboards are available for:
- **Application Performance**: Response times, error rates, throughput
- **Infrastructure Metrics**: CPU, memory, disk, network usage
- **Business Metrics**: User activity, component usage, collaboration stats
- **Database Performance**: Query performance, connection pools

### Log Aggregation

```bash
# Deploy ELK stack (optional)
kubectl apply -f monitoring/kubernetes/elasticsearch/
kubectl apply -f monitoring/kubernetes/logstash/
kubectl apply -f monitoring/kubernetes/kibana/

# Or use cloud logging solutions
# - AWS CloudWatch
# - Google Cloud Logging
# - Azure Monitor
```

## 🔒 Security Configuration

### Network Security

```yaml
# Network Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ui-builder-network-policy
spec:
  podSelector:
    matchLabels:
      app: ui-platform
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 8080
```

### Pod Security Standards

```yaml
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: ui-builder-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

### RBAC Configuration

```yaml
# Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ui-builder-sa

---
# Role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ui-builder-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]

---
# RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ui-builder-rolebinding
subjects:
- kind: ServiceAccount
  name: ui-builder-sa
roleRef:
  kind: Role
  name: ui-builder-role
  apiGroup: rbac.authorization.k8s.io
```

## 🔄 Backup and Recovery

### Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/uiplatform_backup_$DATE.sql"

# Create backup
kubectl exec -it postgres-0 -- pg_dump -U uiplatform uiplatform > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_FILE.gz s3://your-backup-bucket/postgres/
```

### Application Data Backup

```bash
# Backup uploaded files
kubectl exec -it ui-platform-backend-0 -- tar -czf /tmp/uploads_backup.tar.gz /app/uploads
kubectl cp ui-platform-backend-0:/tmp/uploads_backup.tar.gz ./uploads_backup_$(date +%Y%m%d).tar.gz

# Backup Redis data
kubectl exec -it redis-0 -- redis-cli BGSAVE
kubectl cp redis-0:/data/dump.rdb ./redis_backup_$(date +%Y%m%d).rdb
```

### Disaster Recovery

```bash
# Database restore
kubectl exec -i postgres-0 -- psql -U uiplatform uiplatform < backup_file.sql

# Application data restore
kubectl cp uploads_backup.tar.gz ui-platform-backend-0:/tmp/
kubectl exec -it ui-platform-backend-0 -- tar -xzf /tmp/uploads_backup.tar.gz -C /

# Redis restore
kubectl cp redis_backup.rdb redis-0:/data/dump.rdb
kubectl exec -it redis-0 -- redis-cli DEBUG RESTART
```

## 🚀 Scaling and Performance

### Horizontal Pod Autoscaling

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ui-platform-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ui-platform-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Scaling

```yaml
# PostgreSQL with read replicas
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
spec:
  instances: 3
  primaryUpdateStrategy: unsupervised
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
```

### Redis Clustering

```yaml
# Redis Cluster
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: redis-cluster
spec:
  clusterSize: 6
  redisExporter:
    enabled: true
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database connectivity
kubectl exec -it ui-platform-backend-0 -- nc -zv postgres 5432

# Check database logs
kubectl logs postgres-0

# Verify credentials
kubectl get secret postgres-secret -o yaml
```

#### 2. Redis Connection Issues
```bash
# Test Redis connectivity
kubectl exec -it ui-platform-backend-0 -- redis-cli -h redis ping

# Check Redis logs
kubectl logs redis-0

# Monitor Redis performance
kubectl exec -it redis-0 -- redis-cli info
```

#### 3. Kafka Issues
```bash
# Check Kafka topics
kubectl exec -it kafka-0 -- kafka-topics.sh --bootstrap-server localhost:9092 --list

# Monitor consumer lag
kubectl exec -it kafka-0 -- kafka-consumer-groups.sh --bootstrap-server localhost:9092 --describe --all-groups
```

#### 4. Application Startup Issues
```bash
# Check application logs
kubectl logs ui-platform-backend-0 -f

# Check resource usage
kubectl top pods

# Describe pod for events
kubectl describe pod ui-platform-backend-0
```

### Performance Tuning

#### JVM Tuning for Backend Services
```yaml
env:
- name: JAVA_OPTS
  value: "-XX:+UseG1GC -XX:MaxRAMPercentage=75.0 -XX:+UseStringDeduplication"
```

#### Database Performance
```sql
-- Optimize PostgreSQL settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

## 📞 Support and Maintenance

### Health Checks

```bash
# Check all services
kubectl get pods -o wide

# Check service endpoints
kubectl get endpoints

# Test application health
curl -f http://your-domain.com/api/v1/actuator/health
```

### Log Analysis

```bash
# Application logs
kubectl logs -f deployment/ui-platform-backend

# System logs
kubectl logs -f -l app=nginx-ingress

# Aggregated logs with stern
stern ui-platform
```

### Maintenance Windows

```bash
# Graceful shutdown
kubectl scale deployment ui-platform-backend --replicas=0

# Perform maintenance
# ...

# Scale back up
kubectl scale deployment ui-platform-backend --replicas=3
```

---

For additional support, please refer to:
- [Documentation](https://docs.uibuilder.dev)
- [GitHub Issues](https://github.com/ui-builder/platform/issues)
- [Support Email](mailto:<EMAIL>)
