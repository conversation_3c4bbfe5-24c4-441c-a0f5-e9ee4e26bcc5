package com.uibuilder.gateway.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.function.Function;

/**
 * JWT Service for token validation and claims extraction
 * 
 * This service handles JWT token validation, parsing, and claims extraction
 * for the API Gateway authentication filter.
 */
@Service
public class JwtService {

    @Value("${jwt.secret:ui-builder-default-secret-key-change-in-production}")
    private String jwtSecret;

    @Value("${jwt.expiration:86400000}") // 24 hours in milliseconds
    private Long jwtExpiration;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    /**
     * Extract username from JWT token
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * Extract user ID from JWT token
     */
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    /**
     * Extract user email from JWT token
     */
    public String extractUserEmail(String token) {
        return extractClaim(token, claims -> claims.get("email", String.class));
    }

    /**
     * Extract user role from JWT token
     */
    public String extractUserRole(String token) {
        return extractClaim(token, claims -> claims.get("role", String.class));
    }

    /**
     * Extract organization ID from JWT token
     */
    public String extractOrganizationId(String token) {
        return extractClaim(token, claims -> claims.get("organizationId", String.class));
    }

    /**
     * Extract expiration date from JWT token
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * Extract a specific claim from JWT token
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * Extract all claims from JWT token
     */
    private Claims extractAllClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * Check if JWT token is expired
     */
    private Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * Validate JWT token
     */
    public Boolean isTokenValid(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validate JWT token against a specific username
     */
    public Boolean isTokenValid(String token, String username) {
        final String extractedUsername = extractUsername(token);
        return (extractedUsername.equals(username) && !isTokenExpired(token));
    }

    /**
     * Check if user has required role
     */
    public Boolean hasRole(String token, String requiredRole) {
        try {
            String userRole = extractUserRole(token);
            return userRole != null && userRole.equals(requiredRole);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if user has any of the required roles
     */
    public Boolean hasAnyRole(String token, String... requiredRoles) {
        try {
            String userRole = extractUserRole(token);
            if (userRole == null) {
                return false;
            }
            
            for (String role : requiredRoles) {
                if (userRole.equals(role)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if user belongs to specific organization
     */
    public Boolean belongsToOrganization(String token, String organizationId) {
        try {
            String userOrgId = extractOrganizationId(token);
            return userOrgId != null && userOrgId.equals(organizationId);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get token type (access or refresh)
     */
    public String getTokenType(String token) {
        return extractClaim(token, claims -> claims.get("type", String.class));
    }

    /**
     * Check if token is an access token
     */
    public Boolean isAccessToken(String token) {
        try {
            String tokenType = getTokenType(token);
            return "access".equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if token is a refresh token
     */
    public Boolean isRefreshToken(String token) {
        try {
            String tokenType = getTokenType(token);
            return "refresh".equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Extract permissions from JWT token
     */
    @SuppressWarnings("unchecked")
    public java.util.List<String> extractPermissions(String token) {
        return extractClaim(token, claims -> claims.get("permissions", java.util.List.class));
    }

    /**
     * Check if user has specific permission
     */
    public Boolean hasPermission(String token, String permission) {
        try {
            java.util.List<String> permissions = extractPermissions(token);
            return permissions != null && permissions.contains(permission);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if user has any of the required permissions
     */
    public Boolean hasAnyPermission(String token, String... requiredPermissions) {
        try {
            java.util.List<String> userPermissions = extractPermissions(token);
            if (userPermissions == null) {
                return false;
            }
            
            for (String permission : requiredPermissions) {
                if (userPermissions.contains(permission)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get remaining time until token expires (in seconds)
     */
    public Long getTimeUntilExpiration(String token) {
        try {
            Date expiration = extractExpiration(token);
            Date now = new Date();
            return (expiration.getTime() - now.getTime()) / 1000;
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * Check if token will expire within specified minutes
     */
    public Boolean willExpireWithin(String token, int minutes) {
        try {
            Long secondsUntilExpiration = getTimeUntilExpiration(token);
            return secondsUntilExpiration <= (minutes * 60);
        } catch (Exception e) {
            return true;
        }
    }
}
