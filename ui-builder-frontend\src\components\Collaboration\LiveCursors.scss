.live-cursors {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.live-cursor {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1001;
  transition: transform 0.1s ease-out;
  color: var(--user-color, #1890ff);

  .cursor-pointer {
    position: relative;
    z-index: 1002;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    animation: cursorPulse 2s infinite;
  }

  .cursor-label {
    position: absolute;
    top: 20px;
    left: 8px;
    background: var(--user-color, #1890ff);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1003;
    opacity: 0;
    transform: translateY(-4px);
    animation: labelFadeIn 0.3s ease-out 0.5s forwards;

    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 8px;
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 4px solid var(--user-color, #1890ff);
    }

    .cursor-name {
      font-weight: 600;
    }

    .cursor-element {
      opacity: 0.8;
      margin-left: 4px;
      font-style: italic;
    }
  }

  // Hover effect to show label immediately
  &:hover .cursor-label {
    opacity: 1;
    transform: translateY(0);
    animation: none;
  }
}

// Animations
@keyframes cursorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes labelFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .live-cursor {
    .cursor-pointer {
      width: 16px;
      height: 16px;
    }

    .cursor-label {
      top: 16px;
      left: 6px;
      font-size: 10px;
      padding: 3px 6px;
      border-radius: 10px;

      &::before {
        left: 6px;
        border-left-width: 3px;
        border-right-width: 3px;
        border-bottom-width: 3px;
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .live-cursor {
    .cursor-pointer {
      stroke-width: 2;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    }

    .cursor-label {
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .live-cursor {
    transition: none;

    .cursor-pointer {
      animation: none;
    }

    .cursor-label {
      animation: none;
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// Dark theme adjustments
.dark-theme {
  .live-cursor {
    .cursor-pointer {
      stroke: #000;
      filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
    }

    .cursor-label {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
  }
}

// Print styles
@media print {
  .live-cursors {
    display: none;
  }
}
