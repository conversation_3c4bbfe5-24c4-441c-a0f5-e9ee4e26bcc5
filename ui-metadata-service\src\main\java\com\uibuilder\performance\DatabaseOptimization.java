package com.uibuilder.performance;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class DatabaseOptimizationService {

    @PersistenceContext
    private EntityManager entityManager;
    
    private final DataSource dataSource;
    private final QueryPerformanceMonitor queryMonitor;

    /**
     * Optimized UI Config queries with proper indexing
     */
    @Query(value = """
        SELECT uc.* FROM ui_configs uc 
        WHERE uc.workspace_id = :workspaceId 
        AND uc.is_active = true 
        AND uc.created_at >= :since
        ORDER BY uc.updated_at DESC
        """, nativeQuery = true)
    List<UIConfig> findActiveUIConfigsByWorkspace(String workspaceId, LocalDateTime since);

    /**
     * Batch fetch UI configs with related data to avoid N+1 queries
     */
    @Query("""
        SELECT uc FROM UIConfig uc 
        LEFT JOIN FETCH uc.components c
        LEFT JOIN FETCH c.properties
        WHERE uc.id IN :configIds
        """)
    List<UIConfig> findUIConfigsWithComponents(List<String> configIds);

    /**
     * Optimized template search with full-text search
     */
    @Query(value = """
        SELECT t.*, ts_rank(to_tsvector('english', t.name || ' ' || t.description), 
                           plainto_tsquery('english', :searchTerm)) as rank
        FROM templates t 
        WHERE to_tsvector('english', t.name || ' ' || t.description) @@ plainto_tsquery('english', :searchTerm)
        AND t.is_public = true
        ORDER BY rank DESC, t.usage_count DESC
        LIMIT :limit
        """, nativeQuery = true)
    List<Template> searchTemplatesFullText(String searchTerm, int limit);

    /**
     * Efficient pagination with cursor-based approach
     */
    @Query("""
        SELECT uc FROM UIConfig uc 
        WHERE uc.workspace.id = :workspaceId 
        AND uc.createdAt < :cursor
        ORDER BY uc.createdAt DESC
        """)
    List<UIConfig> findUIConfigsAfterCursor(String workspaceId, LocalDateTime cursor, Pageable pageable);

    /**
     * Bulk operations for better performance
     */
    @Modifying
    @Query("""
        UPDATE UIConfig uc SET uc.lastAccessedAt = :accessTime 
        WHERE uc.id IN :configIds
        """)
    int updateLastAccessedBatch(List<String> configIds, LocalDateTime accessTime);

    /**
     * Optimized aggregation queries
     */
    @Query(value = """
        SELECT 
            DATE_TRUNC('day', created_at) as date,
            COUNT(*) as count,
            COUNT(DISTINCT created_by) as unique_users
        FROM ui_configs 
        WHERE workspace_id = :workspaceId 
        AND created_at >= :startDate
        GROUP BY DATE_TRUNC('day', created_at)
        ORDER BY date DESC
        """, nativeQuery = true)
    List<Object[]> getUIConfigCreationStats(String workspaceId, LocalDateTime startDate);

    /**
     * Database connection pool monitoring
     */
    @Component
    public static class DatabaseHealthIndicator implements HealthIndicator {
        
        private final DataSource dataSource;
        
        @Override
        public Health health() {
            try (Connection connection = dataSource.getConnection()) {
                // Check connection validity
                if (!connection.isValid(5)) {
                    return Health.down().withDetail("connection", "invalid").build();
                }
                
                // Check query performance
                long startTime = System.currentTimeMillis();
                try (PreparedStatement stmt = connection.prepareStatement("SELECT 1")) {
                    stmt.executeQuery();
                }
                long queryTime = System.currentTimeMillis() - startTime;
                
                Health.Builder builder = Health.up()
                    .withDetail("queryTime", queryTime + "ms");
                
                // Add connection pool details if available
                if (dataSource instanceof HikariDataSource) {
                    HikariDataSource hikariDS = (HikariDataSource) dataSource;
                    HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                    
                    builder.withDetail("activeConnections", poolBean.getActiveConnections())
                           .withDetail("idleConnections", poolBean.getIdleConnections())
                           .withDetail("totalConnections", poolBean.getTotalConnections())
                           .withDetail("threadsAwaitingConnection", poolBean.getThreadsAwaitingConnection());
                }
                
                return builder.build();
                
            } catch (Exception e) {
                return Health.down().withException(e).build();
            }
        }
    }

    /**
     * Query performance monitoring
     */
    @Component
    @Slf4j
    public static class QueryPerformanceMonitor {
        
        private final MeterRegistry meterRegistry;
        private final Map<String, Timer> queryTimers = new ConcurrentHashMap<>();
        
        public void recordQueryExecution(String queryName, long executionTime) {
            Timer timer = queryTimers.computeIfAbsent(queryName, 
                name -> Timer.builder("database.query.duration")
                    .tag("query", name)
                    .register(meterRegistry));
            
            timer.record(executionTime, TimeUnit.MILLISECONDS);
            
            // Log slow queries
            if (executionTime > 1000) { // Queries taking more than 1 second
                log.warn("Slow query detected: {} took {}ms", queryName, executionTime);
            }
        }
        
        public void recordQueryError(String queryName, Exception error) {
            Counter.builder("database.query.errors")
                .tag("query", queryName)
                .tag("error", error.getClass().getSimpleName())
                .register(meterRegistry)
                .increment();
        }
    }

    /**
     * Database maintenance tasks
     */
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    @Transactional
    public void performDatabaseMaintenance() {
        log.info("Starting database maintenance tasks");
        
        try {
            // Update table statistics
            updateTableStatistics();
            
            // Reindex frequently used tables
            reindexTables();
            
            // Clean up old audit logs
            cleanupOldAuditLogs();
            
            // Analyze query performance
            analyzeQueryPerformance();
            
            log.info("Database maintenance completed successfully");
            
        } catch (Exception e) {
            log.error("Database maintenance failed", e);
        }
    }

    private void updateTableStatistics() {
        List<String> tables = Arrays.asList(
            "ui_configs", "templates", "components", "audit_logs", "users"
        );
        
        tables.forEach(table -> {
            try {
                entityManager.createNativeQuery("ANALYZE " + table).executeUpdate();
                log.debug("Updated statistics for table: {}", table);
            } catch (Exception e) {
                log.warn("Failed to update statistics for table: {}", table, e);
            }
        });
    }

    private void reindexTables() {
        // Reindex tables with high write activity
        List<String> indexes = Arrays.asList(
            "idx_ui_configs_workspace_created",
            "idx_templates_search",
            "idx_audit_logs_user_timestamp"
        );
        
        indexes.forEach(index -> {
            try {
                entityManager.createNativeQuery("REINDEX INDEX " + index).executeUpdate();
                log.debug("Reindexed: {}", index);
            } catch (Exception e) {
                log.warn("Failed to reindex: {}", index, e);
            }
        });
    }

    private void cleanupOldAuditLogs() {
        // Keep audit logs for 1 year
        LocalDateTime cutoffDate = LocalDateTime.now().minusYears(1);
        
        int deletedCount = entityManager.createQuery(
            "DELETE FROM AuditLog a WHERE a.timestamp < :cutoffDate")
            .setParameter("cutoffDate", cutoffDate)
            .executeUpdate();
        
        log.info("Cleaned up {} old audit log entries", deletedCount);
    }

    private void analyzeQueryPerformance() {
        // Get slow queries from PostgreSQL
        String slowQuerySql = """
            SELECT query, calls, total_time, mean_time, rows
            FROM pg_stat_statements 
            WHERE mean_time > 100 
            ORDER BY mean_time DESC 
            LIMIT 10
            """;
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(slowQuerySql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String query = rs.getString("query");
                long calls = rs.getLong("calls");
                double totalTime = rs.getDouble("total_time");
                double meanTime = rs.getDouble("mean_time");
                
                log.warn("Slow query detected - Mean time: {}ms, Calls: {}, Query: {}", 
                        meanTime, calls, query.substring(0, Math.min(100, query.length())));
            }
            
        } catch (Exception e) {
            log.warn("Failed to analyze query performance", e);
        }
    }

    /**
     * Async database operations for better performance
     */
    @Async
    public CompletableFuture<List<UIConfig>> findUIConfigsAsync(String workspaceId) {
        return CompletableFuture.supplyAsync(() -> {
            return uiConfigRepository.findByWorkspaceIdAndIsActiveTrue(workspaceId);
        });
    }

    @Async
    public CompletableFuture<Void> updateUIConfigAsync(String configId, Map<String, Object> updates) {
        return CompletableFuture.runAsync(() -> {
            UIConfig config = uiConfigRepository.findById(configId)
                .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));
            
            // Apply updates
            updates.forEach((key, value) -> {
                try {
                    BeanUtils.setProperty(config, key, value);
                } catch (Exception e) {
                    log.warn("Failed to set property {} to {}", key, value, e);
                }
            });
            
            config.setUpdatedAt(LocalDateTime.now());
            uiConfigRepository.save(config);
        });
    }

    /**
     * Database connection pool optimization
     */
    @EventListener
    public void optimizeConnectionPool(ApplicationReadyEvent event) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDS = (HikariDataSource) dataSource;
            
            // Optimize pool settings based on application load
            int corePoolSize = Runtime.getRuntime().availableProcessors();
            hikariDS.setMaximumPoolSize(corePoolSize * 2);
            hikariDS.setMinimumIdle(corePoolSize);
            hikariDS.setConnectionTimeout(30000);
            hikariDS.setIdleTimeout(600000);
            hikariDS.setMaxLifetime(1800000);
            
            log.info("Optimized database connection pool - Max: {}, Min: {}", 
                    hikariDS.getMaximumPoolSize(), hikariDS.getMinimumIdle());
        }
    }

    /**
     * Read replica support for read-heavy operations
     */
    @Transactional(readOnly = true)
    public List<Template> findTemplatesReadOnly(String workspaceId) {
        // This would route to read replica if configured
        return templateRepository.findByWorkspaceIdOrderByUsageCountDesc(workspaceId);
    }

    /**
     * Database partitioning for large tables
     */
    @PostConstruct
    public void setupPartitioning() {
        // Create partitions for audit logs by month
        try {
            String createPartitionSql = """
                CREATE TABLE IF NOT EXISTS audit_logs_y%d_m%02d 
                PARTITION OF audit_logs 
                FOR VALUES FROM ('%s') TO ('%s')
                """;
            
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime startOfNextMonth = startOfMonth.plusMonths(1);
            
            String sql = String.format(createPartitionSql,
                now.getYear(), now.getMonthValue(),
                startOfMonth.toString(), startOfNextMonth.toString());
            
            entityManager.createNativeQuery(sql).executeUpdate();
            
            log.info("Created audit log partition for {}-{:02d}", now.getYear(), now.getMonthValue());
            
        } catch (Exception e) {
            log.warn("Failed to create partition", e);
        }
    }
}
