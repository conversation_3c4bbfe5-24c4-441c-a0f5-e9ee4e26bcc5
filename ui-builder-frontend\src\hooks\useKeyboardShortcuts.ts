import { useEffect } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useAppDispatch, useAppSelector } from '@store/index';
import {
  undo,
  redo,
  copyComponents,
  cutComponents,
  pasteComponents,
  deleteComponent,
  duplicateComponent,
  selectMultipleComponents,
  clearSelection,
  toggleGrid,
  togglePreviewMode,
  setCanvasZoom,
} from '@store/slices/uiBuilderSlice';

export const useKeyboardShortcuts = () => {
  const dispatch = useAppDispatch();
  const { 
    selection, 
    canvas, 
    currentConfiguration,
    history 
  } = useAppSelector(state => state.uiBuilder);

  // Undo/Redo
  useHotkeys('ctrl+z, cmd+z', (e) => {
    e.preventDefault();
    if (history.past.length > 0) {
      dispatch(undo());
    }
  }, { enableOnFormTags: true });

  useHotkeys('ctrl+y, cmd+y, ctrl+shift+z, cmd+shift+z', (e) => {
    e.preventDefault();
    if (history.future.length > 0) {
      dispatch(redo());
    }
  }, { enableOnFormTags: true });

  // Copy/Cut/Paste
  useHotkeys('ctrl+c, cmd+c', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      dispatch(copyComponents(selection.selectedComponentIds));
    }
  });

  useHotkeys('ctrl+x, cmd+x', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      dispatch(cutComponents(selection.selectedComponentIds));
    }
  });

  useHotkeys('ctrl+v, cmd+v', (e) => {
    e.preventDefault();
    dispatch(pasteComponents({}));
  });

  // Delete
  useHotkeys('delete, backspace', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      selection.selectedComponentIds.forEach(id => {
        dispatch(deleteComponent(id));
      });
    }
  });

  // Duplicate
  useHotkeys('ctrl+d, cmd+d', (e) => {
    if (selection.selectedComponentIds.length === 1) {
      e.preventDefault();
      dispatch(duplicateComponent(selection.selectedComponentIds[0]));
    }
  });

  // Select All
  useHotkeys('ctrl+a, cmd+a', (e) => {
    if (currentConfiguration) {
      e.preventDefault();
      const allComponentIds = getAllComponentIds(currentConfiguration.components);
      dispatch(selectMultipleComponents(allComponentIds));
    }
  });

  // Deselect All
  useHotkeys('escape', (e) => {
    e.preventDefault();
    dispatch(clearSelection());
  });

  // Canvas controls
  useHotkeys('ctrl+0, cmd+0', (e) => {
    e.preventDefault();
    dispatch(setCanvasZoom(1)); // Reset zoom to 100%
  });

  useHotkeys('ctrl+plus, cmd+plus, ctrl+=, cmd+=', (e) => {
    e.preventDefault();
    dispatch(setCanvasZoom(Math.min(canvas.zoom * 1.2, 5)));
  });

  useHotkeys('ctrl+minus, cmd+minus', (e) => {
    e.preventDefault();
    dispatch(setCanvasZoom(Math.max(canvas.zoom * 0.8, 0.1)));
  });

  // Toggle features
  useHotkeys('ctrl+g, cmd+g', (e) => {
    e.preventDefault();
    dispatch(toggleGrid());
  });

  useHotkeys('ctrl+shift+p, cmd+shift+p', (e) => {
    e.preventDefault();
    dispatch(togglePreviewMode());
  });

  // Save (handled by parent component)
  useHotkeys('ctrl+s, cmd+s', (e) => {
    e.preventDefault();
    // Dispatch save action or emit event
    window.dispatchEvent(new CustomEvent('keyboard-save'));
  });

  // Arrow keys for moving components
  useHotkeys('up', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(0, -1);
    }
  });

  useHotkeys('down', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(0, 1);
    }
  });

  useHotkeys('left', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(-1, 0);
    }
  });

  useHotkeys('right', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(1, 0);
    }
  });

  // Arrow keys with shift for larger movements
  useHotkeys('shift+up', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(0, -10);
    }
  });

  useHotkeys('shift+down', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(0, 10);
    }
  });

  useHotkeys('shift+left', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(-10, 0);
    }
  });

  useHotkeys('shift+right', (e) => {
    if (selection.selectedComponentIds.length > 0) {
      e.preventDefault();
      moveSelectedComponents(10, 0);
    }
  });

  // Helper function to move selected components
  const moveSelectedComponents = (deltaX: number, deltaY: number) => {
    if (!currentConfiguration) return;

    selection.selectedComponentIds.forEach(componentId => {
      const component = findComponentById(currentConfiguration.components, componentId);
      if (component) {
        const newPosition = {
          x: component.position.x + deltaX,
          y: component.position.y + deltaY,
        };

        // Snap to grid if enabled
        if (canvas.snapToGrid) {
          newPosition.x = Math.round(newPosition.x / canvas.gridSize) * canvas.gridSize;
          newPosition.y = Math.round(newPosition.y / canvas.gridSize) * canvas.gridSize;
        }

        dispatch({
          type: 'uiBuilder/updateComponent',
          payload: {
            id: componentId,
            updates: { position: newPosition },
          },
        });
      }
    });
  };

  // Prevent default browser shortcuts that might interfere
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent browser zoom
      if ((e.ctrlKey || e.metaKey) && (e.key === '+' || e.key === '-' || e.key === '0')) {
        if (document.activeElement?.tagName !== 'INPUT' && 
            document.activeElement?.tagName !== 'TEXTAREA') {
          e.preventDefault();
        }
      }

      // Prevent browser refresh
      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
      }

      // Prevent browser find
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        if (document.activeElement?.tagName !== 'INPUT' && 
            document.activeElement?.tagName !== 'TEXTAREA') {
          e.preventDefault();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
};

// Helper function to get all component IDs recursively
function getAllComponentIds(components: any[]): string[] {
  const ids: string[] = [];
  
  function traverse(comps: any[]) {
    comps.forEach(comp => {
      ids.push(comp.id);
      if (comp.children) {
        traverse(comp.children);
      }
    });
  }
  
  traverse(components);
  return ids;
}

// Helper function to find component by ID
function findComponentById(components: any[], id: string): any {
  for (const component of components) {
    if (component.id === id) {
      return component;
    }
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }
  return null;
}
