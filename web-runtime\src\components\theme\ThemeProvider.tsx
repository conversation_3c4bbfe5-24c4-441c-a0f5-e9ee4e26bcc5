import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { ThemeConfiguration, ColorScale } from '@types/index';
import { useRuntimeStore } from '@stores/runtimeStore';

interface ThemeContextValue {
  theme: ThemeConfiguration | null;
  setTheme: (theme: ThemeConfiguration) => void;
  applyTheme: (theme: ThemeConfiguration) => void;
  resetTheme: () => void;
}

const ThemeContext = createContext<ThemeContextValue | null>(null);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeConfiguration;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme,
}) => {
  const { theme, setTheme: setStoreTheme } = useRuntimeStore();

  // Apply theme to CSS variables
  const applyTheme = useMemo(() => {
    return (themeConfig: ThemeConfiguration) => {
      const root = document.documentElement;
      
      // Apply color palette
      applyColorPalette(root, themeConfig.colors);
      
      // Apply typography
      applyTypography(root, themeConfig.typography);
      
      // Apply spacing
      applySpacing(root, themeConfig.spacing);
      
      // Apply shadows
      applyShadows(root, themeConfig.shadows);
      
      // Apply borders
      applyBorders(root, themeConfig.borders);
      
      // Apply animations
      applyAnimations(root, themeConfig.animations);
      
      // Update store
      setStoreTheme(themeConfig);
    };
  }, [setStoreTheme]);

  // Reset theme to default
  const resetTheme = useMemo(() => {
    return () => {
      if (defaultTheme) {
        applyTheme(defaultTheme);
      }
    };
  }, [defaultTheme, applyTheme]);

  // Apply theme when it changes
  useEffect(() => {
    if (theme) {
      applyTheme(theme);
    } else if (defaultTheme) {
      applyTheme(defaultTheme);
    }
  }, [theme, defaultTheme, applyTheme]);

  const contextValue: ThemeContextValue = {
    theme: theme || defaultTheme || null,
    setTheme: setStoreTheme,
    applyTheme,
    resetTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Helper functions to apply theme properties to CSS variables
function applyColorPalette(root: HTMLElement, colors: ThemeConfiguration['colors']) {
  // Apply primary colors
  applyColorScale(root, 'primary', colors.primary);
  applyColorScale(root, 'secondary', colors.secondary);
  applyColorScale(root, 'accent', colors.accent);
  applyColorScale(root, 'surface', colors.surface);
  applyColorScale(root, 'success', colors.success);
  applyColorScale(root, 'warning', colors.warning);
  applyColorScale(root, 'error', colors.error);
  applyColorScale(root, 'info', colors.info);
}

function applyColorScale(root: HTMLElement, name: string, scale: ColorScale) {
  Object.entries(scale).forEach(([shade, color]) => {
    const rgb = hexToRgb(color);
    if (rgb) {
      root.style.setProperty(`--color-${name}-${shade}`, `${rgb.r} ${rgb.g} ${rgb.b}`);
    }
  });
}

function applyTypography(root: HTMLElement, typography: ThemeConfiguration['typography']) {
  // Font families
  Object.entries(typography.fontFamilies).forEach(([key, value]) => {
    root.style.setProperty(`--font-family-${key}`, value);
  });

  // Font sizes
  Object.entries(typography.fontSizes).forEach(([key, value]) => {
    root.style.setProperty(`--font-size-${key}`, value);
  });

  // Line heights
  Object.entries(typography.lineHeights).forEach(([key, value]) => {
    root.style.setProperty(`--line-height-${key}`, value);
  });

  // Font weights
  Object.entries(typography.fontWeights).forEach(([key, value]) => {
    root.style.setProperty(`--font-weight-${key}`, value.toString());
  });
}

function applySpacing(root: HTMLElement, spacing: ThemeConfiguration['spacing']) {
  Object.entries(spacing).forEach(([key, value]) => {
    root.style.setProperty(`--spacing-${key}`, value);
  });
}

function applyShadows(root: HTMLElement, shadows: ThemeConfiguration['shadows']) {
  Object.entries(shadows).forEach(([key, value]) => {
    root.style.setProperty(`--shadow-${key}`, value);
  });
}

function applyBorders(root: HTMLElement, borders: ThemeConfiguration['borders']) {
  Object.entries(borders).forEach(([key, value]) => {
    root.style.setProperty(`--border-radius-${key}`, value);
  });
}

function applyAnimations(root: HTMLElement, animations: ThemeConfiguration['animations']) {
  // Animation durations
  Object.entries(animations.durations).forEach(([key, value]) => {
    root.style.setProperty(`--animation-duration-${key}`, value);
  });

  // Animation easings
  Object.entries(animations.easings).forEach(([key, value]) => {
    root.style.setProperty(`--animation-easing-${key}`, value);
  });
}

// Utility function to convert hex to RGB
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// Default theme configuration
export const defaultTheme: ThemeConfiguration = {
  id: 'default',
  name: 'Default Theme',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
    accent: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    surface: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a',
    },
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
  },
  typography: {
    fontFamilies: {
      sans: 'Inter, system-ui, -apple-system, sans-serif',
      serif: 'Georgia, serif',
      mono: 'JetBrains Mono, Fira Code, monospace',
    },
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
    },
    lineHeights: {
      xs: '1rem',
      sm: '1.25rem',
      base: '1.5rem',
      lg: '1.75rem',
      xl: '1.75rem',
      '2xl': '2rem',
      '3xl': '2.25rem',
      '4xl': '2.5rem',
      '5xl': '1',
      '6xl': '1',
    },
    fontWeights: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '2.5rem',
    '3xl': '3rem',
    '4xl': '4rem',
    '5xl': '5rem',
    '6xl': '6rem',
  },
  shadows: {
    xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  },
  borders: {
    xs: '0.125rem',
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
  },
  animations: {
    durations: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easings: {
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};
