package com.uiplatform.repository;

import com.uiplatform.entity.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Permission entity.
 * Provides CRUD operations and custom queries for permission management.
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, UUID>, JpaSpecificationExecutor<Permission> {

    /**
     * Find permission by name.
     */
    Optional<Permission> findByNameAndDeletedFalse(String name);

    /**
     * Find permissions by resource.
     */
    List<Permission> findByResourceAndDeletedFalse(String resource);

    /**
     * Find permissions by action.
     */
    List<Permission> findByActionAndDeletedFalse(String action);

    /**
     * Find permissions by resource and action.
     */
    Optional<Permission> findByResourceAndActionAndDeletedFalse(String resource, String action);

    /**
     * Find permissions by scope.
     */
    List<Permission> findByScopeAndDeletedFalse(Permission.PermissionScope scope);

    /**
     * Find permissions by role.
     */
    @Query("SELECT p FROM Permission p JOIN p.roles r WHERE r.id = :roleId AND p.deleted = false")
    List<Permission> findByRoleId(@Param("roleId") UUID roleId);

    /**
     * Find permissions by user (through roles).
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r JOIN r.users u WHERE u.id = :userId AND p.deleted = false")
    List<Permission> findByUserId(@Param("userId") UUID userId);

    /**
     * Search permissions by name or description.
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "(LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND p.deleted = false")
    Page<Permission> searchPermissions(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find permissions with role count.
     */
    @Query("SELECT p, COUNT(r) as roleCount FROM Permission p LEFT JOIN p.roles r WHERE p.deleted = false GROUP BY p")
    List<Object[]> findPermissionsWithRoleCount();

    /**
     * Check if permission name exists (excluding current permission).
     */
    @Query("SELECT COUNT(p) > 0 FROM Permission p WHERE p.name = :name AND p.id != :excludeId AND p.deleted = false")
    boolean existsByNameAndIdNot(@Param("name") String name, @Param("excludeId") UUID excludeId);

    /**
     * Check if resource-action combination exists (excluding current permission).
     */
    @Query("SELECT COUNT(p) > 0 FROM Permission p WHERE p.resource = :resource AND p.action = :action AND p.id != :excludeId AND p.deleted = false")
    boolean existsByResourceAndActionAndIdNot(@Param("resource") String resource, 
                                            @Param("action") String action, 
                                            @Param("excludeId") UUID excludeId);

    /**
     * Find permissions by multiple criteria.
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "(:resource IS NULL OR p.resource = :resource) AND " +
           "(:action IS NULL OR p.action = :action) AND " +
           "(:scope IS NULL OR p.scope = :scope) AND " +
           "p.deleted = false")
    Page<Permission> findPermissionsByCriteria(@Param("resource") String resource,
                                             @Param("action") String action,
                                             @Param("scope") Permission.PermissionScope scope,
                                             Pageable pageable);

    /**
     * Count permissions by resource.
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.resource = :resource AND p.deleted = false")
    Long countByResource(@Param("resource") String resource);

    /**
     * Count permissions by scope.
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.scope = :scope AND p.deleted = false")
    Long countByScope(@Param("scope") Permission.PermissionScope scope);

    /**
     * Find most used permissions.
     */
    @Query("SELECT p, COUNT(r) as roleCount FROM Permission p LEFT JOIN p.roles r " +
           "WHERE p.deleted = false GROUP BY p ORDER BY roleCount DESC")
    List<Object[]> findMostUsedPermissions(Pageable pageable);

    /**
     * Find permissions by resource with pagination.
     */
    Page<Permission> findByResourceAndDeletedFalse(String resource, Pageable pageable);

    /**
     * Find permissions by scope with pagination.
     */
    Page<Permission> findByScopeAndDeletedFalse(Permission.PermissionScope scope, Pageable pageable);

    /**
     * Find all unique resources.
     */
    @Query("SELECT DISTINCT p.resource FROM Permission p WHERE p.deleted = false ORDER BY p.resource")
    List<String> findAllResources();

    /**
     * Find all unique actions.
     */
    @Query("SELECT DISTINCT p.action FROM Permission p WHERE p.deleted = false ORDER BY p.action")
    List<String> findAllActions();
}
