import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

/// Platform Service for Flutter UI Runtime
/// 
/// Provides native platform integrations including:
/// - Device information and capabilities
/// - Platform-specific features
/// - Permission management
/// - File system access
/// - Native UI components
/// - Deep linking and URL handling

class PlatformService {
  static const MethodChannel _channel = MethodChannel('ui_builder_platform');
  
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  /// Device Information
  
  /// Get device information
  Future<DeviceInfo> getDeviceInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return DeviceInfo(
          platform: 'android',
          model: androidInfo.model,
          manufacturer: androidInfo.manufacturer,
          version: androidInfo.version.release,
          sdkVersion: androidInfo.version.sdkInt.toString(),
          appVersion: packageInfo.version,
          appBuildNumber: packageInfo.buildNumber,
          isPhysicalDevice: androidInfo.isPhysicalDevice,
          screenSize: await _getScreenSize(),
          capabilities: await _getAndroidCapabilities(androidInfo),
        );
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return DeviceInfo(
          platform: 'ios',
          model: iosInfo.model,
          manufacturer: 'Apple',
          version: iosInfo.systemVersion,
          sdkVersion: iosInfo.systemVersion,
          appVersion: packageInfo.version,
          appBuildNumber: packageInfo.buildNumber,
          isPhysicalDevice: iosInfo.isPhysicalDevice,
          screenSize: await _getScreenSize(),
          capabilities: await _getIOSCapabilities(iosInfo),
        );
      } else {
        return DeviceInfo(
          platform: Platform.operatingSystem,
          model: 'Unknown',
          manufacturer: 'Unknown',
          version: Platform.operatingSystemVersion,
          sdkVersion: 'Unknown',
          appVersion: packageInfo.version,
          appBuildNumber: packageInfo.buildNumber,
          isPhysicalDevice: true,
          screenSize: await _getScreenSize(),
          capabilities: [],
        );
      }
    } catch (e) {
      throw PlatformException('Failed to get device info: $e');
    }
  }

  Future<ScreenSize> _getScreenSize() async {
    try {
      final result = await _channel.invokeMethod('getScreenSize');
      return ScreenSize(
        width: result['width']?.toDouble() ?? 0.0,
        height: result['height']?.toDouble() ?? 0.0,
        density: result['density']?.toDouble() ?? 1.0,
      );
    } catch (e) {
      return ScreenSize(width: 0, height: 0, density: 1.0);
    }
  }

  Future<List<String>> _getAndroidCapabilities(AndroidDeviceInfo info) async {
    final capabilities = <String>[];
    
    // Add Android-specific capabilities
    if (info.version.sdkInt >= 21) capabilities.add('material_design');
    if (info.version.sdkInt >= 23) capabilities.add('runtime_permissions');
    if (info.version.sdkInt >= 26) capabilities.add('adaptive_icons');
    if (info.version.sdkInt >= 29) capabilities.add('dark_theme');
    
    // Check hardware features
    try {
      final features = await _channel.invokeMethod('getHardwareFeatures');
      if (features is List) {
        capabilities.addAll(features.cast<String>());
      }
    } catch (e) {
      // Ignore errors
    }
    
    return capabilities;
  }

  Future<List<String>> _getIOSCapabilities(IosDeviceInfo info) async {
    final capabilities = <String>[];
    
    // Add iOS-specific capabilities
    final version = double.tryParse(info.systemVersion.split('.').first) ?? 0;
    if (version >= 13) capabilities.add('dark_mode');
    if (version >= 14) capabilities.add('widgets');
    if (version >= 15) capabilities.add('focus_modes');
    
    // Add device-specific capabilities
    if (info.model.contains('iPad')) capabilities.add('tablet');
    if (info.model.contains('iPhone')) capabilities.add('phone');
    
    return capabilities;
  }

  /// Permission Management
  
  /// Request permission
  Future<bool> requestPermission(PlatformPermission permission) async {
    try {
      final Permission permissionType = _mapPermission(permission);
      final status = await permissionType.request();
      return status.isGranted;
    } catch (e) {
      throw PlatformException('Failed to request permission: $e');
    }
  }

  /// Check permission status
  Future<PermissionStatus> checkPermission(PlatformPermission permission) async {
    try {
      final Permission permissionType = _mapPermission(permission);
      final status = await permissionType.status;
      return _mapPermissionStatus(status);
    } catch (e) {
      throw PlatformException('Failed to check permission: $e');
    }
  }

  /// Request multiple permissions
  Future<Map<PlatformPermission, PermissionStatus>> requestPermissions(
    List<PlatformPermission> permissions,
  ) async {
    try {
      final permissionMap = <Permission, PlatformPermission>{};
      for (final permission in permissions) {
        permissionMap[_mapPermission(permission)] = permission;
      }
      
      final results = await permissionMap.keys.toList().request();
      final mappedResults = <PlatformPermission, PermissionStatus>{};
      
      for (final entry in results.entries) {
        final platformPermission = permissionMap[entry.key]!;
        mappedResults[platformPermission] = _mapPermissionStatus(entry.value);
      }
      
      return mappedResults;
    } catch (e) {
      throw PlatformException('Failed to request permissions: $e');
    }
  }

  Permission _mapPermission(PlatformPermission permission) {
    switch (permission) {
      case PlatformPermission.camera:
        return Permission.camera;
      case PlatformPermission.microphone:
        return Permission.microphone;
      case PlatformPermission.storage:
        return Permission.storage;
      case PlatformPermission.location:
        return Permission.location;
      case PlatformPermission.contacts:
        return Permission.contacts;
      case PlatformPermission.calendar:
        return Permission.calendar;
      case PlatformPermission.photos:
        return Permission.photos;
      case PlatformPermission.notification:
        return Permission.notification;
    }
  }

  PermissionStatus _mapPermissionStatus(Permission status) {
    switch (status) {
      case PermissionStatus.granted:
        return PermissionStatus.granted;
      case PermissionStatus.denied:
        return PermissionStatus.denied;
      case PermissionStatus.restricted:
        return PermissionStatus.restricted;
      case PermissionStatus.limited:
        return PermissionStatus.limited;
      case PermissionStatus.permanentlyDenied:
        return PermissionStatus.permanentlyDenied;
      default:
        return PermissionStatus.denied;
    }
  }

  /// File System Operations
  
  /// Pick file
  Future<PlatformFile?> pickFile({
    List<String>? allowedExtensions,
    bool allowMultiple = false,
  }) async {
    try {
      final result = await _channel.invokeMethod('pickFile', {
        'allowedExtensions': allowedExtensions,
        'allowMultiple': allowMultiple,
      });
      
      if (result != null) {
        return PlatformFile.fromMap(result);
      }
      return null;
    } catch (e) {
      throw PlatformException('Failed to pick file: $e');
    }
  }

  /// Save file
  Future<bool> saveFile(String path, List<int> bytes) async {
    try {
      final result = await _channel.invokeMethod('saveFile', {
        'path': path,
        'bytes': bytes,
      });
      return result == true;
    } catch (e) {
      throw PlatformException('Failed to save file: $e');
    }
  }

  /// Share content
  Future<void> shareContent({
    String? text,
    String? subject,
    List<String>? files,
  }) async {
    try {
      if (files != null && files.isNotEmpty) {
        await Share.shareXFiles(
          files.map((path) => XFile(path)).toList(),
          text: text,
          subject: subject,
        );
      } else if (text != null) {
        await Share.share(text, subject: subject);
      }
    } catch (e) {
      throw PlatformException('Failed to share content: $e');
    }
  }

  /// URL and Deep Linking
  
  /// Launch URL
  Future<bool> launchURL(String url, {LaunchMode mode = LaunchMode.platformDefault}) async {
    try {
      final uri = Uri.parse(url);
      return await launchUrl(uri, mode: mode);
    } catch (e) {
      throw PlatformException('Failed to launch URL: $e');
    }
  }

  /// Check if URL can be launched
  Future<bool> canLaunchURL(String url) async {
    try {
      final uri = Uri.parse(url);
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  /// Handle deep link
  Future<void> handleDeepLink(String link) async {
    try {
      await _channel.invokeMethod('handleDeepLink', {'link': link});
    } catch (e) {
      throw PlatformException('Failed to handle deep link: $e');
    }
  }

  /// Native UI Components
  
  /// Show native alert
  Future<bool> showNativeAlert({
    required String title,
    required String message,
    String? positiveButton,
    String? negativeButton,
  }) async {
    try {
      final result = await _channel.invokeMethod('showAlert', {
        'title': title,
        'message': message,
        'positiveButton': positiveButton ?? 'OK',
        'negativeButton': negativeButton,
      });
      return result == true;
    } catch (e) {
      throw PlatformException('Failed to show alert: $e');
    }
  }

  /// Show native toast
  Future<void> showNativeToast(String message, {Duration? duration}) async {
    try {
      await _channel.invokeMethod('showToast', {
        'message': message,
        'duration': duration?.inMilliseconds ?? 2000,
      });
    } catch (e) {
      throw PlatformException('Failed to show toast: $e');
    }
  }

  /// Haptic Feedback
  
  /// Trigger haptic feedback
  Future<void> triggerHapticFeedback(HapticFeedbackType type) async {
    try {
      switch (type) {
        case HapticFeedbackType.light:
          await HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          await HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          await HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          await HapticFeedback.selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          await HapticFeedback.vibrate();
          break;
      }
    } catch (e) {
      // Haptic feedback might not be available on all devices
    }
  }

  /// System Integration
  
  /// Get system theme
  Future<SystemTheme> getSystemTheme() async {
    try {
      final result = await _channel.invokeMethod('getSystemTheme');
      return SystemTheme.values.firstWhere(
        (theme) => theme.name == result,
        orElse: () => SystemTheme.light,
      );
    } catch (e) {
      return SystemTheme.light;
    }
  }

  /// Set status bar style
  Future<void> setStatusBarStyle(StatusBarStyle style) async {
    try {
      await _channel.invokeMethod('setStatusBarStyle', {'style': style.name});
    } catch (e) {
      // Ignore errors on platforms that don't support this
    }
  }

  /// Set navigation bar style
  Future<void> setNavigationBarStyle(NavigationBarStyle style) async {
    try {
      await _channel.invokeMethod('setNavigationBarStyle', {'style': style.name});
    } catch (e) {
      // Ignore errors on platforms that don't support this
    }
  }

  /// Biometric Authentication
  
  /// Check biometric availability
  Future<bool> isBiometricAvailable() async {
    try {
      final result = await _channel.invokeMethod('isBiometricAvailable');
      return result == true;
    } catch (e) {
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics({
    required String reason,
    String? title,
  }) async {
    try {
      final result = await _channel.invokeMethod('authenticateWithBiometrics', {
        'reason': reason,
        'title': title ?? 'Authenticate',
      });
      return result == true;
    } catch (e) {
      return false;
    }
  }
}

/// Data Models

class DeviceInfo {
  final String platform;
  final String model;
  final String manufacturer;
  final String version;
  final String sdkVersion;
  final String appVersion;
  final String appBuildNumber;
  final bool isPhysicalDevice;
  final ScreenSize screenSize;
  final List<String> capabilities;

  DeviceInfo({
    required this.platform,
    required this.model,
    required this.manufacturer,
    required this.version,
    required this.sdkVersion,
    required this.appVersion,
    required this.appBuildNumber,
    required this.isPhysicalDevice,
    required this.screenSize,
    required this.capabilities,
  });
}

class ScreenSize {
  final double width;
  final double height;
  final double density;

  ScreenSize({
    required this.width,
    required this.height,
    required this.density,
  });
}

class PlatformFile {
  final String name;
  final String path;
  final int size;
  final String? mimeType;

  PlatformFile({
    required this.name,
    required this.path,
    required this.size,
    this.mimeType,
  });

  factory PlatformFile.fromMap(Map<String, dynamic> map) {
    return PlatformFile(
      name: map['name'],
      path: map['path'],
      size: map['size'],
      mimeType: map['mimeType'],
    );
  }
}

/// Enums

enum PlatformPermission {
  camera,
  microphone,
  storage,
  location,
  contacts,
  calendar,
  photos,
  notification,
}

enum PermissionStatus {
  granted,
  denied,
  restricted,
  limited,
  permanentlyDenied,
}

enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}

enum SystemTheme {
  light,
  dark,
  auto,
}

enum StatusBarStyle {
  light,
  dark,
  auto,
}

enum NavigationBarStyle {
  light,
  dark,
  transparent,
}

/// Exceptions

class PlatformException implements Exception {
  final String message;
  PlatformException(this.message);
  
  @override
  String toString() => 'PlatformException: $message';
}

/// Providers

final platformServiceProvider = Provider<PlatformService>((ref) {
  return PlatformService();
});

final deviceInfoProvider = FutureProvider<DeviceInfo>((ref) async {
  final platform = ref.read(platformServiceProvider);
  return platform.getDeviceInfo();
});

final systemThemeProvider = FutureProvider<SystemTheme>((ref) async {
  final platform = ref.read(platformServiceProvider);
  return platform.getSystemTheme();
});

/// Helper extensions

extension PlatformServiceExtension on WidgetRef {
  PlatformService get platform => read(platformServiceProvider);
  
  Future<bool> requestPermission(PlatformPermission permission) {
    return platform.requestPermission(permission);
  }
  
  Future<void> shareText(String text) {
    return platform.shareContent(text: text);
  }
  
  Future<bool> launchURL(String url) {
    return platform.launchURL(url);
  }
  
  Future<void> showToast(String message) {
    return platform.showNativeToast(message);
  }
  
  Future<void> hapticFeedback([HapticFeedbackType type = HapticFeedbackType.light]) {
    return platform.triggerHapticFeedback(type);
  }
}
