import React, { useEffect, Suspense } from 'react';
import { Routes, Route, useParams, useSearchParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';

import { useRuntimeStore } from '@stores/runtimeStore';
import { useRealtimeConnection } from '@hooks/useRealtimeConnection';
import { usePerformanceMonitoring } from '@hooks/usePerformanceMonitoring';
import DynamicRenderer from '@components/renderer/DynamicRenderer';
import LoadingSpinner from '@components/ui/LoadingSpinner';
import ErrorFallback from '@components/ui/ErrorFallback';
import NotFound from '@components/ui/NotFound';
import { fetchUIMetadata } from '@services/api';

const App: React.FC = () => {
  const { 
    metadata, 
    setMetadata, 
    setLoading, 
    setError, 
    connected 
  } = useRuntimeStore();

  // Initialize real-time connection
  useRealtimeConnection();
  
  // Monitor performance
  usePerformanceMonitoring();

  return (
    <div className="min-h-screen bg-surface-50 text-surface-900">
      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/page/:pageId" element={<DynamicPage />} />
          <Route path="/preview/:pageId" element={<PreviewPage />} />
          <Route path="/embed/:pageId" element={<EmbedPage />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </AnimatePresence>
      
      {/* Connection status indicator */}
      <ConnectionStatus connected={connected} />
    </div>
  );
};

// Home page component
const HomePage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="container mx-auto px-4 py-8"
    >
      <div className="text-center">
        <h1 className="text-4xl font-bold text-surface-900 mb-4">
          UI Builder Runtime
        </h1>
        <p className="text-lg text-surface-600 mb-8">
          Dynamic UI rendering from JSON metadata
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <FeatureCard
            title="Dynamic Rendering"
            description="Render UI components from JSON configuration in real-time"
            icon="🎨"
          />
          <FeatureCard
            title="Theme Support"
            description="Apply custom themes and styling dynamically"
            icon="🎭"
          />
          <FeatureCard
            title="Real-time Updates"
            description="Live updates when configurations change"
            icon="⚡"
          />
          <FeatureCard
            title="Data Binding"
            description="Connect components to live data sources"
            icon="🔗"
          />
          <FeatureCard
            title="Responsive Design"
            description="Automatically adapt to different screen sizes"
            icon="📱"
          />
          <FeatureCard
            title="Offline Support"
            description="Works offline with cached configurations"
            icon="🔄"
          />
        </div>
      </div>
    </motion.div>
  );
};

// Dynamic page component
const DynamicPage: React.FC = () => {
  const { pageId } = useParams<{ pageId: string }>();
  const [searchParams] = useSearchParams();
  const { setMetadata, setLoading, setError } = useRuntimeStore();

  // Fetch page metadata
  const { data: metadata, isLoading, error } = useQuery({
    queryKey: ['page-metadata', pageId, searchParams.toString()],
    queryFn: () => fetchUIMetadata(pageId!, Object.fromEntries(searchParams)),
    enabled: !!pageId,
    onSuccess: (data) => {
      setMetadata(data);
    },
    onError: (error) => {
      setError({
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch page metadata',
        timestamp: new Date().toISOString(),
      });
    },
  });

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorFallback
        error={error}
        title="Failed to Load Page"
        message="The requested page could not be loaded. Please try again."
        resetErrorBoundary={() => window.location.reload()}
      />
    );
  }

  if (!metadata) {
    return <NotFound />;
  }

  return (
    <motion.div
      key={pageId}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      <Suspense
        fallback={
          <div className="flex items-center justify-center min-h-screen">
            <LoadingSpinner size="lg" />
          </div>
        }
      >
        <PageRenderer metadata={metadata} />
      </Suspense>
    </motion.div>
  );
};

// Preview page component (for testing configurations)
const PreviewPage: React.FC = () => {
  const { pageId } = useParams<{ pageId: string }>();
  const [searchParams] = useSearchParams();

  // Similar to DynamicPage but with preview-specific features
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-medium">Preview: {pageId}</h1>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Preview Mode</span>
            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
          </div>
        </div>
      </div>
      <DynamicPage />
    </div>
  );
};

// Embed page component (for embedding in other applications)
const EmbedPage: React.FC = () => {
  return (
    <div className="w-full h-full">
      <DynamicPage />
    </div>
  );
};

// Page renderer component
interface PageRendererProps {
  metadata: any; // UIMetadata type
}

const PageRenderer: React.FC<PageRendererProps> = ({ metadata }) => {
  if (!metadata.layout || !metadata.layout.components) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-medium text-surface-900 mb-2">
            No Content
          </h2>
          <p className="text-surface-600">
            This page has no components to display.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {metadata.layout.components.map((component: any, index: number) => (
        <DynamicRenderer
          key={component.id || `component-${index}`}
          configuration={component}
          index={index}
        />
      ))}
    </div>
  );
};

// Feature card component
interface FeatureCardProps {
  title: string;
  description: string;
  icon: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="bg-white rounded-lg shadow-md p-6 border border-surface-200"
    >
      <div className="text-3xl mb-4">{icon}</div>
      <h3 className="text-lg font-semibold text-surface-900 mb-2">{title}</h3>
      <p className="text-surface-600 text-sm">{description}</p>
    </motion.div>
  );
};

// Connection status indicator
interface ConnectionStatusProps {
  connected: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ connected }) => {
  if (connected) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg"
    >
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <span className="text-sm font-medium">Reconnecting...</span>
      </div>
    </motion.div>
  );
};

export default App;
