package com.uiplatform.graphql;

import com.uiplatform.dto.*;
import com.uiplatform.entity.UIConfiguration;
import com.uiplatform.graphql.input.TemplateSearchInput;
import com.uiplatform.service.*;
import graphql.kickstart.tools.GraphQLQueryResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * GraphQL Query Resolver for handling all query operations.
 */
@Component
public class QueryResolver implements GraphQLQueryResolver {

    private static final Logger logger = LoggerFactory.getLogger(QueryResolver.class);

    private final OrganizationService organizationService;
    private final UserService userService;
    private final UIConfigurationService uiConfigurationService;
    private final ComponentService componentService;
    private final TemplateService templateService;

    @Autowired
    public QueryResolver(OrganizationService organizationService,
                        UserService userService,
                        UIConfigurationService uiConfigurationService,
                        ComponentService componentService,
                        TemplateService templateService) {
        this.organizationService = organizationService;
        this.userService = userService;
        this.uiConfigurationService = uiConfigurationService;
        this.componentService = componentService;
        this.templateService = templateService;
    }

    // Organization Queries

    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public OrganizationDTO organization(UUID id) {
        logger.debug("GraphQL query: organization(id: {})", id);
        return organizationService.getOrganizationById(id);
    }

    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public OrganizationDTO organizationBySlug(String slug) {
        logger.debug("GraphQL query: organizationBySlug(slug: {})", slug);
        return organizationService.getOrganizationBySlug(slug);
    }

    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public Page<OrganizationDTO> organizations(int page, int size) {
        logger.debug("GraphQL query: organizations(page: {}, size: {})", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return organizationService.getAllOrganizations(pageable);
    }

    // User Queries

    @PreAuthorize("hasPermission('USER', 'READ')")
    public UserDTO user(UUID id) {
        logger.debug("GraphQL query: user(id: {})", id);
        return userService.getUserById(id);
    }

    @PreAuthorize("hasPermission('USER', 'READ')")
    public UserDTO userByUsername(String username) {
        logger.debug("GraphQL query: userByUsername(username: {})", username);
        return userService.getUserByUsername(username);
    }

    @PreAuthorize("hasPermission('USER', 'READ')")
    public Page<UserDTO> usersByOrganization(UUID organizationId, int page, int size) {
        logger.debug("GraphQL query: usersByOrganization(organizationId: {}, page: {}, size: {})", 
                    organizationId, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return userService.getUsersByOrganization(organizationId, pageable);
    }

    // UI Configuration Queries

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public UIConfigurationDTO uiConfiguration(UUID id) {
        logger.debug("GraphQL query: uiConfiguration(id: {})", id);
        return uiConfigurationService.getUIConfigurationById(id);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public UIConfigurationDTO uiConfigurationBySlug(String slug, UUID organizationId) {
        logger.debug("GraphQL query: uiConfigurationBySlug(slug: {}, organizationId: {})", slug, organizationId);
        return uiConfigurationService.getUIConfigurationBySlug(slug, organizationId);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public Page<UIConfigurationDTO> uiConfigurationsByOrganization(UUID organizationId, int page, int size) {
        logger.debug("GraphQL query: uiConfigurationsByOrganization(organizationId: {}, page: {}, size: {})", 
                    organizationId, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return uiConfigurationService.getUIConfigurationsByOrganization(organizationId, pageable);
    }

    // Component Queries

    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ComponentDTO component(UUID id) {
        logger.debug("GraphQL query: component(id: {})", id);
        return componentService.getComponentById(id);
    }

    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public List<ComponentDTO> componentsByUIConfiguration(UUID uiConfigurationId) {
        logger.debug("GraphQL query: componentsByUIConfiguration(uiConfigurationId: {})", uiConfigurationId);
        return componentService.getComponentsByUIConfiguration(uiConfigurationId);
    }

    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public List<ComponentDTO> componentTree(UUID uiConfigurationId) {
        logger.debug("GraphQL query: componentTree(uiConfigurationId: {})", uiConfigurationId);
        return componentService.getComponentTree(uiConfigurationId);
    }

    // Template Queries

    public TemplateDTO template(UUID id) {
        logger.debug("GraphQL query: template(id: {})", id);
        return templateService.getTemplateById(id);
    }

    public Page<TemplateDTO> marketplaceTemplates(int page, int size) {
        logger.debug("GraphQL query: marketplaceTemplates(page: {}, size: {})", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return templateService.getPublicTemplates(pageable);
    }

    public Page<TemplateDTO> searchTemplates(TemplateSearchInput input, int page, int size) {
        logger.debug("GraphQL query: searchTemplates(input: {}, page: {}, size: {})", input, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return templateService.searchTemplates(
                input.getName(),
                input.getCategory(),
                input.getSubcategory(),
                input.getIsPremium(),
                input.getMinPrice(),
                input.getMaxPrice(),
                input.getMinRating(),
                pageable
        );
    }

    public List<TemplateDTO> featuredTemplates() {
        logger.debug("GraphQL query: featuredTemplates()");
        return templateService.getFeaturedTemplates();
    }

    public List<TemplateDTO> popularTemplates(int limit) {
        logger.debug("GraphQL query: popularTemplates(limit: {})", limit);
        return templateService.getMostPopularTemplates(limit);
    }

    public List<TemplateDTO> topRatedTemplates(int limit) {
        logger.debug("GraphQL query: topRatedTemplates(limit: {})", limit);
        return templateService.getHighestRatedTemplates(limit);
    }

    public List<String> templateCategories() {
        logger.debug("GraphQL query: templateCategories()");
        return templateService.getAllCategories();
    }
}
