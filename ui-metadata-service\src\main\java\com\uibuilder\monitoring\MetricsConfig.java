package com.uibuilder.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.binder.MeterBinder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.health.Status;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Configuration
@RequiredArgsConstructor
public class MetricsConfig {

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config()
            .commonTags(
                "application", "ui-builder",
                "service", "ui-metadata-service",
                "version", getClass().getPackage().getImplementationVersion()
            );
    }

    @Bean
    public UIBuilderMetrics uiBuilderMetrics(MeterRegistry meterRegistry) {
        return new UIBuilderMetrics(meterRegistry);
    }

    @Bean
    public DatabaseMetrics databaseMetrics(MeterRegistry meterRegistry) {
        return new DatabaseMetrics(meterRegistry);
    }

    @Bean
    public BusinessMetrics businessMetrics(MeterRegistry meterRegistry) {
        return new BusinessMetrics(meterRegistry);
    }
}

@Component
@RequiredArgsConstructor
@Slf4j
public class UIBuilderMetrics implements MeterBinder {

    private final MeterRegistry meterRegistry;
    private final UIConfigRepository uiConfigRepository;
    private final TemplateRepository templateRepository;
    private final UserRepository userRepository;

    // Counters
    private Counter uiConfigsCreated;
    private Counter uiConfigsUpdated;
    private Counter uiConfigsDeleted;
    private Counter templatesUsed;
    private Counter userRegistrations;
    private Counter authenticationAttempts;
    private Counter authenticationFailures;
    private Counter rateLimitExceeded;

    // Gauges
    private AtomicInteger activeUsers = new AtomicInteger(0);
    private AtomicInteger activeCollaborationSessions = new AtomicInteger(0);
    private AtomicLong totalUIConfigs = new AtomicLong(0);
    private AtomicLong totalTemplates = new AtomicLong(0);

    // Timers
    private Timer uiConfigLoadTime;
    private Timer templateRenderTime;
    private Timer collaborationSyncTime;

    @Override
    public void bindTo(MeterRegistry registry) {
        // Initialize counters
        uiConfigsCreated = Counter.builder("ui_builder_configs_created_total")
            .description("Total number of UI configs created")
            .register(registry);

        uiConfigsUpdated = Counter.builder("ui_builder_configs_updated_total")
            .description("Total number of UI configs updated")
            .register(registry);

        uiConfigsDeleted = Counter.builder("ui_builder_configs_deleted_total")
            .description("Total number of UI configs deleted")
            .register(registry);

        templatesUsed = Counter.builder("ui_builder_template_usage_total")
            .description("Total number of template uses")
            .register(registry);

        userRegistrations = Counter.builder("ui_builder_user_registrations_total")
            .description("Total number of user registrations")
            .register(registry);

        authenticationAttempts = Counter.builder("ui_builder_auth_attempts_total")
            .description("Total authentication attempts")
            .register(registry);

        authenticationFailures = Counter.builder("ui_builder_auth_failures_total")
            .description("Total authentication failures")
            .register(registry);

        rateLimitExceeded = Counter.builder("ui_builder_rate_limit_exceeded_total")
            .description("Total rate limit violations")
            .register(registry);

        // Initialize gauges
        Gauge.builder("ui_builder_active_users")
            .description("Number of currently active users")
            .register(registry, activeUsers, AtomicInteger::get);

        Gauge.builder("ui_builder_active_collaboration_sessions")
            .description("Number of active collaboration sessions")
            .register(registry, activeCollaborationSessions, AtomicInteger::get);

        Gauge.builder("ui_builder_total_configs")
            .description("Total number of UI configs")
            .register(registry, totalUIConfigs, AtomicLong::get);

        Gauge.builder("ui_builder_total_templates")
            .description("Total number of templates")
            .register(registry, totalTemplates, AtomicLong::get);

        // Initialize timers
        uiConfigLoadTime = Timer.builder("ui_builder_config_load_duration")
            .description("Time taken to load UI config")
            .register(registry);

        templateRenderTime = Timer.builder("ui_builder_template_render_duration")
            .description("Time taken to render template")
            .register(registry);

        collaborationSyncTime = Timer.builder("ui_builder_collaboration_sync_duration")
            .description("Time taken to sync collaboration changes")
            .register(registry);
    }

    // Metric recording methods
    public void recordUIConfigCreated(String workspaceId, String userId) {
        uiConfigsCreated.increment(
            "workspace", workspaceId,
            "user", userId
        );
    }

    public void recordUIConfigUpdated(String configId, String userId) {
        uiConfigsUpdated.increment(
            "config", configId,
            "user", userId
        );
    }

    public void recordUIConfigDeleted(String configId, String userId) {
        uiConfigsDeleted.increment(
            "config", configId,
            "user", userId
        );
    }

    public void recordTemplateUsage(String templateId, String userId) {
        templatesUsed.increment(
            "template", templateId,
            "user", userId
        );
    }

    public void recordUserRegistration(String registrationMethod) {
        userRegistrations.increment(
            "method", registrationMethod
        );
    }

    public void recordAuthenticationAttempt(String method, boolean success) {
        authenticationAttempts.increment(
            "method", method,
            "success", String.valueOf(success)
        );
        
        if (!success) {
            authenticationFailures.increment(
                "method", method
            );
        }
    }

    public void recordRateLimitExceeded(String endpoint, String userId) {
        rateLimitExceeded.increment(
            "endpoint", endpoint,
            "user", userId
        );
    }

    public Timer.Sample startUIConfigLoadTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordUIConfigLoadTime(Timer.Sample sample, String configId) {
        sample.stop(Timer.builder("ui_builder_config_load_duration")
            .tag("config", configId)
            .register(meterRegistry));
    }

    public Timer.Sample startTemplateRenderTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordTemplateRenderTime(Timer.Sample sample, String templateId) {
        sample.stop(Timer.builder("ui_builder_template_render_duration")
            .tag("template", templateId)
            .register(meterRegistry));
    }

    public void setActiveUsers(int count) {
        activeUsers.set(count);
    }

    public void setActiveCollaborationSessions(int count) {
        activeCollaborationSessions.set(count);
    }

    // Scheduled metric updates
    @Scheduled(fixedRate = 60000) // Every minute
    public void updateGaugeMetrics() {
        try {
            // Update total counts
            totalUIConfigs.set(uiConfigRepository.count());
            totalTemplates.set(templateRepository.count());

            // Update active user count (users active in last 5 minutes)
            LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
            long activeUserCount = userRepository.countByLastActivityAfter(fiveMinutesAgo);
            activeUsers.set((int) activeUserCount);

            log.debug("Updated gauge metrics - Active users: {}, Total configs: {}, Total templates: {}",
                     activeUserCount, totalUIConfigs.get(), totalTemplates.get());

        } catch (Exception e) {
            log.error("Failed to update gauge metrics", e);
        }
    }
}

@Component
@RequiredArgsConstructor
@Slf4j
public class DatabaseMetrics implements MeterBinder {

    private final MeterRegistry meterRegistry;
    private final DataSource dataSource;

    @Override
    public void bindTo(MeterRegistry registry) {
        // Database connection pool metrics
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDS = (HikariDataSource) dataSource;
            
            Gauge.builder("database_connections_active")
                .description("Active database connections")
                .register(registry, hikariDS, ds -> ds.getHikariPoolMXBean().getActiveConnections());

            Gauge.builder("database_connections_idle")
                .description("Idle database connections")
                .register(registry, hikariDS, ds -> ds.getHikariPoolMXBean().getIdleConnections());

            Gauge.builder("database_connections_total")
                .description("Total database connections")
                .register(registry, hikariDS, ds -> ds.getHikariPoolMXBean().getTotalConnections());

            Gauge.builder("database_connections_waiting")
                .description("Threads waiting for database connections")
                .register(registry, hikariDS, ds -> ds.getHikariPoolMXBean().getThreadsAwaitingConnection());
        }

        // Query performance metrics
        Timer.builder("database_query_duration")
            .description("Database query execution time")
            .register(registry);
    }

    public void recordQueryExecution(String queryType, long executionTime) {
        Timer.builder("database_query_duration")
            .tag("query_type", queryType)
            .register(meterRegistry)
            .record(executionTime, java.util.concurrent.TimeUnit.MILLISECONDS);
    }
}

@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessMetrics implements MeterBinder {

    private final MeterRegistry meterRegistry;

    // Business-specific counters
    private Counter componentUsage;
    private Counter workspaceCreated;
    private Counter collaborationInvites;
    private Counter exportOperations;

    @Override
    public void bindTo(MeterRegistry registry) {
        componentUsage = Counter.builder("ui_builder_component_usage_total")
            .description("Total component usage by type")
            .register(registry);

        workspaceCreated = Counter.builder("ui_builder_workspaces_created_total")
            .description("Total workspaces created")
            .register(registry);

        collaborationInvites = Counter.builder("ui_builder_collaboration_invites_total")
            .description("Total collaboration invites sent")
            .register(registry);

        exportOperations = Counter.builder("ui_builder_exports_total")
            .description("Total export operations")
            .register(registry);
    }

    public void recordComponentUsage(String componentType, String workspaceId) {
        componentUsage.increment(
            "component_type", componentType,
            "workspace", workspaceId
        );
    }

    public void recordWorkspaceCreated(String userId, String plan) {
        workspaceCreated.increment(
            "user", userId,
            "plan", plan
        );
    }

    public void recordCollaborationInvite(String workspaceId, String inviterUserId) {
        collaborationInvites.increment(
            "workspace", workspaceId,
            "inviter", inviterUserId
        );
    }

    public void recordExportOperation(String exportType, String userId) {
        exportOperations.increment(
            "export_type", exportType,
            "user", userId
        );
    }
}

@Component
@RequiredArgsConstructor
@Slf4j
public class HealthMetrics implements MeterBinder {

    private final HealthEndpoint healthEndpoint;

    @Override
    public void bindTo(MeterRegistry registry) {
        Gauge.builder("application_health_status")
            .description("Application health status (1=UP, 0=DOWN)")
            .register(registry, this, HealthMetrics::getHealthStatus);
    }

    private double getHealthStatus() {
        try {
            Status status = healthEndpoint.health().getStatus();
            return Status.UP.equals(status) ? 1.0 : 0.0;
        } catch (Exception e) {
            log.error("Failed to get health status", e);
            return 0.0;
        }
    }
}

@Component
@RequiredArgsConstructor
@Slf4j
public class CustomMetricsCollector {

    private final UIBuilderMetrics uiBuilderMetrics;
    private final BusinessMetrics businessMetrics;

    @EventListener
    public void handleUIConfigCreated(UIConfigCreatedEvent event) {
        uiBuilderMetrics.recordUIConfigCreated(
            event.getWorkspaceId(), 
            event.getUserId()
        );
    }

    @EventListener
    public void handleTemplateUsed(TemplateUsedEvent event) {
        uiBuilderMetrics.recordTemplateUsage(
            event.getTemplateId(), 
            event.getUserId()
        );
    }

    @EventListener
    public void handleComponentUsed(ComponentUsedEvent event) {
        businessMetrics.recordComponentUsage(
            event.getComponentType(), 
            event.getWorkspaceId()
        );
    }

    @EventListener
    public void handleUserAuthenticated(UserAuthenticatedEvent event) {
        uiBuilderMetrics.recordAuthenticationAttempt(
            event.getMethod(), 
            event.isSuccess()
        );
    }
}
