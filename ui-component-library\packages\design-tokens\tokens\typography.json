{"font": {"family": {"sans": {"value": ["Inter", "system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "<PERSON><PERSON>", "sans-serif"]}, "serif": {"value": ["Georgia", "Cambria", "Times New Roman", "Times", "serif"]}, "mono": {"value": ["JetBrains Mono", "Fira Code", "Monaco", "Consolas", "Liberation Mono", "Courier New", "monospace"]}, "display": {"value": ["Cal Sans", "Inter", "system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif"]}}, "weight": {"thin": {"value": 100}, "extralight": {"value": 200}, "light": {"value": 300}, "normal": {"value": 400}, "medium": {"value": 500}, "semibold": {"value": 600}, "bold": {"value": 700}, "extrabold": {"value": 800}, "black": {"value": 900}}, "size": {"xs": {"value": 12}, "sm": {"value": 14}, "base": {"value": 16}, "lg": {"value": 18}, "xl": {"value": 20}, "2xl": {"value": 24}, "3xl": {"value": 30}, "4xl": {"value": 36}, "5xl": {"value": 48}, "6xl": {"value": 60}, "7xl": {"value": 72}, "8xl": {"value": 96}, "9xl": {"value": 128}}, "lineHeight": {"none": {"value": 1}, "tight": {"value": 1.25}, "snug": {"value": 1.375}, "normal": {"value": 1.5}, "relaxed": {"value": 1.625}, "loose": {"value": 2}}, "letterSpacing": {"tighter": {"value": "-0.05em"}, "tight": {"value": "-0.025em"}, "normal": {"value": "0em"}, "wide": {"value": "0.025em"}, "wider": {"value": "0.05em"}, "widest": {"value": "0.1em"}}}, "typography": {"display": {"2xl": {"fontSize": {"value": "{font.size.7xl}"}, "lineHeight": {"value": "{font.lineHeight.none}"}, "fontWeight": {"value": "{font.weight.bold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.display}"}}, "xl": {"fontSize": {"value": "{font.size.6xl}"}, "lineHeight": {"value": "{font.lineHeight.none}"}, "fontWeight": {"value": "{font.weight.bold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.display}"}}, "lg": {"fontSize": {"value": "{font.size.5xl}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.bold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.display}"}}, "md": {"fontSize": {"value": "{font.size.4xl}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.bold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.display}"}}, "sm": {"fontSize": {"value": "{font.size.3xl}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.display}"}}}, "heading": {"h1": {"fontSize": {"value": "{font.size.3xl}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.bold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.sans}"}}, "h2": {"fontSize": {"value": "{font.size.2xl}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.tight}"}, "fontFamily": {"value": "{font.family.sans}"}}, "h3": {"fontSize": {"value": "{font.size.xl}"}, "lineHeight": {"value": "{font.lineHeight.snug}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "h4": {"fontSize": {"value": "{font.size.lg}"}, "lineHeight": {"value": "{font.lineHeight.snug}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "h5": {"fontSize": {"value": "{font.size.base}"}, "lineHeight": {"value": "{font.lineHeight.snug}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "h6": {"fontSize": {"value": "{font.size.sm}"}, "lineHeight": {"value": "{font.lineHeight.snug}"}, "fontWeight": {"value": "{font.weight.semibold}"}, "letterSpacing": {"value": "{font.letterSpacing.wide}"}, "fontFamily": {"value": "{font.family.sans}"}}}, "body": {"xl": {"fontSize": {"value": "{font.size.xl}"}, "lineHeight": {"value": "{font.lineHeight.relaxed}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "lg": {"fontSize": {"value": "{font.size.lg}"}, "lineHeight": {"value": "{font.lineHeight.relaxed}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "md": {"fontSize": {"value": "{font.size.base}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "sm": {"fontSize": {"value": "{font.size.sm}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}, "xs": {"fontSize": {"value": "{font.size.xs}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.sans}"}}}, "caption": {"lg": {"fontSize": {"value": "{font.size.sm}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.medium}"}, "letterSpacing": {"value": "{font.letterSpacing.wide}"}, "fontFamily": {"value": "{font.family.sans}"}}, "md": {"fontSize": {"value": "{font.size.xs}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.medium}"}, "letterSpacing": {"value": "{font.letterSpacing.wide}"}, "fontFamily": {"value": "{font.family.sans}"}}, "sm": {"fontSize": {"value": "{font.size.xs}"}, "lineHeight": {"value": "{font.lineHeight.tight}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.wider}"}, "fontFamily": {"value": "{font.family.sans}"}}}, "code": {"lg": {"fontSize": {"value": "{font.size.base}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.mono}"}}, "md": {"fontSize": {"value": "{font.size.sm}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.mono}"}}, "sm": {"fontSize": {"value": "{font.size.xs}"}, "lineHeight": {"value": "{font.lineHeight.normal}"}, "fontWeight": {"value": "{font.weight.normal}"}, "letterSpacing": {"value": "{font.letterSpacing.normal}"}, "fontFamily": {"value": "{font.family.mono}"}}}}}