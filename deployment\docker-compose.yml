version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: ui-builder-postgres
    environment:
      POSTGRES_DB: ui_builder
      POSTGRES_USER: ui_builder
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ui_builder_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ui_builder"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ui-builder-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service Discovery
  eureka:
    image: steeltoeoss/eureka-server:latest
    container_name: ui-builder-eureka
    environment:
      EUREKA_CLIENT_REGISTER_WITH_EUREKA: false
      EUREKA_CLIENT_FETCH_REGISTRY: false
    ports:
      - "8761:8761"
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: ui-builder-gateway
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka:8761/eureka
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      JWT_SECRET: ${JWT_SECRET:-your-secret-key}
    ports:
      - "8080:8080"
    depends_on:
      - eureka
      - redis
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth-service:
    build:
      context: ./backend-services/auth-service
      dockerfile: Dockerfile
    container_name: ui-builder-auth
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_DATASOURCE_USERNAME: ui_builder
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-ui_builder_password}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka:8761/eureka
      JWT_SECRET: ${JWT_SECRET:-your-secret-key}
      SMTP_HOST: ${SMTP_HOST:-smtp.gmail.com}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
    depends_on:
      - postgres
      - redis
      - eureka
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Configuration Service
  config-service:
    build:
      context: ./backend-services/config-service
      dockerfile: Dockerfile
    container_name: ui-builder-config
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_DATASOURCE_USERNAME: ui_builder
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-ui_builder_password}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka:8761/eureka
    depends_on:
      - postgres
      - redis
      - eureka
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Template Service
  template-service:
    build:
      context: ./backend-services/template-service
      dockerfile: Dockerfile
    container_name: ui-builder-template
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_DATASOURCE_USERNAME: ui_builder
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-ui_builder_password}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka:8761/eureka
    depends_on:
      - postgres
      - redis
      - eureka
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service
  analytics-service:
    build:
      context: ./backend-services/analytics-service
      dockerfile: Dockerfile
    container_name: ui-builder-analytics
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_DATASOURCE_USERNAME: ui_builder
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-ui_builder_password}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password}
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka:8761/eureka
    depends_on:
      - postgres
      - redis
      - eureka
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./ui-builder-frontend
      dockerfile: Dockerfile
      args:
        REACT_APP_API_URL: http://localhost:8080
        REACT_APP_WS_URL: ws://localhost:8080
    container_name: ui-builder-frontend
    ports:
      - "3000:80"
    depends_on:
      - api-gateway
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Runtime
  web-runtime:
    build:
      context: ./web-runtime
      dockerfile: Dockerfile
    container_name: ui-builder-runtime
    ports:
      - "3001:80"
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # File Storage (MinIO)
  minio:
    image: minio/minio:latest
    container_name: ui-builder-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - ui-builder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ui-builder-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ui-builder-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: ui-builder-grafana
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3002:3000"
    depends_on:
      - prometheus
    networks:
      - ui-builder-network

  # Log Management - Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ui-builder-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ui-builder-network

  # Log Management - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: ui-builder-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - ui-builder-network

volumes:
  postgres_data:
  redis_data:
  minio_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  ui-builder-network:
    driver: bridge
