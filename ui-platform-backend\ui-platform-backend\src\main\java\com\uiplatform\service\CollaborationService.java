package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Service for handling real-time collaboration features.
 */
@Service
public class CollaborationService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationService.class);
    
    private static final String USER_SESSION_KEY = "collaboration:user_sessions:";
    private static final String ACTIVE_USERS_KEY = "collaboration:active_users:";
    private static final String UI_CONFIG_USERS_KEY = "collaboration:ui_config_users:";
    private static final String CURSOR_POSITION_KEY = "collaboration:cursor_position:";
    private static final String EDITING_LOCKS_KEY = "collaboration:editing_locks:";

    private final SimpMessagingTemplate messagingTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    private final PresenceService presenceService;

    // In-memory cache for active sessions (for performance)
    private final Map<String, UserSession> activeSessions = new ConcurrentHashMap<>();
    private final Map<UUID, Set<String>> configSubscriptions = new ConcurrentHashMap<>();

    @Autowired
    public CollaborationService(SimpMessagingTemplate messagingTemplate,
                               RedisTemplate<String, Object> redisTemplate,
                               ObjectMapper objectMapper,
                               PresenceService presenceService) {
        this.messagingTemplate = messagingTemplate;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.presenceService = presenceService;
    }

    /**
     * Handle user connection to WebSocket.
     */
    public void handleUserConnect(UUID userId, String username, UUID organizationId, String sessionId) {
        logger.info("User {} connected with session {}", username, sessionId);
        
        try {
            // Create user session
            UserSession session = new UserSession();
            session.setUserId(userId);
            session.setUsername(username);
            session.setOrganizationId(organizationId);
            session.setSessionId(sessionId);
            session.setConnectedAt(LocalDateTime.now());
            session.setLastActivity(LocalDateTime.now());
            
            // Store in memory and Redis
            activeSessions.put(sessionId, session);
            redisTemplate.opsForValue().set(
                USER_SESSION_KEY + sessionId, 
                session, 
                4, TimeUnit.HOURS
            );
            
            // Update user presence using PresenceService
            presenceService.updateUserPresence(userId, username, organizationId, "active", null, null);
            
        } catch (Exception e) {
            logger.error("Error handling user connection", e);
        }
    }

    /**
     * Handle user disconnection from WebSocket.
     */
    public void handleUserDisconnect(UUID userId, String username, UUID organizationId, String sessionId) {
        logger.info("User {} disconnected from session {}", username, sessionId);
        
        try {
            // Remove from active sessions
            activeSessions.remove(sessionId);
            redisTemplate.delete(USER_SESSION_KEY + sessionId);
            
            // Check if user has other active sessions
            boolean hasOtherSessions = activeSessions.values().stream()
                    .anyMatch(session -> session.getUserId().equals(userId));
            
            if (!hasOtherSessions) {
                // Set user as offline using PresenceService
                presenceService.setUserOffline(userId, username, organizationId);

                // Release any editing locks held by this user
                releaseUserEditingLocks(userId);

                // Remove user from all UI config subscriptions
                removeUserFromAllConfigs(userId, sessionId);
            }
            
        } catch (Exception e) {
            logger.error("Error handling user disconnection", e);
        }
    }

    /**
     * Handle user joining a UI configuration for collaboration.
     */
    public void handleUserJoinConfig(UUID userId, UUID configId, String sessionId) {
        logger.debug("User {} joining UI config {} collaboration", userId, configId);
        
        try {
            // Add user to config subscribers
            configSubscriptions.computeIfAbsent(configId, k -> ConcurrentHashMap.newKeySet()).add(sessionId);
            
            // Store in Redis
            redisTemplate.opsForSet().add(UI_CONFIG_USERS_KEY + configId, sessionId);
            redisTemplate.expire(UI_CONFIG_USERS_KEY + configId, 4, TimeUnit.HOURS);
            
            // Get user session
            UserSession session = activeSessions.get(sessionId);
            if (session != null) {
                // Broadcast user joined config
                CollaborationEvent event = new CollaborationEvent();
                event.setType(CollaborationEventType.USER_JOINED_CONFIG);
                event.setUserId(userId);
                event.setUsername(session.getUsername());
                event.setConfigId(configId);
                event.setTimestamp(LocalDateTime.now());
                
                broadcastToConfigUsers(configId, event, sessionId);
                
                // Send current active users to the joining user
                sendActiveUsersToUser(configId, sessionId);
            }
            
        } catch (Exception e) {
            logger.error("Error handling user join config", e);
        }
    }

    /**
     * Handle user joining organization-wide collaboration.
     */
    public void handleUserJoinOrganization(UUID userId, UUID organizationId, String sessionId) {
        logger.debug("User {} joining organization {} collaboration", userId, organizationId);
        
        try {
            // Store organization subscription
            redisTemplate.opsForSet().add(ACTIVE_USERS_KEY + organizationId, sessionId);
            redisTemplate.expire(ACTIVE_USERS_KEY + organizationId, 4, TimeUnit.HOURS);
            
        } catch (Exception e) {
            logger.error("Error handling user join organization", e);
        }
    }

    /**
     * Handle user unsubscribe from topics.
     */
    public void handleUserUnsubscribe(UUID userId, String subscriptionId, String sessionId) {
        logger.debug("User {} unsubscribed from {}", userId, subscriptionId);
        
        // This would be implemented based on subscription tracking needs
        // For now, we'll handle it in the disconnect logic
    }

    /**
     * Update cursor position for real-time tracking.
     */
    public void updateCursorPosition(UUID userId, UUID configId, CursorPosition position) {
        try {
            // Store cursor position in Redis with short TTL
            String key = CURSOR_POSITION_KEY + configId + ":" + userId;
            redisTemplate.opsForValue().set(key, position, 30, TimeUnit.SECONDS);
            
            // Broadcast cursor position to other users
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.CURSOR_MOVED);
            event.setUserId(userId);
            event.setConfigId(configId);
            event.setData(position);
            event.setTimestamp(LocalDateTime.now());
            
            broadcastToConfigUsers(configId, event, null);
            
        } catch (Exception e) {
            logger.error("Error updating cursor position", e);
        }
    }

    /**
     * Acquire editing lock for a UI element.
     */
    public boolean acquireEditingLock(UUID userId, UUID configId, String elementId) {
        try {
            String lockKey = EDITING_LOCKS_KEY + configId + ":" + elementId;
            
            // Try to acquire lock (atomic operation)
            Boolean acquired = redisTemplate.opsForValue().setIfAbsent(
                lockKey, 
                userId.toString(), 
                5, TimeUnit.MINUTES
            );
            
            if (Boolean.TRUE.equals(acquired)) {
                logger.debug("User {} acquired editing lock for element {} in config {}", 
                           userId, elementId, configId);
                
                // Broadcast lock acquired event
                EditingLock lock = new EditingLock();
                lock.setUserId(userId);
                lock.setConfigId(configId);
                lock.setElementId(elementId);
                lock.setAcquiredAt(LocalDateTime.now());
                
                CollaborationEvent event = new CollaborationEvent();
                event.setType(CollaborationEventType.LOCK_ACQUIRED);
                event.setUserId(userId);
                event.setConfigId(configId);
                event.setData(lock);
                event.setTimestamp(LocalDateTime.now());
                
                broadcastToConfigUsers(configId, event, null);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("Error acquiring editing lock", e);
            return false;
        }
    }

    /**
     * Release editing lock for a UI element.
     */
    public void releaseEditingLock(UUID userId, UUID configId, String elementId) {
        try {
            String lockKey = EDITING_LOCKS_KEY + configId + ":" + elementId;
            String currentLockHolder = (String) redisTemplate.opsForValue().get(lockKey);
            
            if (userId.toString().equals(currentLockHolder)) {
                redisTemplate.delete(lockKey);
                
                logger.debug("User {} released editing lock for element {} in config {}", 
                           userId, elementId, configId);
                
                // Broadcast lock released event
                EditingLock lock = new EditingLock();
                lock.setUserId(userId);
                lock.setConfigId(configId);
                lock.setElementId(elementId);
                lock.setReleasedAt(LocalDateTime.now());
                
                CollaborationEvent event = new CollaborationEvent();
                event.setType(CollaborationEventType.LOCK_RELEASED);
                event.setUserId(userId);
                event.setConfigId(configId);
                event.setData(lock);
                event.setTimestamp(LocalDateTime.now());
                
                broadcastToConfigUsers(configId, event, null);
            }
            
        } catch (Exception e) {
            logger.error("Error releasing editing lock", e);
        }
    }

    /**
     * Get active users for a UI configuration.
     */
    public List<ActiveUser> getActiveUsers(UUID configId) {
        try {
            return presenceService.getActiveUsersInConfig(configId);
        } catch (Exception e) {
            logger.error("Error getting active users", e);
            return Collections.emptyList();
        }
    }

    // Private helper methods



    private void broadcastToConfigUsers(UUID configId, CollaborationEvent event, String excludeSessionId) {
        try {
            Set<String> sessionIds = configSubscriptions.getOrDefault(configId, Collections.emptySet());
            
            for (String sessionId : sessionIds) {
                if (!sessionId.equals(excludeSessionId)) {
                    messagingTemplate.convertAndSendToUser(
                        sessionId, 
                        "/queue/collaboration", 
                        event
                    );
                }
            }
            
            // Also broadcast to topic for general subscribers
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/collaboration", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting to config users", e);
        }
    }

    private void sendActiveUsersToUser(UUID configId, String sessionId) {
        try {
            List<ActiveUser> activeUsers = getActiveUsers(configId);
            
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.ACTIVE_USERS_UPDATE);
            event.setConfigId(configId);
            event.setData(activeUsers);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSendToUser(sessionId, "/queue/collaboration", event);
            
        } catch (Exception e) {
            logger.error("Error sending active users to user", e);
        }
    }

    private void releaseUserEditingLocks(UUID userId) {
        try {
            // This would require scanning Redis keys or maintaining a separate index
            // For now, we'll implement a basic version
            logger.debug("Releasing all editing locks for user {}", userId);
            
        } catch (Exception e) {
            logger.error("Error releasing user editing locks", e);
        }
    }

    private void removeUserFromAllConfigs(UUID userId, String sessionId) {
        try {
            // Remove from all config subscriptions
            configSubscriptions.values().forEach(sessions -> sessions.remove(sessionId));
            
            // Clean up empty sets
            configSubscriptions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            
        } catch (Exception e) {
            logger.error("Error removing user from all configs", e);
        }
    }
}
