# Development Dockerfile for UI Metadata Service
FROM maven:3.9.5-openjdk-17-slim AS development

# Install development tools
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    netcat-openbsd \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy pom.xml first for better caching
COPY pom.xml .

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create logs directory and set permissions
RUN mkdir -p /app/logs && \
    mkdir -p /app/uploads && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8080
EXPOSE 5005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM optimization for development
ENV JAVA_OPTS="-XX:+UseContainerSupport \
    -XX:MaxRAMPercentage=75.0 \
    -XX:+UseG1GC \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.backgroundpreinitializer.ignore=true \
    -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"

# Application configuration
ENV SPRING_PROFILES_ACTIVE=development
ENV SERVER_PORT=8080

# Start with Maven Spring Boot plugin for hot reload
CMD ["mvn", "spring-boot:run", "-Dspring-boot.run.profiles=development", "-Dspring-boot.run.jvmArguments=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"]

# Labels for metadata
LABEL maintainer="UI Builder Team <<EMAIL>>"
LABEL version="1.0.0-dev"
LABEL description="UI Metadata Service - Development"
