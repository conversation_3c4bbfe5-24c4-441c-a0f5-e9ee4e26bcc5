import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { server } from './mocks/server';

// Configure testing library
configure({ testIdAttribute: 'data-testid' });

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock HTMLCanvasElement
HTMLCanvasElement.prototype.getContext = jest.fn();

// Mock Monaco Editor
jest.mock('monaco-editor', () => ({
  editor: {
    create: jest.fn(() => ({
      dispose: jest.fn(),
      getValue: jest.fn(() => ''),
      setValue: jest.fn(),
      onDidChangeModelContent: jest.fn(),
      getModel: jest.fn(() => ({
        onDidChangeContent: jest.fn(),
      })),
    })),
    defineTheme: jest.fn(),
    setTheme: jest.fn(),
  },
  languages: {
    typescript: {
      typescriptDefaults: {
        setCompilerOptions: jest.fn(),
        addExtraLib: jest.fn(),
      },
    },
  },
}));

// Mock Socket.io
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
  })),
}));

// Mock Ant Design components that use DOM measurements
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    Drawer: ({ children, open, ...props }: any) => 
      open ? <div data-testid="drawer" {...props}>{children}</div> : null,
    Modal: ({ children, open, visible, ...props }: any) => 
      (open || visible) ? <div data-testid="modal" {...props}>{children}</div> : null,
    Tooltip: ({ children, title, ...props }: any) => 
      <div data-testid="tooltip" title={title} {...props}>{children}</div>,
  };
});

// Setup MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Global test utilities
global.testUtils = {
  // Mock user data
  mockUser: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    organizationId: 'test-org-id',
  },
  
  // Mock configuration data
  mockConfig: {
    id: 'test-config-id',
    name: 'Test Configuration',
    components: [],
    theme: {
      colors: {
        primary: '#1890ff',
        secondary: '#722ed1',
      },
    },
  },
  
  // Mock component data
  mockComponent: {
    id: 'test-component-id',
    type: 'button',
    properties: {
      text: 'Test Button',
      variant: 'primary',
    },
  },
};

// Console error suppression for known issues
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: findDOMNode is deprecated'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
