package com.uiplatform.service;

import com.uiplatform.dto.ComponentDTO;
import com.uiplatform.entity.Component;
import com.uiplatform.entity.UIConfiguration;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.exception.BusinessException;
import com.uiplatform.mapper.ComponentMapper;
import com.uiplatform.repository.ComponentRepository;
import com.uiplatform.repository.UIConfigurationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for Component management.
 * Handles business logic for UI component operations.
 */
@Service
@Transactional
public class ComponentService {

    private static final Logger logger = LoggerFactory.getLogger(ComponentService.class);
    private static final int MAX_NESTING_LEVEL = 10;

    private final ComponentRepository componentRepository;
    private final UIConfigurationRepository uiConfigurationRepository;
    private final ComponentMapper componentMapper;

    @Autowired
    public ComponentService(ComponentRepository componentRepository,
                           UIConfigurationRepository uiConfigurationRepository,
                           ComponentMapper componentMapper) {
        this.componentRepository = componentRepository;
        this.uiConfigurationRepository = uiConfigurationRepository;
        this.componentMapper = componentMapper;
    }

    /**
     * Create a new component.
     */
    public ComponentDTO createComponent(ComponentDTO.CreateDTO createDTO, UUID uiConfigurationId) {
        logger.info("Creating new component '{}' of type '{}' for UI configuration: {}", 
                   createDTO.getName(), createDTO.getComponentType(), uiConfigurationId);

        // Validate UI configuration exists
        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(uiConfigurationId)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + uiConfigurationId));

        Component component = componentMapper.toEntity(createDTO);
        component.setUiConfiguration(uiConfiguration);

        // Set parent if provided and validate nesting level
        if (createDTO.getParentId() != null) {
            Component parent = componentRepository.findById(createDTO.getParentId())
                    .filter(c -> !c.getDeleted() && c.getUiConfiguration().getId().equals(uiConfigurationId))
                    .orElseThrow(() -> new ResourceNotFoundException("Parent component not found"));

            // Check nesting level
            int parentDepth = calculateDepth(parent);
            if (parentDepth >= MAX_NESTING_LEVEL) {
                throw new BusinessException("Maximum nesting level of " + MAX_NESTING_LEVEL + " exceeded");
            }

            component.setParent(parent);
        }

        // Auto-assign sort order if not provided
        if (createDTO.getSortOrder() == null || createDTO.getSortOrder() == 0) {
            int nextSortOrder = getNextSortOrder(uiConfigurationId, createDTO.getParentId());
            component.setSortOrder(nextSortOrder);
        }

        component = componentRepository.save(component);

        logger.info("Successfully created component with ID: {}", component.getId());
        return componentMapper.toDTO(component);
    }

    /**
     * Get component by ID.
     */
    @Transactional(readOnly = true)
    public ComponentDTO getComponentById(UUID id) {
        logger.debug("Fetching component by ID: {}", id);

        Component component = componentRepository.findById(id)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + id));

        return componentMapper.toDTO(component);
    }

    /**
     * Update component.
     */
    public ComponentDTO updateComponent(UUID id, ComponentDTO updateDTO) {
        logger.info("Updating component with ID: {}", id);

        Component component = componentRepository.findById(id)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + id));

        componentMapper.updateEntityFromDTO(updateDTO, component);
        component = componentRepository.save(component);

        logger.info("Successfully updated component with ID: {}", id);
        return componentMapper.toDTO(component);
    }

    /**
     * Delete component (soft delete).
     */
    public void deleteComponent(UUID id) {
        logger.info("Deleting component with ID: {}", id);

        Component component = componentRepository.findById(id)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + id));

        // Recursively delete all children
        deleteComponentAndChildren(component);

        logger.info("Successfully deleted component with ID: {}", id);
    }

    /**
     * Get components by UI configuration.
     */
    @Transactional(readOnly = true)
    public List<ComponentDTO> getComponentsByUIConfiguration(UUID uiConfigurationId) {
        logger.debug("Fetching components for UI configuration ID: {}", uiConfigurationId);

        List<Component> components = componentRepository.findByUiConfigurationIdAndDeletedFalseOrderBySortOrder(uiConfigurationId);
        return components.stream()
                .map(componentMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get root components (no parent) by UI configuration.
     */
    @Transactional(readOnly = true)
    public List<ComponentDTO> getRootComponentsByUIConfiguration(UUID uiConfigurationId) {
        logger.debug("Fetching root components for UI configuration ID: {}", uiConfigurationId);

        List<Component> components = componentRepository.findByUiConfigurationIdAndParentIsNullAndDeletedFalseOrderBySortOrder(uiConfigurationId);
        return components.stream()
                .map(componentMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get child components by parent ID.
     */
    @Transactional(readOnly = true)
    public List<ComponentDTO> getChildComponents(UUID parentId) {
        logger.debug("Fetching child components for parent ID: {}", parentId);

        List<Component> components = componentRepository.findByParentIdAndDeletedFalseOrderBySortOrder(parentId);
        return components.stream()
                .map(componentMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Search components by criteria.
     */
    @Transactional(readOnly = true)
    public Page<ComponentDTO> searchComponents(UUID uiConfigurationId,
                                             String componentType,
                                             String category,
                                             Boolean isVisible,
                                             Boolean isEnabled,
                                             Pageable pageable) {
        logger.debug("Searching components with criteria for UI configuration: {}", uiConfigurationId);

        Page<Component> components = componentRepository.findByCriteria(
                uiConfigurationId, componentType, category, isVisible, isEnabled, pageable);
        return components.map(componentMapper::toDTO);
    }

    /**
     * Move component to new parent.
     */
    public ComponentDTO moveComponent(UUID componentId, UUID newParentId, Integer newSortOrder) {
        logger.info("Moving component {} to new parent {} with sort order {}", componentId, newParentId, newSortOrder);

        Component component = componentRepository.findById(componentId)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + componentId));

        // Validate new parent if provided
        Component newParent = null;
        if (newParentId != null) {
            newParent = componentRepository.findById(newParentId)
                    .filter(c -> !c.getDeleted() && c.getUiConfiguration().getId().equals(component.getUiConfiguration().getId()))
                    .orElseThrow(() -> new ResourceNotFoundException("New parent component not found"));

            // Check for circular reference
            if (isCircularReference(component, newParent)) {
                throw new BusinessException("Cannot move component: would create circular reference");
            }

            // Check nesting level
            int newDepth = calculateDepth(newParent) + 1;
            if (newDepth > MAX_NESTING_LEVEL) {
                throw new BusinessException("Maximum nesting level of " + MAX_NESTING_LEVEL + " exceeded");
            }
        }

        component.setParent(newParent);
        
        if (newSortOrder != null) {
            component.setSortOrder(newSortOrder);
        } else {
            // Auto-assign sort order
            int nextSortOrder = getNextSortOrder(component.getUiConfiguration().getId(), newParentId);
            component.setSortOrder(nextSortOrder);
        }

        component = componentRepository.save(component);

        logger.info("Successfully moved component with ID: {}", componentId);
        return componentMapper.toDTO(component);
    }

    /**
     * Update component visibility.
     */
    public void updateComponentVisibility(UUID id, Boolean isVisible) {
        logger.info("Updating visibility for component ID: {} to {}", id, isVisible);

        Component component = componentRepository.findById(id)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + id));

        component.setIsVisible(isVisible);
        componentRepository.save(component);

        logger.info("Successfully updated visibility for component ID: {}", id);
    }

    /**
     * Update component enabled status.
     */
    public void updateComponentEnabledStatus(UUID id, Boolean isEnabled) {
        logger.info("Updating enabled status for component ID: {} to {}", id, isEnabled);

        Component component = componentRepository.findById(id)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + id));

        component.setIsEnabled(isEnabled);
        componentRepository.save(component);

        logger.info("Successfully updated enabled status for component ID: {}", id);
    }

    /**
     * Reorder components.
     */
    public void reorderComponents(UUID uiConfigurationId, UUID parentId, List<UUID> componentIds) {
        logger.info("Reordering components for UI configuration: {}, parent: {}", uiConfigurationId, parentId);

        for (int i = 0; i < componentIds.size(); i++) {
            UUID componentId = componentIds.get(i);
            Component component = componentRepository.findById(componentId)
                    .filter(c -> !c.getDeleted() && c.getUiConfiguration().getId().equals(uiConfigurationId))
                    .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + componentId));

            component.setSortOrder(i);
            componentRepository.save(component);
        }

        logger.info("Successfully reordered {} components", componentIds.size());
    }

    /**
     * Get component tree structure.
     */
    @Transactional(readOnly = true)
    public List<ComponentDTO> getComponentTree(UUID uiConfigurationId) {
        logger.debug("Building component tree for UI configuration ID: {}", uiConfigurationId);

        List<Component> rootComponents = componentRepository.findByUiConfigurationIdAndParentIsNullAndDeletedFalseOrderBySortOrder(uiConfigurationId);
        return rootComponents.stream()
                .map(this::buildComponentTree)
                .collect(Collectors.toList());
    }

    /**
     * Clone component with all children.
     */
    public ComponentDTO cloneComponent(UUID componentId, UUID targetUIConfigurationId, UUID newParentId) {
        logger.info("Cloning component {} to UI configuration {} with parent {}", componentId, targetUIConfigurationId, newParentId);

        Component original = componentRepository.findById(componentId)
                .filter(c -> !c.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Component not found with ID: " + componentId));

        UIConfiguration targetUIConfiguration = uiConfigurationRepository.findById(targetUIConfigurationId)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Target UI Configuration not found"));

        Component cloned = cloneComponentRecursive(original, targetUIConfiguration, newParentId);
        
        logger.info("Successfully cloned component with new ID: {}", cloned.getId());
        return componentMapper.toDTO(cloned);
    }

    // Private helper methods

    private void deleteComponentAndChildren(Component component) {
        // Delete all children first
        List<Component> children = componentRepository.findByParentIdAndDeletedFalseOrderBySortOrder(component.getId());
        for (Component child : children) {
            deleteComponentAndChildren(child);
        }

        // Delete the component itself
        component.markAsDeleted("SYSTEM"); // TODO: Get current user
        componentRepository.save(component);
    }

    private int calculateDepth(Component component) {
        int depth = 0;
        Component current = component.getParent();
        while (current != null) {
            depth++;
            current = current.getParent();
        }
        return depth;
    }

    private boolean isCircularReference(Component component, Component potentialParent) {
        Component current = potentialParent;
        while (current != null) {
            if (current.getId().equals(component.getId())) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }

    private int getNextSortOrder(UUID uiConfigurationId, UUID parentId) {
        List<Component> siblings;
        if (parentId == null) {
            siblings = componentRepository.findByUiConfigurationIdAndParentIsNullAndDeletedFalseOrderBySortOrder(uiConfigurationId);
        } else {
            siblings = componentRepository.findByParentIdAndDeletedFalseOrderBySortOrder(parentId);
        }
        return siblings.size();
    }

    private ComponentDTO buildComponentTree(Component component) {
        ComponentDTO dto = componentMapper.toDTO(component);
        
        List<Component> children = componentRepository.findByParentIdAndDeletedFalseOrderBySortOrder(component.getId());
        if (!children.isEmpty()) {
            List<ComponentDTO> childDTOs = children.stream()
                    .map(this::buildComponentTree)
                    .collect(Collectors.toList());
            dto.setChildren(childDTOs);
        }
        
        return dto;
    }

    private Component cloneComponentRecursive(Component original, UIConfiguration targetUIConfiguration, UUID parentId) {
        Component cloned = new Component();
        cloned.setName(original.getName() + " (Copy)");
        cloned.setDescription(original.getDescription());
        cloned.setComponentType(original.getComponentType());
        cloned.setCategory(original.getCategory());
        cloned.setProperties(original.getProperties());
        cloned.setStyleProperties(original.getStyleProperties());
        cloned.setEventHandlers(original.getEventHandlers());
        cloned.setValidationRules(original.getValidationRules());
        cloned.setResponsiveSettings(original.getResponsiveSettings());
        cloned.setIsVisible(original.getIsVisible());
        cloned.setIsEnabled(original.getIsEnabled());
        cloned.setIsRequired(original.getIsRequired());
        cloned.setIsReusable(original.getIsReusable());
        cloned.setCssClasses(original.getCssClasses());
        cloned.setCustomCss(original.getCustomCss());
        cloned.setDataSource(original.getDataSource());
        cloned.setDataBinding(original.getDataBinding());
        cloned.setUiConfiguration(targetUIConfiguration);

        if (parentId != null) {
            Component parent = componentRepository.findById(parentId)
                    .filter(c -> !c.getDeleted())
                    .orElseThrow(() -> new ResourceNotFoundException("Parent component not found"));
            cloned.setParent(parent);
        }

        int nextSortOrder = getNextSortOrder(targetUIConfiguration.getId(), parentId);
        cloned.setSortOrder(nextSortOrder);

        cloned = componentRepository.save(cloned);

        // Clone children
        List<Component> children = componentRepository.findByParentIdAndDeletedFalseOrderBySortOrder(original.getId());
        for (Component child : children) {
            cloneComponentRecursive(child, targetUIConfiguration, cloned.getId());
        }

        return cloned;
    }
}
