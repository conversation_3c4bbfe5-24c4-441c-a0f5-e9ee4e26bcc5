package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.collaboration.ActiveUser;
import com.uiplatform.dto.collaboration.CursorPosition;
import com.uiplatform.dto.collaboration.UserPresence;
import com.uiplatform.entity.ActivityLog;
import com.uiplatform.entity.Comment;
import com.uiplatform.repository.ActivityLogRepository;
import com.uiplatform.repository.CommentRepository;
import com.uiplatform.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for collaboration features.
 */
@RestController
@RequestMapping("/api/v1/collaboration")
@Tag(name = "Collaboration", description = "Real-time collaboration APIs")
public class CollaborationController {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationController.class);

    private final CollaborationService collaborationService;
    private final PresenceService presenceService;
    private final CursorTrackingService cursorTrackingService;
    private final CommentService commentService;
    private final ActivityLogService activityLogService;
    private final OperationHistoryService operationHistoryService;

    @Autowired
    public CollaborationController(CollaborationService collaborationService,
                                 PresenceService presenceService,
                                 CursorTrackingService cursorTrackingService,
                                 CommentService commentService,
                                 ActivityLogService activityLogService,
                                 OperationHistoryService operationHistoryService) {
        this.collaborationService = collaborationService;
        this.presenceService = presenceService;
        this.cursorTrackingService = cursorTrackingService;
        this.commentService = commentService;
        this.activityLogService = activityLogService;
        this.operationHistoryService = operationHistoryService;
    }

    // Presence Management APIs

    @GetMapping("/presence/active-users/{configId}")
    @Operation(summary = "Get active users for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<ActiveUser>>> getActiveUsers(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            List<ActiveUser> activeUsers = collaborationService.getActiveUsers(configId);
            return ResponseEntity.ok(ApiResponse.success(activeUsers));
            
        } catch (Exception e) {
            logger.error("Error getting active users", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get active users: " + e.getMessage()));
        }
    }

    @GetMapping("/presence/organization/{orgId}")
    @Operation(summary = "Get active users in an organization")
    @PreAuthorize("hasPermission(#orgId, 'ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<List<ActiveUser>>> getOrganizationActiveUsers(
            @Parameter(description = "Organization ID") @PathVariable UUID orgId) {
        
        try {
            List<ActiveUser> activeUsers = presenceService.getActiveUsersInOrganization(orgId);
            return ResponseEntity.ok(ApiResponse.success(activeUsers));
            
        } catch (Exception e) {
            logger.error("Error getting organization active users", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get organization active users: " + e.getMessage()));
        }
    }

    @GetMapping("/presence/statistics/{orgId}")
    @Operation(summary = "Get presence statistics for an organization")
    @PreAuthorize("hasPermission(#orgId, 'ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<PresenceService.PresenceStatistics>> getPresenceStatistics(
            @Parameter(description = "Organization ID") @PathVariable UUID orgId) {
        
        try {
            PresenceService.PresenceStatistics stats = presenceService.getPresenceStatistics(orgId);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("Error getting presence statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get presence statistics: " + e.getMessage()));
        }
    }

    // Cursor Tracking APIs

    @GetMapping("/cursors/{configId}")
    @Operation(summary = "Get cursor positions for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<CursorPosition>>> getCursorPositions(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            List<CursorPosition> positions = cursorTrackingService.getCursorPositions(configId);
            return ResponseEntity.ok(ApiResponse.success(positions));
            
        } catch (Exception e) {
            logger.error("Error getting cursor positions", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get cursor positions: " + e.getMessage()));
        }
    }

    @GetMapping("/cursors/{configId}/element/{elementId}")
    @Operation(summary = "Get cursor positions for a specific element")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<CursorPosition>>> getElementCursorPositions(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            @Parameter(description = "Element ID") @PathVariable String elementId) {
        
        try {
            List<CursorPosition> positions = cursorTrackingService.getCursorPositionsForElement(configId, elementId);
            return ResponseEntity.ok(ApiResponse.success(positions));
            
        } catch (Exception e) {
            logger.error("Error getting element cursor positions", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get element cursor positions: " + e.getMessage()));
        }
    }

    @GetMapping("/cursors/statistics/{configId}")
    @Operation(summary = "Get cursor statistics for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<CursorTrackingService.CursorStatistics>> getCursorStatistics(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            CursorTrackingService.CursorStatistics stats = cursorTrackingService.getCursorStatistics(configId);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("Error getting cursor statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get cursor statistics: " + e.getMessage()));
        }
    }

    // Comment Management APIs

    @GetMapping("/comments/{configId}")
    @Operation(summary = "Get comments for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<Comment>>> getComments(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            List<Comment> comments = commentService.getCommentsByUIConfiguration(configId);
            return ResponseEntity.ok(ApiResponse.success(comments));
            
        } catch (Exception e) {
            logger.error("Error getting comments", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get comments: " + e.getMessage()));
        }
    }

    @GetMapping("/comments/{configId}/element/{elementId}")
    @Operation(summary = "Get comments for a specific element")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<Comment>>> getElementComments(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            @Parameter(description = "Element ID") @PathVariable String elementId) {
        
        try {
            List<Comment> comments = commentService.getCommentsByElement(configId, elementId);
            return ResponseEntity.ok(ApiResponse.success(comments));
            
        } catch (Exception e) {
            logger.error("Error getting element comments", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get element comments: " + e.getMessage()));
        }
    }

    @GetMapping("/comments/{configId}/unresolved")
    @Operation(summary = "Get unresolved comments for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<Comment>>> getUnresolvedComments(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            List<Comment> comments = commentService.getUnresolvedComments(configId);
            return ResponseEntity.ok(ApiResponse.success(comments));
            
        } catch (Exception e) {
            logger.error("Error getting unresolved comments", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get unresolved comments: " + e.getMessage()));
        }
    }

    @GetMapping("/comments/statistics/{configId}")
    @Operation(summary = "Get comment statistics for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<CommentRepository.CommentStatistics>> getCommentStatistics(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId) {
        
        try {
            CommentRepository.CommentStatistics stats = commentService.getCommentStatistics(configId);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("Error getting comment statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get comment statistics: " + e.getMessage()));
        }
    }

    // Activity Feed APIs

    @GetMapping("/activity/organization/{orgId}")
    @Operation(summary = "Get activity feed for an organization")
    @PreAuthorize("hasPermission(#orgId, 'ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<Page<ActivityLog>>> getOrganizationActivityFeed(
            @Parameter(description = "Organization ID") @PathVariable UUID orgId,
            Pageable pageable) {
        
        try {
            Page<ActivityLog> activities = activityLogService.getActivityFeed(orgId, pageable);
            return ResponseEntity.ok(ApiResponse.success(activities));
            
        } catch (Exception e) {
            logger.error("Error getting organization activity feed", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get organization activity feed: " + e.getMessage()));
        }
    }

    @GetMapping("/activity/config/{configId}")
    @Operation(summary = "Get activity feed for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<Page<ActivityLog>>> getConfigActivityFeed(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            Pageable pageable) {
        
        try {
            Page<ActivityLog> activities = activityLogService.getUIConfigurationActivityFeed(configId, pageable);
            return ResponseEntity.ok(ApiResponse.success(activities));
            
        } catch (Exception e) {
            logger.error("Error getting config activity feed", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get config activity feed: " + e.getMessage()));
        }
    }

    @GetMapping("/activity/recent/{orgId}")
    @Operation(summary = "Get recent activities for an organization")
    @PreAuthorize("hasPermission(#orgId, 'ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<List<ActivityLog>>> getRecentActivities(
            @Parameter(description = "Organization ID") @PathVariable UUID orgId,
            @Parameter(description = "Hours to look back") @RequestParam(defaultValue = "24") int hours) {
        
        try {
            List<ActivityLog> activities = activityLogService.getRecentActivities(orgId, hours);
            return ResponseEntity.ok(ApiResponse.success(activities));
            
        } catch (Exception e) {
            logger.error("Error getting recent activities", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get recent activities: " + e.getMessage()));
        }
    }

    @GetMapping("/activity/statistics/{orgId}")
    @Operation(summary = "Get activity statistics for an organization")
    @PreAuthorize("hasPermission(#orgId, 'ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<ActivityLogRepository.ActivityStatistics>> getActivityStatistics(
            @Parameter(description = "Organization ID") @PathVariable UUID orgId,
            @Parameter(description = "Start time") @RequestParam LocalDateTime startTime,
            @Parameter(description = "End time") @RequestParam LocalDateTime endTime) {
        
        try {
            ActivityLogRepository.ActivityStatistics stats = 
                    activityLogService.getActivityStatistics(orgId, startTime, endTime);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("Error getting activity statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get activity statistics: " + e.getMessage()));
        }
    }

    // Operation History APIs

    @GetMapping("/operations/history/{configId}")
    @Operation(summary = "Get operation history for a UI configuration")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<List<com.uiplatform.collaboration.OperationalTransformation.Operation>>> getOperationHistory(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            @Parameter(description = "Limit") @RequestParam(defaultValue = "50") int limit) {
        
        try {
            List<com.uiplatform.collaboration.OperationalTransformation.Operation> operations = 
                    operationHistoryService.getOperationHistory(configId, limit);
            return ResponseEntity.ok(ApiResponse.success(operations));
            
        } catch (Exception e) {
            logger.error("Error getting operation history", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get operation history: " + e.getMessage()));
        }
    }

    @PostMapping("/operations/undo/{configId}")
    @Operation(summary = "Undo last operation for a user")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'WRITE')")
    public ResponseEntity<ApiResponse<OperationHistoryService.OperationResult>> undoOperation(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            Principal principal) {
        
        try {
            UUID userId = UUID.fromString(principal.getName());
            OperationHistoryService.OperationResult result = operationHistoryService.undoOperation(configId, userId);
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(ApiResponse.success(result));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error(result.getMessage()));
            }
            
        } catch (Exception e) {
            logger.error("Error undoing operation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to undo operation: " + e.getMessage()));
        }
    }

    @PostMapping("/operations/redo/{configId}")
    @Operation(summary = "Redo last undone operation for a user")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'WRITE')")
    public ResponseEntity<ApiResponse<OperationHistoryService.OperationResult>> redoOperation(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            Principal principal) {
        
        try {
            UUID userId = UUID.fromString(principal.getName());
            OperationHistoryService.OperationResult result = operationHistoryService.redoOperation(configId, userId);
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(ApiResponse.success(result));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error(result.getMessage()));
            }
            
        } catch (Exception e) {
            logger.error("Error redoing operation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to redo operation: " + e.getMessage()));
        }
    }

    @GetMapping("/operations/undo-stack-size/{configId}")
    @Operation(summary = "Get undo stack size for a user")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<Long>> getUndoStackSize(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            Principal principal) {
        
        try {
            UUID userId = UUID.fromString(principal.getName());
            long stackSize = operationHistoryService.getUndoStackSize(configId, userId);
            return ResponseEntity.ok(ApiResponse.success(stackSize));
            
        } catch (Exception e) {
            logger.error("Error getting undo stack size", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get undo stack size: " + e.getMessage()));
        }
    }

    @GetMapping("/operations/redo-stack-size/{configId}")
    @Operation(summary = "Get redo stack size for a user")
    @PreAuthorize("hasPermission(#configId, 'UI_CONFIG', 'READ')")
    public ResponseEntity<ApiResponse<Long>> getRedoStackSize(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID configId,
            Principal principal) {
        
        try {
            UUID userId = UUID.fromString(principal.getName());
            long stackSize = operationHistoryService.getRedoStackSize(configId, userId);
            return ResponseEntity.ok(ApiResponse.success(stackSize));
            
        } catch (Exception e) {
            logger.error("Error getting redo stack size", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get redo stack size: " + e.getMessage()));
        }
    }
}
