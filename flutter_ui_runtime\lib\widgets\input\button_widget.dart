import 'package:flutter/material.dart';
import 'package:vibration/vibration.dart';
import '../../core/widgets/dynamic_widget.dart';
import '../../core/models/ui_metadata.dart';
import '../../core/utils/logger.dart';

/// Dynamic button widget that renders buttons from configuration
class ButtonWidget extends DynamicWidget {
  const ButtonWidget(super.config);

  @override
  Widget build() {
    final text = getRequiredProp<String>('text');
    final variant = getProp<String>('variant', 'elevated');
    final size = getProp<String>('size', 'medium');
    final disabled = getProp<bool>('disabled', false);
    final loading = getProp<bool>('loading', false);
    final fullWidth = getProp<bool>('fullWidth', false);
    final icon = getProp<Map<String, dynamic>>('icon');
    final hapticFeedback = getProp<bool>('hapticFeedback', true);

    final buttonStyle = _buildButtonStyle(variant, size);
    final child = _buildButtonChild(text, icon, loading);

    Widget button;
    switch (variant.toLowerCase()) {
      case 'elevated':
        button = ElevatedButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          style: buttonStyle,
          child: child,
        );
        break;
      case 'outlined':
        button = OutlinedButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          style: buttonStyle,
          child: child,
        );
        break;
      case 'text':
        button = TextButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          style: buttonStyle,
          child: child,
        );
        break;
      case 'icon':
        button = IconButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          style: buttonStyle,
          icon: _buildIcon(icon) ?? const Icon(Icons.touch_app),
        );
        break;
      case 'fab':
        button = FloatingActionButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          child: _buildIcon(icon) ?? const Icon(Icons.add),
        );
        break;
      default:
        button = ElevatedButton(
          onPressed: disabled || loading ? null : () => _handlePress(hapticFeedback),
          style: buttonStyle,
          child: child,
        );
    }

    if (fullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  ButtonStyle _buildButtonStyle(String variant, String size) {
    final padding = _getPaddingForSize(size);
    final textStyle = _getTextStyleForSize(size);

    return ButtonStyle(
      padding: MaterialStateProperty.all(padding),
      textStyle: MaterialStateProperty.all(textStyle),
      minimumSize: MaterialStateProperty.all(_getMinimumSizeForSize(size)),
    );
  }

  Widget _buildButtonChild(String text, Map<String, dynamic>? iconConfig, bool loading) {
    if (loading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }

    if (iconConfig != null) {
      final icon = _buildIcon(iconConfig);
      final iconPosition = iconConfig['position'] as String? ?? 'left';

      if (icon != null) {
        if (iconPosition == 'right') {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(text),
              const SizedBox(width: 8),
              icon,
            ],
          );
        } else {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              icon,
              const SizedBox(width: 8),
              Text(text),
            ],
          );
        }
      }
    }

    return Text(text);
  }

  Widget? _buildIcon(Map<String, dynamic>? iconConfig) {
    if (iconConfig == null) return null;

    final iconName = iconConfig['name'] as String?;
    final iconSize = (iconConfig['size'] as num?)?.toDouble() ?? 24.0;
    final iconColor = buildColor(iconConfig['color']);

    if (iconName != null) {
      final iconData = _getIconData(iconName);
      if (iconData != null) {
        return Icon(
          iconData,
          size: iconSize,
          color: iconColor,
        );
      }
    }

    return null;
  }

  IconData? _getIconData(String iconName) {
    // Map string names to IconData
    switch (iconName.toLowerCase()) {
      case 'add':
        return Icons.add;
      case 'remove':
        return Icons.remove;
      case 'edit':
        return Icons.edit;
      case 'delete':
        return Icons.delete;
      case 'save':
        return Icons.save;
      case 'cancel':
        return Icons.cancel;
      case 'check':
        return Icons.check;
      case 'close':
        return Icons.close;
      case 'search':
        return Icons.search;
      case 'menu':
        return Icons.menu;
      case 'home':
        return Icons.home;
      case 'settings':
        return Icons.settings;
      case 'person':
        return Icons.person;
      case 'favorite':
        return Icons.favorite;
      case 'star':
        return Icons.star;
      case 'share':
        return Icons.share;
      case 'download':
        return Icons.download;
      case 'upload':
        return Icons.upload;
      case 'refresh':
        return Icons.refresh;
      case 'info':
        return Icons.info;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'success':
        return Icons.check_circle;
      case 'arrow_back':
        return Icons.arrow_back;
      case 'arrow_forward':
        return Icons.arrow_forward;
      case 'arrow_up':
        return Icons.arrow_upward;
      case 'arrow_down':
        return Icons.arrow_downward;
      case 'play':
        return Icons.play_arrow;
      case 'pause':
        return Icons.pause;
      case 'stop':
        return Icons.stop;
      case 'volume_up':
        return Icons.volume_up;
      case 'volume_down':
        return Icons.volume_down;
      case 'volume_off':
        return Icons.volume_off;
      case 'camera':
        return Icons.camera_alt;
      case 'photo':
        return Icons.photo;
      case 'video':
        return Icons.videocam;
      case 'location':
        return Icons.location_on;
      case 'map':
        return Icons.map;
      case 'calendar':
        return Icons.calendar_today;
      case 'clock':
        return Icons.access_time;
      case 'email':
        return Icons.email;
      case 'phone':
        return Icons.phone;
      case 'message':
        return Icons.message;
      case 'notification':
        return Icons.notifications;
      case 'lock':
        return Icons.lock;
      case 'unlock':
        return Icons.lock_open;
      case 'visibility':
        return Icons.visibility;
      case 'visibility_off':
        return Icons.visibility_off;
      default:
        return null;
    }
  }

  EdgeInsets _getPaddingForSize(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case 'medium':
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case 'large':
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
      default:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    }
  }

  TextStyle _getTextStyleForSize(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return const TextStyle(fontSize: 12);
      case 'medium':
        return const TextStyle(fontSize: 14);
      case 'large':
        return const TextStyle(fontSize: 16);
      default:
        return const TextStyle(fontSize: 14);
    }
  }

  Size _getMinimumSizeForSize(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return const Size(64, 32);
      case 'medium':
        return const Size(80, 40);
      case 'large':
        return const Size(96, 48);
      default:
        return const Size(80, 40);
    }
  }

  void _handlePress(bool hapticFeedback) {
    if (hapticFeedback) {
      _triggerHapticFeedback();
    }
    
    AppLogger.debug('Button pressed: ${config.id}');
    // Event handling would be implemented here
  }

  void _triggerHapticFeedback() {
    try {
      Vibration.hasVibrator().then((hasVibrator) {
        if (hasVibrator == true) {
          Vibration.vibrate(duration: 50);
        }
      });
    } catch (error) {
      AppLogger.warning('Failed to trigger haptic feedback: $error');
    }
  }
}
