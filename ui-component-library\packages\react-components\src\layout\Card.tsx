import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '../utils/cn';
import type { ExtendedHTMLProps } from '../types/component';

const cardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm',
  {
    variants: {
      variant: {
        default: '',
        outlined: 'border-2',
        elevated: 'shadow-md',
        filled: 'bg-muted border-0',
      },
      size: {
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
      },
      interactive: {
        true: 'cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-[1.02]',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      interactive: false,
    },
  }
);

export interface CardProps
  extends ExtendedHTMLProps,
    VariantProps<typeof cardVariants> {
  /** Whether the card is clickable */
  interactive?: boolean;
  /** Click handler for interactive cards */
  onClick?: () => void;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, interactive, onClick, children, testId, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, interactive, className }))}
        onClick={interactive ? onClick : undefined}
        data-testid={testId}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card Header
export interface CardHeaderProps extends ExtendedHTMLProps {
  /** Whether to add bottom border */
  bordered?: boolean;
}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, bordered, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col space-y-1.5 p-6',
          bordered && 'border-b',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Title
export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  /** Heading level */
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, level = 3, children, ...props }, ref) => {
    const Heading = `h${level}` as keyof JSX.IntrinsicElements;
    
    return (
      <Heading
        ref={ref}
        className={cn(
          'text-2xl font-semibold leading-none tracking-tight',
          level === 1 && 'text-3xl',
          level === 2 && 'text-2xl',
          level === 3 && 'text-xl',
          level === 4 && 'text-lg',
          level === 5 && 'text-base',
          level === 6 && 'text-sm',
          className
        )}
        {...props}
      >
        {children}
      </Heading>
    );
  }
);

CardTitle.displayName = 'CardTitle';

// Card Description
export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn('text-sm text-muted-foreground', className)}
        {...props}
      >
        {children}
      </p>
    );
  }
);

CardDescription.displayName = 'CardDescription';

// Card Content
export interface CardContentProps extends ExtendedHTMLProps {
  /** Whether to remove default padding */
  noPadding?: boolean;
}

const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, noPadding, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(!noPadding && 'p-6 pt-0', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer
export interface CardFooterProps extends ExtendedHTMLProps {
  /** Whether to add top border */
  bordered?: boolean;
  /** Footer alignment */
  align?: 'left' | 'center' | 'right' | 'between';
}

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, bordered, align = 'left', children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center p-6 pt-0',
          bordered && 'border-t pt-6',
          align === 'left' && 'justify-start',
          align === 'center' && 'justify-center',
          align === 'right' && 'justify-end',
          align === 'between' && 'justify-between',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Card Image
export interface CardImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  /** Image position */
  position?: 'top' | 'bottom' | 'left' | 'right';
  /** Aspect ratio */
  aspectRatio?: 'square' | 'video' | 'auto';
}

const CardImage = forwardRef<HTMLImageElement, CardImageProps>(
  ({ className, position = 'top', aspectRatio = 'auto', alt, ...props }, ref) => {
    return (
      <img
        ref={ref}
        className={cn(
          'object-cover',
          position === 'top' && 'rounded-t-lg',
          position === 'bottom' && 'rounded-b-lg',
          position === 'left' && 'rounded-l-lg',
          position === 'right' && 'rounded-r-lg',
          aspectRatio === 'square' && 'aspect-square',
          aspectRatio === 'video' && 'aspect-video',
          aspectRatio === 'auto' && 'w-full h-auto',
          className
        )}
        alt={alt}
        {...props}
      />
    );
  }
);

CardImage.displayName = 'CardImage';

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardImage,
  cardVariants,
};
