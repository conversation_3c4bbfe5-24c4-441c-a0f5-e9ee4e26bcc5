import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Input, 
  Select, 
  Button, 
  Modal, 
  Form, 
  Upload, 
  message, 
  Spin, 
  Empty,
  Tag,
  Tooltip,
  Dropdown,
  Menu,
  Space,
  Rate,
  Avatar
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DownloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  HeartOutlined,
  HeartFilled,
  MoreOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  AppstoreOutlined,
  BarsOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { useAnalytics } from '../../hooks/useAnalytics';
import { apiClient } from '../../services/apiClient';
import './TemplateManager.scss';

const { Search } = Input;
const { Option } = Select;
const { Meta } = Card;

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  previewImage?: string;
  configData: any;
  isPublic: boolean;
  downloads: number;
  rating: number;
  reviews: number;
  createdBy: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    avatarUrl?: string;
  };
  createdAt: string;
  updatedAt: string;
  isFavorite?: boolean;
}

export interface TemplateManagerProps {
  className?: string;
  mode?: 'browse' | 'manage';
  onTemplateSelect?: (template: Template) => void;
  onTemplateApply?: (template: Template) => void;
}

const TEMPLATE_CATEGORIES = [
  'All',
  'Dashboard',
  'Form',
  'Landing Page',
  'E-commerce',
  'Blog',
  'Portfolio',
  'Admin Panel',
  'Mobile App',
  'Email Template',
  'Other'
];

const SORT_OPTIONS = [
  { value: 'popular', label: 'Most Popular' },
  { value: 'recent', label: 'Most Recent' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'downloads', label: 'Most Downloaded' },
  { value: 'name', label: 'Name A-Z' }
];

export const TemplateManager: React.FC<TemplateManagerProps> = ({
  className,
  mode = 'browse',
  onTemplateSelect,
  onTemplateApply
}) => {
  const dispatch = useDispatch();
  const { trackUserAction } = useAnalytics();
  const { currentUser } = useSelector((state: RootState) => state.auth);
  
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  
  // Modal states
  const [previewModal, setPreviewModal] = useState<{ visible: boolean; template: Template | null }>({
    visible: false,
    template: null
  });
  const [uploadModal, setUploadModal] = useState(false);
  const [editModal, setEditModal] = useState<{ visible: boolean; template: Template | null }>({
    visible: false,
    template: null
  });

  // Form instances
  const [uploadForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // Fetch templates
  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        search: searchQuery || undefined,
        category: selectedCategory !== 'All' ? selectedCategory : undefined,
        sortBy,
        isPublic: mode === 'browse' ? true : undefined,
        createdBy: mode === 'manage' ? currentUser?.id : undefined
      };

      const response = await apiClient.get('/templates', { params });
      setTemplates(response.data.content || []);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedCategory, sortBy, mode, currentUser?.id]);

  // Filter and sort templates
  const filteredTemplates = useMemo(() => {
    let filtered = templates;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.downloads - a.downloads;
        case 'recent':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'rating':
          return b.rating - a.rating;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    return filtered;
  }, [templates, searchQuery, selectedCategory, sortBy]);

  // Handle template actions
  const handleTemplatePreview = useCallback((template: Template) => {
    setPreviewModal({ visible: true, template });
    trackUserAction('template_previewed', { templateId: template.id });
  }, [trackUserAction]);

  const handleTemplateApply = useCallback(async (template: Template) => {
    try {
      // Track download
      await apiClient.post(`/templates/${template.id}/download`);
      
      onTemplateApply?.(template);
      trackUserAction('template_applied', { templateId: template.id });
      message.success(`Template "${template.name}" applied successfully`);
    } catch (error) {
      console.error('Failed to apply template:', error);
      message.error('Failed to apply template');
    }
  }, [onTemplateApply, trackUserAction]);

  const handleTemplateFavorite = useCallback(async (template: Template) => {
    try {
      const action = template.isFavorite ? 'unfavorite' : 'favorite';
      await apiClient.post(`/templates/${template.id}/${action}`);
      
      setTemplates(prev => prev.map(t => 
        t.id === template.id ? { ...t, isFavorite: !t.isFavorite } : t
      ));
      
      trackUserAction(`template_${action}d`, { templateId: template.id });
    } catch (error) {
      console.error('Failed to update favorite:', error);
      message.error('Failed to update favorite');
    }
  }, [trackUserAction]);

  const handleTemplateDelete = useCallback(async (template: Template) => {
    Modal.confirm({
      title: 'Delete Template',
      content: `Are you sure you want to delete "${template.name}"?`,
      okText: 'Delete',
      okType: 'danger',
      onOk: async () => {
        try {
          await apiClient.delete(`/templates/${template.id}`);
          setTemplates(prev => prev.filter(t => t.id !== template.id));
          message.success('Template deleted successfully');
          trackUserAction('template_deleted', { templateId: template.id });
        } catch (error) {
          console.error('Failed to delete template:', error);
          message.error('Failed to delete template');
        }
      }
    });
  }, [trackUserAction]);

  // Handle template upload
  const handleTemplateUpload = useCallback(async (values: any) => {
    try {
      const templateData = {
        name: values.name,
        description: values.description,
        category: values.category,
        tags: values.tags || [],
        isPublic: values.isPublic,
        configData: values.configData ? JSON.parse(values.configData) : {}
      };

      const response = await apiClient.post('/templates', templateData);
      setTemplates(prev => [response.data, ...prev]);
      setUploadModal(false);
      uploadForm.resetFields();
      message.success('Template uploaded successfully');
      trackUserAction('template_uploaded', { templateId: response.data.id });
    } catch (error) {
      console.error('Failed to upload template:', error);
      message.error('Failed to upload template');
    }
  }, [uploadForm, trackUserAction]);

  // Load templates on mount and when filters change
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Template card component
  const TemplateCard: React.FC<{ template: Template }> = ({ template }) => {
    const actions = [
      <Tooltip title="Preview">
        <EyeOutlined onClick={() => handleTemplatePreview(template)} />
      </Tooltip>,
      <Tooltip title={template.isFavorite ? "Remove from favorites" : "Add to favorites"}>
        {template.isFavorite ? (
          <HeartFilled style={{ color: '#ff4d4f' }} onClick={() => handleTemplateFavorite(template)} />
        ) : (
          <HeartOutlined onClick={() => handleTemplateFavorite(template)} />
        )}
      </Tooltip>,
      <Tooltip title="Apply Template">
        <DownloadOutlined onClick={() => handleTemplateApply(template)} />
      </Tooltip>
    ];

    if (mode === 'manage' && template.createdBy.id === currentUser?.id) {
      actions.push(
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item key="edit" icon={<EditOutlined />}>
                Edit
              </Menu.Item>
              <Menu.Item key="share" icon={<ShareAltOutlined />}>
                Share
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item key="delete" icon={<DeleteOutlined />} danger>
                Delete
              </Menu.Item>
            </Menu>
          }
          trigger={['click']}
        >
          <MoreOutlined />
        </Dropdown>
      );
    }

    return (
      <Card
        hoverable
        className="template-card"
        cover={
          template.previewImage ? (
            <img alt={template.name} src={template.previewImage} />
          ) : (
            <div className="template-placeholder">
              <AppstoreOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
            </div>
          )
        }
        actions={actions}
      >
        <Meta
          title={
            <div className="template-title">
              <span>{template.name}</span>
              <Tag color="blue">{template.category}</Tag>
            </div>
          }
          description={
            <div className="template-description">
              <p>{template.description}</p>
              <div className="template-meta">
                <Space>
                  <Rate disabled defaultValue={template.rating} allowHalf />
                  <span>({template.reviews})</span>
                  <span>{template.downloads} downloads</span>
                </Space>
              </div>
              <div className="template-tags">
                {template.tags.slice(0, 3).map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
                {template.tags.length > 3 && (
                  <Tag size="small">+{template.tags.length - 3} more</Tag>
                )}
              </div>
              <div className="template-author">
                <Avatar size="small" src={template.createdBy.avatarUrl}>
                  {template.createdBy.firstName?.[0]}
                </Avatar>
                <span>{template.createdBy.firstName} {template.createdBy.lastName}</span>
              </div>
            </div>
          }
        />
      </Card>
    );
  };

  return (
    <div className={`template-manager ${className || ''}`}>
      {/* Header */}
      <div className="template-manager-header">
        <div className="header-left">
          <h2>{mode === 'browse' ? 'Template Gallery' : 'My Templates'}</h2>
        </div>
        <div className="header-right">
          <Space>
            {mode === 'manage' && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setUploadModal(true)}
              >
                Upload Template
              </Button>
            )}
            <Button
              icon={viewMode === 'grid' ? <BarsOutlined /> : <AppstoreOutlined />}
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            />
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </Button>
          </Space>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card className="template-filters" size="small">
          <Row gutter={16}>
            <Col xs={24} sm={8} md={6}>
              <Search
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onSearch={setSearchQuery}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Select
                value={selectedCategory}
                onChange={setSelectedCategory}
                style={{ width: '100%' }}
                placeholder="Category"
              >
                {TEMPLATE_CATEGORIES.map(category => (
                  <Option key={category} value={category}>{category}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Select
                value={sortBy}
                onChange={setSortBy}
                style={{ width: '100%' }}
                placeholder="Sort by"
              >
                {SORT_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Col>
          </Row>
        </Card>
      )}

      {/* Templates Grid/List */}
      <div className="template-content">
        {loading ? (
          <div className="template-loading">
            <Spin size="large" />
            <p>Loading templates...</p>
          </div>
        ) : filteredTemplates.length === 0 ? (
          <Empty
            description="No templates found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <Row gutter={[16, 16]} className={`templates-${viewMode}`}>
            {filteredTemplates.map(template => (
              <Col
                key={template.id}
                xs={24}
                sm={viewMode === 'grid' ? 12 : 24}
                md={viewMode === 'grid' ? 8 : 24}
                lg={viewMode === 'grid' ? 6 : 24}
              >
                <TemplateCard template={template} />
              </Col>
            ))}
          </Row>
        )}
      </div>

      {/* Preview Modal */}
      <Modal
        title={previewModal.template?.name}
        visible={previewModal.visible}
        onCancel={() => setPreviewModal({ visible: false, template: null })}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewModal({ visible: false, template: null })}>
            Close
          </Button>,
          <Button
            key="apply"
            type="primary"
            onClick={() => {
              if (previewModal.template) {
                handleTemplateApply(previewModal.template);
                setPreviewModal({ visible: false, template: null });
              }
            }}
          >
            Apply Template
          </Button>
        ]}
      >
        {previewModal.template && (
          <div className="template-preview">
            <p>{previewModal.template.description}</p>
            <div className="preview-placeholder">
              <p>Template preview would be rendered here</p>
              <pre>{JSON.stringify(previewModal.template.configData, null, 2)}</pre>
            </div>
          </div>
        )}
      </Modal>

      {/* Upload Modal */}
      <Modal
        title="Upload Template"
        visible={uploadModal}
        onCancel={() => setUploadModal(false)}
        onOk={() => uploadForm.submit()}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleTemplateUpload}
        >
          <Form.Item
            name="name"
            label="Template Name"
            rules={[{ required: true, message: 'Please enter template name' }]}
          >
            <Input placeholder="Enter template name" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <Input.TextArea rows={3} placeholder="Describe your template" />
          </Form.Item>
          
          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select placeholder="Select category">
              {TEMPLATE_CATEGORIES.slice(1).map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="tags" label="Tags">
            <Select mode="tags" placeholder="Add tags" />
          </Form.Item>
          
          <Form.Item name="isPublic" label="Make Public" valuePropName="checked">
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="configData"
            label="Configuration Data"
            rules={[{ required: true, message: 'Please enter configuration data' }]}
          >
            <Input.TextArea
              rows={6}
              placeholder="Paste your UI configuration JSON here"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
