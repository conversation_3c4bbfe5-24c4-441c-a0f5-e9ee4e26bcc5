# UI Builder Frontend - Local Development Environment
# This file is used for local development

# Application Configuration
VITE_APP_NAME=UI Builder
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Visual UI Builder Platform

# Environment
VITE_NODE_ENV=development

# API Configuration
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_WEBSOCKET_URL=ws://localhost:8080/ws
VITE_API_TIMEOUT=30000

# Authentication
VITE_JWT_STORAGE_KEY=ui_builder_token
VITE_REFRESH_TOKEN_KEY=ui_builder_refresh_token
VITE_SESSION_TIMEOUT=1800000

# Features Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_COLLABORATION=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_OFFLINE_MODE=false
VITE_ENABLE_PWA=false
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_MULTI_LANGUAGE=false

# File Upload
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,svg,pdf,doc,docx

# External Services (disabled for local development)
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=
VITE_HOTJAR_ID=

# CDN Configuration (local development)
VITE_CDN_BASE_URL=http://localhost:3000
VITE_ASSETS_BASE_URL=http://localhost:3000/assets

# Social Authentication (disabled for local development)
VITE_GOOGLE_CLIENT_ID=
VITE_GITHUB_CLIENT_ID=
VITE_MICROSOFT_CLIENT_ID=

# Development Tools
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_STORYBOOK=true

# Performance
VITE_ENABLE_BUNDLE_ANALYZER=false
VITE_ENABLE_SOURCE_MAPS=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# Collaboration
VITE_MAX_CONCURRENT_USERS=5
VITE_COLLABORATION_TIMEOUT=300000
VITE_AUTO_SAVE_INTERVAL=30000

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_ANIMATIONS=true
VITE_ANIMATION_DURATION=300
VITE_SIDEBAR_WIDTH=280
VITE_TOOLBAR_HEIGHT=60

# Component Library (local development)
VITE_COMPONENT_LIBRARY_URL=http://localhost:3001
VITE_TEMPLATE_LIBRARY_URL=http://localhost:3001/templates

# Monitoring (disabled for local development)
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_PERFORMANCE_MONITORING=false
VITE_LOG_LEVEL=debug

# Security (relaxed for local development)
VITE_ENABLE_CSP=false
VITE_TRUSTED_DOMAINS=localhost,127.0.0.1

# Experimental Features (enabled for testing)
VITE_ENABLE_AI_SUGGESTIONS=false
VITE_ENABLE_VOICE_COMMANDS=false
VITE_ENABLE_GESTURE_CONTROLS=false
