package com.uibuilder.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import io.micrometer.core.instrument.binder.system.UptimeMetrics;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * Metrics Configuration for UI Builder Platform
 * 
 * Configures comprehensive monitoring and observability including:
 * - Application metrics (business and technical)
 * - JVM metrics (memory, GC, threads)
 * - System metrics (CPU, uptime)
 * - Custom business metrics
 * - Prometheus integration
 */
@Configuration
public class MetricsConfig {

    private final Environment environment;

    public MetricsConfig(Environment environment) {
        this.environment = environment;
    }

    /**
     * Prometheus meter registry for metrics collection
     */
    @Bean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    /**
     * Customize meter registry with common tags
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config()
                .commonTags(
                    "application", "ui-builder-backend",
                    "environment", environment.getActiveProfiles().length > 0 
                        ? environment.getActiveProfiles()[0] 
                        : "unknown",
                    "version", environment.getProperty("app.version", "unknown")
                );
    }

    /**
     * JVM memory metrics
     */
    @Bean
    public JvmMemoryMetrics jvmMemoryMetrics() {
        return new JvmMemoryMetrics();
    }

    /**
     * JVM garbage collection metrics
     */
    @Bean
    public JvmGcMetrics jvmGcMetrics() {
        return new JvmGcMetrics();
    }

    /**
     * JVM thread metrics
     */
    @Bean
    public JvmThreadMetrics jvmThreadMetrics() {
        return new JvmThreadMetrics();
    }

    /**
     * System processor metrics
     */
    @Bean
    public ProcessorMetrics processorMetrics() {
        return new ProcessorMetrics();
    }

    /**
     * System uptime metrics
     */
    @Bean
    public UptimeMetrics uptimeMetrics() {
        return new UptimeMetrics();
    }

    /**
     * Business metrics for UI Builder operations
     */
    @Bean
    public UIBuilderMetrics uiBuilderMetrics(MeterRegistry meterRegistry) {
        return new UIBuilderMetrics(meterRegistry);
    }
}

/**
 * Custom business metrics for UI Builder platform
 */
@Component
public class UIBuilderMetrics {

    private final MeterRegistry meterRegistry;
    
    // Configuration metrics
    private final Counter configurationsCreated;
    private final Counter configurationsUpdated;
    private final Counter configurationsDeleted;
    private final Timer configurationLoadTime;
    private final Timer configurationSaveTime;
    
    // Template metrics
    private final Counter templatesCreated;
    private final Counter templatesUsed;
    private final Timer templateRenderTime;
    
    // User metrics
    private final Counter userRegistrations;
    private final Counter userLogins;
    private final Counter userLogouts;
    private final Timer userSessionDuration;
    
    // API metrics
    private final Timer apiRequestDuration;
    private final Counter apiRequestsTotal;
    private final Counter apiErrorsTotal;
    
    // Database metrics
    private final Timer databaseQueryTime;
    private final Counter databaseConnectionsActive;
    private final Counter databaseConnectionsIdle;
    
    // Cache metrics
    private final Counter cacheHits;
    private final Counter cacheMisses;
    private final Timer cacheOperationTime;
    
    // WebSocket metrics
    private final Gauge webSocketConnections;
    private final Counter webSocketMessages;
    
    // Security metrics
    private final Counter authenticationAttempts;
    private final Counter authenticationFailures;
    private final Counter authorizationDenials;

    public UIBuilderMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize configuration metrics
        this.configurationsCreated = Counter.builder("configurations.created")
                .description("Number of configurations created")
                .register(meterRegistry);
                
        this.configurationsUpdated = Counter.builder("configurations.updated")
                .description("Number of configurations updated")
                .register(meterRegistry);
                
        this.configurationsDeleted = Counter.builder("configurations.deleted")
                .description("Number of configurations deleted")
                .register(meterRegistry);
                
        this.configurationLoadTime = Timer.builder("configurations.load.time")
                .description("Time taken to load configurations")
                .register(meterRegistry);
                
        this.configurationSaveTime = Timer.builder("configurations.save.time")
                .description("Time taken to save configurations")
                .register(meterRegistry);
        
        // Initialize template metrics
        this.templatesCreated = Counter.builder("templates.created")
                .description("Number of templates created")
                .register(meterRegistry);
                
        this.templatesUsed = Counter.builder("templates.used")
                .description("Number of times templates were used")
                .register(meterRegistry);
                
        this.templateRenderTime = Timer.builder("templates.render.time")
                .description("Time taken to render templates")
                .register(meterRegistry);
        
        // Initialize user metrics
        this.userRegistrations = Counter.builder("users.registrations")
                .description("Number of user registrations")
                .register(meterRegistry);
                
        this.userLogins = Counter.builder("users.logins")
                .description("Number of user logins")
                .register(meterRegistry);
                
        this.userLogouts = Counter.builder("users.logouts")
                .description("Number of user logouts")
                .register(meterRegistry);
                
        this.userSessionDuration = Timer.builder("users.session.duration")
                .description("Duration of user sessions")
                .register(meterRegistry);
        
        // Initialize API metrics
        this.apiRequestDuration = Timer.builder("api.requests.duration")
                .description("Duration of API requests")
                .register(meterRegistry);
                
        this.apiRequestsTotal = Counter.builder("api.requests.total")
                .description("Total number of API requests")
                .register(meterRegistry);
                
        this.apiErrorsTotal = Counter.builder("api.errors.total")
                .description("Total number of API errors")
                .register(meterRegistry);
        
        // Initialize database metrics
        this.databaseQueryTime = Timer.builder("database.query.time")
                .description("Time taken for database queries")
                .register(meterRegistry);
                
        this.databaseConnectionsActive = Counter.builder("database.connections.active")
                .description("Number of active database connections")
                .register(meterRegistry);
                
        this.databaseConnectionsIdle = Counter.builder("database.connections.idle")
                .description("Number of idle database connections")
                .register(meterRegistry);
        
        // Initialize cache metrics
        this.cacheHits = Counter.builder("cache.hits")
                .description("Number of cache hits")
                .register(meterRegistry);
                
        this.cacheMisses = Counter.builder("cache.misses")
                .description("Number of cache misses")
                .register(meterRegistry);
                
        this.cacheOperationTime = Timer.builder("cache.operation.time")
                .description("Time taken for cache operations")
                .register(meterRegistry);
        
        // Initialize WebSocket metrics
        this.webSocketConnections = Gauge.builder("websocket.connections")
                .description("Number of active WebSocket connections")
                .register(meterRegistry, this, UIBuilderMetrics::getWebSocketConnectionCount);
                
        this.webSocketMessages = Counter.builder("websocket.messages")
                .description("Number of WebSocket messages")
                .register(meterRegistry);
        
        // Initialize security metrics
        this.authenticationAttempts = Counter.builder("security.authentication.attempts")
                .description("Number of authentication attempts")
                .register(meterRegistry);
                
        this.authenticationFailures = Counter.builder("security.authentication.failures")
                .description("Number of authentication failures")
                .register(meterRegistry);
                
        this.authorizationDenials = Counter.builder("security.authorization.denials")
                .description("Number of authorization denials")
                .register(meterRegistry);
    }

    // Configuration metrics methods
    public void incrementConfigurationsCreated() {
        configurationsCreated.increment();
    }

    public void incrementConfigurationsUpdated() {
        configurationsUpdated.increment();
    }

    public void incrementConfigurationsDeleted() {
        configurationsDeleted.increment();
    }

    public Timer.Sample startConfigurationLoadTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordConfigurationLoadTime(Timer.Sample sample) {
        sample.stop(configurationLoadTime);
    }

    public Timer.Sample startConfigurationSaveTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordConfigurationSaveTime(Timer.Sample sample) {
        sample.stop(configurationSaveTime);
    }

    // Template metrics methods
    public void incrementTemplatesCreated() {
        templatesCreated.increment();
    }

    public void incrementTemplatesUsed() {
        templatesUsed.increment();
    }

    public void recordTemplateRenderTime(long milliseconds) {
        templateRenderTime.record(milliseconds, TimeUnit.MILLISECONDS);
    }

    // User metrics methods
    public void incrementUserRegistrations() {
        userRegistrations.increment();
    }

    public void incrementUserLogins() {
        userLogins.increment();
    }

    public void incrementUserLogouts() {
        userLogouts.increment();
    }

    public void recordUserSessionDuration(long milliseconds) {
        userSessionDuration.record(milliseconds, TimeUnit.MILLISECONDS);
    }

    // API metrics methods
    public Timer.Sample startApiRequestTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordApiRequestDuration(Timer.Sample sample, String method, String endpoint, int status) {
        sample.stop(Timer.builder("api.requests.duration")
                .tag("method", method)
                .tag("endpoint", endpoint)
                .tag("status", String.valueOf(status))
                .register(meterRegistry));
    }

    public void incrementApiRequests(String method, String endpoint) {
        apiRequestsTotal.increment(
                Tags.of(
                        "method", method,
                        "endpoint", endpoint
                )
        );
    }

    public void incrementApiErrors(String method, String endpoint, String errorType) {
        apiErrorsTotal.increment(
                Tags.of(
                        "method", method,
                        "endpoint", endpoint,
                        "error_type", errorType
                )
        );
    }

    // Database metrics methods
    public Timer.Sample startDatabaseQueryTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordDatabaseQueryTime(Timer.Sample sample, String operation, String table) {
        sample.stop(Timer.builder("database.query.time")
                .tag("operation", operation)
                .tag("table", table)
                .register(meterRegistry));
    }

    // Cache metrics methods
    public void incrementCacheHits(String cacheName) {
        cacheHits.increment(Tags.of("cache", cacheName));
    }

    public void incrementCacheMisses(String cacheName) {
        cacheMisses.increment(Tags.of("cache", cacheName));
    }

    public void recordCacheOperationTime(long milliseconds, String operation, String cacheName) {
        cacheOperationTime.record(milliseconds, TimeUnit.MILLISECONDS,
                Tags.of("operation", operation, "cache", cacheName));
    }

    // WebSocket metrics methods
    public void incrementWebSocketMessages(String messageType) {
        webSocketMessages.increment(Tags.of("type", messageType));
    }

    private double getWebSocketConnectionCount() {
        // This would be implemented to return actual WebSocket connection count
        return 0.0; // Placeholder
    }

    // Security metrics methods
    public void incrementAuthenticationAttempts(String method) {
        authenticationAttempts.increment(Tags.of("method", method));
    }

    public void incrementAuthenticationFailures(String method, String reason) {
        authenticationFailures.increment(Tags.of("method", method, "reason", reason));
    }

    public void incrementAuthorizationDenials(String resource, String action) {
        authorizationDenials.increment(Tags.of("resource", resource, "action", action));
    }

    // Custom gauge for business metrics
    public void registerBusinessGauge(String name, String description, Supplier<Number> valueSupplier) {
        Gauge.builder(name)
                .description(description)
                .register(meterRegistry, valueSupplier, supplier -> supplier.get().doubleValue());
    }

    // Health check metrics
    public void recordHealthCheckStatus(String component, boolean healthy) {
        Gauge.builder("health.check")
                .tag("component", component)
                .register(meterRegistry, healthy ? 1.0 : 0.0);
    }
}
