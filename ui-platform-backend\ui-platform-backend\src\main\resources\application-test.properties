# Test Environment Configuration

# Database Configuration (H2 for testing)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Flyway Configuration
spring.flyway.enabled=false

# Redis Configuration (Embedded for testing)
spring.data.redis.host=localhost
spring.data.redis.port=6370

# Kafka Configuration (Embedded for testing)
spring.kafka.bootstrap-servers=${spring.embedded.kafka.brokers}

# Security Configuration
app.jwt.secret=testSecretKey123456789012345678901234567890
app.jwt.expiration=3600000

# Logging Configuration
logging.level.com.uiplatform=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN

# GraphQL Configuration
spring.graphql.graphiql.enabled=false

# Actuator Configuration
management.endpoints.web.exposure.include=health

# Cache Configuration
spring.cache.type=simple

# Test specific settings
app.test.enable-test-containers=true
app.test.cleanup-after-test=true
