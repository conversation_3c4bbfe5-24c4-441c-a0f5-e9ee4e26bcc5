const StyleDictionary = require('style-dictionary');

// Custom transforms
StyleDictionary.registerTransform({
  name: 'size/px',
  type: 'value',
  matcher: (token) => {
    return token.attributes.category === 'size' || 
           token.attributes.category === 'spacing' ||
           token.attributes.category === 'border';
  },
  transformer: (token) => {
    return `${token.value}px`;
  }
});

StyleDictionary.registerTransform({
  name: 'shadow/css',
  type: 'value',
  matcher: (token) => {
    return token.attributes.category === 'shadow';
  },
  transformer: (token) => {
    const { x, y, blur, spread, color, opacity } = token.value;
    return `${x}px ${y}px ${blur}px ${spread}px rgba(${color}, ${opacity})`;
  }
});

StyleDictionary.registerTransform({
  name: 'duration/ms',
  type: 'value',
  matcher: (token) => {
    return token.attributes.category === 'duration';
  },
  transformer: (token) => {
    return `${token.value}ms`;
  }
});

StyleDictionary.registerTransform({
  name: 'font/css',
  type: 'value',
  matcher: (token) => {
    return token.attributes.category === 'font' && token.attributes.type === 'family';
  },
  transformer: (token) => {
    return token.value.join(', ');
  }
});

// Custom transform groups
StyleDictionary.registerTransformGroup({
  name: 'css/custom',
  transforms: [
    'attribute/cti',
    'name/cti/kebab',
    'time/seconds',
    'content/icon',
    'size/rem',
    'color/css',
    'size/px',
    'shadow/css',
    'duration/ms',
    'font/css'
  ]
});

StyleDictionary.registerTransformGroup({
  name: 'js/custom',
  transforms: [
    'attribute/cti',
    'name/cti/camel',
    'size/rem',
    'color/hex',
    'size/px',
    'duration/ms'
  ]
});

StyleDictionary.registerTransformGroup({
  name: 'flutter/custom',
  transforms: [
    'attribute/cti',
    'name/cti/camel',
    'color/hex8flutter',
    'size/flutter/remToDouble',
    'content/flutter/literal',
    'asset/flutter/literal',
    'font/flutter/literal'
  ]
});

// Custom formats
StyleDictionary.registerFormat({
  name: 'css/variables',
  formatter: function(dictionary) {
    return `:root {\n${dictionary.allTokens.map(token => 
      `  --${token.name}: ${token.value};`
    ).join('\n')}\n}`;
  }
});

StyleDictionary.registerFormat({
  name: 'typescript/es6-declarations',
  formatter: function(dictionary) {
    return `export const tokens = {\n${dictionary.allTokens.map(token => 
      `  ${token.name}: '${token.value}'`
    ).join(',\n')}\n} as const;\n\nexport type TokenName = keyof typeof tokens;`;
  }
});

StyleDictionary.registerFormat({
  name: 'flutter/class.dart',
  formatter: function(dictionary) {
    const header = `// Generated file - do not edit
import 'package:flutter/material.dart';

class DesignTokens {`;
    
    const tokens = dictionary.allTokens.map(token => {
      const name = token.name.replace(/-/g, '');
      return `  static const ${name} = ${token.value};`;
    }).join('\n');
    
    const footer = '\n}';
    
    return header + '\n' + tokens + footer;
  }
});

module.exports = {
  source: ['tokens/**/*.json'],
  platforms: {
    css: {
      transformGroup: 'css/custom',
      buildPath: 'dist/css/',
      files: [
        {
          destination: 'variables.css',
          format: 'css/variables'
        },
        {
          destination: 'tokens.css',
          format: 'css/variables'
        }
      ]
    },
    scss: {
      transformGroup: 'scss',
      buildPath: 'dist/scss/',
      files: [
        {
          destination: '_variables.scss',
          format: 'scss/variables'
        }
      ]
    },
    js: {
      transformGroup: 'js/custom',
      buildPath: 'dist/js/',
      files: [
        {
          destination: 'tokens.js',
          format: 'javascript/es6'
        }
      ]
    },
    ts: {
      transformGroup: 'js/custom',
      buildPath: 'dist/ts/',
      files: [
        {
          destination: 'tokens.ts',
          format: 'typescript/es6-declarations'
        }
      ]
    },
    json: {
      transformGroup: 'js/custom',
      buildPath: 'dist/json/',
      files: [
        {
          destination: 'tokens.json',
          format: 'json/nested'
        }
      ]
    },
    flutter: {
      transformGroup: 'flutter/custom',
      buildPath: 'dist/flutter/',
      files: [
        {
          destination: 'design_tokens.dart',
          format: 'flutter/class.dart'
        }
      ]
    }
  }
};
