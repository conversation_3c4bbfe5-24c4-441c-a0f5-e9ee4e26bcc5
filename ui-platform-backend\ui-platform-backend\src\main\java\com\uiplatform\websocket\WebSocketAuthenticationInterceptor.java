package com.uiplatform.websocket;

import com.uiplatform.security.JwtTokenProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.Principal;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * WebSocket authentication interceptor for JWT token validation.
 */
@Component
public class WebSocketAuthenticationInterceptor implements ChannelInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketAuthenticationInterceptor.class);

    private final JwtTokenProvider jwtTokenProvider;

    @Autowired
    public WebSocketAuthenticationInterceptor(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
            // Extract JWT token from headers
            String token = extractTokenFromHeaders(accessor);
            
            if (StringUtils.hasText(token) && jwtTokenProvider.validateToken(token)) {
                try {
                    // Get user information from token
                    String userId = jwtTokenProvider.getUserIdFromToken(token);
                    String username = jwtTokenProvider.getUsernameFromToken(token);
                    String organizationId = jwtTokenProvider.getOrganizationIdFromToken(token);
                    Set<String> roles = jwtTokenProvider.getRolesFromToken(token);
                    Set<String> permissions = jwtTokenProvider.getPermissionsFromToken(token);
                    
                    // Create authorities
                    List<SimpleGrantedAuthority> authorities = roles.stream()
                            .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
                            .collect(Collectors.toList());
                    
                    authorities.addAll(permissions.stream()
                            .map(SimpleGrantedAuthority::new)
                            .collect(Collectors.toList()));
                    
                    // Create authentication principal
                    Principal principal = new UsernamePasswordAuthenticationToken(userId, null, authorities);
                    accessor.setUser(principal);
                    
                    // Store additional user information in session attributes
                    accessor.getSessionAttributes().put("userId", UUID.fromString(userId));
                    accessor.getSessionAttributes().put("username", username);
                    accessor.getSessionAttributes().put("organizationId", UUID.fromString(organizationId));
                    accessor.getSessionAttributes().put("roles", roles);
                    accessor.getSessionAttributes().put("permissions", permissions);
                    
                    logger.debug("WebSocket authentication successful for user: {}", username);
                    
                } catch (Exception e) {
                    logger.error("Error during WebSocket authentication", e);
                    throw new IllegalArgumentException("Invalid authentication token");
                }
            } else {
                logger.warn("WebSocket connection attempt without valid JWT token");
                throw new IllegalArgumentException("Authentication required");
            }
        }
        
        return message;
    }

    /**
     * Extract JWT token from WebSocket headers.
     */
    private String extractTokenFromHeaders(StompHeaderAccessor accessor) {
        // Try to get token from Authorization header
        List<String> authHeaders = accessor.getNativeHeader("Authorization");
        if (authHeaders != null && !authHeaders.isEmpty()) {
            String authHeader = authHeaders.get(0);
            if (authHeader.startsWith("Bearer ")) {
                return authHeader.substring(7);
            }
        }
        
        // Try to get token from X-Authorization header (alternative)
        List<String> xAuthHeaders = accessor.getNativeHeader("X-Authorization");
        if (xAuthHeaders != null && !xAuthHeaders.isEmpty()) {
            return xAuthHeaders.get(0);
        }
        
        // Try to get token from query parameter (for cases where headers can't be set)
        List<String> tokenParams = accessor.getNativeHeader("token");
        if (tokenParams != null && !tokenParams.isEmpty()) {
            return tokenParams.get(0);
        }
        
        return null;
    }

    @Override
    public void postSend(Message<?> message, MessageChannel channel, boolean sent) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand()) && sent) {
            String sessionId = accessor.getSessionId();
            Principal user = accessor.getUser();
            
            if (user != null) {
                logger.info("WebSocket connection established for user: {} with session: {}", 
                           user.getName(), sessionId);
            }
        }
    }

    @Override
    public boolean preReceive(MessageChannel channel) {
        return true;
    }

    @Override
    public Message<?> postReceive(Message<?> message, MessageChannel channel) {
        return message;
    }

    @Override
    public void afterSendCompletion(Message<?> message, MessageChannel channel, boolean sent, Exception ex) {
        if (ex != null) {
            logger.error("Error sending WebSocket message", ex);
        }
    }

    @Override
    public void afterReceiveCompletion(Message<?> message, MessageChannel channel, Exception ex) {
        if (ex != null) {
            logger.error("Error receiving WebSocket message", ex);
        }
    }
}
