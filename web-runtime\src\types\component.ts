export interface ComponentConfig {
  id: string;
  type: string;
  displayName: string;
  description?: string;
  category: string;
  tags: string[];
  version: string;
  deprecated?: boolean;
  
  // Component properties schema
  properties: ComponentPropertySchema;
  
  // Required properties
  requiredProperties: string[];
  
  // Default values
  defaultProps?: Record<string, any>;
  
  // Styling options
  styling?: {
    supportedStyles: string[];
    customCSS?: boolean;
    themes?: string[];
  };
  
  // Layout options
  layout?: {
    canHaveChildren: boolean;
    maxChildren?: number;
    allowedChildren?: string[];
    restrictedParents?: string[];
  };
  
  // Events and interactions
  events?: ComponentEvent[];
  
  // Data binding
  dataBinding?: {
    supports: boolean;
    properties: string[];
  };
  
  // Responsive behavior
  responsive?: {
    breakpoints: string[];
    hiddenAt?: string[];
    visibleAt?: string[];
  };
  
  // Accessibility
  accessibility?: {
    role?: string;
    ariaLabels?: string[];
    keyboardNavigation?: boolean;
  };
  
  // Performance
  performance?: {
    lazy?: boolean;
    preload?: boolean;
    cacheStrategy?: 'memory' | 'disk' | 'none';
  };
}

export interface ComponentPropertySchema {
  [propertyName: string]: PropertyDefinition;
}

export interface PropertyDefinition {
  type: PropertyType;
  label: string;
  description?: string;
  defaultValue?: any;
  required?: boolean;
  
  // Validation
  validation?: PropertyValidation;
  
  // UI hints for property editor
  editor?: PropertyEditor;
  
  // Conditional visibility
  conditional?: PropertyConditional;
  
  // Grouping
  group?: string;
  order?: number;
}

export enum PropertyType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  COLOR = 'color',
  ICON = 'icon',
  IMAGE = 'image',
  URL = 'url',
  EMAIL = 'email',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  TEXTAREA = 'textarea',
  RICH_TEXT = 'rich_text',
  CODE = 'code',
  JSON = 'json',
  EXPRESSION = 'expression',
  COMPONENT_REF = 'component_ref',
  STYLE = 'style',
  ANIMATION = 'animation',
  FUNCTION = 'function'
}

export interface PropertyValidation {
  // String validation
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  
  // Number validation
  min?: number;
  max?: number;
  step?: number;
  
  // Array validation
  minItems?: number;
  maxItems?: number;
  uniqueItems?: boolean;
  
  // Select validation
  options?: PropertyOption[];
  
  // Custom validation
  custom?: string; // JavaScript expression
}

export interface PropertyOption {
  label: string;
  value: any;
  description?: string;
  icon?: string;
  disabled?: boolean;
  group?: string;
}

export interface PropertyEditor {
  type: 'input' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'slider' | 'color' | 'date' | 'file' | 'code' | 'rich_text' | 'custom';
  placeholder?: string;
  helpText?: string;
  
  // Editor-specific options
  options?: PropertyOption[];
  multiple?: boolean;
  accept?: string; // For file inputs
  language?: string; // For code editors
  theme?: string;
  
  // Custom editor component
  component?: string;
  props?: Record<string, any>;
}

export interface PropertyConditional {
  property: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists';
  value?: any;
}

export interface ComponentEvent {
  name: string;
  label: string;
  description?: string;
  parameters?: EventParameter[];
  
  // Event handling
  handler?: {
    type: 'function' | 'action' | 'navigation' | 'state_change';
    target?: string;
    payload?: Record<string, any>;
  };
}

export interface EventParameter {
  name: string;
  type: PropertyType;
  description?: string;
  required?: boolean;
}

export interface ComponentProps {
  [key: string]: any;
  
  // Common props
  id?: string;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  
  // Data binding
  data?: any;
  dataSource?: string;
  
  // State management
  state?: any;
  setState?: (state: any) => void;
  
  // Event handlers
  onClick?: (event: any) => void;
  onChange?: (value: any) => void;
  onFocus?: (event: any) => void;
  onBlur?: (event: any) => void;
  onSubmit?: (data: any) => void;
  
  // Device info
  deviceInfo?: DeviceInfo;
  
  // Theme
  theme?: string;
  
  // Accessibility
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
  tabIndex?: number;
  
  // Performance
  lazy?: boolean;
  priority?: 'high' | 'normal' | 'low';
}

export interface DeviceInfo {
  width: number;
  height: number;
  category: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  pixelRatio?: number;
  touchSupport?: boolean;
  platform?: string;
  userAgent?: string;
}

export interface ComponentValidation {
  required?: string[];
  types?: Record<string, PropertyType>;
  custom?: Array<(props: ComponentProps) => boolean | string>;
}

export interface ComponentMetadata {
  // Basic info
  name: string;
  displayName: string;
  description?: string;
  category: string;
  tags: string[];
  version: string;
  author?: string;
  license?: string;
  
  // Status
  deprecated?: boolean;
  experimental?: boolean;
  stable?: boolean;
  
  // Dependencies
  dependencies?: string[];
  peerDependencies?: string[];
  
  // Documentation
  documentation?: string;
  examples?: ComponentExample[];
  
  // Compatibility
  compatibility?: {
    react?: string;
    browsers?: string[];
    devices?: string[];
  };
  
  // Bundle info
  bundle?: {
    size?: number;
    gzipSize?: number;
    dependencies?: string[];
  };
}

export interface ComponentExample {
  name: string;
  description?: string;
  code: string;
  props: ComponentProps;
  preview?: string;
}

// Runtime types
export interface RuntimeConfig {
  // Environment
  environment: 'development' | 'staging' | 'production';
  debug: boolean;
  
  // API configuration
  apiBaseUrl: string;
  websocketUrl?: string;
  
  // Feature flags
  features: Record<string, boolean>;
  
  // Performance
  performance: {
    enableCaching: boolean;
    cacheStrategy: 'memory' | 'localStorage' | 'sessionStorage';
    maxCacheSize: number;
    enableLazyLoading: boolean;
    enableVirtualization: boolean;
  };
  
  // Security
  security: {
    enableCSP: boolean;
    allowedOrigins: string[];
    enableSanitization: boolean;
  };
  
  // Analytics
  analytics: {
    enabled: boolean;
    trackingId?: string;
    customEvents: boolean;
  };
  
  // Theming
  theming: {
    defaultTheme: string;
    allowCustomThemes: boolean;
    themes: string[];
  };
  
  // Localization
  localization: {
    defaultLocale: string;
    supportedLocales: string[];
    fallbackLocale: string;
  };
}

export interface UIMetadata {
  // Configuration info
  id: string;
  name: string;
  description?: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  
  // Author info
  author: {
    id: string;
    name: string;
    email?: string;
  };
  
  // Structure
  components: ComponentInstance[];
  layout: LayoutConfig;
  theme: ThemeConfig;
  
  // Data
  data?: {
    sources: DataSource[];
    bindings: DataBinding[];
  };
  
  // Interactions
  interactions?: Interaction[];
  
  // Settings
  settings: {
    responsive: boolean;
    accessibility: boolean;
    seo: SEOConfig;
    performance: PerformanceConfig;
  };
  
  // Metadata
  tags: string[];
  category?: string;
  isPublic: boolean;
  
  // Validation
  validation?: {
    errors: ValidationError[];
    warnings: ValidationWarning[];
  };
}

export interface ComponentInstance {
  id: string;
  type: string;
  props: ComponentProps;
  children?: ComponentInstance[];
  
  // Layout
  layout?: {
    position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';
    x?: number;
    y?: number;
    width?: number | string;
    height?: number | string;
    zIndex?: number;
  };
  
  // Responsive
  responsive?: {
    [breakpoint: string]: Partial<ComponentInstance>;
  };
  
  // Conditional rendering
  conditions?: RenderCondition[];
  
  // Animation
  animations?: Animation[];
  
  // Data binding
  dataBinding?: {
    source: string;
    property: string;
    transform?: string;
  };
}

export interface LayoutConfig {
  type: 'flex' | 'grid' | 'absolute' | 'flow';
  direction?: 'row' | 'column';
  wrap?: boolean;
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  gap?: number | string;
  
  // Grid specific
  columns?: number | string;
  rows?: number | string;
  areas?: string[][];
  
  // Responsive
  responsive?: {
    [breakpoint: string]: Partial<LayoutConfig>;
  };
}

export interface ThemeConfig {
  name: string;
  colors: Record<string, string>;
  typography: Record<string, any>;
  spacing: Record<string, string>;
  shadows: Record<string, string>;
  borders: Record<string, string>;
  animations: Record<string, any>;
  
  // Component overrides
  components?: Record<string, any>;
  
  // Custom CSS
  customCSS?: string;
}

export interface DataSource {
  id: string;
  name: string;
  type: 'api' | 'static' | 'computed' | 'user_input';
  
  // API source
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  
  // Static source
  data?: any;
  
  // Computed source
  expression?: string;
  dependencies?: string[];
  
  // Caching
  cache?: {
    enabled: boolean;
    ttl: number;
    key?: string;
  };
  
  // Polling
  polling?: {
    enabled: boolean;
    interval: number;
  };
}

export interface DataBinding {
  id: string;
  sourceId: string;
  targetComponentId: string;
  targetProperty: string;
  transform?: string;
  
  // Conditional binding
  condition?: string;
  
  // Two-way binding
  twoWay?: boolean;
}

export interface Interaction {
  id: string;
  trigger: {
    type: 'click' | 'hover' | 'focus' | 'change' | 'submit' | 'load' | 'custom';
    componentId?: string;
    event?: string;
  };
  
  actions: InteractionAction[];
  
  // Conditions
  conditions?: string[];
  
  // Debouncing
  debounce?: number;
}

export interface InteractionAction {
  type: 'navigate' | 'show_modal' | 'hide_modal' | 'update_data' | 'call_api' | 'run_script' | 'animate' | 'custom';
  
  // Navigation
  url?: string;
  target?: '_self' | '_blank' | '_parent' | '_top';
  
  // Modal
  modalId?: string;
  
  // Data update
  dataSourceId?: string;
  value?: any;
  
  // API call
  apiConfig?: {
    url: string;
    method: string;
    data?: any;
    headers?: Record<string, string>;
  };
  
  // Script
  script?: string;
  
  // Animation
  animation?: Animation;
  
  // Custom
  handler?: string;
  params?: Record<string, any>;
}

export interface RenderCondition {
  type: 'show' | 'hide';
  expression: string;
  dependencies?: string[];
}

export interface Animation {
  name: string;
  duration: number;
  delay?: number;
  easing?: string;
  iterations?: number | 'infinite';
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  
  // Keyframes
  keyframes: AnimationKeyframe[];
}

export interface AnimationKeyframe {
  offset: number; // 0-1
  properties: Record<string, any>;
}

export interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonical?: string;
  robots?: string;
  
  // Structured data
  structuredData?: Record<string, any>;
}

export interface PerformanceConfig {
  lazyLoading: boolean;
  imageOptimization: boolean;
  codesplitting: boolean;
  preloading: string[];
  
  // Bundle optimization
  bundleOptimization: {
    minification: boolean;
    compression: boolean;
    treeshaking: boolean;
  };
  
  // Caching
  caching: {
    strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
    maxAge: number;
  };
}

export interface ValidationError {
  id: string;
  type: 'error';
  message: string;
  componentId?: string;
  property?: string;
  severity: 'high' | 'medium' | 'low';
}

export interface ValidationWarning {
  id: string;
  type: 'warning';
  message: string;
  componentId?: string;
  property?: string;
  suggestion?: string;
}
