import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Slot } from '@radix-ui/react-slot';
import { Loader2 } from 'lucide-react';

import { cn } from '../utils/cn';
import type { ExtendedButtonProps, LoadingState, AnimationProps } from '../types/component';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        xs: 'h-7 rounded px-2 text-xs',
        sm: 'h-8 rounded-md px-3 text-xs',
        md: 'h-9 px-4 py-2',
        lg: 'h-10 rounded-md px-8',
        xl: 'h-11 rounded-md px-8',
        icon: 'h-9 w-9',
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends ExtendedButtonProps,
    VariantProps<typeof buttonVariants>,
    LoadingState,
    AnimationProps {
  /** Render as child component */
  asChild?: boolean;
  /** Icon to display before text */
  leftIcon?: React.ReactNode;
  /** Icon to display after text */
  rightIcon?: React.ReactNode;
  /** Loading spinner icon */
  loadingIcon?: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      asChild = false,
      children,
      disabled,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      loadingIcon,
      testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';
    const isDisabled = disabled || loading;

    const renderContent = () => {
      if (loading) {
        return (
          <>
            {loadingIcon || <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loadingText || children}
          </>
        );
      }

      return (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      );
    };

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, fullWidth, className }))}
        ref={ref}
        disabled={isDisabled}
        data-testid={testId}
        {...props}
      >
        {renderContent()}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };

// Button group component for related actions
export interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Button group size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Button group variant */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Whether buttons are attached */
  attached?: boolean;
}

const buttonGroupVariants = cva('inline-flex', {
  variants: {
    orientation: {
      horizontal: 'flex-row',
      vertical: 'flex-col',
    },
    attached: {
      true: '',
      false: 'gap-2',
    },
  },
  defaultVariants: {
    orientation: 'horizontal',
    attached: false,
  },
});

export const ButtonGroup = forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, orientation, attached, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          buttonGroupVariants({ orientation, attached }),
          attached && orientation === 'horizontal' && '[&>*:not(:first-child)]:rounded-l-none [&>*:not(:last-child)]:rounded-r-none [&>*:not(:first-child)]:border-l-0',
          attached && orientation === 'vertical' && '[&>*:not(:first-child)]:rounded-t-none [&>*:not(:last-child)]:rounded-b-none [&>*:not(:first-child)]:border-t-0',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ButtonGroup.displayName = 'ButtonGroup';

// Icon button component
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  /** Icon to display */
  icon: React.ReactNode;
  /** Accessibility label */
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, children, size = 'icon', ...props }, ref) => {
    return (
      <Button ref={ref} size={size} {...props}>
        {icon}
        {children}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Floating action button
export interface FABProps extends ButtonProps {
  /** Position of the FAB */
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  /** Whether FAB is extended with text */
  extended?: boolean;
}

const fabVariants = cva(
  'fixed z-50 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl',
  {
    variants: {
      position: {
        'bottom-right': 'bottom-4 right-4',
        'bottom-left': 'bottom-4 left-4',
        'top-right': 'top-4 right-4',
        'top-left': 'top-4 left-4',
      },
      extended: {
        true: 'px-4',
        false: 'p-4',
      },
    },
    defaultVariants: {
      position: 'bottom-right',
      extended: false,
    },
  }
);

export const FAB = forwardRef<HTMLButtonElement, FABProps>(
  ({ className, position, extended, size, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(fabVariants({ position, extended }), className)}
        size={extended ? size : 'icon'}
        {...props}
      />
    );
  }
);

FAB.displayName = 'FAB';
