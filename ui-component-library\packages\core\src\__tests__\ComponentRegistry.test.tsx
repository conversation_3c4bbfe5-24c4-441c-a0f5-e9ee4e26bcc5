import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComponentRegistry } from '../ComponentRegistry';
import { ComponentConfig, ComponentProps } from '../types/component';

// Mock components for testing
const MockTextComponent: React.FC<ComponentProps> = ({ text, className, style }) => (
  <div className={className} style={style} data-testid="mock-text">
    {text}
  </div>
);

const MockButtonComponent: React.FC<ComponentProps> = ({ 
  text, 
  onClick, 
  disabled, 
  variant = 'primary',
  className,
  style 
}) => (
  <button
    className={`btn btn-${variant} ${className || ''}`}
    style={style}
    onClick={onClick}
    disabled={disabled}
    data-testid="mock-button"
  >
    {text}
  </button>
);

const MockInputComponent: React.FC<ComponentProps> = ({ 
  placeholder, 
  value, 
  onChange, 
  type = 'text',
  className,
  style 
}) => (
  <input
    type={type}
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    className={className}
    style={style}
    data-testid="mock-input"
  />
);

describe('ComponentRegistry', () => {
  let registry: ComponentRegistry;

  beforeEach(() => {
    registry = new ComponentRegistry();
  });

  afterEach(() => {
    registry.clear();
  });

  describe('Component Registration', () => {
    it('should register a component', () => {
      const config: ComponentConfig = {
        id: 'text',
        displayName: 'Text',
        category: 'Display',
        description: 'A text component',
        properties: {
          text: { type: 'string', default: '' },
        },
      };

      registry.register('text', MockTextComponent, config);

      expect(registry.isRegistered('text')).toBe(true);
      expect(registry.getComponent('text')).toBe(MockTextComponent);
      expect(registry.getConfig('text')).toEqual(config);
    });

    it('should register multiple components', () => {
      const textConfig: ComponentConfig = {
        id: 'text',
        displayName: 'Text',
        category: 'Display',
        description: 'A text component',
        properties: {},
      };

      const buttonConfig: ComponentConfig = {
        id: 'button',
        displayName: 'Button',
        category: 'Interactive',
        description: 'A button component',
        properties: {},
      };

      registry.register('text', MockTextComponent, textConfig);
      registry.register('button', MockButtonComponent, buttonConfig);

      expect(registry.isRegistered('text')).toBe(true);
      expect(registry.isRegistered('button')).toBe(true);
      expect(registry.getAllComponents()).toHaveLength(2);
    });

    it('should override existing component when registering with same key', () => {
      const config1: ComponentConfig = {
        id: 'text',
        displayName: 'Text v1',
        category: 'Display',
        description: 'Version 1',
        properties: {},
      };

      const config2: ComponentConfig = {
        id: 'text',
        displayName: 'Text v2',
        category: 'Display',
        description: 'Version 2',
        properties: {},
      };

      registry.register('text', MockTextComponent, config1);
      registry.register('text', MockTextComponent, config2);

      expect(registry.getConfig('text')?.displayName).toBe('Text v2');
    });
  });

  describe('Component Retrieval', () => {
    beforeEach(() => {
      const textConfig: ComponentConfig = {
        id: 'text',
        displayName: 'Text',
        category: 'Display',
        description: 'A text component',
        properties: {
          text: { type: 'string', default: 'Default text' },
        },
      };

      registry.register('text', MockTextComponent, textConfig);
    });

    it('should retrieve registered component', () => {
      const component = registry.getComponent('text');
      expect(component).toBe(MockTextComponent);
    });

    it('should return null for unregistered component', () => {
      const component = registry.getComponent('nonexistent');
      expect(component).toBeNull();
    });

    it('should retrieve component config', () => {
      const config = registry.getConfig('text');
      expect(config?.displayName).toBe('Text');
    });

    it('should return null for config of unregistered component', () => {
      const config = registry.getConfig('nonexistent');
      expect(config).toBeNull();
    });
  });

  describe('Component Creation', () => {
    beforeEach(() => {
      const textConfig: ComponentConfig = {
        id: 'text',
        displayName: 'Text',
        category: 'Display',
        description: 'A text component',
        properties: {
          text: { type: 'string', default: 'Default text' },
          className: { type: 'string', default: '' },
        },
      };

      const buttonConfig: ComponentConfig = {
        id: 'button',
        displayName: 'Button',
        category: 'Interactive',
        description: 'A button component',
        properties: {
          text: { type: 'string', default: 'Button' },
          variant: { type: 'string', default: 'primary' },
          disabled: { type: 'boolean', default: false },
        },
      };

      registry.register('text', MockTextComponent, textConfig);
      registry.register('button', MockButtonComponent, buttonConfig);
    });

    it('should create component with props', () => {
      const props = { text: 'Hello World', className: 'custom-class' };
      const element = registry.createElement('text', props);

      render(element);

      expect(screen.getByTestId('mock-text')).toBeInTheDocument();
      expect(screen.getByTestId('mock-text')).toHaveTextContent('Hello World');
      expect(screen.getByTestId('mock-text')).toHaveClass('custom-class');
    });

    it('should create component with default props', () => {
      const element = registry.createElement('text', {});

      render(element);

      expect(screen.getByTestId('mock-text')).toBeInTheDocument();
      expect(screen.getByTestId('mock-text')).toHaveTextContent('Default text');
    });

    it('should merge provided props with defaults', () => {
      const props = { text: 'Custom text' };
      const element = registry.createElement('button', props);

      render(element);

      const button = screen.getByTestId('mock-button');
      expect(button).toHaveTextContent('Custom text');
      expect(button).toHaveClass('btn-primary'); // Default variant
      expect(button).not.toBeDisabled(); // Default disabled: false
    });

    it('should return null for unregistered component', () => {
      const element = registry.createElement('nonexistent', {});
      expect(element).toBeNull();
    });

    it('should handle component with event handlers', () => {
      const handleClick = jest.fn();
      const props = { text: 'Click me', onClick: handleClick };
      const element = registry.createElement('button', props);

      render(element);

      const button = screen.getByTestId('mock-button');
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Lazy Loading', () => {
    it('should register lazy component', async () => {
      const lazyLoader = () => Promise.resolve(MockTextComponent);
      const config: ComponentConfig = {
        id: 'lazy-text',
        displayName: 'Lazy Text',
        category: 'Display',
        description: 'A lazy-loaded text component',
        properties: {},
      };

      registry.registerLazy('lazy-text', lazyLoader, config);

      expect(registry.isRegistered('lazy-text')).toBe(true);
      expect(registry.isLazy('lazy-text')).toBe(true);
    });

    it('should load lazy component on first access', async () => {
      const lazyLoader = jest.fn(() => Promise.resolve(MockTextComponent));
      const config: ComponentConfig = {
        id: 'lazy-text',
        displayName: 'Lazy Text',
        category: 'Display',
        description: 'A lazy-loaded text component',
        properties: {
          text: { type: 'string', default: 'Lazy text' },
        },
      };

      registry.registerLazy('lazy-text', lazyLoader, config);

      const element = registry.createElement('lazy-text', { text: 'Hello Lazy' });
      render(element);

      // Should show loading state initially
      expect(screen.getByText('Loading...')).toBeInTheDocument();

      // Wait for lazy component to load
      await waitFor(() => {
        expect(screen.getByTestId('mock-text')).toBeInTheDocument();
      });

      expect(screen.getByTestId('mock-text')).toHaveTextContent('Hello Lazy');
      expect(lazyLoader).toHaveBeenCalledTimes(1);
    });

    it('should cache loaded lazy component', async () => {
      const lazyLoader = jest.fn(() => Promise.resolve(MockTextComponent));
      const config: ComponentConfig = {
        id: 'lazy-text',
        displayName: 'Lazy Text',
        category: 'Display',
        description: 'A lazy-loaded text component',
        properties: {},
      };

      registry.registerLazy('lazy-text', lazyLoader, config);

      // Create component twice
      const element1 = registry.createElement('lazy-text', {});
      const element2 = registry.createElement('lazy-text', {});

      render(<div>{element1}{element2}</div>);

      await waitFor(() => {
        expect(screen.getAllByTestId('mock-text')).toHaveLength(2);
      });

      // Loader should only be called once due to caching
      expect(lazyLoader).toHaveBeenCalledTimes(1);
    });

    it('should handle lazy loading errors', async () => {
      const lazyLoader = () => Promise.reject(new Error('Loading failed'));
      const config: ComponentConfig = {
        id: 'error-component',
        displayName: 'Error Component',
        category: 'Display',
        description: 'A component that fails to load',
        properties: {},
      };

      registry.registerLazy('error-component', lazyLoader, config);

      const element = registry.createElement('error-component', {});
      render(element);

      await waitFor(() => {
        expect(screen.getByText(/Error loading component/)).toBeInTheDocument();
      });
    });
  });

  describe('Component Categories', () => {
    beforeEach(() => {
      const configs = [
        {
          id: 'text',
          displayName: 'Text',
          category: 'Display',
          description: 'Text component',
          properties: {},
        },
        {
          id: 'button',
          displayName: 'Button',
          category: 'Interactive',
          description: 'Button component',
          properties: {},
        },
        {
          id: 'input',
          displayName: 'Input',
          category: 'Form',
          description: 'Input component',
          properties: {},
        },
      ];

      configs.forEach(config => {
        registry.register(config.id, MockTextComponent, config);
      });
    });

    it('should get components by category', () => {
      const displayComponents = registry.getComponentsByCategory('Display');
      const interactiveComponents = registry.getComponentsByCategory('Interactive');
      const formComponents = registry.getComponentsByCategory('Form');

      expect(displayComponents).toHaveLength(1);
      expect(displayComponents[0].id).toBe('text');

      expect(interactiveComponents).toHaveLength(1);
      expect(interactiveComponents[0].id).toBe('button');

      expect(formComponents).toHaveLength(1);
      expect(formComponents[0].id).toBe('input');
    });

    it('should get all categories', () => {
      const categories = registry.getCategories();
      expect(categories).toEqual(['Display', 'Interactive', 'Form']);
    });

    it('should return empty array for non-existent category', () => {
      const components = registry.getComponentsByCategory('NonExistent');
      expect(components).toEqual([]);
    });
  });

  describe('Component Search', () => {
    beforeEach(() => {
      const configs = [
        {
          id: 'text-display',
          displayName: 'Text Display',
          category: 'Display',
          description: 'Displays text content',
          properties: {},
        },
        {
          id: 'rich-text',
          displayName: 'Rich Text',
          category: 'Display',
          description: 'Rich text editor component',
          properties: {},
        },
        {
          id: 'button',
          displayName: 'Button',
          category: 'Interactive',
          description: 'Clickable button element',
          properties: {},
        },
      ];

      configs.forEach(config => {
        registry.register(config.id, MockTextComponent, config);
      });
    });

    it('should search components by name', () => {
      const results = registry.searchComponents('text');
      expect(results).toHaveLength(2);
      expect(results.map(r => r.id)).toEqual(['text-display', 'rich-text']);
    });

    it('should search components by description', () => {
      const results = registry.searchComponents('editor');
      expect(results).toHaveLength(1);
      expect(results[0].id).toBe('rich-text');
    });

    it('should return empty array for no matches', () => {
      const results = registry.searchComponents('nonexistent');
      expect(results).toEqual([]);
    });

    it('should be case insensitive', () => {
      const results = registry.searchComponents('TEXT');
      expect(results).toHaveLength(2);
    });
  });

  describe('Component Validation', () => {
    beforeEach(() => {
      const config: ComponentConfig = {
        id: 'validated-input',
        displayName: 'Validated Input',
        category: 'Form',
        description: 'Input with validation',
        properties: {
          value: { type: 'string', default: '' },
          required: { type: 'boolean', default: false },
          minLength: { type: 'number', default: 0 },
        },
        requiredProperties: ['value'],
      };

      registry.register('validated-input', MockInputComponent, config);
    });

    it('should validate required properties', () => {
      const isValid = registry.validateProps('validated-input', {});
      expect(isValid).toBe(false);
    });

    it('should pass validation with required properties', () => {
      const isValid = registry.validateProps('validated-input', { value: 'test' });
      expect(isValid).toBe(true);
    });

    it('should return true for unregistered component', () => {
      const isValid = registry.validateProps('nonexistent', {});
      expect(isValid).toBe(true);
    });
  });

  describe('Registry Management', () => {
    beforeEach(() => {
      registry.register('text', MockTextComponent, {
        id: 'text',
        displayName: 'Text',
        category: 'Display',
        description: 'Text component',
        properties: {},
      });

      registry.register('button', MockButtonComponent, {
        id: 'button',
        displayName: 'Button',
        category: 'Interactive',
        description: 'Button component',
        properties: {},
      });
    });

    it('should unregister component', () => {
      expect(registry.isRegistered('text')).toBe(true);

      registry.unregister('text');

      expect(registry.isRegistered('text')).toBe(false);
      expect(registry.getComponent('text')).toBeNull();
    });

    it('should clear all components', () => {
      expect(registry.getAllComponents()).toHaveLength(2);

      registry.clear();

      expect(registry.getAllComponents()).toHaveLength(0);
      expect(registry.isRegistered('text')).toBe(false);
      expect(registry.isRegistered('button')).toBe(false);
    });

    it('should get all registered components', () => {
      const components = registry.getAllComponents();
      expect(components).toHaveLength(2);
      expect(components.map(c => c.id)).toEqual(['text', 'button']);
    });
  });

  describe('Error Handling', () => {
    it('should handle component creation errors gracefully', () => {
      const ErrorComponent = () => {
        throw new Error('Component error');
      };

      registry.register('error-component', ErrorComponent, {
        id: 'error-component',
        displayName: 'Error Component',
        category: 'Test',
        description: 'Component that throws error',
        properties: {},
      });

      const element = registry.createElement('error-component', {});
      
      // Should not throw, but return error boundary or null
      expect(() => render(element)).not.toThrow();
    });
  });
});
