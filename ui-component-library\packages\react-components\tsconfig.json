{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "../../dist/packages/react-components", "declaration": true, "declarationMap": true, "sourceMap": true, "rootDir": "src", "module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx"}, "include": ["src/**/*", "**/*.stories.*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.spec.tsx", "**/*.test.ts", "**/*.test.tsx"], "references": [{"path": "../design-tokens/tsconfig.lib.json"}]}