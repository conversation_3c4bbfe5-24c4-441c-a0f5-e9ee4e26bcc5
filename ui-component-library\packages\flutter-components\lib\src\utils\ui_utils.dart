import 'dart:async';
import 'package:flutter/material.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// Utility functions for UI Builder components
class UIUtils {
  const UIUtils._();

  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1024) {
      return desktop ?? tablet ?? mobile;
    } else if (screenWidth >= 768) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  /// Get current breakpoint
  static UIBreakpoint getCurrentBreakpoint(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    for (final breakpoint in UIBreakpoint.values.reversed) {
      if (breakpoint.matches(screenWidth)) {
        return breakpoint;
      }
    }

    return UIBreakpoint.xs;
  }

  /// Check if screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 768;
  }

  /// Check if screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 768 && width < 1024;
  }

  /// Check if screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1024;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Check if keyboard is visible
  static bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Get theme colors
  static ColorScheme getColorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }

  /// Get text theme
  static TextTheme getTextTheme(BuildContext context) {
    return Theme.of(context).textTheme;
  }

  /// Check if dark mode is enabled
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Get contrast color for background
  static Color getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// Darken a color by percentage
  static Color darkenColor(Color color, double percentage) {
    final hsl = HSLColor.fromColor(color);
    final darkened = hsl.withLightness((hsl.lightness - percentage).clamp(0.0, 1.0));
    return darkened.toColor();
  }

  /// Lighten a color by percentage
  static Color lightenColor(Color color, double percentage) {
    final hsl = HSLColor.fromColor(color);
    final lightened = hsl.withLightness((hsl.lightness + percentage).clamp(0.0, 1.0));
    return lightened.toColor();
  }

  /// Get color with opacity
  static Color getColorWithOpacity(Color color, double opacity) {
    return color.withOpacity(opacity.clamp(0.0, 1.0));
  }

  /// Convert hex string to Color
  static Color? hexToColor(String? hex) {
    if (hex == null || hex.isEmpty) return null;

    String hexColor = hex.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }

    try {
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return null;
    }
  }

  /// Convert Color to hex string
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  /// Get size multiplier for UISize
  static double getSizeMultiplier(UISize size) {
    return size.multiplier;
  }

  /// Get spacing value for size
  static double getSpacingForSize(UISize size) {
    final tokens = DesignTokens.instance.spacing;
    switch (size) {
      case UISize.xs:
        return tokens.size1;
      case UISize.sm:
        return tokens.size2;
      case UISize.md:
        return tokens.size4;
      case UISize.lg:
        return tokens.size6;
      case UISize.xl:
        return tokens.size8;
      case UISize.xxl:
        return tokens.size12;
    }
  }

  /// Get border radius for size
  static BorderRadius getBorderRadiusForSize(UISize size) {
    final tokens = DesignTokens.instance.borderRadius;
    switch (size) {
      case UISize.xs:
        return tokens.sm;
      case UISize.sm:
        return tokens.md;
      case UISize.md:
        return tokens.lg;
      case UISize.lg:
        return tokens.xl;
      case UISize.xl:
        return tokens.xxl;
      case UISize.xxl:
        return BorderRadius.circular(20);
    }
  }

  /// Get elevation for size
  static double getElevationForSize(UISize size) {
    switch (size) {
      case UISize.xs:
        return 1;
      case UISize.sm:
        return 2;
      case UISize.md:
        return 4;
      case UISize.lg:
        return 6;
      case UISize.xl:
        return 8;
      case UISize.xxl:
        return 12;
    }
  }

  /// Format duration to human readable string
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  /// Format file size to human readable string
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Debounce function calls
  static void debounce(
    String key,
    Duration delay,
    VoidCallback callback,
  ) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }

  static final Map<String, Timer> _debounceTimers = {};

  /// Throttle function calls
  static void throttle(
    String key,
    Duration delay,
    VoidCallback callback,
  ) {
    if (_throttleTimers.containsKey(key)) return;

    callback();
    _throttleTimers[key] = Timer(delay, () {
      _throttleTimers.remove(key);
    });
  }

  static final Map<String, Timer> _throttleTimers = {};

  /// Show snackbar with consistent styling
  static void showSnackBar(
    BuildContext context,
    String message, {
    UIColorVariant variant = UIColorVariant.info,
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    final colorScheme = getColorScheme(context);
    final backgroundColor = variant.getColor(colorScheme);
    final textColor = getContrastColor(backgroundColor);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(color: textColor),
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        action: action,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
        ),
      ),
    );
  }

  /// Focus next field
  static void focusNextField(BuildContext context, FocusNode currentFocus, FocusNode nextFocus) {
    currentFocus.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }

  /// Unfocus all fields
  static void unfocusAll(BuildContext context) {
    FocusScope.of(context).unfocus();
  }
}
