package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.OrganizationDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.service.OrganizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST Controller for Organization management operations.
 */
@RestController
@RequestMapping("/api/v1/organizations")
@Tag(name = "Organizations", description = "Organization management endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class OrganizationController {

    private static final Logger logger = LoggerFactory.getLogger(OrganizationController.class);

    private final OrganizationService organizationService;

    @Autowired
    public OrganizationController(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    /**
     * Create new organization.
     */
    @PostMapping
    @Operation(summary = "Create organization", description = "Create a new organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Organization created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid organization data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "Organization already exists")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'CREATE')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> createOrganization(
            @Valid @RequestBody OrganizationDTO.CreateDTO createDTO) {
        
        logger.info("Creating organization with slug: {}", createDTO.getSlug());
        
        try {
            OrganizationDTO organization = organizationService.createOrganization(createDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Organization created successfully", organization));
        } catch (Exception e) {
            logger.error("Failed to create organization", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to create organization: " + e.getMessage()));
        }
    }

    /**
     * Get organization by ID.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get organization", description = "Get organization by ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> getOrganization(
            @Parameter(description = "Organization ID") @PathVariable UUID id) {
        
        logger.debug("Fetching organization with ID: {}", id);
        
        try {
            OrganizationDTO organization = organizationService.getOrganizationById(id);
            return ResponseEntity.ok(ApiResponse.success("Organization found", organization));
        } catch (Exception e) {
            logger.error("Failed to fetch organization with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Organization not found: " + e.getMessage()));
        }
    }

    /**
     * Get organization by slug.
     */
    @GetMapping("/slug/{slug}")
    @Operation(summary = "Get organization by slug", description = "Get organization by slug")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> getOrganizationBySlug(
            @Parameter(description = "Organization slug") @PathVariable String slug) {
        
        logger.debug("Fetching organization with slug: {}", slug);
        
        try {
            OrganizationDTO organization = organizationService.getOrganizationBySlug(slug);
            return ResponseEntity.ok(ApiResponse.success("Organization found", organization));
        } catch (Exception e) {
            logger.error("Failed to fetch organization with slug: {}", slug, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Organization not found: " + e.getMessage()));
        }
    }

    /**
     * Update organization.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update organization", description = "Update organization information")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid organization data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> updateOrganization(
            @Parameter(description = "Organization ID") @PathVariable UUID id,
            @Valid @RequestBody OrganizationDTO.UpdateDTO updateDTO) {
        
        logger.info("Updating organization with ID: {}", id);
        
        try {
            OrganizationDTO organization = organizationService.updateOrganization(id, updateDTO);
            return ResponseEntity.ok(ApiResponse.success("Organization updated successfully", organization));
        } catch (Exception e) {
            logger.error("Failed to update organization with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to update organization: " + e.getMessage()));
        }
    }

    /**
     * Delete organization.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete organization", description = "Delete organization (soft delete)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteOrganization(
            @Parameter(description = "Organization ID") @PathVariable UUID id) {
        
        logger.info("Deleting organization with ID: {}", id);
        
        try {
            organizationService.deleteOrganization(id);
            return ResponseEntity.ok(ApiResponse.success("Organization deleted successfully"));
        } catch (Exception e) {
            logger.error("Failed to delete organization with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to delete organization: " + e.getMessage()));
        }
    }

    /**
     * Get all organizations with pagination.
     */
    @GetMapping
    @Operation(summary = "Get all organizations", description = "Get all organizations with pagination")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organizations retrieved successfully")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<Page<OrganizationDTO>>> getAllOrganizations(
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Fetching all organizations with pagination");
        
        try {
            Page<OrganizationDTO> organizations = organizationService.getAllOrganizations(pageable);
            
            ApiResponse<Page<OrganizationDTO>> response = ApiResponse.success("Organizations retrieved successfully", organizations);
            response.setPagination(new ApiResponse.PaginationInfo(
                    organizations.getNumber(),
                    organizations.getSize(),
                    organizations.getTotalElements(),
                    organizations.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch organizations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch organizations: " + e.getMessage()));
        }
    }

    /**
     * Search organizations.
     */
    @GetMapping("/search")
    @Operation(summary = "Search organizations", description = "Search organizations by criteria")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<Page<OrganizationDTO>>> searchOrganizations(
            @Parameter(description = "Organization name") @RequestParam(required = false) String name,
            @Parameter(description = "Organization status") @RequestParam(required = false) Organization.OrganizationStatus status,
            @Parameter(description = "Subscription plan") @RequestParam(required = false) Organization.SubscriptionPlan plan,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Searching organizations with criteria - name: {}, status: {}, plan: {}", name, status, plan);
        
        try {
            Page<OrganizationDTO> organizations = organizationService.searchOrganizations(name, status, plan, pageable);
            
            ApiResponse<Page<OrganizationDTO>> response = ApiResponse.success("Search completed successfully", organizations);
            response.setPagination(new ApiResponse.PaginationInfo(
                    organizations.getNumber(),
                    organizations.getSize(),
                    organizations.getTotalElements(),
                    organizations.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search organizations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to search organizations: " + e.getMessage()));
        }
    }

    /**
     * Update organization subscription plan.
     */
    @PutMapping("/{id}/subscription")
    @Operation(summary = "Update subscription plan", description = "Update organization subscription plan")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Subscription plan updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> updateSubscriptionPlan(
            @Parameter(description = "Organization ID") @PathVariable UUID id,
            @Parameter(description = "New subscription plan") @RequestParam Organization.SubscriptionPlan plan) {
        
        logger.info("Updating subscription plan for organization ID: {} to {}", id, plan);
        
        try {
            OrganizationDTO organization = organizationService.updateSubscriptionPlan(id, plan);
            return ResponseEntity.ok(ApiResponse.success("Subscription plan updated successfully", organization));
        } catch (Exception e) {
            logger.error("Failed to update subscription plan for organization ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to update subscription plan: " + e.getMessage()));
        }
    }

    /**
     * Update organization status.
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "Update organization status", description = "Update organization status")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization status updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> updateStatus(
            @Parameter(description = "Organization ID") @PathVariable UUID id,
            @Parameter(description = "New status") @RequestParam Organization.OrganizationStatus status) {
        
        logger.info("Updating status for organization ID: {} to {}", id, status);
        
        try {
            OrganizationDTO organization = organizationService.updateStatus(id, status);
            return ResponseEntity.ok(ApiResponse.success("Organization status updated successfully", organization));
        } catch (Exception e) {
            logger.error("Failed to update status for organization ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to update organization status: " + e.getMessage()));
        }
    }

    /**
     * Get organization with statistics.
     */
    @GetMapping("/{id}/stats")
    @Operation(summary = "Get organization statistics", description = "Get organization with detailed statistics")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Organization statistics retrieved"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("hasPermission('ORGANIZATION', 'READ')")
    public ResponseEntity<ApiResponse<OrganizationDTO>> getOrganizationWithStats(
            @Parameter(description = "Organization ID") @PathVariable UUID id) {
        
        logger.debug("Fetching organization statistics for ID: {}", id);
        
        try {
            OrganizationDTO organization = organizationService.getOrganizationWithStats(id);
            return ResponseEntity.ok(ApiResponse.success("Organization statistics retrieved", organization));
        } catch (Exception e) {
            logger.error("Failed to fetch organization statistics for ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to fetch organization statistics: " + e.getMessage()));
        }
    }
}
