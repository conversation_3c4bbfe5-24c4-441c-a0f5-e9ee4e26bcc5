import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Breadcrumb component
class UIBreadcrumb extends StatelessWidget {
  const UIBreadcrumb({
    super.key,
    required this.items,
    this.separator = '/',
    this.onTap,
  });

  final List<UIBreadcrumbItem> items;
  final String separator;
  final Function(int index)? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    return Wrap(
      children: items.asMap().entries.expand((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == items.length - 1;

        return [
          GestureDetector(
            onTap: item.onTap ?? (onTap != null ? () => onTap!(index) : null),
            child: Text(
              item.label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isLast ? colorScheme.onSurface : colorScheme.primary,
                decoration: isLast ? null : TextDecoration.underline,
              ),
            ),
          ),
          if (!isLast) ...[
            SizedBox(width: tokens.spacing.size2),
            Text(
              separator,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(width: tokens.spacing.size2),
          ],
        ];
      }).toList(),
    );
  }
}

/// Breadcrumb item data class
class UIBreadcrumbItem {
  const UIBreadcrumbItem({
    required this.label,
    this.onTap,
  });

  final String label;
  final VoidCallback? onTap;
}
