import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'config/app_config.dart';
import 'router/app_router.dart';
import 'theme/app_theme.dart';
import 'providers/theme_provider.dart';
import 'providers/connectivity_provider.dart';
import 'providers/auth_provider.dart';
import 'widgets/connectivity_banner.dart';
import 'widgets/error_boundary.dart';

class UIRuntimeApp extends ConsumerWidget {
  const UIRuntimeApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isConnected = ref.watch(connectivityProvider);
    final authState = ref.watch(authProvider);

    return ErrorBoundary(
      child: MaterialApp.router(
        title: AppConfig.appName,
        debugShowCheckedModeBanner: AppConfig.isDebug,
        
        // Routing
        routerConfig: router,
        
        // Theme
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        
        // Localization
        supportedLocales: const [
          Locale('en', 'US'),
          Locale('es', 'ES'),
          Locale('fr', 'FR'),
          Locale('de', 'DE'),
          Locale('ja', 'JP'),
          Locale('zh', 'CN'),
        ],
        
        // Builder for global overlays
        builder: (context, child) {
          return Stack(
            children: [
              child ?? const SizedBox.shrink(),
              
              // Connectivity banner
              if (!isConnected)
                const Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: ConnectivityBanner(),
                ),
              
              // Loading overlay for authentication
              if (authState.isLoading)
                const Positioned.fill(
                  child: ColoredBox(
                    color: Colors.black26,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}

/// Cupertino variant of the app for iOS-specific features
class UIRuntimeCupertinoApp extends ConsumerWidget {
  const UIRuntimeCupertinoApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isConnected = ref.watch(connectivityProvider);

    return ErrorBoundary(
      child: CupertinoApp.router(
        title: AppConfig.appName,
        debugShowCheckedModeBanner: AppConfig.isDebug,
        
        // Routing
        routerConfig: router,
        
        // Theme
        theme: themeMode == ThemeMode.dark
            ? AppTheme.darkCupertinoTheme
            : AppTheme.lightCupertinoTheme,
        
        // Localization
        supportedLocales: const [
          Locale('en', 'US'),
          Locale('es', 'ES'),
          Locale('fr', 'FR'),
          Locale('de', 'DE'),
          Locale('ja', 'JP'),
          Locale('zh', 'CN'),
        ],
        
        // Builder for global overlays
        builder: (context, child) {
          return Stack(
            children: [
              child ?? const SizedBox.shrink(),
              
              // Connectivity banner
              if (!isConnected)
                const Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: ConnectivityBanner(),
                ),
            ],
          );
        },
      ),
    );
  }
}
