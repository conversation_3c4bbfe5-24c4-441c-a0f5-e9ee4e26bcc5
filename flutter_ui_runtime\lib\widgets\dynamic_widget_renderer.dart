import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../core/widget_registry.dart';
import '../models/widget_config.dart';
import '../models/device_info.dart';
import '../providers/app_state_provider.dart';
import '../utils/expression_evaluator.dart';
import '../utils/conditional_renderer.dart';
import '../widgets/error_widget.dart';
import '../widgets/loading_widget.dart';

/// Dynamic widget renderer that builds widgets from configuration
class DynamicWidgetRenderer extends ConsumerWidget {
  const DynamicWidgetRenderer({
    super.key,
    required this.config,
    this.context,
    this.mode = RenderMode.preview,
    this.debugMode = false,
    this.onWidgetMount,
    this.onWidgetUnmount,
    this.onWidgetError,
  });

  final dynamic config;
  final Map<String, dynamic>? context;
  final RenderMode mode;
  final bool debugMode;
  final Function(String widgetId, String widgetType)? onWidgetMount;
  final Function(String widgetId, String widgetType)? onWidgetUnmount;
  final Function(Object error, String widgetType)? onWidgetError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appStateProvider);
    final deviceInfo = appState.deviceInfo;

    // Handle null or invalid configuration
    if (config == null) {
      return _buildEmptyState();
    }

    // Handle array of widgets
    if (config is List) {
      return _buildWidgetList(config, context, ref, deviceInfo);
    }

    // Handle single widget configuration
    if (config is Map<String, dynamic>) {
      return _buildSingleWidget(config, context, ref, deviceInfo);
    }

    // Invalid configuration type
    return _buildErrorWidget('Invalid configuration type: ${config.runtimeType}');
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.widgets_outlined,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No configuration provided',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWidgetList(
    List<dynamic> configs,
    BuildContext context,
    WidgetRef ref,
    DeviceInfoModel deviceInfo,
  ) {
    final widgets = <Widget>[];

    for (int i = 0; i < configs.length; i++) {
      final widgetConfig = configs[i];
      if (widgetConfig != null) {
        widgets.add(
          DynamicWidgetRenderer(
            key: ValueKey('widget_$i'),
            config: widgetConfig,
            context: this.context,
            mode: mode,
            debugMode: debugMode,
            onWidgetMount: onWidgetMount,
            onWidgetUnmount: onWidgetUnmount,
            onWidgetError: onWidgetError,
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: widgets,
    );
  }

  Widget _buildSingleWidget(
    Map<String, dynamic> config,
    BuildContext context,
    WidgetRef ref,
    DeviceInfoModel deviceInfo,
  ) {
    try {
      final widgetConfig = WidgetConfig.fromJson(config);
      return _renderWidget(widgetConfig, context, ref, deviceInfo);
    } catch (error) {
      return _buildErrorWidget('Failed to parse widget configuration: $error');
    }
  }

  Widget _renderWidget(
    WidgetConfig config,
    BuildContext context,
    WidgetRef ref,
    DeviceInfoModel deviceInfo,
  ) {
    // Check conditional rendering
    if (config.conditions != null && config.conditions!.isNotEmpty) {
      final shouldRender = ConditionalRenderer.evaluateConditions(
        config.conditions!,
        this.context ?? {},
      );
      if (!shouldRender) {
        return const SizedBox.shrink();
      }
    }

    // Handle loop rendering
    if (config.loop != null) {
      return _renderLoop(config, context, ref, deviceInfo);
    }

    // Get widget builder from registry
    final builder = widgetRegistry.getBuilder(config.type);
    if (builder == null) {
      return _buildErrorWidget('Unknown widget type: ${config.type}');
    }

    // Process properties with expression evaluation
    final processedConfig = _processWidgetConfig(config, this.context ?? {});

    // Validate configuration
    final validation = widgetRegistry.validateConfig(config.type, processedConfig);
    if (!validation.isValid && debugMode) {
      return _buildValidationErrorWidget(config.type, validation.errors);
    }

    // Build the widget
    try {
      final widget = builder(
        config: processedConfig,
        context: {
          ...this.context ?? {},
          'deviceInfo': deviceInfo,
          'mode': mode.name,
          'debugMode': debugMode,
        },
        key: config.id != null ? ValueKey(config.id) : null,
      );

      if (widget == null) {
        return _buildErrorWidget('Widget builder returned null for type: ${config.type}');
      }

      // Wrap with debug information if needed
      if (debugMode) {
        return _wrapWithDebugInfo(widget, config);
      }

      // Track widget lifecycle
      _trackWidgetLifecycle(config);

      return widget;
    } catch (error, stackTrace) {
      onWidgetError?.call(error, config.type);
      return _buildErrorWidget('Error rendering ${config.type}: $error');
    }
  }

  Widget _renderLoop(
    WidgetConfig config,
    BuildContext context,
    WidgetRef ref,
    DeviceInfoModel deviceInfo,
  ) {
    final loop = config.loop!;
    
    try {
      // Evaluate loop data
      final loopData = ExpressionEvaluator.evaluate(
        loop.dataSource,
        this.context ?? {},
      );

      if (loopData is! List) {
        return _buildErrorWidget('Loop data is not a list: ${loopData.runtimeType}');
      }

      final widgets = <Widget>[];
      
      for (int index = 0; index < loopData.length; index++) {
        final item = loopData[index];
        
        // Create loop context
        final loopContext = {
          ...this.context ?? {},
          loop.itemVariable: item,
          loop.indexVariable: index,
        };

        // Create widget config for this iteration
        final iterationConfig = config.copyWith(
          id: '${config.id}_$index',
          loop: null, // Remove loop to prevent infinite recursion
        );

        widgets.add(
          DynamicWidgetRenderer(
            key: ValueKey('${config.id}_$index'),
            config: iterationConfig.toJson(),
            context: loopContext,
            mode: mode,
            debugMode: debugMode,
            onWidgetMount: onWidgetMount,
            onWidgetUnmount: onWidgetUnmount,
            onWidgetError: onWidgetError,
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: widgets,
      );
    } catch (error) {
      return _buildErrorWidget('Error in loop rendering: $error');
    }
  }

  WidgetConfig _processWidgetConfig(
    WidgetConfig config,
    Map<String, dynamic> context,
  ) {
    final processedProperties = <String, dynamic>{};

    // Process each property
    config.properties.forEach((key, value) {
      try {
        if (value is String && value.contains('{{')) {
          // Evaluate expression
          processedProperties[key] = ExpressionEvaluator.evaluate(value, context);
        } else {
          processedProperties[key] = value;
        }
      } catch (error) {
        // Keep original value if evaluation fails
        processedProperties[key] = value;
        if (debugMode) {
          debugPrint('Failed to evaluate expression for property $key: $error');
        }
      }
    });

    return config.copyWith(properties: processedProperties);
  }

  Widget _buildErrorWidget(String message) {
    return CustomErrorWidget(
      message: message,
      debugMode: debugMode,
    );
  }

  Widget _buildValidationErrorWidget(String widgetType, List<String> errors) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Validation Error: $widgetType',
            style: const TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          ...errors.map((error) => Text(
            '• $error',
            style: const TextStyle(
              color: Colors.orange,
              fontSize: 10,
            ),
          )),
        ],
      ),
    );
  }

  Widget _wrapWithDebugInfo(Widget child, WidgetConfig config) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.blue.withOpacity(0.5),
              style: BorderStyle.solid,
            ),
          ),
          child: child,
        ),
        Positioned(
          top: 0,
          left: 0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.8),
              borderRadius: const BorderRadius.only(
                bottomRight: Radius.circular(4),
              ),
            ),
            child: Text(
              config.type,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _trackWidgetLifecycle(WidgetConfig config) {
    if (config.id != null) {
      // Track mount
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onWidgetMount?.call(config.id!, config.type);
      });
    }
  }
}

/// Render modes for the dynamic widget renderer
enum RenderMode {
  design,
  preview,
  interactive,
}

/// Extension to get render mode name
extension RenderModeExtension on RenderMode {
  String get name {
    switch (this) {
      case RenderMode.design:
        return 'design';
      case RenderMode.preview:
        return 'preview';
      case RenderMode.interactive:
        return 'interactive';
    }
  }
}
