import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { ThemeConfig } from '../types/component';

export interface Theme {
  id: string;
  name: string;
  mode: 'light' | 'dark' | 'auto';
  
  // CSS Custom Properties
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      muted: string;
      inverse: string;
    };
    border: {
      light: string;
      medium: string;
      strong: string;
    };
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };
  
  typography: {
    fontFamily: {
      sans: string;
      serif: string;
      mono: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      light: string;
      normal: string;
      medium: string;
      semibold: string;
      bold: string;
    };
    lineHeight: {
      tight: string;
      normal: string;
      relaxed: string;
    };
  };
  
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    inner: string;
  };
  
  animation: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      linear: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
  
  // Component-specific styles
  components?: Record<string, any>;
  
  // Custom CSS
  customCSS?: string;
}

export interface ThemeContextValue {
  // Current theme
  theme: Theme;
  themeName: string;
  
  // Available themes
  themes: Theme[];
  
  // Theme management
  setTheme: (themeId: string) => void;
  updateTheme: (updates: Partial<Theme>) => void;
  addTheme: (theme: Theme) => void;
  removeTheme: (themeId: string) => void;
  
  // Mode management
  mode: 'light' | 'dark' | 'auto';
  setMode: (mode: 'light' | 'dark' | 'auto') => void;
  toggleMode: () => void;
  
  // Utilities
  getCSSVariable: (variable: string) => string;
  setCSSVariable: (variable: string, value: string) => void;
  applyTheme: (theme: Theme) => void;
  resetTheme: () => void;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Default themes
const defaultLightTheme: Theme = {
  id: 'default-light',
  name: 'Default Light',
  mode: 'light',
  colors: {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    accent: '#06b6d4',
    background: '#ffffff',
    surface: '#f8fafc',
    text: {
      primary: '#1e293b',
      secondary: '#64748b',
      muted: '#94a3b8',
      inverse: '#ffffff',
    },
    border: {
      light: '#f1f5f9',
      medium: '#e2e8f0',
      strong: '#cbd5e1',
    },
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
  },
  typography: {
    fontFamily: {
      sans: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
      serif: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
      mono: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  },
  animation: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

const defaultDarkTheme: Theme = {
  ...defaultLightTheme,
  id: 'default-dark',
  name: 'Default Dark',
  mode: 'dark',
  colors: {
    primary: '#60a5fa',
    secondary: '#a78bfa',
    accent: '#22d3ee',
    background: '#0f172a',
    surface: '#1e293b',
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
      muted: '#64748b',
      inverse: '#0f172a',
    },
    border: {
      light: '#334155',
      medium: '#475569',
      strong: '#64748b',
    },
    status: {
      success: '#34d399',
      warning: '#fbbf24',
      error: '#f87171',
      info: '#60a5fa',
    },
  },
};

export interface ThemeProviderProps {
  children: React.ReactNode;
  initialTheme?: string;
  customThemes?: Theme[];
  persistTheme?: boolean;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = 'default-light',
  customThemes = [],
  persistTheme = true,
  storageKey = 'ui-runtime-theme',
}) => {
  const [themes] = useState<Theme[]>([defaultLightTheme, defaultDarkTheme, ...customThemes]);
  const [currentThemeId, setCurrentThemeId] = useState<string>(() => {
    if (persistTheme && typeof window !== 'undefined') {
      return localStorage.getItem(storageKey) || initialTheme;
    }
    return initialTheme;
  });

  const theme = useMemo(() => {
    return themes.find(t => t.id === currentThemeId) || defaultLightTheme;
  }, [themes, currentThemeId]);

  const [mode, setModeState] = useState<'light' | 'dark' | 'auto'>(() => {
    if (persistTheme && typeof window !== 'undefined') {
      return (localStorage.getItem(`${storageKey}-mode`) as any) || theme.mode;
    }
    return theme.mode;
  });

  // Apply theme to CSS custom properties
  const applyTheme = useCallback((themeToApply: Theme) => {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;

    // Colors
    root.style.setProperty('--color-primary', themeToApply.colors.primary);
    root.style.setProperty('--color-secondary', themeToApply.colors.secondary);
    root.style.setProperty('--color-accent', themeToApply.colors.accent);
    root.style.setProperty('--color-background', themeToApply.colors.background);
    root.style.setProperty('--color-surface', themeToApply.colors.surface);
    
    // Text colors
    root.style.setProperty('--color-text-primary', themeToApply.colors.text.primary);
    root.style.setProperty('--color-text-secondary', themeToApply.colors.text.secondary);
    root.style.setProperty('--color-text-muted', themeToApply.colors.text.muted);
    root.style.setProperty('--color-text-inverse', themeToApply.colors.text.inverse);
    
    // Border colors
    root.style.setProperty('--color-border-light', themeToApply.colors.border.light);
    root.style.setProperty('--color-border-medium', themeToApply.colors.border.medium);
    root.style.setProperty('--color-border-strong', themeToApply.colors.border.strong);
    
    // Status colors
    root.style.setProperty('--color-success', themeToApply.colors.status.success);
    root.style.setProperty('--color-warning', themeToApply.colors.status.warning);
    root.style.setProperty('--color-error', themeToApply.colors.status.error);
    root.style.setProperty('--color-info', themeToApply.colors.status.info);
    
    // Typography
    root.style.setProperty('--font-family-sans', themeToApply.typography.fontFamily.sans);
    root.style.setProperty('--font-family-serif', themeToApply.typography.fontFamily.serif);
    root.style.setProperty('--font-family-mono', themeToApply.typography.fontFamily.mono);
    
    // Font sizes
    Object.entries(themeToApply.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });
    
    // Font weights
    Object.entries(themeToApply.typography.fontWeight).forEach(([key, value]) => {
      root.style.setProperty(`--font-weight-${key}`, value);
    });
    
    // Line heights
    Object.entries(themeToApply.typography.lineHeight).forEach(([key, value]) => {
      root.style.setProperty(`--line-height-${key}`, value);
    });
    
    // Spacing
    Object.entries(themeToApply.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });
    
    // Border radius
    Object.entries(themeToApply.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--border-radius-${key}`, value);
    });
    
    // Shadows
    Object.entries(themeToApply.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });
    
    // Animation
    Object.entries(themeToApply.animation.duration).forEach(([key, value]) => {
      root.style.setProperty(`--duration-${key}`, value);
    });
    
    Object.entries(themeToApply.animation.easing).forEach(([key, value]) => {
      root.style.setProperty(`--easing-${key}`, value);
    });
    
    // Apply custom CSS if provided
    if (themeToApply.customCSS) {
      let styleElement = document.getElementById('custom-theme-styles');
      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'custom-theme-styles';
        document.head.appendChild(styleElement);
      }
      styleElement.textContent = themeToApply.customCSS;
    }
    
    // Update body class for theme mode
    document.body.className = document.body.className
      .replace(/theme-(light|dark|auto)/g, '')
      .trim();
    document.body.classList.add(`theme-${themeToApply.mode}`);
  }, []);

  // Auto theme detection for 'auto' mode
  useEffect(() => {
    if (mode !== 'auto') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      const prefersDark = e.matches;
      const autoTheme = prefersDark ? defaultDarkTheme : defaultLightTheme;
      applyTheme(autoTheme);
    };

    mediaQuery.addEventListener('change', handleChange);
    
    // Apply initial auto theme
    const prefersDark = mediaQuery.matches;
    const autoTheme = prefersDark ? defaultDarkTheme : defaultLightTheme;
    applyTheme(autoTheme);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [mode, applyTheme]);

  // Apply theme when it changes
  useEffect(() => {
    if (mode !== 'auto') {
      applyTheme(theme);
    }
  }, [theme, mode, applyTheme]);

  // Persist theme changes
  useEffect(() => {
    if (persistTheme && typeof window !== 'undefined') {
      localStorage.setItem(storageKey, currentThemeId);
      localStorage.setItem(`${storageKey}-mode`, mode);
    }
  }, [currentThemeId, mode, persistTheme, storageKey]);

  const setTheme = useCallback((themeId: string) => {
    setCurrentThemeId(themeId);
  }, []);

  const updateTheme = useCallback((updates: Partial<Theme>) => {
    // This would typically update a custom theme
    // For now, we'll just apply the updates directly
    const updatedTheme = { ...theme, ...updates };
    applyTheme(updatedTheme);
  }, [theme, applyTheme]);

  const addTheme = useCallback((newTheme: Theme) => {
    // This would typically add to the themes array
    // Implementation depends on state management approach
    console.log('Add theme:', newTheme);
  }, []);

  const removeTheme = useCallback((themeId: string) => {
    // This would typically remove from the themes array
    // Implementation depends on state management approach
    console.log('Remove theme:', themeId);
  }, []);

  const setMode = useCallback((newMode: 'light' | 'dark' | 'auto') => {
    setModeState(newMode);
  }, []);

  const toggleMode = useCallback(() => {
    setModeState(prev => {
      if (prev === 'auto') return 'light';
      if (prev === 'light') return 'dark';
      return 'auto';
    });
  }, []);

  const getCSSVariable = useCallback((variable: string): string => {
    if (typeof document === 'undefined') return '';
    return getComputedStyle(document.documentElement).getPropertyValue(variable);
  }, []);

  const setCSSVariable = useCallback((variable: string, value: string) => {
    if (typeof document === 'undefined') return;
    document.documentElement.style.setProperty(variable, value);
  }, []);

  const resetTheme = useCallback(() => {
    setCurrentThemeId('default-light');
    setModeState('light');
  }, []);

  const contextValue: ThemeContextValue = {
    theme,
    themeName: theme.name,
    themes,
    setTheme,
    updateTheme,
    addTheme,
    removeTheme,
    mode,
    setMode,
    toggleMode,
    getCSSVariable,
    setCSSVariable,
    applyTheme,
    resetTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Utility functions
export const createTheme = (baseTheme: Theme, overrides: Partial<Theme>): Theme => {
  return {
    ...baseTheme,
    ...overrides,
    colors: { ...baseTheme.colors, ...overrides.colors },
    typography: { ...baseTheme.typography, ...overrides.typography },
    spacing: { ...baseTheme.spacing, ...overrides.spacing },
    borderRadius: { ...baseTheme.borderRadius, ...overrides.borderRadius },
    shadows: { ...baseTheme.shadows, ...overrides.shadows },
    animation: { ...baseTheme.animation, ...overrides.animation },
  };
};

export { defaultLightTheme, defaultDarkTheme };
