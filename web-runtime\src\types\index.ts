// Core runtime types for dynamic UI rendering

export interface UIMetadata {
  pageId: string;
  version: string;
  title?: string;
  description?: string;
  theme: string | ThemeConfiguration;
  layout: LayoutConfiguration;
  data?: DataConfiguration;
  permissions?: PermissionConfiguration;
  metadata?: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    tags?: string[];
  };
}

export interface LayoutConfiguration {
  type: 'grid' | 'flex' | 'stack' | 'absolute' | 'masonry';
  columns?: number;
  rows?: number;
  gap?: number | string;
  padding?: SpacingValue;
  margin?: SpacingValue;
  components: ComponentConfiguration[];
  responsive?: ResponsiveConfiguration;
}

export interface ComponentConfiguration {
  id: string;
  type: string;
  props: Record<string, any>;
  position?: PositionConfiguration;
  size?: SizeConfiguration;
  style?: StyleConfiguration;
  children?: ComponentConfiguration[];
  conditions?: ConditionConfiguration[];
  events?: EventConfiguration[];
  data?: DataBindingConfiguration;
  permissions?: string[];
}

export interface PositionConfiguration {
  col?: number;
  row?: number;
  span?: number;
  rowSpan?: number;
  x?: number;
  y?: number;
  z?: number;
}

export interface SizeConfiguration {
  width?: number | string;
  height?: number | string;
  minWidth?: number | string;
  minHeight?: number | string;
  maxWidth?: number | string;
  maxHeight?: number | string;
}

export interface StyleConfiguration {
  className?: string;
  style?: React.CSSProperties;
  variants?: {
    [key: string]: React.CSSProperties;
  };
  responsive?: {
    [breakpoint: string]: React.CSSProperties;
  };
}

export interface ThemeConfiguration {
  id: string;
  name: string;
  colors: ColorPalette;
  typography: TypographyConfiguration;
  spacing: SpacingConfiguration;
  shadows: ShadowConfiguration;
  borders: BorderConfiguration;
  animations: AnimationConfiguration;
}

export interface ColorPalette {
  primary: ColorScale;
  secondary: ColorScale;
  accent: ColorScale;
  surface: ColorScale;
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;
}

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface TypographyConfiguration {
  fontFamilies: {
    sans: string;
    serif: string;
    mono: string;
  };
  fontSizes: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
  };
  lineHeights: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
  };
  fontWeights: {
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
}

export interface SpacingConfiguration {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
}

export interface ShadowConfiguration {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
}

export interface BorderConfiguration {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface AnimationConfiguration {
  durations: {
    fast: string;
    normal: string;
    slow: string;
  };
  easings: {
    easeIn: string;
    easeOut: string;
    easeInOut: string;
  };
}

export type SpacingValue = number | string | {
  top?: number | string;
  right?: number | string;
  bottom?: number | string;
  left?: number | string;
  x?: number | string;
  y?: number | string;
  all?: number | string;
};

export interface ResponsiveConfiguration {
  breakpoints: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  columns: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
}

export interface DataConfiguration {
  sources: DataSourceConfiguration[];
  bindings: DataBindingConfiguration[];
  cache?: CacheConfiguration;
}

export interface DataSourceConfiguration {
  id: string;
  type: 'api' | 'static' | 'computed' | 'form';
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  transform?: string; // JavaScript expression
  refresh?: number; // Refresh interval in ms
  dependencies?: string[]; // Other data source IDs
}

export interface DataBindingConfiguration {
  componentId: string;
  property: string;
  source: string;
  transform?: string; // JavaScript expression
  fallback?: any;
}

export interface CacheConfiguration {
  enabled: boolean;
  ttl: number; // Time to live in ms
  strategy: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';
}

export interface ConditionConfiguration {
  type: 'show' | 'hide' | 'enable' | 'disable' | 'style';
  expression: string; // JavaScript expression
  value?: any;
  style?: React.CSSProperties;
}

export interface EventConfiguration {
  type: string; // onClick, onChange, onSubmit, etc.
  action: ActionConfiguration;
}

export interface ActionConfiguration {
  type: 'navigate' | 'api' | 'form' | 'data' | 'custom';
  target?: string;
  params?: Record<string, any>;
  confirmation?: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
  };
  success?: {
    message?: string;
    action?: ActionConfiguration;
  };
  error?: {
    message?: string;
    action?: ActionConfiguration;
  };
}

export interface PermissionConfiguration {
  required: string[];
  fallback?: ComponentConfiguration;
  message?: string;
}

// Component registry types
export interface ComponentDefinition {
  type: string;
  component: React.ComponentType<any>;
  props?: PropDefinition[];
  category?: string;
  description?: string;
  examples?: ComponentExample[];
}

export interface PropDefinition {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
  required?: boolean;
  default?: any;
  description?: string;
  options?: { label: string; value: any }[];
}

export interface ComponentExample {
  name: string;
  description: string;
  props: Record<string, any>;
}

// Runtime state types
export interface RuntimeState {
  metadata: UIMetadata | null;
  theme: ThemeConfiguration | null;
  data: Record<string, any>;
  loading: boolean;
  error: string | null;
  permissions: string[];
  user: UserInfo | null;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  accessibility: {
    reducedMotion: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large';
  };
}

// Form types
export interface FormConfiguration {
  id: string;
  fields: FormFieldConfiguration[];
  validation?: ValidationConfiguration;
  submission: FormSubmissionConfiguration;
  layout?: FormLayoutConfiguration;
}

export interface FormFieldConfiguration {
  id: string;
  type: string;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  validation?: FieldValidationConfiguration[];
  options?: { label: string; value: any }[];
  props?: Record<string, any>;
  conditions?: ConditionConfiguration[];
}

export interface ValidationConfiguration {
  mode: 'onChange' | 'onBlur' | 'onSubmit';
  revalidateMode: 'onChange' | 'onBlur' | 'onSubmit';
  shouldFocusError: boolean;
}

export interface FieldValidationConfiguration {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  expression?: string; // For custom validation
}

export interface FormSubmissionConfiguration {
  url: string;
  method: 'POST' | 'PUT' | 'PATCH';
  headers?: Record<string, string>;
  transform?: string; // JavaScript expression
  success?: ActionConfiguration;
  error?: ActionConfiguration;
}

export interface FormLayoutConfiguration {
  type: 'single' | 'double' | 'grid';
  columns?: number;
  spacing?: number;
  grouping?: FormGroupConfiguration[];
}

export interface FormGroupConfiguration {
  title: string;
  fields: string[];
  collapsible?: boolean;
  collapsed?: boolean;
}

// Error types
export interface RuntimeError {
  code: string;
  message: string;
  componentId?: string;
  stack?: string;
  timestamp: string;
}

// Performance types
export interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  dataFetchTime: number;
  bundleSize: number;
  memoryUsage: number;
}

// Real-time types
export interface RealtimeUpdate {
  type: 'metadata' | 'theme' | 'data' | 'component';
  payload: any;
  timestamp: string;
  userId?: string;
}

// Export utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type ComponentProps<T = any> = T & {
  id?: string;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
};
