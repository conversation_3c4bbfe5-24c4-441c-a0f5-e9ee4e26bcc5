import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntApp } from 'antd';
import { useAppSelector, useAppDispatch } from '@store/index';
import { initializeAuth } from '@store/slices/authSlice';

// Pages
import LoginPage from '@pages/LoginPage';
import DashboardPage from '@pages/DashboardPage';
import UIBuilderPage from '@pages/UIBuilderPage';
import TemplatesPage from '@pages/TemplatesPage';
import SettingsPage from '@pages/SettingsPage';
import NotFoundPage from '@pages/NotFoundPage';

// Components
import ProtectedRoute from '@components/auth/ProtectedRoute';
import LoadingScreen from '@components/common/LoadingScreen';
import NotificationContainer from '@components/common/NotificationContainer';
import ErrorBoundary from '@components/common/ErrorBoundary';

// Hooks
import { useWebSocket } from '@hooks/useWebSocket';
import { useKeyboardShortcuts } from '@hooks/useKeyboardShortcuts';

// Styles
import './styles/index.css';

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading, user } = useAppSelector(state => state.auth);
  const { theme } = useAppSelector(state => state.theme);

  // Initialize authentication on app start
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Initialize WebSocket connection when authenticated
  useWebSocket(isAuthenticated);

  // Setup global keyboard shortcuts
  useKeyboardShortcuts();

  // Show loading screen while initializing
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: theme.colors.primary[500],
            colorSuccess: theme.colors.success[500],
            colorWarning: theme.colors.warning[500],
            colorError: theme.colors.error[500],
            colorInfo: theme.colors.info[500],
            borderRadius: 8,
            fontFamily: theme.typography.fontFamilies.primary,
          },
          components: {
            Button: {
              borderRadius: 8,
              controlHeight: 40,
            },
            Input: {
              borderRadius: 8,
              controlHeight: 40,
            },
            Select: {
              borderRadius: 8,
              controlHeight: 40,
            },
            Card: {
              borderRadius: 12,
            },
            Modal: {
              borderRadius: 12,
            },
            Drawer: {
              borderRadius: 12,
            },
          },
        }}
      >
        <AntApp>
          <div className="app">
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <LoginPage />
                  )
                }
              />

              {/* Protected routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <DashboardPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/builder/:configId?"
                element={
                  <ProtectedRoute>
                    <UIBuilderPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/templates"
                element={
                  <ProtectedRoute>
                    <TemplatesPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/settings/*"
                element={
                  <ProtectedRoute>
                    <SettingsPage />
                  </ProtectedRoute>
                }
              />

              {/* Default redirect */}
              <Route
                path="/"
                element={
                  <Navigate
                    to={isAuthenticated ? "/dashboard" : "/login"}
                    replace
                  />
                }
              />

              {/* 404 page */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>

            {/* Global components */}
            <NotificationContainer />
          </div>
        </AntApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;
