import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Tabs component
class UITabs extends StatelessWidget {
  const UITabs({
    super.key,
    required this.tabs,
    required this.children,
    this.controller,
    this.isScrollable = false,
  });

  final List<UITab> tabs;
  final List<Widget> children;
  final TabController? controller;
  final bool isScrollable;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: Column(
        children: [
          TabBar(
            controller: controller,
            isScrollable: isScrollable,
            tabs: tabs.map((tab) => Tab(
              text: tab.label,
              icon: tab.icon != null ? Icon(tab.icon) : null,
            )).toList(),
          ),
          Expanded(
            child: TabBar<PERSON>iew(
              controller: controller,
              children: children,
            ),
          ),
        ],
      ),
    );
  }
}

/// Tab data class
class UITab {
  const UITab({
    required this.label,
    this.icon,
  });

  final String label;
  final IconData? icon;
}
