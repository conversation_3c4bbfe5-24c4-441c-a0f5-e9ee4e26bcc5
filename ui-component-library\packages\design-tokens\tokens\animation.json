{"duration": {"instant": {"value": 0}, "fast": {"value": 150}, "normal": {"value": 300}, "slow": {"value": 500}, "slower": {"value": 750}, "slowest": {"value": 1000}}, "easing": {"linear": {"value": "linear"}, "ease": {"value": "ease"}, "easeIn": {"value": "ease-in"}, "easeOut": {"value": "ease-out"}, "easeInOut": {"value": "ease-in-out"}, "easeInSine": {"value": "cubic-bezier(0.12, 0, 0.39, 0)"}, "easeOutSine": {"value": "cubic-bezier(0.61, 1, 0.88, 1)"}, "easeInOutSine": {"value": "cubic-bezier(0.37, 0, 0.63, 1)"}, "easeInQuad": {"value": "cubic-bezier(0.11, 0, 0.5, 0)"}, "easeOutQuad": {"value": "cubic-bezier(0.5, 1, 0.89, 1)"}, "easeInOutQuad": {"value": "cubic-bezier(0.45, 0, 0.55, 1)"}, "easeInCubic": {"value": "cubic-bezier(0.32, 0, 0.67, 0)"}, "easeOutCubic": {"value": "cubic-bezier(0.33, 1, 0.68, 1)"}, "easeInOutCubic": {"value": "cubic-bezier(0.65, 0, 0.35, 1)"}, "easeInQuart": {"value": "cubic-bezier(0.5, 0, 0.75, 0)"}, "easeOutQuart": {"value": "cubic-bezier(0.25, 1, 0.5, 1)"}, "easeInOutQuart": {"value": "cubic-bezier(0.76, 0, 0.24, 1)"}, "easeInQuint": {"value": "cubic-bezier(0.64, 0, 0.78, 0)"}, "easeOutQuint": {"value": "cubic-bezier(0.22, 1, 0.36, 1)"}, "easeInOutQuint": {"value": "cubic-bezier(0.83, 0, 0.17, 1)"}, "easeInExpo": {"value": "cubic-bezier(0.7, 0, 0.84, 0)"}, "easeOutExpo": {"value": "cubic-bezier(0.16, 1, 0.3, 1)"}, "easeInOutExpo": {"value": "cubic-bezier(0.87, 0, 0.13, 1)"}, "easeInCirc": {"value": "cubic-bezier(0.55, 0, 1, 0.45)"}, "easeOutCirc": {"value": "cubic-bezier(0, 0.55, 0.45, 1)"}, "easeInOutCirc": {"value": "cubic-bezier(0.85, 0, 0.15, 1)"}, "easeInBack": {"value": "cubic-bezier(0.36, 0, 0.66, -0.56)"}, "easeOutBack": {"value": "cubic-bezier(0.34, 1.56, 0.64, 1)"}, "easeInOutBack": {"value": "cubic-bezier(0.68, -0.6, 0.32, 1.6)"}, "spring": {"value": "cubic-bezier(0.175, 0.885, 0.32, 1.275)"}, "bounce": {"value": "cubic-bezier(0.68, -0.55, 0.265, 1.55)"}}, "transition": {"all": {"value": {"property": "all", "duration": "{duration.normal}", "easing": "{easing.easeInOut}"}}, "colors": {"value": {"property": "color, background-color, border-color, text-decoration-color, fill, stroke", "duration": "{duration.fast}", "easing": "{easing.easeInOut}"}}, "opacity": {"value": {"property": "opacity", "duration": "{duration.fast}", "easing": "{easing.easeInOut}"}}, "shadow": {"value": {"property": "box-shadow", "duration": "{duration.fast}", "easing": "{easing.easeInOut}"}}, "transform": {"value": {"property": "transform", "duration": "{duration.normal}", "easing": "{easing.easeInOut}"}}, "size": {"value": {"property": "width, height", "duration": "{duration.normal}", "easing": "{easing.easeInOut}"}}, "position": {"value": {"property": "top, right, bottom, left", "duration": "{duration.normal}", "easing": "{easing.easeInOut}"}}}, "keyframes": {"fadeIn": {"value": {"0%": {"opacity": 0}, "100%": {"opacity": 1}}}, "fadeOut": {"value": {"0%": {"opacity": 1}, "100%": {"opacity": 0}}}, "slideInUp": {"value": {"0%": {"transform": "translateY(100%)", "opacity": 0}, "100%": {"transform": "translateY(0)", "opacity": 1}}}, "slideInDown": {"value": {"0%": {"transform": "translateY(-100%)", "opacity": 0}, "100%": {"transform": "translateY(0)", "opacity": 1}}}, "slideInLeft": {"value": {"0%": {"transform": "translateX(-100%)", "opacity": 0}, "100%": {"transform": "translateX(0)", "opacity": 1}}}, "slideInRight": {"value": {"0%": {"transform": "translateX(100%)", "opacity": 0}, "100%": {"transform": "translateX(0)", "opacity": 1}}}, "scaleIn": {"value": {"0%": {"transform": "scale(0.8)", "opacity": 0}, "100%": {"transform": "scale(1)", "opacity": 1}}}, "scaleOut": {"value": {"0%": {"transform": "scale(1)", "opacity": 1}, "100%": {"transform": "scale(0.8)", "opacity": 0}}}, "spin": {"value": {"0%": {"transform": "rotate(0deg)"}, "100%": {"transform": "rotate(360deg)"}}}, "pulse": {"value": {"0%, 100%": {"opacity": 1}, "50%": {"opacity": 0.5}}}, "bounce": {"value": {"0%, 20%, 53%, 80%, 100%": {"transform": "translate3d(0,0,0)"}, "40%, 43%": {"transform": "translate3d(0, -30px, 0)"}, "70%": {"transform": "translate3d(0, -15px, 0)"}, "90%": {"transform": "translate3d(0, -4px, 0)"}}}, "shake": {"value": {"0%, 100%": {"transform": "translateX(0)"}, "10%, 30%, 50%, 70%, 90%": {"transform": "translateX(-10px)"}, "20%, 40%, 60%, 80%": {"transform": "translateX(10px)"}}}}}