package com.uiplatform.service;

import com.uiplatform.dto.AuthDTO;
import com.uiplatform.dto.OrganizationDTO;
import com.uiplatform.dto.UserDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.entity.Role;
import com.uiplatform.entity.User;
import com.uiplatform.exception.BusinessException;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.mapper.UserMapper;
import com.uiplatform.repository.OrganizationRepository;
import com.uiplatform.repository.RoleRepository;
import com.uiplatform.repository.UserRepository;
import com.uiplatform.security.JwtTokenProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * Service class for Authentication and Authorization.
 * Handles login, registration, password management, and JWT token operations.
 */
@Service
@Transactional
public class AuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int ACCOUNT_LOCK_DURATION_MINUTES = 30;

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider jwtTokenProvider;
    private final UserRepository userRepository;
    private final OrganizationRepository organizationRepository;
    private final RoleRepository roleRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final OrganizationService organizationService;

    @Autowired
    public AuthenticationService(AuthenticationManager authenticationManager,
                                JwtTokenProvider jwtTokenProvider,
                                UserRepository userRepository,
                                OrganizationRepository organizationRepository,
                                RoleRepository roleRepository,
                                UserMapper userMapper,
                                PasswordEncoder passwordEncoder,
                                UserService userService,
                                OrganizationService organizationService) {
        this.authenticationManager = authenticationManager;
        this.jwtTokenProvider = jwtTokenProvider;
        this.userRepository = userRepository;
        this.organizationRepository = organizationRepository;
        this.roleRepository = roleRepository;
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
        this.userService = userService;
        this.organizationService = organizationService;
    }

    /**
     * Authenticate user and generate JWT tokens.
     */
    public AuthDTO.LoginResponse login(AuthDTO.LoginRequest loginRequest) {
        logger.info("Attempting login for user: {}", loginRequest.getUsernameOrEmail());

        try {
            // Find user by username or email
            User user = findUserByUsernameOrEmail(loginRequest.getUsernameOrEmail());

            // Check if account is locked
            if (isAccountLocked(user)) {
                throw new BusinessException("Account is temporarily locked due to multiple failed login attempts");
            }

            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            user.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            // Generate tokens
            String accessToken = jwtTokenProvider.generateAccessToken(user);
            String refreshToken = jwtTokenProvider.generateRefreshToken(user);
            long expiresIn = jwtTokenProvider.getAccessTokenValidityInSeconds();

            // Update last login and reset login attempts
            userService.updateLastLogin(user.getId());

            UserDTO userDTO = userMapper.toDTO(user);

            logger.info("Successfully authenticated user: {}", user.getUsername());
            return new AuthDTO.LoginResponse(accessToken, refreshToken, expiresIn, userDTO);

        } catch (AuthenticationException e) {
            // Handle failed login attempt
            handleFailedLoginAttempt(loginRequest.getUsernameOrEmail());
            throw new BadCredentialsException("Invalid username/email or password");
        }
    }

    /**
     * Register new user and organization.
     */
    public AuthDTO.LoginResponse register(AuthDTO.RegisterRequest registerRequest) {
        logger.info("Registering new user: {} with organization: {}", 
                   registerRequest.getUsername(), registerRequest.getOrganizationName());

        // Check if username already exists globally
        if (userRepository.findByUsernameAndDeletedFalse(registerRequest.getUsername()).isPresent()) {
            throw new BusinessException("Username already exists");
        }

        // Check if email already exists
        if (userRepository.findByEmailAndDeletedFalse(registerRequest.getEmail()).isPresent()) {
            throw new BusinessException("Email already exists");
        }

        // Check if organization slug already exists
        if (organizationRepository.findBySlugAndDeletedFalse(registerRequest.getOrganizationSlug()).isPresent()) {
            throw new BusinessException("Organization slug already exists");
        }

        // Create organization
        OrganizationDTO.CreateDTO orgCreateDTO = new OrganizationDTO.CreateDTO();
        orgCreateDTO.setName(registerRequest.getOrganizationName());
        orgCreateDTO.setSlug(registerRequest.getOrganizationSlug());
        OrganizationDTO organizationDTO = organizationService.createOrganization(orgCreateDTO);

        // Create user
        UserDTO.CreateDTO userCreateDTO = new UserDTO.CreateDTO();
        userCreateDTO.setUsername(registerRequest.getUsername());
        userCreateDTO.setEmail(registerRequest.getEmail());
        userCreateDTO.setPassword(registerRequest.getPassword());
        userCreateDTO.setFirstName(registerRequest.getFirstName());
        userCreateDTO.setLastName(registerRequest.getLastName());
        userCreateDTO.setOrganizationId(organizationDTO.getId());

        UserDTO userDTO = userService.createUser(userCreateDTO);

        // Assign ADMIN role to the first user (organization owner)
        assignAdminRole(userDTO.getId());

        // Generate tokens
        User user = userRepository.findById(userDTO.getId()).orElseThrow();
        String accessToken = jwtTokenProvider.generateAccessToken(user);
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
        long expiresIn = jwtTokenProvider.getAccessTokenValidityInSeconds();

        logger.info("Successfully registered user: {} with organization: {}", 
                   registerRequest.getUsername(), registerRequest.getOrganizationName());
        return new AuthDTO.LoginResponse(accessToken, refreshToken, expiresIn, userDTO);
    }

    /**
     * Refresh JWT access token.
     */
    public AuthDTO.LoginResponse refreshToken(AuthDTO.RefreshTokenRequest refreshRequest) {
        logger.debug("Refreshing access token");

        if (!jwtTokenProvider.validateToken(refreshRequest.getRefreshToken())) {
            throw new BusinessException("Invalid or expired refresh token");
        }

        String username = jwtTokenProvider.getUsernameFromToken(refreshRequest.getRefreshToken());
        User user = userRepository.findByUsernameAndDeletedFalse(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        String newAccessToken = jwtTokenProvider.generateAccessToken(user);
        String newRefreshToken = jwtTokenProvider.generateRefreshToken(user);
        long expiresIn = jwtTokenProvider.getAccessTokenValidityInSeconds();

        UserDTO userDTO = userMapper.toDTO(user);

        logger.debug("Successfully refreshed token for user: {}", username);
        return new AuthDTO.LoginResponse(newAccessToken, newRefreshToken, expiresIn, userDTO);
    }

    /**
     * Change user password.
     */
    public void changePassword(UUID userId, AuthDTO.ChangePasswordRequest changePasswordRequest) {
        logger.info("Changing password for user ID: {}", userId);

        userService.changePassword(userId, 
                                 changePasswordRequest.getCurrentPassword(), 
                                 changePasswordRequest.getNewPassword());

        logger.info("Successfully changed password for user ID: {}", userId);
    }

    /**
     * Request password reset.
     */
    public void requestPasswordReset(AuthDTO.PasswordResetRequest resetRequest) {
        logger.info("Password reset requested for email: {}", resetRequest.getEmail());

        Optional<User> userOpt = userRepository.findByEmailAndDeletedFalse(resetRequest.getEmail());
        if (userOpt.isEmpty()) {
            // Don't reveal if email exists or not for security
            logger.warn("Password reset requested for non-existent email: {}", resetRequest.getEmail());
            return;
        }

        User user = userOpt.get();
        String resetToken = jwtTokenProvider.generatePasswordResetToken(user);
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(1); // 1 hour expiry

        user.setPasswordResetToken(resetToken);
        user.setPasswordResetExpiresAt(expiresAt);
        userRepository.save(user);

        // TODO: Send password reset email
        logger.info("Password reset token generated for user: {}", user.getUsername());
    }

    /**
     * Confirm password reset with token.
     */
    public void confirmPasswordReset(AuthDTO.PasswordResetConfirmRequest confirmRequest) {
        logger.info("Confirming password reset with token");

        User user = userRepository.findByPasswordResetTokenAndDeletedFalse(confirmRequest.getToken())
                .orElseThrow(() -> new BusinessException("Invalid or expired password reset token"));

        if (user.getPasswordResetExpiresAt().isBefore(LocalDateTime.now())) {
            throw new BusinessException("Password reset token has expired");
        }

        user.setPassword(passwordEncoder.encode(confirmRequest.getNewPassword()));
        user.setPasswordResetToken(null);
        user.setPasswordResetExpiresAt(null);
        user.setLoginAttempts(0);
        user.setLockedUntil(null);
        userRepository.save(user);

        logger.info("Successfully reset password for user: {}", user.getUsername());
    }

    /**
     * Verify email with token.
     */
    public void verifyEmail(String token) {
        logger.info("Verifying email with token");
        userService.verifyEmail(token);
        logger.info("Successfully verified email");
    }

    /**
     * Logout user (invalidate tokens).
     */
    public void logout(String token) {
        logger.info("Logging out user");
        // TODO: Add token to blacklist/revocation list
        logger.info("Successfully logged out user");
    }

    // Private helper methods

    private User findUserByUsernameOrEmail(String usernameOrEmail) {
        // Try to find by username first
        Optional<User> userOpt = userRepository.findByUsernameAndDeletedFalse(usernameOrEmail);
        if (userOpt.isPresent()) {
            return userOpt.get();
        }

        // Try to find by email
        userOpt = userRepository.findByEmailAndDeletedFalse(usernameOrEmail);
        if (userOpt.isPresent()) {
            return userOpt.get();
        }

        throw new ResourceNotFoundException("User not found with username or email: " + usernameOrEmail);
    }

    private boolean isAccountLocked(User user) {
        return user.getLockedUntil() != null && user.getLockedUntil().isAfter(LocalDateTime.now());
    }

    private void handleFailedLoginAttempt(String usernameOrEmail) {
        try {
            User user = findUserByUsernameOrEmail(usernameOrEmail);
            userService.incrementLoginAttempts(user.getId());

            if (user.getLoginAttempts() + 1 >= MAX_LOGIN_ATTEMPTS) {
                userService.lockUserAccount(user.getId(), ACCOUNT_LOCK_DURATION_MINUTES);
                logger.warn("Account locked for user: {} due to {} failed login attempts", 
                           user.getUsername(), MAX_LOGIN_ATTEMPTS);
            }
        } catch (ResourceNotFoundException e) {
            // User doesn't exist, ignore
        }
    }

    private void assignAdminRole(UUID userId) {
        User user = userRepository.findById(userId).orElseThrow();
        Optional<Role> adminRole = roleRepository.findByNameAndDeletedFalse("ADMIN");
        
        if (adminRole.isPresent()) {
            user.getRoles().add(adminRole.get());
            userRepository.save(user);
        }
    }
}
