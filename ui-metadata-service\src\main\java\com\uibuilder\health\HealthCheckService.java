package com.uibuilder.health;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
@Slf4j
public class HealthCheckService {

    private final DataSource dataSource;
    private final RedisTemplate<String, Object> redisTemplate;
    private final KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * Comprehensive application health check
     */
    @Component("application")
    public static class ApplicationHealthIndicator implements HealthIndicator {
        
        private final HealthCheckService healthCheckService;
        
        public ApplicationHealthIndicator(HealthCheckService healthCheckService) {
            this.healthCheckService = healthCheckService;
        }

        @Override
        public Health health() {
            try {
                Map<String, Object> details = new HashMap<>();
                boolean allHealthy = true;

                // Check database connectivity
                DatabaseHealthResult dbHealth = healthCheckService.checkDatabaseHealth();
                details.put("database", dbHealth);
                if (!dbHealth.isHealthy()) {
                    allHealthy = false;
                }

                // Check Redis connectivity
                RedisHealthResult redisHealth = healthCheckService.checkRedisHealth();
                details.put("redis", redisHealth);
                if (!redisHealth.isHealthy()) {
                    allHealthy = false;
                }

                // Check Kafka connectivity
                KafkaHealthResult kafkaHealth = healthCheckService.checkKafkaHealth();
                details.put("kafka", kafkaHealth);
                if (!kafkaHealth.isHealthy()) {
                    allHealthy = false;
                }

                // Check disk space
                DiskSpaceHealthResult diskHealth = healthCheckService.checkDiskSpace();
                details.put("diskSpace", diskHealth);
                if (!diskHealth.isHealthy()) {
                    allHealthy = false;
                }

                // Check memory usage
                MemoryHealthResult memoryHealth = healthCheckService.checkMemoryUsage();
                details.put("memory", memoryHealth);
                if (!memoryHealth.isHealthy()) {
                    allHealthy = false;
                }

                details.put("timestamp", LocalDateTime.now());
                details.put("version", getClass().getPackage().getImplementationVersion());

                return allHealthy ? 
                    Health.up().withDetails(details).build() :
                    Health.down().withDetails(details).build();

            } catch (Exception e) {
                log.error("Health check failed", e);
                return Health.down()
                    .withException(e)
                    .withDetail("timestamp", LocalDateTime.now())
                    .build();
            }
        }
    }

    /**
     * Liveness probe - checks if application is running
     */
    @Component("liveness")
    public static class LivenessHealthIndicator implements HealthIndicator {
        
        @Override
        public Health health() {
            // Simple check - if this method executes, the application is alive
            return Health.up()
                .withDetail("status", "alive")
                .withDetail("timestamp", LocalDateTime.now())
                .build();
        }
    }

    /**
     * Readiness probe - checks if application is ready to serve traffic
     */
    @Component("readiness")
    public static class ReadinessHealthIndicator implements HealthIndicator {
        
        private final HealthCheckService healthCheckService;
        
        public ReadinessHealthIndicator(HealthCheckService healthCheckService) {
            this.healthCheckService = healthCheckService;
        }

        @Override
        public Health health() {
            try {
                // Check critical dependencies for readiness
                boolean ready = true;
                Map<String, Object> details = new HashMap<>();

                // Database must be available
                DatabaseHealthResult dbHealth = healthCheckService.checkDatabaseHealth();
                details.put("database", dbHealth);
                if (!dbHealth.isHealthy()) {
                    ready = false;
                }

                // Redis should be available (but not critical for readiness)
                RedisHealthResult redisHealth = healthCheckService.checkRedisHealth();
                details.put("redis", redisHealth);

                details.put("timestamp", LocalDateTime.now());

                return ready ? 
                    Health.up().withDetails(details).build() :
                    Health.down().withDetails(details).build();

            } catch (Exception e) {
                log.error("Readiness check failed", e);
                return Health.down()
                    .withException(e)
                    .withDetail("timestamp", LocalDateTime.now())
                    .build();
            }
        }
    }

    /**
     * Check database health
     */
    public DatabaseHealthResult checkDatabaseHealth() {
        try {
            long startTime = System.currentTimeMillis();
            
            try (Connection connection = dataSource.getConnection()) {
                boolean isValid = connection.isValid(5); // 5 second timeout
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (isValid && responseTime < 1000) { // Less than 1 second
                    return DatabaseHealthResult.builder()
                        .healthy(true)
                        .responseTime(responseTime)
                        .status("UP")
                        .details("Database connection successful")
                        .build();
                } else {
                    return DatabaseHealthResult.builder()
                        .healthy(false)
                        .responseTime(responseTime)
                        .status("SLOW")
                        .details("Database response time too slow: " + responseTime + "ms")
                        .build();
                }
            }
        } catch (Exception e) {
            log.error("Database health check failed", e);
            return DatabaseHealthResult.builder()
                .healthy(false)
                .responseTime(-1)
                .status("DOWN")
                .details("Database connection failed: " + e.getMessage())
                .build();
        }
    }

    /**
     * Check Redis health
     */
    public RedisHealthResult checkRedisHealth() {
        try {
            long startTime = System.currentTimeMillis();
            
            // Test Redis connectivity with ping
            String result = redisTemplate.getConnectionFactory()
                .getConnection()
                .ping();
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            if ("PONG".equals(result) && responseTime < 500) {
                return RedisHealthResult.builder()
                    .healthy(true)
                    .responseTime(responseTime)
                    .status("UP")
                    .details("Redis ping successful")
                    .build();
            } else {
                return RedisHealthResult.builder()
                    .healthy(false)
                    .responseTime(responseTime)
                    .status("SLOW")
                    .details("Redis response time too slow: " + responseTime + "ms")
                    .build();
            }
        } catch (Exception e) {
            log.error("Redis health check failed", e);
            return RedisHealthResult.builder()
                .healthy(false)
                .responseTime(-1)
                .status("DOWN")
                .details("Redis connection failed: " + e.getMessage())
                .build();
        }
    }

    /**
     * Check Kafka health
     */
    public KafkaHealthResult checkKafkaHealth() {
        try {
            // Test Kafka connectivity by checking cluster metadata
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    kafkaTemplate.getProducerFactory().createProducer().partitionsFor("health-check");
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            future.get(5, TimeUnit.SECONDS); // 5 second timeout
            
            return KafkaHealthResult.builder()
                .healthy(true)
                .status("UP")
                .details("Kafka cluster accessible")
                .build();
                
        } catch (Exception e) {
            log.error("Kafka health check failed", e);
            return KafkaHealthResult.builder()
                .healthy(false)
                .status("DOWN")
                .details("Kafka connection failed: " + e.getMessage())
                .build();
        }
    }

    /**
     * Check disk space
     */
    public DiskSpaceHealthResult checkDiskSpace() {
        try {
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            double usagePercentage = (double) usedSpace / totalSpace * 100;

            boolean healthy = usagePercentage < 85.0; // Alert if more than 85% used

            return DiskSpaceHealthResult.builder()
                .healthy(healthy)
                .totalSpace(totalSpace)
                .freeSpace(freeSpace)
                .usedSpace(usedSpace)
                .usagePercentage(usagePercentage)
                .status(healthy ? "UP" : "WARNING")
                .details(String.format("Disk usage: %.1f%% (%d MB free)", 
                        usagePercentage, freeSpace / 1024 / 1024))
                .build();
                
        } catch (Exception e) {
            log.error("Disk space check failed", e);
            return DiskSpaceHealthResult.builder()
                .healthy(false)
                .status("ERROR")
                .details("Failed to check disk space: " + e.getMessage())
                .build();
        }
    }

    /**
     * Check memory usage
     */
    public MemoryHealthResult checkMemoryUsage() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double usagePercentage = (double) usedMemory / maxMemory * 100;

            boolean healthy = usagePercentage < 85.0; // Alert if more than 85% used

            return MemoryHealthResult.builder()
                .healthy(healthy)
                .maxMemory(maxMemory)
                .totalMemory(totalMemory)
                .freeMemory(freeMemory)
                .usedMemory(usedMemory)
                .usagePercentage(usagePercentage)
                .status(healthy ? "UP" : "WARNING")
                .details(String.format("Memory usage: %.1f%% (%d MB used)", 
                        usagePercentage, usedMemory / 1024 / 1024))
                .build();
                
        } catch (Exception e) {
            log.error("Memory usage check failed", e);
            return MemoryHealthResult.builder()
                .healthy(false)
                .status("ERROR")
                .details("Failed to check memory usage: " + e.getMessage())
                .build();
        }
    }

    // Health result classes
    @lombok.Data
    @lombok.Builder
    public static class DatabaseHealthResult {
        private boolean healthy;
        private long responseTime;
        private String status;
        private String details;
    }

    @lombok.Data
    @lombok.Builder
    public static class RedisHealthResult {
        private boolean healthy;
        private long responseTime;
        private String status;
        private String details;
    }

    @lombok.Data
    @lombok.Builder
    public static class KafkaHealthResult {
        private boolean healthy;
        private String status;
        private String details;
    }

    @lombok.Data
    @lombok.Builder
    public static class DiskSpaceHealthResult {
        private boolean healthy;
        private long totalSpace;
        private long freeSpace;
        private long usedSpace;
        private double usagePercentage;
        private String status;
        private String details;
    }

    @lombok.Data
    @lombok.Builder
    public static class MemoryHealthResult {
        private boolean healthy;
        private long maxMemory;
        private long totalMemory;
        private long freeMemory;
        private long usedMemory;
        private double usagePercentage;
        private String status;
        private String details;
    }
}

/**
 * Custom health endpoint for external monitoring
 */
@RestController
@RequestMapping("/health")
@RequiredArgsConstructor
@Slf4j
class CustomHealthController {

    private final HealthCheckService healthCheckService;

    @GetMapping("/detailed")
    public ResponseEntity<Map<String, Object>> getDetailedHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            health.put("database", healthCheckService.checkDatabaseHealth());
            health.put("redis", healthCheckService.checkRedisHealth());
            health.put("kafka", healthCheckService.checkKafkaHealth());
            health.put("diskSpace", healthCheckService.checkDiskSpace());
            health.put("memory", healthCheckService.checkMemoryUsage());
            health.put("timestamp", LocalDateTime.now());
            health.put("status", "UP");
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("Detailed health check failed", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.status(503).body(health);
        }
    }

    @GetMapping("/simple")
    public ResponseEntity<String> getSimpleHealth() {
        try {
            // Quick health check - just verify database connectivity
            HealthCheckService.DatabaseHealthResult dbHealth = healthCheckService.checkDatabaseHealth();
            
            if (dbHealth.isHealthy()) {
                return ResponseEntity.ok("OK");
            } else {
                return ResponseEntity.status(503).body("UNHEALTHY");
            }
        } catch (Exception e) {
            return ResponseEntity.status(503).body("ERROR");
        }
    }
}
