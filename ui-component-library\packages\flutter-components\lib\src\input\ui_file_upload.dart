import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder File Upload component
class UIFileUpload extends StatefulWidget {
  const UIFileUpload({
    super.key,
    this.onChanged,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.multiple = false,
    this.acceptedTypes = const [],
    this.maxSize,
    this.required = false,
    this.variant = UIInputVariant.outlined,
  });

  final ValueChanged<List<UIFile>>? onChanged;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final bool multiple;
  final List<String> acceptedTypes;
  final int? maxSize; // in bytes
  final bool required;
  final UIInputVariant variant;

  @override
  State<UIFileUpload> createState() => _UIFileUploadState();
}

class _UIFileUploadState extends State<UIFileUpload> {
  List<UIFile> _files = [];

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: tokens.spacing.size2),
        ],
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(tokens.spacing.size6),
          decoration: BoxDecoration(
            border: Border.all(
              color: colorScheme.outline,
              style: BorderStyle.solid,
            ),
            borderRadius: tokens.borderRadius.lg,
            color: widget.enabled 
              ? colorScheme.surface 
              : colorScheme.surfaceVariant.withOpacity(0.5),
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: widget.enabled 
                  ? colorScheme.primary 
                  : colorScheme.onSurfaceVariant,
              ),
              SizedBox(height: tokens.spacing.size2),
              Text(
                widget.hint ?? 'Click to upload files',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: widget.enabled 
                    ? colorScheme.onSurface 
                    : colorScheme.onSurfaceVariant,
                ),
              ),
              if (widget.acceptedTypes.isNotEmpty) ...[
                SizedBox(height: tokens.spacing.size1),
                Text(
                  'Accepted: ${widget.acceptedTypes.join(', ')}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
              if (widget.maxSize != null) ...[
                SizedBox(height: tokens.spacing.size1),
                Text(
                  'Max size: ${_formatFileSize(widget.maxSize!)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
              SizedBox(height: tokens.spacing.size4),
              ElevatedButton.icon(
                onPressed: widget.enabled ? _selectFiles : null,
                icon: Icon(Icons.attach_file),
                label: Text(widget.multiple ? 'Select Files' : 'Select File'),
              ),
            ],
          ),
        ),
        if (_files.isNotEmpty) ...[
          SizedBox(height: tokens.spacing.size3),
          ...(_files.map((file) => _buildFileItem(file))),
        ],
        if (widget.helperText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.helperText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFileItem(UIFile file) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    return Container(
      margin: EdgeInsets.only(bottom: tokens.spacing.size2),
      padding: EdgeInsets.all(tokens.spacing.size3),
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline),
        borderRadius: tokens.borderRadius.md,
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(file.name),
            color: colorScheme.primary,
          ),
          SizedBox(width: tokens.spacing.size2),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (file.size != null)
                  Text(
                    _formatFileSize(file.size!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeFile(file),
            icon: Icon(Icons.close),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _selectFiles() {
    // In a real implementation, this would use file_picker package
    // For now, we'll simulate file selection
    final mockFile = UIFile(
      name: 'example.pdf',
      size: 1024 * 1024, // 1MB
      path: '/path/to/file',
    );
    
    setState(() {
      if (widget.multiple) {
        _files.add(mockFile);
      } else {
        _files = [mockFile];
      }
    });
    
    widget.onChanged?.call(_files);
  }

  void _removeFile(UIFile file) {
    setState(() {
      _files.remove(file);
    });
    widget.onChanged?.call(_files);
  }
}

/// File data class
class UIFile {
  const UIFile({
    required this.name,
    this.size,
    this.path,
    this.data,
  });

  final String name;
  final int? size;
  final String? path;
  final List<int>? data;
}
