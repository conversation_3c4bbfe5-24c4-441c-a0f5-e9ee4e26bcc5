import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { ConfigProvider, theme as antdTheme } from 'antd';
import { ThemeConfig } from 'antd/es/config-provider/context';

export interface UIBuilderTheme {
  // Base theme properties
  name: string;
  mode: 'light' | 'dark' | 'auto';
  
  // Color palette
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      disabled: string;
      inverse: string;
    };
    border: {
      light: string;
      medium: string;
      heavy: string;
    };
  };
  
  // Typography
  typography: {
    fontFamily: {
      primary: string;
      secondary: string;
      monospace: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  
  // Spacing
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  
  // Border radius
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  
  // Shadows
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    inner: string;
  };
  
  // Breakpoints
  breakpoints: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  
  // Z-index scale
  zIndex: {
    hide: number;
    auto: number;
    base: number;
    docked: number;
    dropdown: number;
    sticky: number;
    banner: number;
    overlay: number;
    modal: number;
    popover: number;
    skipLink: number;
    toast: number;
    tooltip: number;
  };
  
  // Animation
  animation: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      linear: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
  
  // Component-specific overrides
  components: {
    button: Record<string, any>;
    input: Record<string, any>;
    card: Record<string, any>;
    modal: Record<string, any>;
    [key: string]: Record<string, any>;
  };
}

export interface ThemeContextValue {
  theme: UIBuilderTheme;
  setTheme: (theme: UIBuilderTheme) => void;
  toggleMode: () => void;
  updateTheme: (updates: Partial<UIBuilderTheme>) => void;
  resetTheme: () => void;
  availableThemes: UIBuilderTheme[];
  currentThemeName: string;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Default light theme
const defaultLightTheme: UIBuilderTheme = {
  name: 'Default Light',
  mode: 'light',
  colors: {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#13c2c2',
    background: '#ffffff',
    surface: '#fafafa',
    text: {
      primary: '#262626',
      secondary: '#595959',
      disabled: '#bfbfbf',
      inverse: '#ffffff',
    },
    border: {
      light: '#f0f0f0',
      medium: '#d9d9d9',
      heavy: '#bfbfbf',
    },
  },
  typography: {
    fontFamily: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      secondary: 'Georgia, "Times New Roman", Times, serif',
      monospace: '"SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  },
  breakpoints: {
    xs: '480px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    '2xl': '1600px',
  },
  zIndex: {
    hide: -1,
    auto: 0,
    base: 1,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
  animation: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
  components: {
    button: {},
    input: {},
    card: {},
    modal: {},
  },
};

// Default dark theme
const defaultDarkTheme: UIBuilderTheme = {
  ...defaultLightTheme,
  name: 'Default Dark',
  mode: 'dark',
  colors: {
    ...defaultLightTheme.colors,
    background: '#141414',
    surface: '#1f1f1f',
    text: {
      primary: '#ffffff',
      secondary: '#a6a6a6',
      disabled: '#595959',
      inverse: '#000000',
    },
    border: {
      light: '#303030',
      medium: '#434343',
      heavy: '#595959',
    },
  },
};

const availableThemes = [defaultLightTheme, defaultDarkTheme];

export interface ThemeProviderProps {
  children: React.ReactNode;
  initialTheme?: UIBuilderTheme;
  persistTheme?: boolean;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme,
  persistTheme = true,
  storageKey = 'ui-builder-theme',
}) => {
  const [theme, setThemeState] = useState<UIBuilderTheme>(() => {
    if (initialTheme) return initialTheme;
    
    if (persistTheme && typeof window !== 'undefined') {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch {
          // Fall back to default if parsing fails
        }
      }
    }
    
    return defaultLightTheme;
  });

  // Persist theme changes
  useEffect(() => {
    if (persistTheme && typeof window !== 'undefined') {
      localStorage.setItem(storageKey, JSON.stringify(theme));
    }
  }, [theme, persistTheme, storageKey]);

  // Auto theme detection
  useEffect(() => {
    if (theme.mode === 'auto' && typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        const newTheme = e.matches ? defaultDarkTheme : defaultLightTheme;
        setThemeState(prev => ({ ...prev, ...newTheme, mode: 'auto' }));
      };

      mediaQuery.addEventListener('change', handleChange);
      
      // Set initial theme based on system preference
      const newTheme = mediaQuery.matches ? defaultDarkTheme : defaultLightTheme;
      setThemeState(prev => ({ ...prev, ...newTheme, mode: 'auto' }));

      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme.mode]);

  const setTheme = useCallback((newTheme: UIBuilderTheme) => {
    setThemeState(newTheme);
  }, []);

  const toggleMode = useCallback(() => {
    setThemeState(prev => {
      const newMode = prev.mode === 'light' ? 'dark' : 'light';
      const baseTheme = newMode === 'dark' ? defaultDarkTheme : defaultLightTheme;
      return { ...prev, ...baseTheme, mode: newMode };
    });
  }, []);

  const updateTheme = useCallback((updates: Partial<UIBuilderTheme>) => {
    setThemeState(prev => ({ ...prev, ...updates }));
  }, []);

  const resetTheme = useCallback(() => {
    setThemeState(defaultLightTheme);
  }, []);

  // Convert theme to Ant Design theme config
  const antdThemeConfig = useMemo((): ThemeConfig => {
    const { token } = antdTheme;
    
    return {
      algorithm: theme.mode === 'dark' ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
      token: {
        colorPrimary: theme.colors.primary,
        colorSuccess: theme.colors.success,
        colorWarning: theme.colors.warning,
        colorError: theme.colors.error,
        colorInfo: theme.colors.info,
        colorBgBase: theme.colors.background,
        colorBgContainer: theme.colors.surface,
        colorText: theme.colors.text.primary,
        colorTextSecondary: theme.colors.text.secondary,
        colorTextDisabled: theme.colors.text.disabled,
        colorBorder: theme.colors.border.medium,
        colorBorderSecondary: theme.colors.border.light,
        fontFamily: theme.typography.fontFamily.primary,
        fontSize: parseInt(theme.typography.fontSize.base) * 16,
        borderRadius: parseInt(theme.borderRadius.md) * 16,
        boxShadow: theme.shadows.md,
        motionDurationSlow: theme.animation.duration.slow,
        motionDurationMid: theme.animation.duration.normal,
        motionDurationFast: theme.animation.duration.fast,
      },
      components: theme.components,
    };
  }, [theme]);

  // Apply CSS custom properties for theme variables
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      
      // Colors
      root.style.setProperty('--color-primary', theme.colors.primary);
      root.style.setProperty('--color-secondary', theme.colors.secondary);
      root.style.setProperty('--color-success', theme.colors.success);
      root.style.setProperty('--color-warning', theme.colors.warning);
      root.style.setProperty('--color-error', theme.colors.error);
      root.style.setProperty('--color-info', theme.colors.info);
      root.style.setProperty('--color-background', theme.colors.background);
      root.style.setProperty('--color-surface', theme.colors.surface);
      root.style.setProperty('--color-text-primary', theme.colors.text.primary);
      root.style.setProperty('--color-text-secondary', theme.colors.text.secondary);
      root.style.setProperty('--color-text-disabled', theme.colors.text.disabled);
      root.style.setProperty('--color-border-light', theme.colors.border.light);
      root.style.setProperty('--color-border-medium', theme.colors.border.medium);
      root.style.setProperty('--color-border-heavy', theme.colors.border.heavy);
      
      // Typography
      root.style.setProperty('--font-family-primary', theme.typography.fontFamily.primary);
      root.style.setProperty('--font-family-secondary', theme.typography.fontFamily.secondary);
      root.style.setProperty('--font-family-monospace', theme.typography.fontFamily.monospace);
      
      // Spacing
      Object.entries(theme.spacing).forEach(([key, value]) => {
        root.style.setProperty(`--spacing-${key}`, value);
      });
      
      // Border radius
      Object.entries(theme.borderRadius).forEach(([key, value]) => {
        root.style.setProperty(`--border-radius-${key}`, value);
      });
      
      // Shadows
      Object.entries(theme.shadows).forEach(([key, value]) => {
        root.style.setProperty(`--shadow-${key}`, value);
      });
      
      // Animation
      root.style.setProperty('--animation-duration-fast', theme.animation.duration.fast);
      root.style.setProperty('--animation-duration-normal', theme.animation.duration.normal);
      root.style.setProperty('--animation-duration-slow', theme.animation.duration.slow);
    }
  }, [theme]);

  const contextValue: ThemeContextValue = {
    theme,
    setTheme,
    toggleMode,
    updateTheme,
    resetTheme,
    availableThemes,
    currentThemeName: theme.name,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={antdThemeConfig}>
        <div className={`theme-${theme.mode}`} data-theme={theme.name}>
          {children}
        </div>
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme utilities
export const createTheme = (overrides: Partial<UIBuilderTheme>): UIBuilderTheme => {
  return { ...defaultLightTheme, ...overrides };
};

export const createDarkTheme = (overrides: Partial<UIBuilderTheme>): UIBuilderTheme => {
  return { ...defaultDarkTheme, ...overrides };
};

export { defaultLightTheme, defaultDarkTheme, availableThemes };
