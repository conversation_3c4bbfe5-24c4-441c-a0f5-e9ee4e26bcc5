import 'package:flutter/material.dart';

/// Theme mode options for the UI Builder
enum UIThemeMode {
  light,
  dark,
  system,
}

/// Theme configuration for UI Builder components
class UIThemeConfig {
  const UIThemeConfig({
    this.mode = UIThemeMode.system,
    this.primaryColor,
    this.accentColor,
    this.backgroundColor,
    this.surfaceColor,
    this.errorColor,
    this.customColors = const {},
    this.fontFamily,
    this.borderRadius,
    this.elevation,
  });

  /// Theme mode
  final UIThemeMode mode;

  /// Primary color override
  final Color? primaryColor;

  /// Accent color override
  final Color? accentColor;

  /// Background color override
  final Color? backgroundColor;

  /// Surface color override
  final Color? surfaceColor;

  /// Error color override
  final Color? errorColor;

  /// Custom color definitions
  final Map<String, Color> customColors;

  /// Font family override
  final String? fontFamily;

  /// Border radius override
  final BorderRadius? borderRadius;

  /// Elevation override
  final double? elevation;

  /// Copy with new values
  UIThemeConfig copyWith({
    UIThemeMode? mode,
    Color? primaryColor,
    Color? accentColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? errorColor,
    Map<String, Color>? customColors,
    String? fontFamily,
    BorderRadius? borderRadius,
    double? elevation,
  }) {
    return UIThemeConfig(
      mode: mode ?? this.mode,
      primaryColor: primaryColor ?? this.primaryColor,
      accentColor: accentColor ?? this.accentColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      errorColor: errorColor ?? this.errorColor,
      customColors: customColors ?? this.customColors,
      fontFamily: fontFamily ?? this.fontFamily,
      borderRadius: borderRadius ?? this.borderRadius,
      elevation: elevation ?? this.elevation,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'mode': mode.name,
      'primaryColor': primaryColor?.value,
      'accentColor': accentColor?.value,
      'backgroundColor': backgroundColor?.value,
      'surfaceColor': surfaceColor?.value,
      'errorColor': errorColor?.value,
      'customColors': customColors.map((key, value) => MapEntry(key, value.value)),
      'fontFamily': fontFamily,
      'borderRadius': borderRadius?.topLeft.x,
      'elevation': elevation,
    };
  }

  /// Create from JSON
  factory UIThemeConfig.fromJson(Map<String, dynamic> json) {
    return UIThemeConfig(
      mode: UIThemeMode.values.firstWhere(
        (mode) => mode.name == json['mode'],
        orElse: () => UIThemeMode.system,
      ),
      primaryColor: json['primaryColor'] != null ? Color(json['primaryColor']) : null,
      accentColor: json['accentColor'] != null ? Color(json['accentColor']) : null,
      backgroundColor: json['backgroundColor'] != null ? Color(json['backgroundColor']) : null,
      surfaceColor: json['surfaceColor'] != null ? Color(json['surfaceColor']) : null,
      errorColor: json['errorColor'] != null ? Color(json['errorColor']) : null,
      customColors: (json['customColors'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key, Color(value)),
      ) ?? {},
      fontFamily: json['fontFamily'],
      borderRadius: json['borderRadius'] != null 
        ? BorderRadius.circular(json['borderRadius'].toDouble()) 
        : null,
      elevation: json['elevation']?.toDouble(),
    );
  }
}

/// Theme extension for custom properties
class UIThemeExtension extends ThemeExtension<UIThemeExtension> {
  const UIThemeExtension({
    this.customColors = const {},
    this.customSpacing = const {},
    this.customBorderRadius = const {},
    this.customElevations = const {},
  });

  /// Custom color definitions
  final Map<String, Color> customColors;

  /// Custom spacing definitions
  final Map<String, double> customSpacing;

  /// Custom border radius definitions
  final Map<String, BorderRadius> customBorderRadius;

  /// Custom elevation definitions
  final Map<String, double> customElevations;

  @override
  UIThemeExtension copyWith({
    Map<String, Color>? customColors,
    Map<String, double>? customSpacing,
    Map<String, BorderRadius>? customBorderRadius,
    Map<String, double>? customElevations,
  }) {
    return UIThemeExtension(
      customColors: customColors ?? this.customColors,
      customSpacing: customSpacing ?? this.customSpacing,
      customBorderRadius: customBorderRadius ?? this.customBorderRadius,
      customElevations: customElevations ?? this.customElevations,
    );
  }

  @override
  UIThemeExtension lerp(ThemeExtension<UIThemeExtension>? other, double t) {
    if (other is! UIThemeExtension) {
      return this;
    }

    return UIThemeExtension(
      customColors: _lerpColorMap(customColors, other.customColors, t),
      customSpacing: _lerpDoubleMap(customSpacing, other.customSpacing, t),
      customBorderRadius: _lerpBorderRadiusMap(customBorderRadius, other.customBorderRadius, t),
      customElevations: _lerpDoubleMap(customElevations, other.customElevations, t),
    );
  }

  Map<String, Color> _lerpColorMap(Map<String, Color> a, Map<String, Color> b, double t) {
    final result = <String, Color>{};
    final allKeys = {...a.keys, ...b.keys};
    
    for (final key in allKeys) {
      final colorA = a[key] ?? Colors.transparent;
      final colorB = b[key] ?? Colors.transparent;
      result[key] = Color.lerp(colorA, colorB, t) ?? colorA;
    }
    
    return result;
  }

  Map<String, double> _lerpDoubleMap(Map<String, double> a, Map<String, double> b, double t) {
    final result = <String, double>{};
    final allKeys = {...a.keys, ...b.keys};
    
    for (final key in allKeys) {
      final valueA = a[key] ?? 0.0;
      final valueB = b[key] ?? 0.0;
      result[key] = valueA + (valueB - valueA) * t;
    }
    
    return result;
  }

  Map<String, BorderRadius> _lerpBorderRadiusMap(
    Map<String, BorderRadius> a, 
    Map<String, BorderRadius> b, 
    double t,
  ) {
    final result = <String, BorderRadius>{};
    final allKeys = {...a.keys, ...b.keys};
    
    for (final key in allKeys) {
      final radiusA = a[key] ?? BorderRadius.zero;
      final radiusB = b[key] ?? BorderRadius.zero;
      result[key] = BorderRadius.lerp(radiusA, radiusB, t) ?? radiusA;
    }
    
    return result;
  }
}

/// Theme data provider
class UIThemeData {
  const UIThemeData({
    required this.colorScheme,
    required this.textTheme,
    this.config = const UIThemeConfig(),
    this.extension = const UIThemeExtension(),
  });

  /// Color scheme
  final ColorScheme colorScheme;

  /// Text theme
  final TextTheme textTheme;

  /// Theme configuration
  final UIThemeConfig config;

  /// Theme extension
  final UIThemeExtension extension;

  /// Get custom color by name
  Color? getCustomColor(String name) {
    return config.customColors[name] ?? extension.customColors[name];
  }

  /// Get custom spacing by name
  double? getCustomSpacing(String name) {
    return extension.customSpacing[name];
  }

  /// Get custom border radius by name
  BorderRadius? getCustomBorderRadius(String name) {
    return extension.customBorderRadius[name];
  }

  /// Get custom elevation by name
  double? getCustomElevation(String name) {
    return extension.customElevations[name];
  }
}
