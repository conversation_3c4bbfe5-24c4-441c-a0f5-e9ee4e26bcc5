import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setUser, clearUser, setLoading, setError } from '../../store/slices/authSlice';
import { apiClient } from '../../services/apiClient';
import { useAnalytics } from '../../hooks/useAnalytics';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  avatarUrl?: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: string[];
  organizationId: string;
  workspaceIds: string[];
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      inApp: boolean;
    };
  };
  createdAt: string;
  lastLoginAt: string;
  isEmailVerified: boolean;
  isTwoFactorEnabled: boolean;
}

export interface AuthContextValue {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  resendVerification: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  enableTwoFactor: () => Promise<{ qrCode: string; secret: string }>;
  verifyTwoFactor: (code: string) => Promise<void>;
  disableTwoFactor: (password: string) => Promise<void>;
  refreshToken: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username: string;
  organizationName?: string;
  inviteToken?: string;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { trackUserAction, trackError } = useAnalytics();
  const { user, isLoading, error } = useSelector((state: RootState) => state.auth);
  
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('authToken');
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (token) {
        try {
          dispatch(setLoading(true));
          
          // Verify token and get user data
          const response = await apiClient.get('/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          dispatch(setUser(response.data));
          trackUserAction('auth_initialized', { userId: response.data.id });
        } catch (error: any) {
          console.error('Token verification failed:', error);
          
          // Try to refresh token
          if (refreshToken) {
            try {
              await refreshTokenInternal();
            } catch (refreshError) {
              // Clear invalid tokens
              localStorage.removeItem('authToken');
              localStorage.removeItem('refreshToken');
              dispatch(clearUser());
            }
          } else {
            localStorage.removeItem('authToken');
            dispatch(clearUser());
          }
        } finally {
          dispatch(setLoading(false));
        }
      }
      
      setIsInitialized(true);
    };

    initializeAuth();
  }, [dispatch, trackUserAction]);

  // Login function
  const login = useCallback(async (
    email: string, 
    password: string, 
    rememberMe: boolean = false
  ) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const response = await apiClient.post('/auth/login', {
        email,
        password,
        rememberMe
      });

      const { user: userData, token, refreshToken } = response.data;

      // Store tokens
      localStorage.setItem('authToken', token);
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken);
      }

      // Update state
      dispatch(setUser(userData));
      
      // Track login
      trackUserAction('user_logged_in', {
        userId: userData.id,
        method: 'email_password',
        rememberMe
      });

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Login failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'login' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      dispatch(setLoading(true));

      // Call logout endpoint to invalidate server-side session
      try {
        await apiClient.post('/auth/logout');
      } catch (error) {
        // Continue with logout even if server call fails
        console.warn('Server logout failed:', error);
      }

      // Clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');

      // Clear state
      dispatch(clearUser());
      
      // Track logout
      trackUserAction('user_logged_out');

    } catch (error: any) {
      console.error('Logout error:', error);
      trackError(error, { context: 'logout' });
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Register function
  const register = useCallback(async (userData: RegisterData) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const response = await apiClient.post('/auth/register', userData);
      
      const { user: newUser, token, refreshToken } = response.data;

      // Store tokens
      localStorage.setItem('authToken', token);
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken);
      }

      // Update state
      dispatch(setUser(newUser));
      
      // Track registration
      trackUserAction('user_registered', {
        userId: newUser.id,
        method: 'email_password'
      });

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Registration failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'register' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Forgot password function
  const forgotPassword = useCallback(async (email: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/forgot-password', { email });
      
      trackUserAction('password_reset_requested', { email });

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to send reset email';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'forgot_password' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Reset password function
  const resetPassword = useCallback(async (token: string, newPassword: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/reset-password', {
        token,
        newPassword
      });
      
      trackUserAction('password_reset_completed');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Password reset failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'reset_password' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Verify email function
  const verifyEmail = useCallback(async (token: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const response = await apiClient.post('/auth/verify-email', { token });
      
      if (user) {
        dispatch(setUser({ ...user, isEmailVerified: true }));
      }
      
      trackUserAction('email_verified');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Email verification failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'verify_email' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, user, trackUserAction, trackError]);

  // Resend verification function
  const resendVerification = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/resend-verification');
      
      trackUserAction('verification_email_resent');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to resend verification';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'resend_verification' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Update profile function
  const updateProfile = useCallback(async (updates: Partial<User>) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const response = await apiClient.put('/auth/profile', updates);
      
      dispatch(setUser(response.data));
      
      trackUserAction('profile_updated', { fields: Object.keys(updates) });

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Profile update failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'update_profile' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Change password function
  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/change-password', {
        currentPassword,
        newPassword
      });
      
      trackUserAction('password_changed');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Password change failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'change_password' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Enable two-factor authentication
  const enableTwoFactor = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const response = await apiClient.post('/auth/2fa/enable');
      
      trackUserAction('two_factor_enabled');
      
      return response.data; // { qrCode, secret }

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to enable 2FA';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'enable_2fa' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, trackUserAction, trackError]);

  // Verify two-factor authentication
  const verifyTwoFactor = useCallback(async (code: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/2fa/verify', { code });
      
      if (user) {
        dispatch(setUser({ ...user, isTwoFactorEnabled: true }));
      }
      
      trackUserAction('two_factor_verified');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '2FA verification failed';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'verify_2fa' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, user, trackUserAction, trackError]);

  // Disable two-factor authentication
  const disableTwoFactor = useCallback(async (password: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      await apiClient.post('/auth/2fa/disable', { password });
      
      if (user) {
        dispatch(setUser({ ...user, isTwoFactorEnabled: false }));
      }
      
      trackUserAction('two_factor_disabled');

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to disable 2FA';
      dispatch(setError(errorMessage));
      trackError(error, { context: 'disable_2fa' });
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, user, trackUserAction, trackError]);

  // Refresh token function
  const refreshTokenInternal = useCallback(async () => {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post('/auth/refresh', {
      refreshToken
    });

    const { token, refreshToken: newRefreshToken, user: userData } = response.data;

    localStorage.setItem('authToken', token);
    if (newRefreshToken) {
      localStorage.setItem('refreshToken', newRefreshToken);
    }

    dispatch(setUser(userData));
  }, [dispatch]);

  const refreshToken = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      await refreshTokenInternal();
    } catch (error: any) {
      dispatch(setError('Session expired. Please login again.'));
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      dispatch(clearUser());
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, refreshTokenInternal]);

  // Permission checking functions
  const checkPermission = useCallback((permission: string): boolean => {
    return user?.permissions.includes(permission) || false;
  }, [user]);

  const hasRole = useCallback((role: string): boolean => {
    return user?.role === role || false;
  }, [user]);

  const contextValue: AuthContextValue = {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    logout,
    register,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
    updateProfile,
    changePassword,
    enableTwoFactor,
    verifyTwoFactor,
    disableTwoFactor,
    refreshToken,
    checkPermission,
    hasRole,
  };

  // Don't render children until auth is initialized
  if (!isInitialized) {
    return (
      <div className="auth-initializing">
        <div className="loading-spinner" />
        <p>Initializing...</p>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
