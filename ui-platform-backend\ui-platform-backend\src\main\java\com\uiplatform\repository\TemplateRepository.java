package com.uiplatform.repository;

import com.uiplatform.entity.Template;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for Template entity.
 * Provides CRUD operations and custom queries for template marketplace management.
 */
@Repository
public interface TemplateRepository extends JpaRepository<Template, UUID>, JpaSpecificationExecutor<Template> {

    /**
     * Find templates by organization.
     */
    List<Template> findByOrganizationIdAndDeletedFalse(UUID organizationId);

    /**
     * Find templates by organization with pagination.
     */
    Page<Template> findByOrganizationIdAndDeletedFalse(UUID organizationId, Pageable pageable);

    /**
     * Find templates by author.
     */
    List<Template> findByAuthorIdAndDeletedFalse(UUID authorId);

    /**
     * Find templates by author with pagination.
     */
    Page<Template> findByAuthorIdAndDeletedFalse(UUID authorId, Pageable pageable);

    /**
     * Find templates by category.
     */
    List<Template> findByCategoryAndDeletedFalse(String category);

    /**
     * Find templates by category with pagination.
     */
    Page<Template> findByCategoryAndDeletedFalse(String category, Pageable pageable);

    /**
     * Find templates by status.
     */
    List<Template> findByStatusAndDeletedFalse(Template.TemplateStatus status);

    /**
     * Find templates by status with pagination.
     */
    Page<Template> findByStatusAndDeletedFalse(Template.TemplateStatus status, Pageable pageable);

    /**
     * Find public templates.
     */
    List<Template> findByIsPublicTrueAndDeletedFalse();

    /**
     * Find public templates with pagination.
     */
    Page<Template> findByIsPublicTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find published templates with pagination.
     */
    Page<Template> findByStatusAndIsPublicTrueAndDeletedFalse(Template.TemplateStatus status, Pageable pageable);

    /**
     * Find premium templates.
     */
    List<Template> findByIsPremiumTrueAndDeletedFalse();

    /**
     * Find premium templates with pagination.
     */
    Page<Template> findByIsPremiumTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find free templates.
     */
    @Query("SELECT t FROM Template t WHERE t.price = 0 AND t.deleted = false")
    List<Template> findFreeTemplates();

    /**
     * Find free templates with pagination.
     */
    @Query("SELECT t FROM Template t WHERE t.price = 0 AND t.deleted = false")
    Page<Template> findFreeTemplates(Pageable pageable);

    /**
     * Find featured templates.
     */
    List<Template> findByIsFeaturedTrueAndDeletedFalse();

    /**
     * Find featured templates with pagination.
     */
    Page<Template> findByIsFeaturedTrueAndDeletedFalse(Pageable pageable);

    /**
     * Search templates by name.
     */
    @Query("SELECT t FROM Template t WHERE LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%')) AND t.isPublic = true AND t.deleted = false")
    Page<Template> searchByName(@Param("name") String name, Pageable pageable);

    /**
     * Search templates by tags.
     */
    @Query("SELECT t FROM Template t WHERE LOWER(t.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND t.isPublic = true AND t.deleted = false")
    Page<Template> searchByTags(@Param("tag") String tag, Pageable pageable);

    /**
     * Search templates by multiple criteria.
     */
    @Query("SELECT t FROM Template t WHERE " +
           "(:name IS NULL OR LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:category IS NULL OR t.category = :category) AND " +
           "(:subcategory IS NULL OR t.subcategory = :subcategory) AND " +
           "(:isPremium IS NULL OR t.isPremium = :isPremium) AND " +
           "(:minPrice IS NULL OR t.price >= :minPrice) AND " +
           "(:maxPrice IS NULL OR t.price <= :maxPrice) AND " +
           "(:minRating IS NULL OR t.rating >= :minRating) AND " +
           "t.isPublic = true AND t.status = 'PUBLISHED' AND t.deleted = false")
    Page<Template> searchTemplates(@Param("name") String name,
                                  @Param("category") String category,
                                  @Param("subcategory") String subcategory,
                                  @Param("isPremium") Boolean isPremium,
                                  @Param("minPrice") BigDecimal minPrice,
                                  @Param("maxPrice") BigDecimal maxPrice,
                                  @Param("minRating") BigDecimal minRating,
                                  Pageable pageable);

    /**
     * Find most popular templates (by download count).
     */
    @Query("SELECT t FROM Template t WHERE t.isPublic = true AND t.status = 'PUBLISHED' AND t.deleted = false ORDER BY t.downloadCount DESC")
    List<Template> findMostPopular(Pageable pageable);

    /**
     * Find highest rated templates.
     */
    @Query("SELECT t FROM Template t WHERE t.isPublic = true AND t.status = 'PUBLISHED' AND t.ratingCount > 0 AND t.deleted = false ORDER BY t.rating DESC")
    List<Template> findHighestRated(Pageable pageable);

    /**
     * Find recently published templates.
     */
    @Query("SELECT t FROM Template t WHERE t.isPublic = true AND t.status = 'PUBLISHED' AND t.deleted = false ORDER BY t.createdAt DESC")
    List<Template> findRecentlyPublished(Pageable pageable);

    /**
     * Find templates by price range.
     */
    @Query("SELECT t FROM Template t WHERE t.price BETWEEN :minPrice AND :maxPrice AND t.isPublic = true AND t.deleted = false")
    Page<Template> findByPriceRange(@Param("minPrice") BigDecimal minPrice, 
                                   @Param("maxPrice") BigDecimal maxPrice, 
                                   Pageable pageable);

    /**
     * Find templates by rating range.
     */
    @Query("SELECT t FROM Template t WHERE t.rating BETWEEN :minRating AND :maxRating AND t.ratingCount > 0 AND t.isPublic = true AND t.deleted = false")
    Page<Template> findByRatingRange(@Param("minRating") BigDecimal minRating, 
                                    @Param("maxRating") BigDecimal maxRating, 
                                    Pageable pageable);

    /**
     * Count templates by category.
     */
    @Query("SELECT COUNT(t) FROM Template t WHERE t.category = :category AND t.isPublic = true AND t.deleted = false")
    Long countByCategory(@Param("category") String category);

    /**
     * Count templates by author.
     */
    @Query("SELECT COUNT(t) FROM Template t WHERE t.author.id = :authorId AND t.deleted = false")
    Long countByAuthorId(@Param("authorId") UUID authorId);

    /**
     * Count published templates by author.
     */
    @Query("SELECT COUNT(t) FROM Template t WHERE t.author.id = :authorId AND t.status = 'PUBLISHED' AND t.deleted = false")
    Long countPublishedByAuthorId(@Param("authorId") UUID authorId);

    /**
     * Increment download count.
     */
    @Modifying
    @Query("UPDATE Template t SET t.downloadCount = t.downloadCount + 1 WHERE t.id = :id")
    void incrementDownloadCount(@Param("id") UUID id);

    /**
     * Increment view count.
     */
    @Modifying
    @Query("UPDATE Template t SET t.viewCount = t.viewCount + 1 WHERE t.id = :id")
    void incrementViewCount(@Param("id") UUID id);

    /**
     * Update template rating.
     */
    @Modifying
    @Query("UPDATE Template t SET t.rating = :rating, t.ratingCount = :ratingCount WHERE t.id = :id")
    void updateRating(@Param("id") UUID id, @Param("rating") BigDecimal rating, @Param("ratingCount") Long ratingCount);

    /**
     * Update template status.
     */
    @Modifying
    @Query("UPDATE Template t SET t.status = :status WHERE t.id = :id")
    void updateStatus(@Param("id") UUID id, @Param("status") Template.TemplateStatus status);

    /**
     * Update template public status.
     */
    @Modifying
    @Query("UPDATE Template t SET t.isPublic = :isPublic WHERE t.id = :id")
    void updatePublicStatus(@Param("id") UUID id, @Param("isPublic") Boolean isPublic);

    /**
     * Update template featured status.
     */
    @Modifying
    @Query("UPDATE Template t SET t.isFeatured = :isFeatured WHERE t.id = :id")
    void updateFeaturedStatus(@Param("id") UUID id, @Param("isFeatured") Boolean isFeatured);

    /**
     * Find all unique categories.
     */
    @Query("SELECT DISTINCT t.category FROM Template t WHERE t.category IS NOT NULL AND t.isPublic = true AND t.deleted = false ORDER BY t.category")
    List<String> findAllCategories();

    /**
     * Find all unique subcategories by category.
     */
    @Query("SELECT DISTINCT t.subcategory FROM Template t WHERE t.category = :category AND t.subcategory IS NOT NULL AND t.isPublic = true AND t.deleted = false ORDER BY t.subcategory")
    List<String> findSubcategoriesByCategory(@Param("category") String category);

    /**
     * Find templates by license type.
     */
    @Query("SELECT t FROM Template t WHERE t.licenseType = :licenseType AND t.isPublic = true AND t.deleted = false")
    Page<Template> findByLicenseType(@Param("licenseType") String licenseType, Pageable pageable);

    /**
     * Find templates by version.
     */
    @Query("SELECT t FROM Template t WHERE t.version = :version AND t.deleted = false")
    List<Template> findByVersion(@Param("version") String version);

    /**
     * Find templates requiring minimum platform version.
     */
    @Query("SELECT t FROM Template t WHERE t.minPlatformVersion = :minVersion AND t.isPublic = true AND t.deleted = false")
    List<Template> findByMinPlatformVersion(@Param("minVersion") String minVersion);
}
