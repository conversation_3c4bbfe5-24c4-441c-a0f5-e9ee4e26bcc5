import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Select component for dropdown selection
class UISelect<T> extends StatefulWidget {
  const UISelect({
    super.key,
    required this.items,
    this.value,
    this.onChanged,
    this.hint,
    this.label,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.variant = UIInputVariant.outlined,
    this.size = UISize.md,
    this.fullWidth = true,
    this.required = false,
    this.searchable = false,
    this.multiple = false,
  });

  final List<UISelectItem<T>> items;
  final T? value;
  final ValueChanged<T?>? onChanged;
  final String? hint;
  final String? label;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final UIInputVariant variant;
  final UISize size;
  final bool fullWidth;
  final bool required;
  final bool searchable;
  final bool multiple;

  @override
  State<UISelect<T>> createState() => _UISelectState<T>();
}

class _UISelectState<T> extends State<UISelect<T>> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    Widget dropdown = DropdownButtonFormField<T>(
      value: widget.value,
      items: widget.items.map((item) {
        return DropdownMenuItem<T>(
          value: item.value,
          enabled: item.enabled,
          child: Row(
            children: [
              if (item.icon != null) ...[
                Icon(item.icon, size: 20),
                SizedBox(width: tokens.spacing.size2),
              ],
              Expanded(child: Text(item.label)),
            ],
          ),
        );
      }).toList(),
      onChanged: widget.enabled ? widget.onChanged : null,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: widget.hint,
        helperText: widget.helperText,
        errorText: widget.errorText,
        border: OutlineInputBorder(
          borderRadius: tokens.borderRadius.md,
        ),
        contentPadding: EdgeInsets.all(tokens.spacing.size4),
      ),
      isExpanded: true,
    );

    if (!widget.fullWidth) {
      dropdown = IntrinsicWidth(child: dropdown);
    }

    return dropdown;
  }
}

/// Select item data class
class UISelectItem<T> {
  const UISelectItem({
    required this.value,
    required this.label,
    this.icon,
    this.enabled = true,
    this.subtitle,
  });

  final T value;
  final String label;
  final IconData? icon;
  final bool enabled;
  final String? subtitle;
}

/// Multi-select component
class UIMultiSelect<T> extends StatefulWidget {
  const UIMultiSelect({
    super.key,
    required this.items,
    this.values = const [],
    this.onChanged,
    this.hint,
    this.label,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.variant = UIInputVariant.outlined,
    this.size = UISize.md,
    this.fullWidth = true,
    this.required = false,
    this.maxSelection,
  });

  final List<UISelectItem<T>> items;
  final List<T> values;
  final ValueChanged<List<T>>? onChanged;
  final String? hint;
  final String? label;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final UIInputVariant variant;
  final UISize size;
  final bool fullWidth;
  final bool required;
  final int? maxSelection;

  @override
  State<UIMultiSelect<T>> createState() => _UIMultiSelectState<T>();
}

class _UIMultiSelectState<T> extends State<UIMultiSelect<T>> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          SizedBox(height: tokens.spacing.size1),
        ],
        Container(
          width: widget.fullWidth ? double.infinity : null,
          decoration: BoxDecoration(
            border: Border.all(color: colorScheme.outline),
            borderRadius: tokens.borderRadius.md,
          ),
          child: Column(
            children: widget.items.map((item) {
              final isSelected = widget.values.contains(item.value);
              return CheckboxListTile(
                value: isSelected,
                onChanged: widget.enabled ? (bool? value) {
                  if (value == true) {
                    if (widget.maxSelection == null || 
                        widget.values.length < widget.maxSelection!) {
                      widget.onChanged?.call([...widget.values, item.value]);
                    }
                  } else {
                    widget.onChanged?.call(
                      widget.values.where((v) => v != item.value).toList(),
                    );
                  }
                } : null,
                title: Text(item.label),
                subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                secondary: item.icon != null ? Icon(item.icon) : null,
                enabled: item.enabled && widget.enabled,
              );
            }).toList(),
          ),
        ),
        if (widget.helperText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.helperText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }
}
