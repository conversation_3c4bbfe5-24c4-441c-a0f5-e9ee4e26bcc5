import React, { forwardRef, useState, useCallback, useMemo } from 'react';
import { ComponentConfig, ComponentProps } from '../../types/component';
import { useTheme } from '../../theme/ThemeProvider';
import './Input.scss';

export interface InputProps extends ComponentProps {
  // Content
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  
  // Type and behavior
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  multiline?: boolean;
  rows?: number;
  autoResize?: boolean;
  
  // Validation
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  
  // Appearance
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'filled' | 'outlined' | 'borderless';
  status?: 'default' | 'error' | 'warning' | 'success';
  
  // Layout
  block?: boolean;
  
  // Prefix and suffix
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
  
  // States
  loading?: boolean;
  showCount?: boolean;
  allowClear?: boolean;
  
  // Events
  onChange?: (value: string, event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onPressEnter?: (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onClear?: () => void;
}

export const Input = forwardRef<HTMLInputElement | HTMLTextAreaElement, InputProps>(({
  value,
  defaultValue,
  placeholder,
  type = 'text',
  multiline = false,
  rows = 3,
  autoResize = false,
  required = false,
  disabled = false,
  readonly = false,
  maxLength,
  minLength,
  pattern,
  size = 'md',
  variant = 'default',
  status = 'default',
  block = false,
  prefix,
  suffix,
  addonBefore,
  addonAfter,
  loading = false,
  showCount = false,
  allowClear = false,
  className,
  style,
  onChange,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPressEnter,
  onClear,
  ...props
}, ref) => {
  const { theme } = useTheme();
  const [internalValue, setInternalValue] = useState(defaultValue || '');
  const [focused, setFocused] = useState(false);

  // Determine if this is a controlled component
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value : internalValue;

  // Build class names
  const classNames = useMemo(() => {
    const classes = ['ui-input-wrapper'];
    
    classes.push(`ui-input-wrapper--${size}`);
    classes.push(`ui-input-wrapper--${variant}`);
    classes.push(`ui-input-wrapper--${status}`);
    
    if (disabled) classes.push('ui-input-wrapper--disabled');
    if (readonly) classes.push('ui-input-wrapper--readonly');
    if (focused) classes.push('ui-input-wrapper--focused');
    if (block) classes.push('ui-input-wrapper--block');
    if (loading) classes.push('ui-input-wrapper--loading');
    if (prefix || suffix) classes.push('ui-input-wrapper--with-affix');
    if (addonBefore || addonAfter) classes.push('ui-input-wrapper--with-addon');
    
    if (className) classes.push(className);
    
    return classes.join(' ');
  }, [size, variant, status, disabled, readonly, focused, block, loading, prefix, suffix, addonBefore, addonAfter, className]);

  // Handle value change
  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
    
    onChange?.(newValue, event);
  }, [isControlled, onChange]);

  // Handle focus
  const handleFocus = useCallback((event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFocused(true);
    onFocus?.(event);
  }, [onFocus]);

  // Handle blur
  const handleBlur = useCallback((event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFocused(false);
    onBlur?.(event);
  }, [onBlur]);

  // Handle key down
  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !multiline) {
      onPressEnter?.(event);
    }
    onKeyDown?.(event);
  }, [multiline, onPressEnter, onKeyDown]);

  // Handle clear
  const handleClear = useCallback(() => {
    if (!isControlled) {
      setInternalValue('');
    }
    onClear?.();
    
    // Create synthetic change event
    const syntheticEvent = {
      target: { value: '' },
      currentTarget: { value: '' }
    } as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>;
    
    onChange?.('', syntheticEvent);
  }, [isControlled, onClear, onChange]);

  // Common input props
  const inputProps = {
    value: currentValue,
    placeholder,
    disabled,
    readOnly: readonly,
    required,
    maxLength,
    minLength,
    pattern,
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onKeyDown: handleKeyDown,
    onKeyUp,
    className: 'ui-input',
    ...props
  };

  // Render input element
  const renderInput = () => {
    if (multiline) {
      return (
        <textarea
          ref={ref as React.Ref<HTMLTextAreaElement>}
          rows={rows}
          {...inputProps}
          style={{
            resize: autoResize ? 'vertical' : 'none',
            ...inputProps.style
          }}
        />
      );
    }

    return (
      <input
        ref={ref as React.Ref<HTMLInputElement>}
        type={type}
        {...inputProps}
      />
    );
  };

  // Render character count
  const renderCount = () => {
    if (!showCount) return null;

    const count = currentValue?.length || 0;
    const max = maxLength;

    return (
      <div className="ui-input-count">
        {max ? `${count}/${max}` : count}
      </div>
    );
  };

  // Render clear button
  const renderClearButton = () => {
    if (!allowClear || !currentValue || disabled || readonly) return null;

    return (
      <button
        type="button"
        className="ui-input-clear"
        onClick={handleClear}
        tabIndex={-1}
      >
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <path d="M6 4.586L10.293.293a1 1 0 011.414 1.414L7.414 6l4.293 4.293a1 1 0 01-1.414 1.414L6 7.414l-4.293 4.293a1 1 0 01-1.414-1.414L4.586 6 .293 1.707A1 1 0 011.707.293L6 4.586z" />
        </svg>
      </button>
    );
  };

  // Render loading spinner
  const renderLoading = () => {
    if (!loading) return null;

    return (
      <div className="ui-input-loading">
        <svg className="ui-input-spinner" width="14" height="14" viewBox="0 0 14 14">
          <circle
            cx="7"
            cy="7"
            r="6"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeDasharray="31.416"
            strokeDashoffset="31.416"
          />
        </svg>
      </div>
    );
  };

  // Build the complete input structure
  const inputElement = (
    <div className={classNames} style={style}>
      {addonBefore && (
        <div className="ui-input-addon ui-input-addon--before">
          {addonBefore}
        </div>
      )}
      
      <div className="ui-input-container">
        {prefix && (
          <div className="ui-input-prefix">
            {prefix}
          </div>
        )}
        
        {renderInput()}
        
        {renderLoading()}
        {renderClearButton()}
        
        {suffix && (
          <div className="ui-input-suffix">
            {suffix}
          </div>
        )}
        
        {renderCount()}
      </div>
      
      {addonAfter && (
        <div className="ui-input-addon ui-input-addon--after">
          {addonAfter}
        </div>
      )}
    </div>
  );

  return inputElement;
});

Input.displayName = 'Input';

// Component configuration for UI Builder
export const InputConfig: ComponentConfig = {
  id: 'input',
  type: 'input',
  displayName: 'Input',
  description: 'A text input component for collecting user input',
  category: 'form',
  tags: ['input', 'form', 'text', 'field'],
  version: '1.0.0',
  
  properties: {
    placeholder: {
      type: 'string',
      label: 'Placeholder',
      description: 'Placeholder text shown when input is empty',
      group: 'content',
      order: 1
    },
    type: {
      type: 'select',
      label: 'Type',
      description: 'Input type',
      defaultValue: 'text',
      options: [
        { label: 'Text', value: 'text' },
        { label: 'Email', value: 'email' },
        { label: 'Password', value: 'password' },
        { label: 'Number', value: 'number' },
        { label: 'Telephone', value: 'tel' },
        { label: 'URL', value: 'url' },
        { label: 'Search', value: 'search' }
      ],
      group: 'behavior',
      order: 2
    },
    multiline: {
      type: 'boolean',
      label: 'Multiline',
      description: 'Whether the input should be a textarea',
      defaultValue: false,
      group: 'behavior',
      order: 3
    },
    rows: {
      type: 'number',
      label: 'Rows',
      description: 'Number of rows for textarea',
      defaultValue: 3,
      conditional: {
        property: 'multiline',
        operator: 'equals',
        value: true
      },
      group: 'behavior',
      order: 4
    },
    size: {
      type: 'select',
      label: 'Size',
      description: 'Input size',
      defaultValue: 'md',
      options: [
        { label: 'Extra Small', value: 'xs' },
        { label: 'Small', value: 'sm' },
        { label: 'Medium', value: 'md' },
        { label: 'Large', value: 'lg' },
        { label: 'Extra Large', value: 'xl' }
      ],
      group: 'appearance',
      order: 5
    },
    variant: {
      type: 'select',
      label: 'Variant',
      description: 'Input style variant',
      defaultValue: 'default',
      options: [
        { label: 'Default', value: 'default' },
        { label: 'Filled', value: 'filled' },
        { label: 'Outlined', value: 'outlined' },
        { label: 'Borderless', value: 'borderless' }
      ],
      group: 'appearance',
      order: 6
    },
    required: {
      type: 'boolean',
      label: 'Required',
      description: 'Whether the input is required',
      defaultValue: false,
      group: 'validation',
      order: 7
    },
    disabled: {
      type: 'boolean',
      label: 'Disabled',
      description: 'Whether the input is disabled',
      defaultValue: false,
      group: 'state',
      order: 8
    },
    maxLength: {
      type: 'number',
      label: 'Max Length',
      description: 'Maximum number of characters',
      group: 'validation',
      order: 9
    },
    showCount: {
      type: 'boolean',
      label: 'Show Count',
      description: 'Whether to show character count',
      defaultValue: false,
      group: 'behavior',
      order: 10
    },
    allowClear: {
      type: 'boolean',
      label: 'Allow Clear',
      description: 'Whether to show clear button',
      defaultValue: false,
      group: 'behavior',
      order: 11
    }
  },
  
  requiredProperties: [],
  
  defaultProps: {
    type: 'text',
    size: 'md',
    variant: 'default',
    multiline: false,
    required: false,
    disabled: false,
    showCount: false,
    allowClear: false
  },
  
  styling: {
    supportedStyles: ['margin', 'padding', 'width', 'height'],
    customCSS: true,
    themes: ['light', 'dark']
  },
  
  layout: {
    canHaveChildren: false,
    maxChildren: 0
  },
  
  events: [
    {
      name: 'onChange',
      label: 'On Change',
      description: 'Triggered when input value changes',
      parameters: [
        {
          name: 'value',
          type: 'string',
          description: 'New input value'
        },
        {
          name: 'event',
          type: 'object',
          description: 'Change event object'
        }
      ]
    },
    {
      name: 'onFocus',
      label: 'On Focus',
      description: 'Triggered when input receives focus'
    },
    {
      name: 'onBlur',
      label: 'On Blur',
      description: 'Triggered when input loses focus'
    },
    {
      name: 'onPressEnter',
      label: 'On Press Enter',
      description: 'Triggered when Enter key is pressed'
    }
  ],
  
  accessibility: {
    role: 'textbox',
    ariaLabels: ['aria-label', 'aria-describedby', 'aria-required'],
    keyboardNavigation: true
  },
  
  performance: {
    lazy: false,
    preload: false,
    cacheStrategy: 'memory'
  }
};
