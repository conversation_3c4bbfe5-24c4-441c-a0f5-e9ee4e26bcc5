import React, { useCallback, useRef, useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useAppDispatch } from '@store/index';
import {
  selectComponent,
  updateComponent,
  setHoveredComponent,
} from '@store/slices/uiBuilderSlice';
import { UIComponent, Position, Size } from '@types/index';

// Component renderers
import { renderComponent } from '@utils/componentRenderer';

// Components
import ResizeHandles from './ResizeHandles';
import ComponentContextMenu from './ComponentContextMenu';

interface CanvasComponentProps {
  component: UIComponent;
  isSelected: boolean;
  isHovered: boolean;
  zoom: number;
  snapToGrid: boolean;
  gridSize: number;
  previewMode: boolean;
}

const CanvasComponent: React.FC<CanvasComponentProps> = ({
  component,
  isSelected,
  isHovered,
  zoom,
  snapToGrid,
  gridSize,
  previewMode,
}) => {
  const dispatch = useAppDispatch();
  const componentRef = useRef<HTMLDivElement>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<Position>({ x: 0, y: 0 });

  // DnD sortable
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: component.id,
    data: {
      type: 'canvas-component',
      component,
    },
    disabled: previewMode || !component.constraints?.draggable,
  });

  // Handle component click
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (previewMode) return;

    if (event.ctrlKey || event.metaKey) {
      // Multi-select mode
      dispatch(selectComponent(component.id));
    } else {
      // Single select
      dispatch(selectComponent(component.id));
    }
  }, [dispatch, component.id, previewMode]);

  // Handle mouse enter
  const handleMouseEnter = useCallback(() => {
    if (!previewMode && !isDragging) {
      dispatch(setHoveredComponent(component.id));
    }
  }, [dispatch, component.id, previewMode, isDragging]);

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    if (!previewMode) {
      dispatch(setHoveredComponent(null));
    }
  }, [dispatch, previewMode]);

  // Handle context menu
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    if (previewMode) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuVisible(true);
  }, [previewMode]);

  // Handle resize
  const handleResize = useCallback((newSize: Size, newPosition?: Position) => {
    setIsResizing(true);
    
    const updates: Partial<UIComponent> = { size: newSize };
    
    if (newPosition) {
      // Snap to grid if enabled
      if (snapToGrid) {
        newPosition.x = Math.round(newPosition.x / gridSize) * gridSize;
        newPosition.y = Math.round(newPosition.y / gridSize) * gridSize;
      }
      updates.position = newPosition;
    }

    dispatch(updateComponent({
      id: component.id,
      updates,
    }));
  }, [dispatch, component.id, snapToGrid, gridSize]);

  // Handle resize end
  const handleResizeEnd = useCallback(() => {
    setIsResizing(false);
  }, []);

  // Calculate component styles
  const getComponentStyles = useCallback(() => {
    const baseStyles: React.CSSProperties = {
      position: 'absolute',
      left: component.position.x,
      top: component.position.y,
      width: component.size.width,
      height: component.size.height,
      zIndex: component.position.z || 1,
      ...component.styles,
    };

    // Apply selection styles
    if (isSelected && !previewMode) {
      baseStyles.outline = '2px solid #3b82f6';
      baseStyles.outlineOffset = '2px';
    } else if (isHovered && !previewMode && !isDragging) {
      baseStyles.outline = '1px solid #93c5fd';
      baseStyles.outlineOffset = '1px';
    }

    // Apply drag transform
    if (transform && !previewMode) {
      baseStyles.transform = CSS.Transform.toString(transform);
      baseStyles.transition = transition;
    }

    // Apply dragging styles
    if (isDragging) {
      baseStyles.opacity = 0.8;
      baseStyles.zIndex = 1000;
    }

    return baseStyles;
  }, [
    component.position,
    component.size,
    component.styles,
    isSelected,
    isHovered,
    previewMode,
    isDragging,
    transform,
    transition,
  ]);

  // Get component class names
  const getComponentClassNames = useCallback(() => {
    const classes = ['canvas-component'];
    
    if (isSelected && !previewMode) {
      classes.push('component-selected');
    }
    
    if (isHovered && !previewMode && !isDragging) {
      classes.push('component-hover');
    }
    
    if (isDragging) {
      classes.push('component-dragging');
    }
    
    if (isResizing) {
      classes.push('component-resizing');
    }

    return classes.join(' ');
  }, [isSelected, isHovered, isDragging, isResizing, previewMode]);

  return (
    <>
      <div
        ref={(node) => {
          setNodeRef(node);
          componentRef.current = node;
        }}
        style={getComponentStyles()}
        className={getComponentClassNames()}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onContextMenu={handleContextMenu}
        {...attributes}
        {...(!previewMode && component.constraints?.draggable ? listeners : {})}
        data-component-id={component.id}
        data-component-type={component.type}
      >
        {/* Render the actual component */}
        {renderComponent(component, {
          isSelected,
          isHovered,
          previewMode,
          zoom,
        })}

        {/* Render child components */}
        {component.children?.map((child) => (
          <CanvasComponent
            key={child.id}
            component={child}
            isSelected={false} // Child selection handled separately
            isHovered={false}
            zoom={zoom}
            snapToGrid={snapToGrid}
            gridSize={gridSize}
            previewMode={previewMode}
          />
        ))}

        {/* Resize handles */}
        {isSelected && 
         !previewMode && 
         component.constraints?.resizable && 
         !isDragging && (
          <ResizeHandles
            component={component}
            zoom={zoom}
            onResize={handleResize}
            onResizeEnd={handleResizeEnd}
          />
        )}

        {/* Component label for debugging */}
        {!previewMode && (isSelected || isHovered) && (
          <div
            className="absolute -top-6 left-0 bg-blue-600 text-white text-xs px-2 py-1 rounded pointer-events-none"
            style={{ fontSize: Math.max(10, 12 / zoom) }}
          >
            {component.displayName || component.type}
          </div>
        )}
      </div>

      {/* Context menu */}
      {contextMenuVisible && (
        <ComponentContextMenu
          component={component}
          position={contextMenuPosition}
          onClose={() => setContextMenuVisible(false)}
        />
      )}
    </>
  );
};

export default CanvasComponent;
