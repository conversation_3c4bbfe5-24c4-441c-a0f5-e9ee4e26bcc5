package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Layout entity for managing UI positioning and responsive rules.
 * Layouts define how components are arranged and behave across different screen sizes.
 */
@Entity
@Table(name = "layouts", indexes = {
    @Index(name = "idx_layout_name", columnList = "name"),
    @Index(name = "idx_layout_type", columnList = "layout_type"),
    @Index(name = "idx_layout_organization", columnList = "organization_id"),
    @Index(name = "idx_layout_is_default", columnList = "is_default")
})
public class Layout extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "layout_type", nullable = false)
    private LayoutType layoutType = LayoutType.GRID;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "grid_config", columnDefinition = "jsonb")
    private Map<String, Object> gridConfig = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "flexbox_config", columnDefinition = "jsonb")
    private Map<String, Object> flexboxConfig = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "responsive_rules", columnDefinition = "jsonb")
    private Map<String, Object> responsiveRules = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "positioning_rules", columnDefinition = "jsonb")
    private Map<String, Object> positioningRules = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "spacing_rules", columnDefinition = "jsonb")
    private Map<String, Object> spacingRules = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "alignment_rules", columnDefinition = "jsonb")
    private Map<String, Object> alignmentRules = new HashMap<>();

    @Column(name = "max_width")
    private String maxWidth;

    @Column(name = "min_width")
    private String minWidth;

    @Column(name = "max_height")
    private String maxHeight;

    @Column(name = "min_height")
    private String minHeight;

    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    @Column(name = "preview_image_url")
    private String previewImageUrl;

    @Column(name = "tags")
    private String tags;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    @JsonIgnore
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id")
    @JsonIgnore
    private User author;

    @OneToMany(mappedBy = "layout", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<UIConfiguration> uiConfigurations = new HashSet<>();

    // Constructors
    public Layout() {}

    public Layout(String name, LayoutType layoutType, Organization organization, User author) {
        this.name = name;
        this.layoutType = layoutType;
        this.organization = organization;
        this.author = author;
        initializeDefaultValues();
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LayoutType getLayoutType() {
        return layoutType;
    }

    public void setLayoutType(LayoutType layoutType) {
        this.layoutType = layoutType;
    }

    public Map<String, Object> getGridConfig() {
        return gridConfig;
    }

    public void setGridConfig(Map<String, Object> gridConfig) {
        this.gridConfig = gridConfig;
    }

    public Map<String, Object> getFlexboxConfig() {
        return flexboxConfig;
    }

    public void setFlexboxConfig(Map<String, Object> flexboxConfig) {
        this.flexboxConfig = flexboxConfig;
    }

    public Map<String, Object> getResponsiveRules() {
        return responsiveRules;
    }

    public void setResponsiveRules(Map<String, Object> responsiveRules) {
        this.responsiveRules = responsiveRules;
    }

    public Map<String, Object> getPositioningRules() {
        return positioningRules;
    }

    public void setPositioningRules(Map<String, Object> positioningRules) {
        this.positioningRules = positioningRules;
    }

    public Map<String, Object> getSpacingRules() {
        return spacingRules;
    }

    public void setSpacingRules(Map<String, Object> spacingRules) {
        this.spacingRules = spacingRules;
    }

    public Map<String, Object> getAlignmentRules() {
        return alignmentRules;
    }

    public void setAlignmentRules(Map<String, Object> alignmentRules) {
        this.alignmentRules = alignmentRules;
    }

    public String getMaxWidth() {
        return maxWidth;
    }

    public void setMaxWidth(String maxWidth) {
        this.maxWidth = maxWidth;
    }

    public String getMinWidth() {
        return minWidth;
    }

    public void setMinWidth(String minWidth) {
        this.minWidth = minWidth;
    }

    public String getMaxHeight() {
        return maxHeight;
    }

    public void setMaxHeight(String maxHeight) {
        this.maxHeight = maxHeight;
    }

    public String getMinHeight() {
        return minHeight;
    }

    public void setMinHeight(String minHeight) {
        this.minHeight = minHeight;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }

    public Set<UIConfiguration> getUiConfigurations() {
        return uiConfigurations;
    }

    public void setUiConfigurations(Set<UIConfiguration> uiConfigurations) {
        this.uiConfigurations = uiConfigurations;
    }

    // Utility methods
    private void initializeDefaultValues() {
        switch (layoutType) {
            case GRID:
                initializeGridDefaults();
                break;
            case FLEXBOX:
                initializeFlexboxDefaults();
                break;
            case ABSOLUTE:
                initializeAbsoluteDefaults();
                break;
            case FLOAT:
                initializeFloatDefaults();
                break;
        }
    }

    private void initializeGridDefaults() {
        Map<String, Object> defaultGrid = new HashMap<>();
        defaultGrid.put("columns", 12);
        defaultGrid.put("gap", "16px");
        defaultGrid.put("autoRows", "minmax(100px, auto)");
        this.gridConfig = defaultGrid;
    }

    private void initializeFlexboxDefaults() {
        Map<String, Object> defaultFlex = new HashMap<>();
        defaultFlex.put("direction", "row");
        defaultFlex.put("wrap", "wrap");
        defaultFlex.put("justifyContent", "flex-start");
        defaultFlex.put("alignItems", "stretch");
        defaultFlex.put("gap", "16px");
        this.flexboxConfig = defaultFlex;
    }

    private void initializeAbsoluteDefaults() {
        Map<String, Object> defaultPositioning = new HashMap<>();
        defaultPositioning.put("position", "relative");
        this.positioningRules = defaultPositioning;
    }

    private void initializeFloatDefaults() {
        Map<String, Object> defaultFloat = new HashMap<>();
        defaultFloat.put("clearfix", true);
        this.positioningRules = defaultFloat;
    }

    public void setAsDefault() {
        this.isDefault = true;
    }

    public void unsetAsDefault() {
        this.isDefault = false;
    }

    // Enums
    public enum LayoutType {
        GRID,       // CSS Grid Layout
        FLEXBOX,    // CSS Flexbox Layout
        ABSOLUTE,   // Absolute positioning
        FLOAT,      // Float-based layout (legacy)
        CUSTOM      // Custom layout with specific rules
    }

    // Static factory methods for common layouts
    public static Layout createGridLayout(String name, Organization organization, User author) {
        return new Layout(name, LayoutType.GRID, organization, author);
    }

    public static Layout createFlexboxLayout(String name, Organization organization, User author) {
        return new Layout(name, LayoutType.FLEXBOX, organization, author);
    }

    public static Layout createAbsoluteLayout(String name, Organization organization, User author) {
        return new Layout(name, LayoutType.ABSOLUTE, organization, author);
    }
}
