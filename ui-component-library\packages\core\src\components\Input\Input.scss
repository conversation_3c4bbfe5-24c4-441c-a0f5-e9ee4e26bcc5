.ui-input-wrapper {
  // Base styles
  display: inline-flex;
  align-items: stretch;
  width: auto;
  position: relative;
  
  // Block layout
  &--block {
    width: 100%;
    display: flex;
  }
  
  // Focus state
  &--focused {
    .ui-input-container {
      border-color: var(--color-primary, #3b82f6);
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
  
  // Disabled state
  &--disabled {
    .ui-input-container {
      background-color: var(--color-surface, #f8fafc);
      border-color: var(--color-border-light, #f1f5f9);
      cursor: not-allowed;
    }
    
    .ui-input {
      color: var(--color-text-muted, #94a3b8);
      cursor: not-allowed;
      
      &::placeholder {
        color: var(--color-text-muted, #94a3b8);
      }
    }
  }
  
  // Readonly state
  &--readonly {
    .ui-input-container {
      background-color: var(--color-surface, #f8fafc);
    }
    
    .ui-input {
      cursor: default;
    }
  }
  
  // Loading state
  &--loading {
    .ui-input {
      padding-right: 32px;
    }
  }
}

// Input container
.ui-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  border: 1px solid var(--color-border-medium, #e2e8f0);
  border-radius: var(--border-radius-md, 0.375rem);
  background-color: var(--color-background, #ffffff);
  transition: all 0.2s ease-in-out;
  position: relative;
  
  &:hover:not(.ui-input-wrapper--disabled &) {
    border-color: var(--color-border-strong, #cbd5e1);
  }
}

// Input element
.ui-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: var(--font-family-primary, inherit);
  font-size: var(--font-size-base, 1rem);
  line-height: var(--line-height-normal, 1.5);
  color: var(--color-text-primary, #1e293b);
  
  &::placeholder {
    color: var(--color-text-muted, #94a3b8);
  }
  
  // Remove default styles
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  &[type="number"] {
    -moz-appearance: textfield;
  }
  
  &::-webkit-search-decoration,
  &::-webkit-search-cancel-button,
  &::-webkit-search-results-button,
  &::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }
}

// Size variants
.ui-input-wrapper--xs {
  .ui-input-container {
    min-height: 24px;
  }
  
  .ui-input {
    padding: 2px 8px;
    font-size: var(--font-size-xs, 0.75rem);
  }
  
  .ui-input-prefix,
  .ui-input-suffix {
    padding: 0 4px;
    font-size: var(--font-size-xs, 0.75rem);
  }
}

.ui-input-wrapper--sm {
  .ui-input-container {
    min-height: 32px;
  }
  
  .ui-input {
    padding: 4px 12px;
    font-size: var(--font-size-sm, 0.875rem);
  }
  
  .ui-input-prefix,
  .ui-input-suffix {
    padding: 0 8px;
    font-size: var(--font-size-sm, 0.875rem);
  }
}

.ui-input-wrapper--md {
  .ui-input-container {
    min-height: 40px;
  }
  
  .ui-input {
    padding: 8px 16px;
    font-size: var(--font-size-base, 1rem);
  }
  
  .ui-input-prefix,
  .ui-input-suffix {
    padding: 0 12px;
    font-size: var(--font-size-base, 1rem);
  }
}

.ui-input-wrapper--lg {
  .ui-input-container {
    min-height: 48px;
  }
  
  .ui-input {
    padding: 12px 20px;
    font-size: var(--font-size-lg, 1.125rem);
  }
  
  .ui-input-prefix,
  .ui-input-suffix {
    padding: 0 16px;
    font-size: var(--font-size-lg, 1.125rem);
  }
}

.ui-input-wrapper--xl {
  .ui-input-container {
    min-height: 56px;
  }
  
  .ui-input {
    padding: 16px 24px;
    font-size: var(--font-size-xl, 1.25rem);
  }
  
  .ui-input-prefix,
  .ui-input-suffix {
    padding: 0 20px;
    font-size: var(--font-size-xl, 1.25rem);
  }
}

// Variant styles
.ui-input-wrapper--filled {
  .ui-input-container {
    background-color: var(--color-surface, #f8fafc);
    border-color: transparent;
    
    &:hover {
      background-color: var(--color-border-light, #f1f5f9);
    }
  }
  
  &.ui-input-wrapper--focused .ui-input-container {
    background-color: var(--color-background, #ffffff);
    border-color: var(--color-primary, #3b82f6);
  }
}

.ui-input-wrapper--outlined {
  .ui-input-container {
    border-width: 2px;
  }
}

.ui-input-wrapper--borderless {
  .ui-input-container {
    border-color: transparent;
    background-color: transparent;
    
    &:hover {
      border-color: var(--color-border-medium, #e2e8f0);
    }
  }
  
  &.ui-input-wrapper--focused .ui-input-container {
    border-color: var(--color-primary, #3b82f6);
  }
}

// Status variants
.ui-input-wrapper--error {
  .ui-input-container {
    border-color: var(--color-error, #ef4444);
    
    &:hover {
      border-color: var(--color-error, #ef4444);
    }
  }
  
  &.ui-input-wrapper--focused .ui-input-container {
    border-color: var(--color-error, #ef4444);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
}

.ui-input-wrapper--warning {
  .ui-input-container {
    border-color: var(--color-warning, #faad14);
    
    &:hover {
      border-color: var(--color-warning, #faad14);
    }
  }
  
  &.ui-input-wrapper--focused .ui-input-container {
    border-color: var(--color-warning, #faad14);
    box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  }
}

.ui-input-wrapper--success {
  .ui-input-container {
    border-color: var(--color-success, #10b981);
    
    &:hover {
      border-color: var(--color-success, #10b981);
    }
  }
  
  &.ui-input-wrapper--focused .ui-input-container {
    border-color: var(--color-success, #10b981);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }
}

// Prefix and suffix
.ui-input-prefix,
.ui-input-suffix {
  display: flex;
  align-items: center;
  color: var(--color-text-secondary, #64748b);
  white-space: nowrap;
  flex-shrink: 0;
}

.ui-input-prefix {
  border-right: 1px solid var(--color-border-light, #f1f5f9);
}

.ui-input-suffix {
  border-left: 1px solid var(--color-border-light, #f1f5f9);
}

// Addons
.ui-input-addon {
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: var(--color-surface, #f8fafc);
  border: 1px solid var(--color-border-medium, #e2e8f0);
  color: var(--color-text-secondary, #64748b);
  white-space: nowrap;
  flex-shrink: 0;
  
  &--before {
    border-right: none;
    border-radius: var(--border-radius-md, 0.375rem) 0 0 var(--border-radius-md, 0.375rem);
  }
  
  &--after {
    border-left: none;
    border-radius: 0 var(--border-radius-md, 0.375rem) var(--border-radius-md, 0.375rem) 0;
  }
}

.ui-input-wrapper--with-addon {
  .ui-input-container {
    border-radius: 0;
  }
  
  &:not(.ui-input-wrapper--with-addon):first-child .ui-input-container {
    border-radius: var(--border-radius-md, 0.375rem) 0 0 var(--border-radius-md, 0.375rem);
  }
  
  &:not(.ui-input-wrapper--with-addon):last-child .ui-input-container {
    border-radius: 0 var(--border-radius-md, 0.375rem) var(--border-radius-md, 0.375rem) 0;
  }
}

// Clear button
.ui-input-clear {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-muted, #94a3b8);
  padding: 2px;
  border-radius: 2px;
  transition: color 0.2s ease;
  
  &:hover {
    color: var(--color-text-secondary, #64748b);
  }
  
  &:focus {
    outline: none;
    color: var(--color-text-primary, #1e293b);
  }
}

// Loading spinner
.ui-input-loading {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-primary, #3b82f6);
}

.ui-input-spinner {
  animation: ui-input-spin 1s linear infinite;
}

@keyframes ui-input-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Character count
.ui-input-count {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: var(--font-size-xs, 0.75rem);
  color: var(--color-text-muted, #94a3b8);
}

// Textarea specific styles
textarea.ui-input {
  resize: vertical;
  min-height: 80px;
  padding-top: 8px;
  padding-bottom: 8px;
  line-height: var(--line-height-relaxed, 1.75);
}

// Dark theme support
.dark-theme {
  .ui-input-container {
    background-color: var(--color-surface, #1e293b);
    border-color: var(--color-border-medium, #475569);
  }
  
  .ui-input {
    color: var(--color-text-primary, #f8fafc);
    
    &::placeholder {
      color: var(--color-text-muted, #64748b);
    }
  }
  
  .ui-input-addon {
    background-color: var(--color-surface, #1e293b);
    border-color: var(--color-border-medium, #475569);
    color: var(--color-text-secondary, #cbd5e1);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .ui-input-container {
    border-width: 2px;
  }
  
  .ui-input-wrapper--focused .ui-input-container {
    border-width: 3px;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .ui-input-container,
  .ui-input-clear,
  .ui-input-spinner {
    transition: none;
    animation: none;
  }
}

// Print styles
@media print {
  .ui-input-wrapper {
    .ui-input-container {
      border: 1px solid #000;
      background: transparent;
    }
    
    .ui-input {
      color: #000;
    }
    
    .ui-input-clear,
    .ui-input-loading {
      display: none;
    }
  }
}
