version: '3.8'

# Development-specific Docker Compose configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # PostgreSQL with development settings
  postgres:
    environment:
      POSTGRES_DB: uiplatform_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/dev-init.sql:/docker-entrypoint-initdb.d/dev-init.sql

  # Redis with development settings
  redis:
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Kafka with development settings
  kafka:
    environment:
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9093
    ports:
      - "9093:9092"  # Different port to avoid conflicts

  # Backend with development settings
  ui-platform-backend:
    build:
      context: ./ui-platform-backend/ui-platform-backend
      dockerfile: Dockerfile.dev
      target: development
    environment:
      SPRING_PROFILES_ACTIVE: development
      DATABASE_URL: **********************************************
      DATABASE_USERNAME: dev_user
      DATABASE_PASSWORD: dev_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      JWT_SECRET: devSecretKey
      CORS_ALLOWED_ORIGINS: "*"
      LOG_LEVEL: DEBUG
      JPA_SHOW_SQL: true
      SWAGGER_ENABLED: true
    volumes:
      - ./ui-platform-backend/ui-platform-backend/src:/app/src
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - maven_cache:/root/.m2
    ports:
      - "8080:8080"
      - "5005:5005"  # Debug port
    command: >
      sh -c "mvn spring-boot:run 
             -Dspring-boot.run.jvmArguments='-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005'
             -Dspring-boot.run.profiles=development"

  # Metadata service with development settings
  ui-metadata-service:
    build:
      context: ./ui-metadata-service
      dockerfile: Dockerfile.dev
      target: development
    environment:
      SPRING_PROFILES_ACTIVE: development
      DATABASE_URL: **********************************************
      DATABASE_USERNAME: dev_user
      DATABASE_PASSWORD: dev_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      JWT_SECRET: devSecretKey
      CORS_ALLOWED_ORIGINS: "*"
      LOG_LEVEL: DEBUG
      SWAGGER_ENABLED: true
    volumes:
      - ./ui-metadata-service/src:/app/src
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - maven_cache:/root/.m2
    ports:
      - "8081:8080"
      - "5006:5005"  # Debug port
    command: >
      sh -c "mvn spring-boot:run 
             -Dspring-boot.run.jvmArguments='-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005'
             -Dspring-boot.run.profiles=development"

  # Frontend with hot reload
  ui-builder-frontend:
    build:
      context: ./ui-builder-frontend
      dockerfile: Dockerfile.dev
      target: development
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api/v1
      VITE_WEBSOCKET_URL: ws://localhost:8080/ws
      VITE_NODE_ENV: development
      VITE_ENABLE_REDUX_DEVTOOLS: true
      VITE_ENABLE_REACT_DEVTOOLS: true
      VITE_LOG_LEVEL: debug
    volumes:
      - ./ui-builder-frontend:/app
      - /app/node_modules
      - /app/dist
    ports:
      - "3000:3000"
      - "24678:24678"  # Vite HMR port
    command: npm run dev -- --host 0.0.0.0

  # Web runtime with hot reload
  web-runtime:
    build:
      context: ./web-runtime
      dockerfile: Dockerfile.dev
      target: development
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api/v1
      VITE_WEBSOCKET_URL: ws://localhost:8080/ws
      VITE_NODE_ENV: development
      VITE_LOG_LEVEL: debug
    volumes:
      - ./web-runtime:/app
      - /app/node_modules
      - /app/dist
    ports:
      - "3001:3000"
      - "24679:24678"  # Vite HMR port
    command: npm run dev -- --host 0.0.0.0

  # Development tools
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ui-platform-mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - ui-platform-network

  # Database admin tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ui-platform-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - ui-platform-network

  # Redis admin tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ui-platform-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8082:8081"
    networks:
      - ui-platform-network

  # Kafka UI
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: ui-platform-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8083:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - ui-platform-network

  # File browser for uploads
  filebrowser:
    image: filebrowser/filebrowser:latest
    container_name: ui-platform-filebrowser
    ports:
      - "8084:80"
    volumes:
      - ./uploads:/srv
      - filebrowser_data:/database
    environment:
      FB_DATABASE: /database/filebrowser.db
    networks:
      - ui-platform-network

volumes:
  postgres_dev_data:
    driver: local
  pgadmin_data:
    driver: local
  filebrowser_data:
    driver: local
  maven_cache:
    driver: local

networks:
  ui-platform-network:
    external: true
