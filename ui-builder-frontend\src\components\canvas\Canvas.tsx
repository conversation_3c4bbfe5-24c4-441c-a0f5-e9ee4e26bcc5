import React, { useRef, useCallback, useEffect, useState } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent, useSensor, useSensors, PointerSensor, KeyboardSensor } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useAppDispatch, useAppSelector } from '@store/index';
import {
  addComponent,
  updateComponent,
  moveComponent,
  selectComponent,
  clearSelection,
  setCanvasPan,
  setCanvasZoom,
  startDrag,
  endDrag,
  setDropTarget,
} from '@store/slices/uiBuilderSlice';
import { ComponentType, UIComponent, Position } from '@types/index';

// Components
import CanvasComponent from './CanvasComponent';
import CanvasGrid from './CanvasGrid';
import CanvasRulers from './CanvasRulers';
import CanvasGuides from './CanvasGuides';
import SelectionBox from './SelectionBox';
import CollaborationOverlay from '../collaboration/CollaborationOverlay';

// Utils
import { generateComponentId, createDefaultComponent } from '@utils/componentUtils';
import { snapToGrid, getDropPosition } from '@utils/canvasUtils';

interface CanvasProps {
  className?: string;
}

const Canvas: React.FC<CanvasProps> = ({ className = '' }) => {
  const dispatch = useAppDispatch();
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPosition, setLastPanPosition] = useState<Position>({ x: 0, y: 0 });

  const {
    currentConfiguration,
    canvas,
    selection,
    dragDrop,
    ui,
  } = useAppSelector(state => state.uiBuilder);

  const { collaborationUsers } = useAppSelector(state => state.collaboration);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Handle component drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    
    if (active.data.current?.type === 'component-palette') {
      // Dragging from component palette
      const componentType = active.data.current.componentType as ComponentType;
      const newComponent = createDefaultComponent(componentType);
      dispatch(startDrag(newComponent));
    } else if (active.data.current?.type === 'canvas-component') {
      // Dragging existing component
      const component = active.data.current.component as UIComponent;
      dispatch(startDrag(component));
    }
  }, [dispatch]);

  // Handle drag over
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over, active } = event;
    
    if (!over || !active) {
      dispatch(setDropTarget({ targetId: null, position: null }));
      return;
    }

    const overData = over.data.current;
    const activeData = active.data.current;

    if (overData?.type === 'canvas' || overData?.type === 'canvas-component') {
      const dropPosition = getDropPosition(event, overData);
      dispatch(setDropTarget({ 
        targetId: over.id as string, 
        position: dropPosition 
      }));
    }
  }, [dispatch]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { over, active } = event;
    
    dispatch(endDrag());

    if (!over || !dragDrop.draggedComponent) {
      return;
    }

    const overData = over.data.current;
    const rect = canvasRef.current?.getBoundingClientRect();
    
    if (!rect) return;

    // Calculate drop position
    const dropX = (event.activatorEvent as PointerEvent).clientX - rect.left;
    const dropY = (event.activatorEvent as PointerEvent).clientY - rect.top;

    let finalPosition = {
      x: dropX - canvas.pan.x,
      y: dropY - canvas.pan.y,
    };

    // Apply zoom
    finalPosition.x /= canvas.zoom;
    finalPosition.y /= canvas.zoom;

    // Snap to grid if enabled
    if (canvas.snapToGrid) {
      finalPosition = snapToGrid(finalPosition, canvas.gridSize);
    }

    if (active.data.current?.type === 'component-palette') {
      // Adding new component from palette
      const newComponent: UIComponent = {
        ...dragDrop.draggedComponent,
        id: generateComponentId(),
        position: finalPosition,
      };

      // Determine parent and index based on drop target
      let parentId: string | undefined;
      let index: number | undefined;

      if (overData?.type === 'canvas-component') {
        const targetComponent = overData.component as UIComponent;
        
        if (dragDrop.dropPosition === 'inside') {
          parentId = targetComponent.id;
        } else {
          parentId = targetComponent.parentId;
          // Calculate index based on drop position
          // This would need more sophisticated logic
        }
      }

      dispatch(addComponent({ 
        component: newComponent, 
        parentId, 
        index 
      }));

    } else if (active.data.current?.type === 'canvas-component') {
      // Moving existing component
      const componentId = dragDrop.draggedComponent.id;
      
      // Update position
      dispatch(updateComponent({
        id: componentId,
        updates: { position: finalPosition },
      }));

      // Handle parent change if needed
      if (overData?.type === 'canvas-component' && dragDrop.dropPosition) {
        const targetComponent = overData.component as UIComponent;
        
        if (dragDrop.dropPosition === 'inside') {
          dispatch(moveComponent({
            componentId,
            newParentId: targetComponent.id,
            newIndex: 0,
          }));
        }
      }
    }
  }, [dispatch, dragDrop, canvas, canvasRef]);

  // Handle canvas click
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (event.target === canvasRef.current) {
      dispatch(clearSelection());
    }
  }, [dispatch]);

  // Handle canvas mouse down for panning
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.button === 1 || (event.button === 0 && event.altKey)) {
      // Middle mouse button or Alt + left click for panning
      event.preventDefault();
      setIsPanning(true);
      setLastPanPosition({ x: event.clientX, y: event.clientY });
      document.body.style.cursor = 'grabbing';
    }
  }, []);

  // Handle mouse move for panning
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isPanning) return;

    const deltaX = event.clientX - lastPanPosition.x;
    const deltaY = event.clientY - lastPanPosition.y;

    dispatch(setCanvasPan({
      x: canvas.pan.x + deltaX,
      y: canvas.pan.y + deltaY,
    }));

    setLastPanPosition({ x: event.clientX, y: event.clientY });
  }, [isPanning, lastPanPosition, canvas.pan, dispatch]);

  // Handle mouse up for panning
  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
    document.body.style.cursor = '';
  }, []);

  // Handle wheel for zooming
  const handleWheel = useCallback((event: WheelEvent) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      
      const delta = event.deltaY > 0 ? 0.9 : 1.1;
      const newZoom = Math.max(0.1, Math.min(5, canvas.zoom * delta));
      
      dispatch(setCanvasZoom(newZoom));
    }
  }, [canvas.zoom, dispatch]);

  // Setup event listeners
  useEffect(() => {
    const canvasElement = canvasRef.current;
    if (!canvasElement) return;

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    canvasElement.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      canvasElement.removeEventListener('wheel', handleWheel);
    };
  }, [handleMouseMove, handleMouseUp, handleWheel]);

  if (!currentConfiguration) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Configuration Loaded
          </h3>
          <p className="text-gray-600">
            Create a new configuration or open an existing one to start building.
          </p>
        </div>
      </div>
    );
  }

  const canvasStyle = {
    transform: `scale(${canvas.zoom}) translate(${canvas.pan.x}px, ${canvas.pan.y}px)`,
    transformOrigin: '0 0',
  };

  return (
    <div className={`relative flex-1 overflow-hidden bg-gray-100 ${className}`}>
      {/* Rulers */}
      {canvas.showRulers && !ui.previewMode && (
        <CanvasRulers zoom={canvas.zoom} pan={canvas.pan} />
      )}

      {/* Main canvas area */}
      <div
        ref={canvasRef}
        className="absolute inset-0 ui-builder-canvas"
        style={{
          backgroundSize: canvas.showGrid ? `${canvas.gridSize}px ${canvas.gridSize}px` : 'none',
        }}
        onClick={handleCanvasClick}
        onMouseDown={handleMouseDown}
      >
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          {/* Grid */}
          {canvas.showGrid && !ui.previewMode && (
            <CanvasGrid size={canvas.gridSize} zoom={canvas.zoom} />
          )}

          {/* Guides */}
          {canvas.showGuides && !ui.previewMode && (
            <CanvasGuides />
          )}

          {/* Canvas content */}
          <div style={canvasStyle} className="relative">
            <SortableContext
              items={currentConfiguration.components.map(c => c.id)}
              strategy={verticalListSortingStrategy}
            >
              {currentConfiguration.components.map((component) => (
                <CanvasComponent
                  key={component.id}
                  component={component}
                  isSelected={selection.selectedComponentIds.includes(component.id)}
                  isHovered={selection.hoveredComponentId === component.id}
                  zoom={canvas.zoom}
                  snapToGrid={canvas.snapToGrid}
                  gridSize={canvas.gridSize}
                  previewMode={ui.previewMode}
                />
              ))}
            </SortableContext>

            {/* Selection box for multi-select */}
            {selection.selectedComponentIds.length > 1 && (
              <SelectionBox
                componentIds={selection.selectedComponentIds}
                components={currentConfiguration.components}
              />
            )}

            {/* Collaboration overlay */}
            <CollaborationOverlay
              users={collaborationUsers}
              zoom={canvas.zoom}
            />
          </div>
        </DndContext>
      </div>

      {/* Canvas info overlay */}
      {!ui.previewMode && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md px-3 py-2 text-sm text-gray-600">
          Zoom: {Math.round(canvas.zoom * 100)}% | 
          Pan: {Math.round(canvas.pan.x)}, {Math.round(canvas.pan.y)} |
          Grid: {canvas.gridSize}px
        </div>
      )}
    </div>
  );
};

export default Canvas;
