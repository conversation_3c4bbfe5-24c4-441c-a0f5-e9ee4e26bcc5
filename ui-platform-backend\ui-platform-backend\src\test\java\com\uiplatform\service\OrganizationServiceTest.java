package com.uiplatform.service;

import com.uiplatform.dto.OrganizationDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.exception.DuplicateResourceException;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.mapper.OrganizationMapper;
import com.uiplatform.repository.OrganizationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for OrganizationService.
 */
@ExtendWith(MockitoExtension.class)
class OrganizationServiceTest {

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private OrganizationMapper organizationMapper;

    @InjectMocks
    private OrganizationService organizationService;

    private Organization testOrganization;
    private OrganizationDTO testOrganizationDTO;
    private OrganizationDTO.CreateDTO testCreateDTO;
    private UUID testId;

    @BeforeEach
    void setUp() {
        testId = UUID.randomUUID();
        
        testOrganization = new Organization();
        testOrganization.setId(testId);
        testOrganization.setName("Test Organization");
        testOrganization.setSlug("test-org");
        testOrganization.setDescription("Test Description");
        testOrganization.setStatus(Organization.OrganizationStatus.ACTIVE);
        testOrganization.setSubscriptionPlan(Organization.SubscriptionPlan.FREE);
        testOrganization.setDeleted(false);

        testOrganizationDTO = new OrganizationDTO();
        testOrganizationDTO.setId(testId);
        testOrganizationDTO.setName("Test Organization");
        testOrganizationDTO.setSlug("test-org");
        testOrganizationDTO.setDescription("Test Description");

        testCreateDTO = new OrganizationDTO.CreateDTO();
        testCreateDTO.setName("Test Organization");
        testCreateDTO.setSlug("test-org");
        testCreateDTO.setDescription("Test Description");
    }

    @Test
    void createOrganization_Success() {
        // Arrange
        when(organizationRepository.findBySlugAndDeletedFalse(testCreateDTO.getSlug()))
                .thenReturn(Optional.empty());
        when(organizationMapper.toEntity(testCreateDTO)).thenReturn(testOrganization);
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.createOrganization(testCreateDTO);

        // Assert
        assertNotNull(result);
        assertEquals(testOrganizationDTO.getName(), result.getName());
        assertEquals(testOrganizationDTO.getSlug(), result.getSlug());
        verify(organizationRepository).save(any(Organization.class));
    }

    @Test
    void createOrganization_DuplicateSlug_ThrowsException() {
        // Arrange
        when(organizationRepository.findBySlugAndDeletedFalse(testCreateDTO.getSlug()))
                .thenReturn(Optional.of(testOrganization));

        // Act & Assert
        assertThrows(DuplicateResourceException.class, 
                () -> organizationService.createOrganization(testCreateDTO));
        verify(organizationRepository, never()).save(any(Organization.class));
    }

    @Test
    void getOrganizationById_Success() {
        // Arrange
        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.getOrganizationById(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testOrganizationDTO.getId(), result.getId());
        assertEquals(testOrganizationDTO.getName(), result.getName());
    }

    @Test
    void getOrganizationById_NotFound_ThrowsException() {
        // Arrange
        when(organizationRepository.findById(testId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, 
                () -> organizationService.getOrganizationById(testId));
    }

    @Test
    void getOrganizationById_Deleted_ThrowsException() {
        // Arrange
        testOrganization.setDeleted(true);
        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, 
                () -> organizationService.getOrganizationById(testId));
    }

    @Test
    void getOrganizationBySlug_Success() {
        // Arrange
        when(organizationRepository.findBySlugAndDeletedFalse("test-org"))
                .thenReturn(Optional.of(testOrganization));
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.getOrganizationBySlug("test-org");

        // Assert
        assertNotNull(result);
        assertEquals(testOrganizationDTO.getSlug(), result.getSlug());
    }

    @Test
    void updateOrganization_Success() {
        // Arrange
        OrganizationDTO.UpdateDTO updateDTO = new OrganizationDTO.UpdateDTO();
        updateDTO.setName("Updated Organization");
        updateDTO.setDescription("Updated Description");

        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.updateOrganization(testId, updateDTO);

        // Assert
        assertNotNull(result);
        verify(organizationMapper).updateEntityFromDTO(updateDTO, testOrganization);
        verify(organizationRepository).save(testOrganization);
    }

    @Test
    void deleteOrganization_Success() {
        // Arrange
        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);

        // Act
        organizationService.deleteOrganization(testId);

        // Assert
        verify(organizationRepository).save(testOrganization);
        // Note: We can't verify markAsDeleted was called because it's a method on the entity
    }

    @Test
    void getAllOrganizations_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Organization> organizationPage = new PageImpl<>(Arrays.asList(testOrganization));
        when(organizationRepository.findAll(pageable)).thenReturn(organizationPage);
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        Page<OrganizationDTO> result = organizationService.getAllOrganizations(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testOrganizationDTO.getName(), result.getContent().get(0).getName());
    }

    @Test
    void updateSubscriptionPlan_Success() {
        // Arrange
        Organization.SubscriptionPlan newPlan = Organization.SubscriptionPlan.PROFESSIONAL;
        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.updateSubscriptionPlan(testId, newPlan);

        // Assert
        assertNotNull(result);
        verify(organizationRepository).save(testOrganization);
        assertEquals(newPlan, testOrganization.getSubscriptionPlan());
    }

    @Test
    void updateStatus_Success() {
        // Arrange
        Organization.OrganizationStatus newStatus = Organization.OrganizationStatus.SUSPENDED;
        when(organizationRepository.findById(testId)).thenReturn(Optional.of(testOrganization));
        when(organizationRepository.save(any(Organization.class))).thenReturn(testOrganization);
        when(organizationMapper.toDTO(testOrganization)).thenReturn(testOrganizationDTO);

        // Act
        OrganizationDTO result = organizationService.updateStatus(testId, newStatus);

        // Assert
        assertNotNull(result);
        verify(organizationRepository).save(testOrganization);
        assertEquals(newStatus, testOrganization.getStatus());
    }

    @Test
    void existsBySlug_True() {
        // Arrange
        when(organizationRepository.findBySlugAndDeletedFalse("test-org"))
                .thenReturn(Optional.of(testOrganization));

        // Act
        boolean result = organizationService.existsBySlug("test-org");

        // Assert
        assertTrue(result);
    }

    @Test
    void existsBySlug_False() {
        // Arrange
        when(organizationRepository.findBySlugAndDeletedFalse("non-existent"))
                .thenReturn(Optional.empty());

        // Act
        boolean result = organizationService.existsBySlug("non-existent");

        // Assert
        assertFalse(result);
    }
}
