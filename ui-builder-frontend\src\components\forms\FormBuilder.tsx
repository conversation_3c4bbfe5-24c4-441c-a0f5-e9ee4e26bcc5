import React, { useState, useCallback, useMemo } from 'react';
import { DndContext, DragEndEvent, DragOverlay, useSensor, useSensors, PointerSensor } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Button, Space, Card, Tabs, Modal, message } from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  DeleteOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@store/index';
import { FormField, FormFieldType, FormConfiguration, ValidationRule } from '@types/index';

// Components
import FormFieldPalette from './FormFieldPalette';
import FormFieldEditor from './FormFieldEditor';
import FormCanvas from './FormCanvas';
import FormPreview from './FormPreview';
import FormSettings from './FormSettings';
import SortableFormField from './SortableFormField';

// Utils
import { generateFormFieldId, createDefaultFormField } from '@utils/formUtils';

const { TabPane } = Tabs;

interface FormBuilderProps {
  className?: string;
  initialForm?: FormConfiguration;
  onSave?: (form: FormConfiguration) => void;
  onCancel?: () => void;
}

const FormBuilder: React.FC<FormBuilderProps> = ({
  className = '',
  initialForm,
  onSave,
  onCancel,
}) => {
  const dispatch = useAppDispatch();
  
  const [formConfig, setFormConfig] = useState<FormConfiguration>(
    initialForm || createDefaultFormConfiguration()
  );
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('design');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [draggedField, setDraggedField] = useState<FormField | null>(null);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Get selected field
  const selectedField = useMemo(() => {
    return formConfig.fields.find(field => field.id === selectedFieldId) || null;
  }, [formConfig.fields, selectedFieldId]);

  // Handle field addition from palette
  const handleAddField = useCallback((fieldType: FormFieldType) => {
    const newField = createDefaultFormField(fieldType);
    
    setFormConfig(prev => ({
      ...prev,
      fields: [...prev.fields, newField],
    }));
    
    setSelectedFieldId(newField.id);
  }, []);

  // Handle field update
  const handleFieldUpdate = useCallback((fieldId: string, updates: Partial<FormField>) => {
    setFormConfig(prev => ({
      ...prev,
      fields: prev.fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field
      ),
    }));
  }, []);

  // Handle field deletion
  const handleFieldDelete = useCallback((fieldId: string) => {
    setFormConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId),
    }));
    
    if (selectedFieldId === fieldId) {
      setSelectedFieldId(null);
    }
  }, [selectedFieldId]);

  // Handle field duplication
  const handleFieldDuplicate = useCallback((fieldId: string) => {
    const field = formConfig.fields.find(f => f.id === fieldId);
    if (!field) return;

    const duplicatedField: FormField = {
      ...field,
      id: generateFormFieldId(),
      name: `${field.name}_copy`,
      label: `${field.label} (Copy)`,
      position: {
        ...field.position,
        y: field.position.y + 60,
      },
    };

    setFormConfig(prev => ({
      ...prev,
      fields: [...prev.fields, duplicatedField],
    }));
    
    setSelectedFieldId(duplicatedField.id);
  }, [formConfig.fields]);

  // Handle drag start
  const handleDragStart = useCallback((event: any) => {
    const { active } = event;
    
    if (active.data.current?.type === 'form-field-palette') {
      const fieldType = active.data.current.fieldType as FormFieldType;
      const newField = createDefaultFormField(fieldType);
      setDraggedField(newField);
    } else if (active.data.current?.type === 'form-field') {
      const field = active.data.current.field as FormField;
      setDraggedField(field);
    }
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    setDraggedField(null);
    
    if (!over) return;

    if (active.data.current?.type === 'form-field-palette') {
      // Adding new field from palette
      const fieldType = active.data.current.fieldType as FormFieldType;
      const newField = createDefaultFormField(fieldType);
      
      // Calculate drop position
      const dropPosition = calculateDropPosition(event);
      newField.position = dropPosition;
      
      setFormConfig(prev => ({
        ...prev,
        fields: [...prev.fields, newField],
      }));
      
      setSelectedFieldId(newField.id);
      
    } else if (active.data.current?.type === 'form-field') {
      // Reordering existing field
      const activeField = formConfig.fields.find(f => f.id === active.id);
      const overField = formConfig.fields.find(f => f.id === over.id);
      
      if (activeField && overField && activeField.id !== overField.id) {
        const activeIndex = formConfig.fields.indexOf(activeField);
        const overIndex = formConfig.fields.indexOf(overField);
        
        const newFields = [...formConfig.fields];
        newFields.splice(activeIndex, 1);
        newFields.splice(overIndex, 0, activeField);
        
        setFormConfig(prev => ({
          ...prev,
          fields: newFields,
        }));
      }
    }
  }, [formConfig.fields]);

  // Handle form save
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(formConfig);
    } else {
      // Default save behavior
      message.success('Form saved successfully');
    }
  }, [formConfig, onSave]);

  // Handle form settings update
  const handleFormSettingsUpdate = useCallback((updates: Partial<FormConfiguration>) => {
    setFormConfig(prev => ({ ...prev, ...updates }));
  }, []);

  // Calculate drop position based on drag event
  const calculateDropPosition = (event: DragEndEvent) => {
    // This is a simplified position calculation
    // In a real implementation, you'd calculate based on the actual drop coordinates
    const existingFields = formConfig.fields;
    const lastField = existingFields[existingFields.length - 1];
    
    return {
      x: 20,
      y: lastField ? lastField.position.y + lastField.size.height + 20 : 20,
    };
  };

  return (
    <div className={`flex flex-col h-full bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Form Builder</h2>
          <p className="text-sm text-gray-600 mt-1">{formConfig.name}</p>
        </div>
        
        <Space>
          <Button
            icon={<EyeOutlined />}
            onClick={() => setPreviewVisible(true)}
          >
            Preview
          </Button>
          
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            Settings
          </Button>
          
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            Save Form
          </Button>
          
          {onCancel && (
            <Button onClick={onCancel}>
              Cancel
            </Button>
          )}
        </Space>
      </div>

      {/* Main content */}
      <div className="flex-1 flex overflow-hidden">
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          {/* Left sidebar - Field palette */}
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Form Fields</h3>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              <FormFieldPalette onAddField={handleAddField} />
            </div>
          </div>

          {/* Center - Form canvas */}
          <div className="flex-1 flex flex-col">
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              className="flex-1 flex flex-col"
              tabBarStyle={{ margin: 0, padding: '0 16px', backgroundColor: 'white' }}
            >
              <TabPane tab="Design" key="design" className="flex-1">
                <FormCanvas
                  fields={formConfig.fields}
                  selectedFieldId={selectedFieldId}
                  onFieldSelect={setSelectedFieldId}
                  onFieldUpdate={handleFieldUpdate}
                  onFieldDelete={handleFieldDelete}
                  onFieldDuplicate={handleFieldDuplicate}
                  layout={formConfig.layout}
                />
              </TabPane>
              
              <TabPane tab="Preview" key="preview" className="flex-1">
                <FormPreview form={formConfig} />
              </TabPane>
            </Tabs>
          </div>

          {/* Right sidebar - Field properties */}
          <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedField ? 'Field Properties' : 'Form Properties'}
              </h3>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {selectedField ? (
                <FormFieldEditor
                  field={selectedField}
                  onChange={(updates) => handleFieldUpdate(selectedField.id, updates)}
                />
              ) : (
                <div className="p-4">
                  <Card title="Form Information" size="small">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Form Name
                        </label>
                        <input
                          type="text"
                          value={formConfig.name}
                          onChange={(e) => setFormConfig(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={formConfig.description || ''}
                          onChange={(e) => setFormConfig(prev => ({ ...prev, description: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md"
                          rows={3}
                        />
                      </div>
                      
                      <div className="text-sm text-gray-500">
                        Fields: {formConfig.fields.length}
                      </div>
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </div>

          {/* Drag overlay */}
          <DragOverlay>
            {draggedField && (
              <div className="bg-white border border-blue-300 rounded-lg p-3 shadow-lg opacity-80">
                <div className="text-sm font-medium">{draggedField.label}</div>
                <div className="text-xs text-gray-500">{draggedField.type}</div>
              </div>
            )}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Preview modal */}
      <Modal
        title="Form Preview"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        footer={null}
        destroyOnClose
      >
        <FormPreview form={formConfig} />
      </Modal>

      {/* Settings modal */}
      <Modal
        title="Form Settings"
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        width="60%"
        footer={null}
        destroyOnClose
      >
        <FormSettings
          form={formConfig}
          onChange={handleFormSettingsUpdate}
        />
      </Modal>
    </div>
  );
};

// Helper function to create default form configuration
function createDefaultFormConfiguration(): FormConfiguration {
  return {
    id: 'new-form',
    name: 'Untitled Form',
    description: '',
    fields: [],
    layout: {
      type: 'single-column',
      spacing: 16,
    },
    validation: {
      validateOnChange: true,
      validateOnBlur: true,
      showErrorsInline: true,
      showErrorSummary: false,
    },
    submission: {
      method: 'POST',
      action: '',
      successMessage: 'Form submitted successfully!',
      errorMessage: 'There was an error submitting the form.',
    },
    styling: {
      theme: 'default',
      fieldSpacing: 16,
      labelPosition: 'top',
      buttonAlignment: 'left',
    },
    settings: {
      allowDrafts: false,
      showProgress: false,
      enableAutosave: false,
      autosaveInterval: 30,
      captcha: {
        enabled: false,
        provider: 'recaptcha',
        siteKey: '',
      },
      fileUpload: {
        maxFileSize: 10,
        allowedTypes: ['image/*', '.pdf', '.doc', '.docx'],
        maxFiles: 5,
        storage: 'local',
      },
    },
  };
}

export default FormBuilder;
