#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Flutter UI Runtime Project Verification Script
/// Checks project structure, dependencies, and critical files
void main() async {
  print('🔍 Flutter UI Runtime Project Verification');
  print('==========================================\n');

  final verifier = ProjectVerifier();
  await verifier.runAllChecks();
}

class ProjectVerifier {
  int passedChecks = 0;
  int failedChecks = 0;
  int warningChecks = 0;

  Future<void> runAllChecks() async {
    print('📋 Phase 1: Project Structure Verification');
    print('==========================================');
    await checkProjectStructure();

    print('\n📋 Phase 2: Dependencies Verification');
    print('=====================================');
    await checkDependencies();

    print('\n📋 Phase 3: Critical Files Verification');
    print('=======================================');
    await checkCriticalFiles();

    print('\n📋 Phase 4: Build System Verification');
    print('=====================================');
    await checkBuildSystem();

    print('\n📊 Verification Summary');
    print('======================');
    print('✅ Passed: $passedChecks');
    print('⚠️  Warnings: $warningChecks');
    print('❌ Failed: $failedChecks');
    print('Total: ${passedChecks + warningChecks + failedChecks}');

    if (failedChecks == 0) {
      print('\n🎉 All critical checks passed! Project is ready for development.');
    } else {
      print('\n⚠️  Some checks failed. Please address the issues above.');
      exit(1);
    }
  }

  Future<void> checkProjectStructure() async {
    final requiredDirs = [
      'android',
      'ios',
      'lib',
      'test',
      'assets',
      'assets/images',
      'assets/icons',
      'assets/animations',
      'assets/fonts',
    ];

    for (final dir in requiredDirs) {
      if (await Directory(dir).exists()) {
        printPass('Directory exists: $dir');
      } else {
        printFail('Missing directory: $dir');
      }
    }

    final requiredFiles = [
      'pubspec.yaml',
      'analysis_options.yaml',
      'lib/main.dart',
      '.env',
    ];

    for (final file in requiredFiles) {
      if (await File(file).exists()) {
        printPass('File exists: $file');
      } else {
        printFail('Missing file: $file');
      }
    }
  }

  Future<void> checkDependencies() async {
    try {
      final pubspecFile = File('pubspec.yaml');
      if (!await pubspecFile.exists()) {
        printFail('pubspec.yaml not found');
        return;
      }

      final content = await pubspecFile.readAsString();
      
      final criticalDeps = [
        'flutter_riverpod',
        'dio',
        'hive_flutter',
        'go_router',
        'firebase_core',
        'flutter_secure_storage',
      ];

      for (final dep in criticalDeps) {
        if (content.contains(dep)) {
          printPass('Dependency found: $dep');
        } else {
          printFail('Missing dependency: $dep');
        }
      }

      // Check for pub get
      final pubspecLock = File('pubspec.lock');
      if (await pubspecLock.exists()) {
        printPass('Dependencies resolved (pubspec.lock exists)');
      } else {
        printWarn('Dependencies not resolved - run flutter pub get');
      }

    } catch (e) {
      printFail('Error checking dependencies: $e');
    }
  }

  Future<void> checkCriticalFiles() async {
    final criticalFiles = [
      'lib/main.dart',
      'lib/core/app.dart',
      'lib/core/config/app_config.dart',
      'lib/core/utils/logger.dart',
      'lib/core/services/notification_service.dart',
      'lib/firebase_options.dart',
      'lib/core/router/app_router.dart',
      'lib/core/providers/auth_provider.dart',
    ];

    for (final file in criticalFiles) {
      if (await File(file).exists()) {
        printPass('Critical file exists: $file');
      } else {
        printFail('Missing critical file: $file');
      }
    }

    // Check .env file content
    final envFile = File('.env');
    if (await envFile.exists()) {
      final content = await envFile.readAsString();
      final requiredVars = [
        'API_BASE_URL',
        'WEBSOCKET_URL',
        'JWT_STORAGE_KEY',
      ];

      for (final envVar in requiredVars) {
        if (content.contains(envVar)) {
          printPass('Environment variable configured: $envVar');
        } else {
          printWarn('Missing environment variable: $envVar');
        }
      }
    }
  }

  Future<void> checkBuildSystem() async {
    try {
      // Check Flutter version
      final flutterResult = await Process.run('flutter', ['--version']);
      if (flutterResult.exitCode == 0) {
        printPass('Flutter CLI available');
        final version = flutterResult.stdout.toString();
        if (version.contains('3.')) {
          printPass('Flutter 3.x detected');
        } else {
          printWarn('Flutter version may be outdated');
        }
      } else {
        printFail('Flutter CLI not available');
      }

      // Check Dart version
      final dartResult = await Process.run('dart', ['--version']);
      if (dartResult.exitCode == 0) {
        printPass('Dart CLI available');
      } else {
        printFail('Dart CLI not available');
      }

      // Check if project can analyze
      print('Running flutter analyze...');
      final analyzeResult = await Process.run('flutter', ['analyze', '--no-pub']);
      if (analyzeResult.exitCode == 0) {
        printPass('Flutter analyze completed without errors');
      } else {
        final errors = analyzeResult.stdout.toString();
        final errorCount = RegExp(r'error').allMatches(errors).length;
        if (errorCount > 0) {
          printWarn('Flutter analyze found $errorCount errors');
        } else {
          printPass('Flutter analyze completed with warnings only');
        }
      }

    } catch (e) {
      printFail('Error checking build system: $e');
    }
  }

  void printPass(String message) {
    print('✅ $message');
    passedChecks++;
  }

  void printFail(String message) {
    print('❌ $message');
    failedChecks++;
  }

  void printWarn(String message) {
    print('⚠️  $message');
    warningChecks++;
  }
}
