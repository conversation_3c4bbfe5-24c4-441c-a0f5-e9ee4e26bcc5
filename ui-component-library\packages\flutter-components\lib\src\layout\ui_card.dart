import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';
import '../utils/ui_utils.dart';

/// UI Builder Card component that provides consistent card styling
/// across Material and Cupertino platforms
class UICard extends StatefulWidget {
  const UICard({
    super.key,
    required this.child,
    this.variant = UICardVariant.defaultVariant,
    this.size = UISize.md,
    this.interactive = false,
    this.onTap,
    this.elevation,
    this.borderRadius,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.shadowColor,
    this.testId,
    this.semanticLabel,
    this.animationProps,
    this.platformBehavior = UIPlatformBehavior.auto,
  });

  /// Card content
  final Widget child;

  /// Card variant
  final UICardVariant variant;

  /// Card size
  final UISize size;

  /// Whether card is interactive (clickable)
  final bool interactive;

  /// Tap handler for interactive cards
  final VoidCallback? onTap;

  /// Custom elevation
  final double? elevation;

  /// Custom border radius
  final BorderRadius? borderRadius;

  /// Card margin
  final EdgeInsets? margin;

  /// Card padding
  final EdgeInsets? padding;

  /// Background color override
  final Color? backgroundColor;

  /// Border color override
  final Color? borderColor;

  /// Shadow color override
  final Color? shadowColor;

  /// Test identifier
  final String? testId;

  /// Semantic label for accessibility
  final String? semanticLabel;

  /// Animation properties
  final UIAnimationProps? animationProps;

  /// Platform-specific behavior
  final UIPlatformBehavior platformBehavior;

  @override
  State<UICard> createState() => _UICardState();
}

class _UICardState extends State<UICard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationProps?.duration ?? const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationProps?.curve ?? Curves.easeInOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: _getDefaultElevation(),
      end: _getDefaultElevation() + 2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationProps?.curve ?? Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.interactive) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.interactive) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.interactive) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tokens = DesignTokens.of(context);

    return Semantics(
      label: widget.semanticLabel,
      button: widget.interactive,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.interactive ? _scaleAnimation.value : 1.0,
            child: _buildPlatformCard(context, theme, tokens),
          );
        },
      ),
    );
  }

  Widget _buildPlatformCard(
    BuildContext context,
    ThemeData theme,
    DesignTokens tokens,
  ) {
    final shouldUseCupertino = widget.platformBehavior == UIPlatformBehavior.cupertino || (widget.platformBehavior == UIPlatformBehavior.auto && UIUtils.isCupertino(context));

    if (shouldUseCupertino) {
      return _buildCupertinoCard(context, tokens);
    } else {
      return _buildMaterialCard(context, theme, tokens);
    }
  }

  Widget _buildMaterialCard(
    BuildContext context,
    ThemeData theme,
    DesignTokens tokens,
  ) {
    final cardStyle = _getCardStyle(theme, tokens);

    Widget card = Card(
      elevation: widget.interactive ? _elevationAnimation.value : (widget.elevation ?? cardStyle.elevation),
      color: widget.backgroundColor ?? cardStyle.backgroundColor,
      shadowColor: widget.shadowColor ?? cardStyle.shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: widget.borderRadius ?? cardStyle.borderRadius,
        side: cardStyle.borderSide,
      ),
      margin: widget.margin ?? cardStyle.margin,
      child: Padding(
        padding: widget.padding ?? cardStyle.padding,
        child: widget.child,
      ),
    );

    if (widget.interactive) {
      card = GestureDetector(
        onTap: widget.onTap,
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: card,
      );
    }

    return _wrapCard(card);
  }

  Widget _buildCupertinoCard(BuildContext context, DesignTokens tokens) {
    final cardStyle = _getCardStyle(Theme.of(context), tokens);

    Widget card = Container(
      margin: widget.margin ?? cardStyle.margin,
      padding: widget.padding ?? cardStyle.padding,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? cardStyle.backgroundColor,
        borderRadius: widget.borderRadius ?? cardStyle.borderRadius,
        border: cardStyle.borderSide.width > 0
            ? Border.all(
                color: cardStyle.borderSide.color,
                width: cardStyle.borderSide.width,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: (widget.shadowColor ?? cardStyle.shadowColor).withOpacity(0.1),
            blurRadius: widget.interactive ? _elevationAnimation.value : (widget.elevation ?? cardStyle.elevation),
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.child,
    );

    if (widget.interactive) {
      card = GestureDetector(
        onTap: widget.onTap,
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: card,
      );
    }

    return _wrapCard(card);
  }

  Widget _wrapCard(Widget card) {
    if (widget.testId != null) {
      card = Semantics(
        identifier: widget.testId,
        child: card,
      );
    }

    return card;
  }

  _CardStyle _getCardStyle(ThemeData theme, DesignTokens tokens) {
    final size = _getCardSize(tokens);

    switch (widget.variant) {
      case UICardVariant.defaultVariant:
        return _CardStyle(
          backgroundColor: theme.cardColor,
          shadowColor: theme.shadowColor,
          elevation: size.elevation,
          borderRadius: BorderRadius.circular(tokens.borderRadius.lg),
          borderSide: BorderSide.none,
          margin: size.margin,
          padding: size.padding,
        );

      case UICardVariant.outlined:
        return _CardStyle(
          backgroundColor: theme.cardColor,
          shadowColor: Colors.transparent,
          elevation: 0,
          borderRadius: BorderRadius.circular(tokens.borderRadius.lg),
          borderSide: BorderSide(
            color: widget.borderColor ?? theme.dividerColor,
            width: 2,
          ),
          margin: size.margin,
          padding: size.padding,
        );

      case UICardVariant.elevated:
        return _CardStyle(
          backgroundColor: theme.cardColor,
          shadowColor: theme.shadowColor,
          elevation: size.elevation + 4,
          borderRadius: BorderRadius.circular(tokens.borderRadius.lg),
          borderSide: BorderSide.none,
          margin: size.margin,
          padding: size.padding,
        );

      case UICardVariant.filled:
        return _CardStyle(
          backgroundColor: theme.colorScheme.surfaceVariant,
          shadowColor: Colors.transparent,
          elevation: 0,
          borderRadius: BorderRadius.circular(tokens.borderRadius.lg),
          borderSide: BorderSide.none,
          margin: size.margin,
          padding: size.padding,
        );
    }
  }

  _CardSize _getCardSize(DesignTokens tokens) {
    switch (widget.size) {
      case UISize.xs:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size2),
          margin: EdgeInsets.all(tokens.spacing.size1),
          elevation: 1,
        );
      case UISize.sm:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size3),
          margin: EdgeInsets.all(tokens.spacing.size1_5),
          elevation: 2,
        );
      case UISize.md:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size4),
          margin: EdgeInsets.all(tokens.spacing.size2),
          elevation: 2,
        );
      case UISize.lg:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size6),
          margin: EdgeInsets.all(tokens.spacing.size2_5),
          elevation: 3,
        );
      case UISize.xl:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size8),
          margin: EdgeInsets.all(tokens.spacing.size3),
          elevation: 4,
        );
      case UISize.xxl:
        return _CardSize(
          padding: EdgeInsets.all(tokens.spacing.size10),
          margin: EdgeInsets.all(tokens.spacing.size4),
          elevation: 4,
        );
    }
  }

  double _getDefaultElevation() {
    return _getCardSize(DesignTokens.of(context)).elevation;
  }
}

class _CardStyle {
  const _CardStyle({
    required this.backgroundColor,
    required this.shadowColor,
    required this.elevation,
    required this.borderRadius,
    required this.borderSide,
    required this.margin,
    required this.padding,
  });

  final Color backgroundColor;
  final Color shadowColor;
  final double elevation;
  final BorderRadius borderRadius;
  final BorderSide borderSide;
  final EdgeInsets margin;
  final EdgeInsets padding;
}

class _CardSize {
  const _CardSize({
    required this.padding,
    required this.margin,
    required this.elevation,
  });

  final EdgeInsets padding;
  final EdgeInsets margin;
  final double elevation;
}
