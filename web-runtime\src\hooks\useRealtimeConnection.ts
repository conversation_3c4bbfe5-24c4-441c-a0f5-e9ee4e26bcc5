import { useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useRuntimeStore } from '@stores/runtimeStore';
import { RealtimeUpdate } from '@types/index';

interface UseRealtimeConnectionOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export function useRealtimeConnection(options: UseRealtimeConnectionOptions = {}) {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 1000,
  } = options;

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);

  const {
    setConnected,
    handleRealtimeUpdate,
    metadata,
    user,
  } = useRuntimeStore();

  // Connect to WebSocket server
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8080';
    const token = localStorage.getItem('auth_token');

    socketRef.current = io(wsUrl, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true,
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('WebSocket connected');
      setConnected(true);
      reconnectCountRef.current = 0;

      // Join page room if metadata is available
      if (metadata?.pageId) {
        socket.emit('join-page', metadata.pageId);
      }

      // Send user info for presence
      if (user) {
        socket.emit('user-presence', {
          userId: user.id,
          username: user.username,
          pageId: metadata?.pageId,
        });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setConnected(false);

      // Attempt to reconnect if not manually disconnected
      if (reason !== 'io client disconnect' && reconnectCountRef.current < reconnectAttempts) {
        scheduleReconnect();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnected(false);
      scheduleReconnect();
    });

    // Real-time update events
    socket.on('ui-update', (update: RealtimeUpdate) => {
      console.log('Received UI update:', update);
      handleRealtimeUpdate(update);
    });

    socket.on('theme-update', (update: RealtimeUpdate) => {
      console.log('Received theme update:', update);
      handleRealtimeUpdate(update);
    });

    socket.on('data-update', (update: RealtimeUpdate) => {
      console.log('Received data update:', update);
      handleRealtimeUpdate(update);
    });

    socket.on('component-update', (update: RealtimeUpdate) => {
      console.log('Received component update:', update);
      handleRealtimeUpdate(update);
    });

    // Collaboration events
    socket.on('user-joined', (userData) => {
      console.log('User joined:', userData);
      // Handle user presence updates
    });

    socket.on('user-left', (userData) => {
      console.log('User left:', userData);
      // Handle user presence updates
    });

    socket.on('cursor-update', (cursorData) => {
      // Handle real-time cursor updates
      console.log('Cursor update:', cursorData);
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

  }, [setConnected, handleRealtimeUpdate, metadata?.pageId, user, reconnectAttempts]);

  // Disconnect from WebSocket server
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setConnected(false);
  }, [setConnected]);

  // Schedule reconnection attempt
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (reconnectCountRef.current >= reconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    const delay = reconnectDelay * Math.pow(2, reconnectCountRef.current);
    console.log(`Scheduling reconnection attempt ${reconnectCountRef.current + 1} in ${delay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectCountRef.current++;
      connect();
    }, delay);
  }, [connect, reconnectAttempts, reconnectDelay]);

  // Send message to server
  const sendMessage = useCallback((event: string, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Cannot send message: WebSocket not connected');
    }
  }, []);

  // Join a specific page room
  const joinPage = useCallback((pageId: string) => {
    sendMessage('join-page', pageId);
  }, [sendMessage]);

  // Leave a page room
  const leavePage = useCallback((pageId: string) => {
    sendMessage('leave-page', pageId);
  }, [sendMessage]);

  // Send cursor position
  const sendCursorPosition = useCallback((position: { x: number; y: number }) => {
    sendMessage('cursor-update', {
      position,
      pageId: metadata?.pageId,
      userId: user?.id,
      timestamp: Date.now(),
    });
  }, [sendMessage, metadata?.pageId, user?.id]);

  // Send user presence update
  const sendPresenceUpdate = useCallback((presence: any) => {
    sendMessage('user-presence', {
      ...presence,
      pageId: metadata?.pageId,
      userId: user?.id,
      timestamp: Date.now(),
    });
  }, [sendMessage, metadata?.pageId, user?.id]);

  // Initialize connection
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Handle page changes
  useEffect(() => {
    if (socketRef.current?.connected && metadata?.pageId) {
      joinPage(metadata.pageId);
    }
  }, [metadata?.pageId, joinPage]);

  // Handle user changes
  useEffect(() => {
    if (socketRef.current?.connected && user) {
      sendPresenceUpdate({
        userId: user.id,
        username: user.username,
      });
    }
  }, [user, sendPresenceUpdate]);

  return {
    connect,
    disconnect,
    sendMessage,
    joinPage,
    leavePage,
    sendCursorPosition,
    sendPresenceUpdate,
    isConnected: socketRef.current?.connected || false,
  };
}

// Hook for listening to specific real-time events
export function useRealtimeEvent(
  event: string,
  handler: (data: any) => void,
  dependencies: any[] = []
) {
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8080';
    const token = localStorage.getItem('auth_token');

    if (!socketRef.current) {
      socketRef.current = io(wsUrl, {
        auth: { token },
        transports: ['websocket', 'polling'],
      });
    }

    const socket = socketRef.current;
    socket.on(event, handler);

    return () => {
      socket.off(event, handler);
    };
  }, [event, handler, ...dependencies]);

  return socketRef.current;
}

// Hook for real-time cursor tracking
export function useCursorTracking() {
  const { sendCursorPosition } = useRealtimeConnection({ autoConnect: false });
  const throttleRef = useRef<NodeJS.Timeout | null>(null);

  const trackCursor = useCallback((event: MouseEvent) => {
    if (throttleRef.current) {
      clearTimeout(throttleRef.current);
    }

    throttleRef.current = setTimeout(() => {
      sendCursorPosition({
        x: event.clientX,
        y: event.clientY,
      });
    }, 100); // Throttle to 10fps
  }, [sendCursorPosition]);

  useEffect(() => {
    document.addEventListener('mousemove', trackCursor);
    return () => {
      document.removeEventListener('mousemove', trackCursor);
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
      }
    };
  }, [trackCursor]);
}
