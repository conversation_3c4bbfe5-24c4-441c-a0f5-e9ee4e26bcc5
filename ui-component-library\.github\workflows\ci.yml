name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  FLUTTER_VERSION: '3.16.0'

jobs:
  # Install dependencies and cache
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Generate cache key
        id: cache-key
        run: echo "key=node-modules-${{ hashFiles('**/package-lock.json') }}" >> $GITHUB_OUTPUT

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ steps.cache-key.outputs.key }}
          restore-keys: |
            node-modules-

      - name: Install dependencies
        run: npm ci

  # Lint and format check
  lint:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Lint
        run: npm run lint

      - name: Format check
        run: npm run format:check

  # Type checking
  typecheck:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Type check
        run: npm run type-check

  # Build design tokens
  build-tokens:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Build design tokens
        run: npm run tokens:build

      - name: Upload tokens artifacts
        uses: actions/upload-artifact@v3
        with:
          name: design-tokens
          path: packages/design-tokens/dist/

  # Test React components
  test-react:
    runs-on: ubuntu-latest
    needs: [setup, build-tokens]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Download tokens artifacts
        uses: actions/download-artifact@v3
        with:
          name: design-tokens
          path: packages/design-tokens/dist/

      - name: Test React components
        run: npm run react:test -- --coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: react-components

  # Test Flutter components
  test-flutter:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'

      - name: Flutter doctor
        run: flutter doctor -v

      - name: Get Flutter dependencies
        run: flutter pub get
        working-directory: packages/flutter-components

      - name: Analyze Flutter code
        run: flutter analyze
        working-directory: packages/flutter-components

      - name: Test Flutter components
        run: flutter test --coverage
        working-directory: packages/flutter-components

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/flutter-components/coverage/lcov.info
          flags: flutter-components

  # Build React components
  build-react:
    runs-on: ubuntu-latest
    needs: [setup, build-tokens, test-react]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Download tokens artifacts
        uses: actions/download-artifact@v3
        with:
          name: design-tokens
          path: packages/design-tokens/dist/

      - name: Build React components
        run: npm run react:build

      - name: Upload React build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: react-components
          path: packages/react-components/dist/

  # Build icons
  build-icons:
    runs-on: ubuntu-latest
    needs: [setup, build-tokens]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Generate icons
        run: npm run icons:generate

      - name: Build icons
        run: npm run icons:build

      - name: Upload icons artifacts
        uses: actions/upload-artifact@v3
        with:
          name: icons
          path: packages/icons/dist/

  # Build Storybook
  build-storybook:
    runs-on: ubuntu-latest
    needs: [setup, build-tokens, build-react, build-icons]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            packages/*/node_modules
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: design-tokens
          path: packages/design-tokens/dist/

      - name: Download React components
        uses: actions/download-artifact@v3
        with:
          name: react-components
          path: packages/react-components/dist/

      - name: Download icons
        uses: actions/download-artifact@v3
        with:
          name: icons
          path: packages/icons/dist/

      - name: Build Storybook
        run: npm run react:build-storybook

      - name: Upload Storybook artifacts
        uses: actions/upload-artifact@v3
        with:
          name: storybook
          path: dist/storybook/

  # Visual regression tests
  visual-tests:
    runs-on: ubuntu-latest
    needs: [build-storybook]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download Storybook
        uses: actions/download-artifact@v3
        with:
          name: storybook
          path: dist/storybook/

      - name: Run visual regression tests
        uses: chromaui/action@v1
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          storybookBuildDir: dist/storybook/

  # Accessibility tests
  a11y-tests:
    runs-on: ubuntu-latest
    needs: [build-storybook]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download Storybook
        uses: actions/download-artifact@v3
        with:
          name: storybook
          path: dist/storybook/

      - name: Install dependencies
        run: npm ci

      - name: Run accessibility tests
        run: npm run test:a11y

  # Security audit
  security:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run security audit
        run: npm audit --audit-level=high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Bundle size analysis
  bundle-size:
    runs-on: ubuntu-latest
    needs: [build-react, build-icons]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download React components
        uses: actions/download-artifact@v3
        with:
          name: react-components
          path: packages/react-components/dist/

      - name: Download icons
        uses: actions/download-artifact@v3
        with:
          name: icons
          path: packages/icons/dist/

      - name: Analyze bundle size
        uses: preactjs/compressed-size-action@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          pattern: 'packages/*/dist/**/*.{js,css}'
