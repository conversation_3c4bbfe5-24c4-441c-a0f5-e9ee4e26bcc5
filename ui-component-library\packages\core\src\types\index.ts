/**
 * Core type definitions for UI Builder Component Library
 */

// Base component props
export interface BaseComponentProps {
  id?: string;
  className?: string;
  style?: React.CSSProperties;
  'data-testid'?: string;
}

// Theme types
export interface ThemeConfig {
  colors: Record<string, string>;
  spacing: Record<string, number>;
  typography: Record<string, any>;
  breakpoints: Record<string, string>;
}

// Component size variants
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Component variants
export type ComponentVariant = 'primary' | 'secondary' | 'tertiary' | 'danger' | 'warning' | 'success';

// Layout types
export interface LayoutProps {
  direction?: 'row' | 'column';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: number | string;
  wrap?: boolean;
}

// Responsive types
export type ResponsiveValue<T> = T | {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
};

// Event handler types
export interface ComponentEventHandlers {
  onClick?: (event: React.MouseEvent) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

// Accessibility types
export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  role?: string;
  tabIndex?: number;
}

// Form types
export interface FormFieldProps {
  name: string;
  value?: any;
  defaultValue?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  helperText?: string;
}

// Animation types
export interface AnimationProps {
  duration?: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

// Component state types
export interface ComponentState {
  isLoading?: boolean;
  isDisabled?: boolean;
  isActive?: boolean;
  isSelected?: boolean;
  isHovered?: boolean;
  isFocused?: boolean;
}

// Design system types
export interface DesignSystemConfig {
  theme: ThemeConfig;
  components: Record<string, any>;
  tokens: Record<string, any>;
}

// Export all types
export type {
  BaseComponentProps,
  ThemeConfig,
  ComponentSize,
  ComponentVariant,
  LayoutProps,
  ResponsiveValue,
  ComponentEventHandlers,
  AccessibilityProps,
  FormFieldProps,
  AnimationProps,
  ComponentState,
  DesignSystemConfig,
};
