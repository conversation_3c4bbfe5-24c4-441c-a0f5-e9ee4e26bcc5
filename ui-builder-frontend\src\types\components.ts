// Component-related types
export interface UIComponent {
  id: string;
  type: ComponentType;
  name: string;
  displayName: string;
  properties: ComponentProperties;
  children?: UIComponent[];
  parentId?: string;
  position: Position;
  size: Size;
  styles: ComponentStyles;
  constraints?: ComponentConstraints;
  metadata?: ComponentMetadata;
}

export interface ComponentProperties {
  [key: string]: any;
  text?: string;
  placeholder?: string;
  value?: any;
  disabled?: boolean;
  required?: boolean;
  validation?: ValidationRule[];
  events?: ComponentEvent[];
}

export interface Position {
  x: number;
  y: number;
  z?: number;
}

export interface Size {
  width: number | 'auto' | string;
  height: number | 'auto' | string;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface ComponentStyles {
  backgroundColor?: string;
  color?: string;
  fontSize?: number | string;
  fontFamily?: string;
  fontWeight?: string | number;
  padding?: Spacing;
  margin?: Spacing;
  border?: BorderStyle;
  borderRadius?: number | string;
  boxShadow?: string;
  opacity?: number;
  transform?: string;
  transition?: string;
  [key: string]: any;
}

export interface Spacing {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
  all?: number;
}

export interface BorderStyle {
  width?: number;
  style?: 'solid' | 'dashed' | 'dotted' | 'none';
  color?: string;
}

export interface ComponentConstraints {
  resizable?: boolean;
  draggable?: boolean;
  deletable?: boolean;
  duplicatable?: boolean;
  lockAspectRatio?: boolean;
  snapToGrid?: boolean;
}

export interface ComponentMetadata {
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  version: number;
  tags?: string[];
  description?: string;
}

export enum ComponentType {
  // Layout Components
  CONTAINER = 'container',
  GRID = 'grid',
  FLEX = 'flex',
  STACK = 'stack',
  DIVIDER = 'divider',
  SPACER = 'spacer',
  
  // Text Components
  TEXT = 'text',
  HEADING = 'heading',
  PARAGRAPH = 'paragraph',
  LINK = 'link',
  
  // Form Components
  INPUT = 'input',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SWITCH = 'switch',
  SLIDER = 'slider',
  DATE_PICKER = 'datePicker',
  TIME_PICKER = 'timePicker',
  FILE_UPLOAD = 'fileUpload',
  
  // Interactive Components
  BUTTON = 'button',
  ICON_BUTTON = 'iconButton',
  DROPDOWN = 'dropdown',
  MENU = 'menu',
  TABS = 'tabs',
  ACCORDION = 'accordion',
  MODAL = 'modal',
  DRAWER = 'drawer',
  TOOLTIP = 'tooltip',
  POPOVER = 'popover',
  
  // Display Components
  IMAGE = 'image',
  ICON = 'icon',
  AVATAR = 'avatar',
  BADGE = 'badge',
  TAG = 'tag',
  PROGRESS = 'progress',
  SPINNER = 'spinner',
  SKELETON = 'skeleton',
  
  // Data Components
  TABLE = 'table',
  LIST = 'list',
  CARD = 'card',
  TIMELINE = 'timeline',
  TREE = 'tree',
  
  // Chart Components
  LINE_CHART = 'lineChart',
  BAR_CHART = 'barChart',
  PIE_CHART = 'pieChart',
  AREA_CHART = 'areaChart',
  
  // Media Components
  VIDEO = 'video',
  AUDIO = 'audio',
  IFRAME = 'iframe',
  
  // Custom Components
  CUSTOM = 'custom',
}

export interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'email' | 'url' | 'number' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean | Promise<boolean>;
}

export interface ComponentEvent {
  type: EventType;
  handler: string;
  parameters?: { [key: string]: any };
}

export enum EventType {
  CLICK = 'click',
  DOUBLE_CLICK = 'doubleClick',
  MOUSE_ENTER = 'mouseEnter',
  MOUSE_LEAVE = 'mouseLeave',
  FOCUS = 'focus',
  BLUR = 'blur',
  CHANGE = 'change',
  INPUT = 'input',
  SUBMIT = 'submit',
  RESET = 'reset',
  SCROLL = 'scroll',
  RESIZE = 'resize',
  LOAD = 'load',
  ERROR = 'error',
}

// Component Library Types
export interface ComponentLibrary {
  id: string;
  name: string;
  description: string;
  version: string;
  categories: ComponentCategory[];
  components: ComponentDefinition[];
  themes?: string[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ComponentCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  order: number;
  components: string[];
}

export interface ComponentDefinition {
  id: string;
  type: ComponentType;
  name: string;
  displayName: string;
  description: string;
  icon?: string;
  category: string;
  tags: string[];
  defaultProperties: ComponentProperties;
  defaultStyles: ComponentStyles;
  defaultSize: Size;
  propertySchema: PropertySchema;
  styleSchema: StyleSchema;
  constraints: ComponentConstraints;
  preview?: ComponentPreview;
  documentation?: ComponentDocumentation;
}

export interface PropertySchema {
  [key: string]: PropertyDefinition;
}

export interface PropertyDefinition {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum' | 'color' | 'image' | 'icon';
  label: string;
  description?: string;
  defaultValue?: any;
  required?: boolean;
  options?: { label: string; value: any }[];
  validation?: ValidationRule[];
  group?: string;
  order?: number;
  conditional?: ConditionalProperty;
}

export interface ConditionalProperty {
  property: string;
  value: any;
  operator?: 'equals' | 'notEquals' | 'contains' | 'notContains';
}

export interface StyleSchema {
  [key: string]: StyleDefinition;
}

export interface StyleDefinition {
  type: 'color' | 'size' | 'spacing' | 'border' | 'shadow' | 'typography' | 'layout' | 'animation';
  label: string;
  description?: string;
  defaultValue?: any;
  group?: string;
  order?: number;
  responsive?: boolean;
  states?: string[]; // hover, focus, active, etc.
}

export interface ComponentPreview {
  thumbnail: string;
  screenshots: string[];
  demo?: string;
}

export interface ComponentDocumentation {
  overview: string;
  usage: string;
  examples: ComponentExample[];
  api: ApiDocumentation;
  accessibility: AccessibilityInfo;
}

export interface ComponentExample {
  title: string;
  description: string;
  code: string;
  preview: string;
}

export interface ApiDocumentation {
  properties: PropertyDocumentation[];
  events: EventDocumentation[];
  methods: MethodDocumentation[];
}

export interface PropertyDocumentation {
  name: string;
  type: string;
  description: string;
  defaultValue?: any;
  required?: boolean;
}

export interface EventDocumentation {
  name: string;
  description: string;
  parameters: ParameterDocumentation[];
}

export interface ParameterDocumentation {
  name: string;
  type: string;
  description: string;
}

export interface MethodDocumentation {
  name: string;
  description: string;
  parameters: ParameterDocumentation[];
  returnType: string;
}

export interface AccessibilityInfo {
  ariaLabels: string[];
  keyboardNavigation: string;
  screenReaderSupport: string;
  colorContrast: string;
  focusManagement: string;
}
