import React, { useMemo, useCallback } from 'react';
import { LayoutConfig, ComponentInstance, DeviceInfo } from '../../types/component';
import { DynamicRenderer } from '../DynamicRenderer/DynamicRenderer';
import './LayoutRenderer.scss';

export interface LayoutRendererProps {
  layout: LayoutConfig;
  components: ComponentInstance[];
  deviceInfo?: DeviceInfo;
  className?: string;
  style?: React.CSSProperties;
  onComponentMount?: (componentId: string) => void;
  onComponentUnmount?: (componentId: string) => void;
  debugMode?: boolean;
}

export const LayoutRenderer: React.FC<LayoutRendererProps> = ({
  layout,
  components,
  deviceInfo,
  className,
  style,
  onComponentMount,
  onComponentUnmount,
  debugMode = false
}) => {
  // Get responsive layout configuration
  const responsiveLayout = useMemo(() => {
    if (!layout.responsive || !deviceInfo) {
      return layout;
    }

    const breakpoint = getBreakpoint(deviceInfo);
    const responsiveConfig = layout.responsive[breakpoint];
    
    return responsiveConfig ? { ...layout, ...responsiveConfig } : layout;
  }, [layout, deviceInfo]);

  // Generate layout styles
  const layoutStyles = useMemo(() => {
    const styles: React.CSSProperties = { ...style };

    switch (responsiveLayout.type) {
      case 'flex':
        styles.display = 'flex';
        styles.flexDirection = responsiveLayout.direction || 'row';
        styles.flexWrap = responsiveLayout.wrap ? 'wrap' : 'nowrap';
        styles.justifyContent = getJustifyContent(responsiveLayout.justify);
        styles.alignItems = getAlignItems(responsiveLayout.align);
        styles.gap = responsiveLayout.gap;
        break;

      case 'grid':
        styles.display = 'grid';
        styles.gridTemplateColumns = getGridColumns(responsiveLayout.columns);
        styles.gridTemplateRows = getGridRows(responsiveLayout.rows);
        styles.gap = responsiveLayout.gap;
        
        if (responsiveLayout.areas) {
          styles.gridTemplateAreas = responsiveLayout.areas
            .map(row => `"${row.join(' ')}"`)
            .join(' ');
        }
        break;

      case 'absolute':
        styles.position = 'relative';
        break;

      case 'flow':
      default:
        styles.display = 'block';
        break;
    }

    return styles;
  }, [responsiveLayout, style]);

  // Get layout class names
  const layoutClassName = useMemo(() => {
    const classes = ['layout-renderer'];
    
    classes.push(`layout-${responsiveLayout.type}`);
    
    if (responsiveLayout.direction) {
      classes.push(`layout-direction-${responsiveLayout.direction}`);
    }
    
    if (responsiveLayout.wrap) {
      classes.push('layout-wrap');
    }
    
    if (className) {
      classes.push(className);
    }
    
    if (debugMode) {
      classes.push('layout-debug');
    }

    return classes.join(' ');
  }, [responsiveLayout, className, debugMode]);

  // Render component with layout-specific positioning
  const renderComponent = useCallback((component: ComponentInstance, index: number) => {
    const componentStyles: React.CSSProperties = {};
    
    // Apply layout-specific styles
    if (responsiveLayout.type === 'absolute' && component.layout) {
      componentStyles.position = component.layout.position || 'absolute';
      componentStyles.left = component.layout.x;
      componentStyles.top = component.layout.y;
      componentStyles.width = component.layout.width;
      componentStyles.height = component.layout.height;
      componentStyles.zIndex = component.layout.zIndex;
    }
    
    // Apply grid area if specified
    if (responsiveLayout.type === 'grid' && component.layout?.gridArea) {
      componentStyles.gridArea = component.layout.gridArea;
    }
    
    // Apply flex properties
    if (responsiveLayout.type === 'flex' && component.layout) {
      componentStyles.flex = component.layout.flex;
      componentStyles.flexGrow = component.layout.flexGrow;
      componentStyles.flexShrink = component.layout.flexShrink;
      componentStyles.flexBasis = component.layout.flexBasis;
      componentStyles.alignSelf = component.layout.alignSelf;
    }

    return (
      <div
        key={component.id}
        className={`layout-item layout-item-${responsiveLayout.type}`}
        style={componentStyles}
        data-component-id={component.id}
        data-component-type={component.type}
      >
        <DynamicRenderer
          config={component}
          deviceInfo={deviceInfo}
          onComponentMount={onComponentMount}
          onComponentUnmount={onComponentUnmount}
          debugMode={debugMode}
        />
      </div>
    );
  }, [responsiveLayout, deviceInfo, onComponentMount, onComponentUnmount, debugMode]);

  // Sort components for absolute positioning
  const sortedComponents = useMemo(() => {
    if (responsiveLayout.type === 'absolute') {
      return [...components].sort((a, b) => {
        const aZ = a.layout?.zIndex || 0;
        const bZ = b.layout?.zIndex || 0;
        return aZ - bZ;
      });
    }
    return components;
  }, [components, responsiveLayout.type]);

  return (
    <div
      className={layoutClassName}
      style={layoutStyles}
      data-layout-type={responsiveLayout.type}
    >
      {debugMode && (
        <div className="layout-debug-info">
          <div className="debug-label">Layout: {responsiveLayout.type}</div>
          <div className="debug-details">
            {responsiveLayout.type === 'flex' && (
              <>
                <span>Direction: {responsiveLayout.direction}</span>
                <span>Justify: {responsiveLayout.justify}</span>
                <span>Align: {responsiveLayout.align}</span>
              </>
            )}
            {responsiveLayout.type === 'grid' && (
              <>
                <span>Columns: {responsiveLayout.columns}</span>
                <span>Rows: {responsiveLayout.rows}</span>
              </>
            )}
          </div>
        </div>
      )}
      
      {sortedComponents.map(renderComponent)}
    </div>
  );
};

// Utility functions
function getBreakpoint(deviceInfo: DeviceInfo): string {
  const { width, category } = deviceInfo;
  
  if (category === 'mobile' || width < 768) return 'mobile';
  if (category === 'tablet' || width < 1024) return 'tablet';
  return 'desktop';
}

function getJustifyContent(justify?: string): React.CSSProperties['justifyContent'] {
  switch (justify) {
    case 'start': return 'flex-start';
    case 'center': return 'center';
    case 'end': return 'flex-end';
    case 'space-between': return 'space-between';
    case 'space-around': return 'space-around';
    case 'space-evenly': return 'space-evenly';
    default: return 'flex-start';
  }
}

function getAlignItems(align?: string): React.CSSProperties['alignItems'] {
  switch (align) {
    case 'start': return 'flex-start';
    case 'center': return 'center';
    case 'end': return 'flex-end';
    case 'stretch': return 'stretch';
    case 'baseline': return 'baseline';
    default: return 'stretch';
  }
}

function getGridColumns(columns?: number | string): string {
  if (typeof columns === 'number') {
    return `repeat(${columns}, 1fr)`;
  }
  return columns || 'none';
}

function getGridRows(rows?: number | string): string {
  if (typeof rows === 'number') {
    return `repeat(${rows}, auto)`;
  }
  return rows || 'none';
}

// Layout presets
export const LayoutPresets = {
  // Flex layouts
  flexRow: {
    type: 'flex' as const,
    direction: 'row' as const,
    justify: 'start' as const,
    align: 'stretch' as const,
    gap: '1rem'
  },
  
  flexColumn: {
    type: 'flex' as const,
    direction: 'column' as const,
    justify: 'start' as const,
    align: 'stretch' as const,
    gap: '1rem'
  },
  
  flexCenter: {
    type: 'flex' as const,
    direction: 'column' as const,
    justify: 'center' as const,
    align: 'center' as const,
    gap: '1rem'
  },
  
  flexSpaceBetween: {
    type: 'flex' as const,
    direction: 'row' as const,
    justify: 'space-between' as const,
    align: 'center' as const,
    gap: '1rem'
  },
  
  // Grid layouts
  grid2Column: {
    type: 'grid' as const,
    columns: 2,
    gap: '1rem'
  },
  
  grid3Column: {
    type: 'grid' as const,
    columns: 3,
    gap: '1rem'
  },
  
  grid4Column: {
    type: 'grid' as const,
    columns: 4,
    gap: '1rem'
  },
  
  gridAutoFit: {
    type: 'grid' as const,
    columns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '1rem'
  },
  
  // Common page layouts
  headerContentFooter: {
    type: 'grid' as const,
    rows: 'auto 1fr auto',
    areas: [
      ['header'],
      ['content'],
      ['footer']
    ]
  },
  
  sidebarContent: {
    type: 'grid' as const,
    columns: '250px 1fr',
    areas: [
      ['sidebar', 'content']
    ],
    responsive: {
      mobile: {
        columns: '1fr',
        areas: [
          ['content'],
          ['sidebar']
        ]
      }
    }
  },
  
  dashboardLayout: {
    type: 'grid' as const,
    columns: '250px 1fr',
    rows: 'auto 1fr',
    areas: [
      ['sidebar', 'header'],
      ['sidebar', 'content']
    ],
    responsive: {
      mobile: {
        columns: '1fr',
        rows: 'auto auto 1fr',
        areas: [
          ['header'],
          ['sidebar'],
          ['content']
        ]
      }
    }
  }
};

// Layout utilities
export const LayoutUtils = {
  // Create responsive layout configuration
  createResponsiveLayout: (
    baseLayout: LayoutConfig,
    breakpoints: Record<string, Partial<LayoutConfig>>
  ): LayoutConfig => ({
    ...baseLayout,
    responsive: breakpoints
  }),
  
  // Merge layout configurations
  mergeLayouts: (base: LayoutConfig, override: Partial<LayoutConfig>): LayoutConfig => ({
    ...base,
    ...override,
    responsive: {
      ...base.responsive,
      ...override.responsive
    }
  }),
  
  // Validate layout configuration
  validateLayout: (layout: LayoutConfig): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!layout.type) {
      errors.push('Layout type is required');
    }
    
    if (layout.type === 'grid') {
      if (!layout.columns && !layout.rows) {
        errors.push('Grid layout requires columns or rows');
      }
    }
    
    if (layout.type === 'flex') {
      if (layout.direction && !['row', 'column'].includes(layout.direction)) {
        errors.push('Invalid flex direction');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};
