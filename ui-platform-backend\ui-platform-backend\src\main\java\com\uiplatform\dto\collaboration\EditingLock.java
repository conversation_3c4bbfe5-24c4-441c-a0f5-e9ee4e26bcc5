package com.uiplatform.dto.collaboration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO representing an editing lock on a UI element.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EditingLock {
    
    private UUID userId;
    private String username;
    private UUID configId;
    private String elementId;
    private String elementType;
    private String lockType; // "exclusive", "shared", "read-only"
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime acquiredAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime expiresAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime releasedAt;
    
    private boolean autoRelease;
    private String sessionId;
    
    // Constructors
    public EditingLock() {}
    
    public EditingLock(UUID userId, String username, UUID configId, String elementId) {
        this.userId = userId;
        this.username = username;
        this.configId = configId;
        this.elementId = elementId;
        this.acquiredAt = LocalDateTime.now();
        this.expiresAt = LocalDateTime.now().plusMinutes(5); // Default 5 minute expiry
        this.lockType = "exclusive";
        this.autoRelease = true;
    }
    
    // Getters and Setters
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public UUID getConfigId() {
        return configId;
    }
    
    public void setConfigId(UUID configId) {
        this.configId = configId;
    }
    
    public String getElementId() {
        return elementId;
    }
    
    public void setElementId(String elementId) {
        this.elementId = elementId;
    }
    
    public String getElementType() {
        return elementType;
    }
    
    public void setElementType(String elementType) {
        this.elementType = elementType;
    }
    
    public String getLockType() {
        return lockType;
    }
    
    public void setLockType(String lockType) {
        this.lockType = lockType;
    }
    
    public LocalDateTime getAcquiredAt() {
        return acquiredAt;
    }
    
    public void setAcquiredAt(LocalDateTime acquiredAt) {
        this.acquiredAt = acquiredAt;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public LocalDateTime getReleasedAt() {
        return releasedAt;
    }
    
    public void setReleasedAt(LocalDateTime releasedAt) {
        this.releasedAt = releasedAt;
    }
    
    public boolean isAutoRelease() {
        return autoRelease;
    }
    
    public void setAutoRelease(boolean autoRelease) {
        this.autoRelease = autoRelease;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    /**
     * Check if the lock is currently active (not expired and not released).
     */
    public boolean isActive() {
        return releasedAt == null && 
               (expiresAt == null || expiresAt.isAfter(LocalDateTime.now()));
    }
    
    /**
     * Check if the lock has expired.
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }
    
    @Override
    public String toString() {
        return "EditingLock{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", configId=" + configId +
                ", elementId='" + elementId + '\'' +
                ", lockType='" + lockType + '\'' +
                ", acquiredAt=" + acquiredAt +
                ", expiresAt=" + expiresAt +
                ", releasedAt=" + releasedAt +
                '}';
    }
}
