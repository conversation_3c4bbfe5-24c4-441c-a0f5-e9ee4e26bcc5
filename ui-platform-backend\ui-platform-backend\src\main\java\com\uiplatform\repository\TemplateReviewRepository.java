package com.uiplatform.repository;

import com.uiplatform.entity.TemplateReview;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for TemplateReview entity.
 * Provides CRUD operations and custom queries for template review management.
 */
@Repository
public interface TemplateReviewRepository extends JpaRepository<TemplateReview, UUID>, JpaSpecificationExecutor<TemplateReview> {

    /**
     * Find reviews by template.
     */
    List<TemplateReview> findByTemplateIdAndDeletedFalse(UUID templateId);

    /**
     * Find reviews by template with pagination.
     */
    Page<TemplateReview> findByTemplateIdAndDeletedFalse(UUID templateId, Pageable pageable);

    /**
     * Find approved reviews by template.
     */
    List<TemplateReview> findByTemplateIdAndIsApprovedTrueAndDeletedFalse(UUID templateId);

    /**
     * Find approved reviews by template with pagination.
     */
    Page<TemplateReview> findByTemplateIdAndIsApprovedTrueAndDeletedFalse(UUID templateId, Pageable pageable);

    /**
     * Find reviews by reviewer.
     */
    List<TemplateReview> findByReviewerIdAndDeletedFalse(UUID reviewerId);

    /**
     * Find reviews by reviewer with pagination.
     */
    Page<TemplateReview> findByReviewerIdAndDeletedFalse(UUID reviewerId, Pageable pageable);

    /**
     * Find review by template and reviewer.
     */
    Optional<TemplateReview> findByTemplateIdAndReviewerIdAndDeletedFalse(UUID templateId, UUID reviewerId);

    /**
     * Find reviews by rating.
     */
    List<TemplateReview> findByRatingAndDeletedFalse(Integer rating);

    /**
     * Find reviews by rating with pagination.
     */
    Page<TemplateReview> findByRatingAndDeletedFalse(Integer rating, Pageable pageable);

    /**
     * Find reviews by rating range.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.rating BETWEEN :minRating AND :maxRating AND r.deleted = false")
    Page<TemplateReview> findByRatingRange(@Param("minRating") Integer minRating, 
                                          @Param("maxRating") Integer maxRating, 
                                          Pageable pageable);

    /**
     * Find verified purchase reviews.
     */
    List<TemplateReview> findByIsVerifiedPurchaseTrueAndDeletedFalse();

    /**
     * Find verified purchase reviews with pagination.
     */
    Page<TemplateReview> findByIsVerifiedPurchaseTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find verified purchase reviews by template.
     */
    List<TemplateReview> findByTemplateIdAndIsVerifiedPurchaseTrueAndDeletedFalse(UUID templateId);

    /**
     * Find verified purchase reviews by template with pagination.
     */
    Page<TemplateReview> findByTemplateIdAndIsVerifiedPurchaseTrueAndDeletedFalse(UUID templateId, Pageable pageable);

    /**
     * Find reported reviews.
     */
    List<TemplateReview> findByIsReportedTrueAndDeletedFalse();

    /**
     * Find reported reviews with pagination.
     */
    Page<TemplateReview> findByIsReportedTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find pending approval reviews.
     */
    List<TemplateReview> findByIsApprovedFalseAndDeletedFalse();

    /**
     * Find pending approval reviews with pagination.
     */
    Page<TemplateReview> findByIsApprovedFalseAndDeletedFalse(Pageable pageable);

    /**
     * Search reviews by title or comment.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.template.id = :templateId AND " +
           "(LOWER(r.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.comment) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "r.isApproved = true AND r.deleted = false")
    Page<TemplateReview> searchReviews(@Param("templateId") UUID templateId, 
                                      @Param("searchTerm") String searchTerm, 
                                      Pageable pageable);

    /**
     * Find reviews by multiple criteria.
     */
    @Query("SELECT r FROM TemplateReview r WHERE " +
           "(:templateId IS NULL OR r.template.id = :templateId) AND " +
           "(:reviewerId IS NULL OR r.reviewer.id = :reviewerId) AND " +
           "(:rating IS NULL OR r.rating = :rating) AND " +
           "(:isVerifiedPurchase IS NULL OR r.isVerifiedPurchase = :isVerifiedPurchase) AND " +
           "(:isApproved IS NULL OR r.isApproved = :isApproved) AND " +
           "(:isReported IS NULL OR r.isReported = :isReported) AND " +
           "r.deleted = false")
    Page<TemplateReview> findByCriteria(@Param("templateId") UUID templateId,
                                       @Param("reviewerId") UUID reviewerId,
                                       @Param("rating") Integer rating,
                                       @Param("isVerifiedPurchase") Boolean isVerifiedPurchase,
                                       @Param("isApproved") Boolean isApproved,
                                       @Param("isReported") Boolean isReported,
                                       Pageable pageable);

    /**
     * Count reviews by template.
     */
    @Query("SELECT COUNT(r) FROM TemplateReview r WHERE r.template.id = :templateId AND r.deleted = false")
    Long countByTemplateId(@Param("templateId") UUID templateId);

    /**
     * Count approved reviews by template.
     */
    @Query("SELECT COUNT(r) FROM TemplateReview r WHERE r.template.id = :templateId AND r.isApproved = true AND r.deleted = false")
    Long countApprovedByTemplateId(@Param("templateId") UUID templateId);

    /**
     * Count reviews by reviewer.
     */
    @Query("SELECT COUNT(r) FROM TemplateReview r WHERE r.reviewer.id = :reviewerId AND r.deleted = false")
    Long countByReviewerId(@Param("reviewerId") UUID reviewerId);

    /**
     * Calculate average rating for template.
     */
    @Query("SELECT AVG(r.rating) FROM TemplateReview r WHERE r.template.id = :templateId AND r.isApproved = true AND r.deleted = false")
    BigDecimal calculateAverageRating(@Param("templateId") UUID templateId);

    /**
     * Count reviews by rating for template.
     */
    @Query("SELECT r.rating, COUNT(r) FROM TemplateReview r WHERE r.template.id = :templateId AND r.isApproved = true AND r.deleted = false GROUP BY r.rating ORDER BY r.rating DESC")
    List<Object[]> countByRatingForTemplate(@Param("templateId") UUID templateId);

    /**
     * Find most helpful reviews.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.template.id = :templateId AND r.isApproved = true AND r.deleted = false ORDER BY r.helpfulCount DESC")
    List<TemplateReview> findMostHelpful(@Param("templateId") UUID templateId, Pageable pageable);

    /**
     * Find recent reviews.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.template.id = :templateId AND r.isApproved = true AND r.deleted = false ORDER BY r.createdAt DESC")
    List<TemplateReview> findRecent(@Param("templateId") UUID templateId, Pageable pageable);

    /**
     * Check if user has already reviewed template.
     */
    @Query("SELECT COUNT(r) > 0 FROM TemplateReview r WHERE r.template.id = :templateId AND r.reviewer.id = :reviewerId AND r.deleted = false")
    boolean hasUserReviewedTemplate(@Param("templateId") UUID templateId, @Param("reviewerId") UUID reviewerId);

    /**
     * Increment helpful count.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.helpfulCount = r.helpfulCount + 1 WHERE r.id = :id")
    void incrementHelpfulCount(@Param("id") UUID id);

    /**
     * Decrement helpful count.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.helpfulCount = CASE WHEN r.helpfulCount > 0 THEN r.helpfulCount - 1 ELSE 0 END WHERE r.id = :id")
    void decrementHelpfulCount(@Param("id") UUID id);

    /**
     * Approve review.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.isApproved = true WHERE r.id = :id")
    void approveReview(@Param("id") UUID id);

    /**
     * Reject review.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.isApproved = false WHERE r.id = :id")
    void rejectReview(@Param("id") UUID id);

    /**
     * Report review.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.isReported = true WHERE r.id = :id")
    void reportReview(@Param("id") UUID id);

    /**
     * Unreport review.
     */
    @Modifying
    @Query("UPDATE TemplateReview r SET r.isReported = false WHERE r.id = :id")
    void unreportReview(@Param("id") UUID id);

    /**
     * Find reviews with comments.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.template.id = :templateId AND r.comment IS NOT NULL AND r.comment != '' AND r.isApproved = true AND r.deleted = false")
    Page<TemplateReview> findWithComments(@Param("templateId") UUID templateId, Pageable pageable);

    /**
     * Find reviews without comments.
     */
    @Query("SELECT r FROM TemplateReview r WHERE r.template.id = :templateId AND (r.comment IS NULL OR r.comment = '') AND r.isApproved = true AND r.deleted = false")
    Page<TemplateReview> findWithoutComments(@Param("templateId") UUID templateId, Pageable pageable);
}
