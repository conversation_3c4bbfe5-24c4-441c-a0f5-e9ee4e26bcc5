import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DynamicRenderer } from './DynamicRenderer';
import { ComponentRegistry } from '../../utils/ComponentRegistry';

// Mock the ComponentRegistry
jest.mock('../../utils/ComponentRegistry', () => ({
  ComponentRegistry: {
    getComponent: jest.fn(),
    registerComponent: jest.fn(),
    hasComponent: jest.fn()
  }
}));

// Mock components for testing
const MockButton = ({ text, onClick, ...props }: any) => (
  <button onClick={onClick} {...props} data-testid="mock-button">
    {text}
  </button>
);

const MockText = ({ content, variant, ...props }: any) => (
  <span {...props} data-testid="mock-text" className={`text-${variant}`}>
    {content}
  </span>
);

const MockContainer = ({ children, padding, ...props }: any) => (
  <div {...props} data-testid="mock-container" style={{ padding }}>
    {children}
  </div>
);

describe('DynamicRenderer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mock component registry
    (ComponentRegistry.getComponent as jest.Mock).mockImplementation((type: string) => {
      switch (type) {
        case 'button':
          return MockButton;
        case 'text':
          return MockText;
        case 'container':
          return MockContainer;
        default:
          return null;
      }
    });

    (ComponentRegistry.hasComponent as jest.Mock).mockImplementation((type: string) => {
      return ['button', 'text', 'container'].includes(type);
    });
  });

  it('renders a simple component', () => {
    const config = {
      id: 'button-1',
      type: 'button',
      props: {
        text: 'Click me',
        variant: 'primary'
      }
    };

    render(<DynamicRenderer config={config} />);

    const button = screen.getByTestId('mock-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Click me');
  });

  it('renders a component with children', () => {
    const config = {
      id: 'container-1',
      type: 'container',
      props: {
        padding: '20px'
      },
      children: [
        {
          id: 'text-1',
          type: 'text',
          props: {
            content: 'Hello World',
            variant: 'h1'
          }
        },
        {
          id: 'button-1',
          type: 'button',
          props: {
            text: 'Click me'
          }
        }
      ]
    };

    render(<DynamicRenderer config={config} />);

    const container = screen.getByTestId('mock-container');
    const text = screen.getByTestId('mock-text');
    const button = screen.getByTestId('mock-button');

    expect(container).toBeInTheDocument();
    expect(text).toBeInTheDocument();
    expect(button).toBeInTheDocument();
    expect(text).toHaveTextContent('Hello World');
    expect(text).toHaveClass('text-h1');
    expect(button).toHaveTextContent('Click me');
  });

  it('renders nested components correctly', () => {
    const config = {
      id: 'root-container',
      type: 'container',
      props: {
        padding: '10px'
      },
      children: [
        {
          id: 'nested-container',
          type: 'container',
          props: {
            padding: '5px'
          },
          children: [
            {
              id: 'nested-text',
              type: 'text',
              props: {
                content: 'Nested content',
                variant: 'body'
              }
            }
          ]
        }
      ]
    };

    render(<DynamicRenderer config={config} />);

    const containers = screen.getAllByTestId('mock-container');
    const text = screen.getByTestId('mock-text');

    expect(containers).toHaveLength(2);
    expect(text).toBeInTheDocument();
    expect(text).toHaveTextContent('Nested content');
  });

  it('handles unknown component types gracefully', () => {
    const config = {
      id: 'unknown-1',
      type: 'unknown-component',
      props: {
        someProp: 'value'
      }
    };

    (ComponentRegistry.hasComponent as jest.Mock).mockReturnValue(false);
    (ComponentRegistry.getComponent as jest.Mock).mockReturnValue(null);

    render(<DynamicRenderer config={config} />);

    const fallback = screen.getByTestId('unknown-component-fallback');
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveTextContent('Unknown component: unknown-component');
  });

  it('handles component rendering errors gracefully', () => {
    const ErrorComponent = () => {
      throw new Error('Component render error');
    };

    (ComponentRegistry.getComponent as jest.Mock).mockReturnValue(ErrorComponent);

    const config = {
      id: 'error-component',
      type: 'error-component',
      props: {}
    };

    // Mock console.error to avoid test output noise
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<DynamicRenderer config={config} />);

    const errorBoundary = screen.getByTestId('component-error-boundary');
    expect(errorBoundary).toBeInTheDocument();
    expect(errorBoundary).toHaveTextContent('Error rendering component');

    consoleSpy.mockRestore();
  });

  it('passes event handlers correctly', () => {
    const mockOnClick = jest.fn();
    
    const config = {
      id: 'button-1',
      type: 'button',
      props: {
        text: 'Click me',
        onClick: mockOnClick
      }
    };

    render(<DynamicRenderer config={config} />);

    const button = screen.getByTestId('mock-button');
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('handles conditional rendering', () => {
    const config = {
      id: 'conditional-text',
      type: 'text',
      props: {
        content: 'Conditional content',
        variant: 'body'
      },
      conditions: {
        visible: true
      }
    };

    const { rerender } = render(<DynamicRenderer config={config} />);

    expect(screen.getByTestId('mock-text')).toBeInTheDocument();

    // Update config to hide component
    const hiddenConfig = {
      ...config,
      conditions: {
        visible: false
      }
    };

    rerender(<DynamicRenderer config={hiddenConfig} />);

    expect(screen.queryByTestId('mock-text')).not.toBeInTheDocument();
  });

  it('applies custom styles correctly', () => {
    const config = {
      id: 'styled-text',
      type: 'text',
      props: {
        content: 'Styled text',
        variant: 'body',
        style: {
          color: 'red',
          fontSize: '18px'
        }
      }
    };

    render(<DynamicRenderer config={config} />);

    const text = screen.getByTestId('mock-text');
    expect(text).toHaveStyle({
      color: 'red',
      fontSize: '18px'
    });
  });

  it('handles dynamic data binding', async () => {
    const config = {
      id: 'dynamic-text',
      type: 'text',
      props: {
        content: '{{user.name}}',
        variant: 'body'
      }
    };

    const context = {
      user: {
        name: 'John Doe'
      }
    };

    render(<DynamicRenderer config={config} context={context} />);

    await waitFor(() => {
      const text = screen.getByTestId('mock-text');
      expect(text).toHaveTextContent('John Doe');
    });
  });

  it('handles array rendering with loops', () => {
    const config = {
      id: 'list-container',
      type: 'container',
      props: {
        padding: '10px'
      },
      children: [
        {
          id: 'item-template',
          type: 'text',
          props: {
            content: '{{item.name}}',
            variant: 'body'
          },
          loop: {
            data: '{{items}}',
            variable: 'item'
          }
        }
      ]
    };

    const context = {
      items: [
        { name: 'Item 1' },
        { name: 'Item 2' },
        { name: 'Item 3' }
      ]
    };

    render(<DynamicRenderer config={config} context={context} />);

    const texts = screen.getAllByTestId('mock-text');
    expect(texts).toHaveLength(3);
    expect(texts[0]).toHaveTextContent('Item 1');
    expect(texts[1]).toHaveTextContent('Item 2');
    expect(texts[2]).toHaveTextContent('Item 3');
  });

  it('handles component updates correctly', () => {
    const initialConfig = {
      id: 'text-1',
      type: 'text',
      props: {
        content: 'Initial content',
        variant: 'body'
      }
    };

    const { rerender } = render(<DynamicRenderer config={initialConfig} />);

    expect(screen.getByTestId('mock-text')).toHaveTextContent('Initial content');

    const updatedConfig = {
      ...initialConfig,
      props: {
        ...initialConfig.props,
        content: 'Updated content'
      }
    };

    rerender(<DynamicRenderer config={updatedConfig} />);

    expect(screen.getByTestId('mock-text')).toHaveTextContent('Updated content');
  });

  it('handles empty configuration gracefully', () => {
    render(<DynamicRenderer config={null} />);

    const emptyState = screen.getByTestId('empty-configuration');
    expect(emptyState).toBeInTheDocument();
    expect(emptyState).toHaveTextContent('No configuration provided');
  });

  it('handles malformed configuration gracefully', () => {
    const malformedConfig = {
      // Missing required fields
      props: {
        content: 'Test'
      }
    };

    render(<DynamicRenderer config={malformedConfig} />);

    const errorState = screen.getByTestId('malformed-configuration');
    expect(errorState).toBeInTheDocument();
    expect(errorState).toHaveTextContent('Invalid configuration');
  });

  it('supports custom component props validation', () => {
    const config = {
      id: 'validated-button',
      type: 'button',
      props: {
        text: '', // Invalid: empty text
        variant: 'invalid-variant' // Invalid variant
      }
    };

    render(<DynamicRenderer config={config} validateProps={true} />);

    const validationError = screen.getByTestId('validation-error');
    expect(validationError).toBeInTheDocument();
    expect(validationError).toHaveTextContent('Component validation failed');
  });

  it('handles performance optimization with memoization', () => {
    const config = {
      id: 'memoized-text',
      type: 'text',
      props: {
        content: 'Memoized content',
        variant: 'body'
      }
    };

    const { rerender } = render(<DynamicRenderer config={config} />);

    // Re-render with same config should not re-create component
    rerender(<DynamicRenderer config={config} />);

    expect(screen.getByTestId('mock-text')).toBeInTheDocument();
    expect(ComponentRegistry.getComponent).toHaveBeenCalledTimes(1);
  });
});
