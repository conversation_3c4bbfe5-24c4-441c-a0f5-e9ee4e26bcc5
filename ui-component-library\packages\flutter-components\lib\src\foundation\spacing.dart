import 'package:flutter/material.dart';

/// UI Builder spacing system
class UIBuilderSpacing {
  const UIBuilderSpacing._();

  // Base spacing unit (4px)
  static const double baseUnit = 4.0;

  // Spacing scale
  static const double space0 = 0;
  static const double space0_5 = baseUnit * 0.5; // 2px
  static const double space1 = baseUnit * 1; // 4px
  static const double space1_5 = baseUnit * 1.5; // 6px
  static const double space2 = baseUnit * 2; // 8px
  static const double space2_5 = baseUnit * 2.5; // 10px
  static const double space3 = baseUnit * 3; // 12px
  static const double space3_5 = baseUnit * 3.5; // 14px
  static const double space4 = baseUnit * 4; // 16px
  static const double space5 = baseUnit * 5; // 20px
  static const double space6 = baseUnit * 6; // 24px
  static const double space7 = baseUnit * 7; // 28px
  static const double space8 = baseUnit * 8; // 32px
  static const double space9 = baseUnit * 9; // 36px
  static const double space10 = baseUnit * 10; // 40px
  static const double space11 = baseUnit * 11; // 44px
  static const double space12 = baseUnit * 12; // 48px
  static const double space14 = baseUnit * 14; // 56px
  static const double space16 = baseUnit * 16; // 64px
  static const double space20 = baseUnit * 20; // 80px
  static const double space24 = baseUnit * 24; // 96px
  static const double space28 = baseUnit * 28; // 112px
  static const double space32 = baseUnit * 32; // 128px
  static const double space36 = baseUnit * 36; // 144px
  static const double space40 = baseUnit * 40; // 160px
  static const double space44 = baseUnit * 44; // 176px
  static const double space48 = baseUnit * 48; // 192px
  static const double space52 = baseUnit * 52; // 208px
  static const double space56 = baseUnit * 56; // 224px
  static const double space60 = baseUnit * 60; // 240px
  static const double space64 = baseUnit * 64; // 256px
  static const double space72 = baseUnit * 72; // 288px
  static const double space80 = baseUnit * 80; // 320px
  static const double space96 = baseUnit * 96; // 384px

  /// Get spacing value by name
  static double getSpacing(String spacingName) {
    switch (spacingName.toLowerCase()) {
      case '0': return space0;
      case '0.5': case 'xs': return space0_5;
      case '1': case 'sm': return space1;
      case '1.5': return space1_5;
      case '2': case 'md': return space2;
      case '2.5': return space2_5;
      case '3': case 'lg': return space3;
      case '3.5': return space3_5;
      case '4': case 'xl': return space4;
      case '5': return space5;
      case '6': case 'xxl': return space6;
      case '7': return space7;
      case '8': return space8;
      case '9': return space9;
      case '10': return space10;
      case '11': return space11;
      case '12': return space12;
      case '14': return space14;
      case '16': return space16;
      case '20': return space20;
      case '24': return space24;
      case '28': return space28;
      case '32': return space32;
      case '36': return space36;
      case '40': return space40;
      case '44': return space44;
      case '48': return space48;
      case '52': return space52;
      case '56': return space56;
      case '60': return space60;
      case '64': return space64;
      case '72': return space72;
      case '80': return space80;
      case '96': return space96;
      default: return space4; // Default to 16px
    }
  }

  /// Create EdgeInsets with uniform spacing
  static EdgeInsets all(double value) => EdgeInsets.all(value);

  /// Create EdgeInsets with horizontal and vertical spacing
  static EdgeInsets symmetric({double horizontal = 0, double vertical = 0}) =>
      EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);

  /// Create EdgeInsets with individual spacing values
  static EdgeInsets only({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) =>
      EdgeInsets.only(left: left, top: top, right: right, bottom: bottom);

  /// Create EdgeInsets from LTRB values
  static EdgeInsets fromLTRB(double left, double top, double right, double bottom) =>
      EdgeInsets.fromLTRB(left, top, right, bottom);

  /// Common padding presets
  static EdgeInsets get paddingXS => all(space1);
  static EdgeInsets get paddingSM => all(space2);
  static EdgeInsets get paddingMD => all(space4);
  static EdgeInsets get paddingLG => all(space6);
  static EdgeInsets get paddingXL => all(space8);
  static EdgeInsets get paddingXXL => all(space12);

  /// Common margin presets
  static EdgeInsets get marginXS => all(space1);
  static EdgeInsets get marginSM => all(space2);
  static EdgeInsets get marginMD => all(space4);
  static EdgeInsets get marginLG => all(space6);
  static EdgeInsets get marginXL => all(space8);
  static EdgeInsets get marginXXL => all(space12);

  /// Responsive spacing based on screen size
  static double getResponsiveSpacing(BuildContext context, {
    double mobile = space4,
    double tablet = space6,
    double desktop = space8,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 768) {
      return mobile;
    } else if (screenWidth < 1024) {
      return tablet;
    } else {
      return desktop;
    }
  }

  /// Create responsive EdgeInsets
  static EdgeInsets getResponsivePadding(BuildContext context, {
    EdgeInsets mobile = const EdgeInsets.all(space4),
    EdgeInsets tablet = const EdgeInsets.all(space6),
    EdgeInsets desktop = const EdgeInsets.all(space8),
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 768) {
      return mobile;
    } else if (screenWidth < 1024) {
      return tablet;
    } else {
      return desktop;
    }
  }

  /// Gap widgets for Flex layouts
  static Widget get gapXS => SizedBox(width: space1, height: space1);
  static Widget get gapSM => SizedBox(width: space2, height: space2);
  static Widget get gapMD => SizedBox(width: space4, height: space4);
  static Widget get gapLG => SizedBox(width: space6, height: space6);
  static Widget get gapXL => SizedBox(width: space8, height: space8);
  static Widget get gapXXL => SizedBox(width: space12, height: space12);

  /// Create custom gap widget
  static Widget gap(double size) => SizedBox(width: size, height: size);

  /// Create horizontal gap widget
  static Widget hGap(double width) => SizedBox(width: width);

  /// Create vertical gap widget
  static Widget vGap(double height) => SizedBox(height: height);
}
