import 'package:flutter/material.dart';

/// Design tokens for the UI Builder component library
/// Provides consistent spacing, colors, typography, and other design values
class DesignTokens {
  const DesignTokens._();

  static const DesignTokens instance = DesignTokens._();

  /// Spacing tokens
  SpacingTokens get spacing => const SpacingTokens();

  /// Color tokens
  ColorTokens get colors => const ColorTokens();

  /// Typography tokens
  TypographyTokens get typography => const TypographyTokens();

  /// Border radius tokens
  BorderRadiusTokens get borderRadius => const BorderRadiusTokens();

  /// Shadow tokens
  ShadowTokens get shadows => const ShadowTokens();
}

/// Spacing design tokens
class SpacingTokens {
  const SpacingTokens();

  double get size0_5 => 2.0;
  double get size1 => 4.0;
  double get size1_5 => 6.0;
  double get size2 => 8.0;
  double get size2_5 => 10.0;
  double get size3 => 12.0;
  double get size3_5 => 14.0;
  double get size4 => 16.0;
  double get size5 => 20.0;
  double get size6 => 24.0;
  double get size7 => 28.0;
  double get size8 => 32.0;
  double get size9 => 36.0;
  double get size10 => 40.0;
  double get size11 => 44.0;
  double get size12 => 48.0;
  double get size14 => 56.0;
  double get size16 => 64.0;
  double get size20 => 80.0;
  double get size24 => 96.0;
  double get size28 => 112.0;
  double get size32 => 128.0;
}

/// Color design tokens
class ColorTokens {
  const ColorTokens();

  // Primary colors
  Color get primary50 => const Color(0xFFF0F9FF);
  Color get primary100 => const Color(0xFFE0F2FE);
  Color get primary500 => const Color(0xFF0EA5E9);
  Color get primary600 => const Color(0xFF0284C7);
  Color get primary700 => const Color(0xFF0369A1);

  // Gray colors
  Color get gray50 => const Color(0xFFF9FAFB);
  Color get gray100 => const Color(0xFFF3F4F6);
  Color get gray200 => const Color(0xFFE5E7EB);
  Color get gray300 => const Color(0xFFD1D5DB);
  Color get gray400 => const Color(0xFF9CA3AF);
  Color get gray500 => const Color(0xFF6B7280);
  Color get gray600 => const Color(0xFF4B5563);
  Color get gray700 => const Color(0xFF374151);
  Color get gray800 => const Color(0xFF1F2937);
  Color get gray900 => const Color(0xFF111827);

  // Semantic colors
  Color get success => const Color(0xFF10B981);
  Color get warning => const Color(0xFFF59E0B);
  Color get error => const Color(0xFFEF4444);
  Color get info => const Color(0xFF3B82F6);
}

/// Typography design tokens
class TypographyTokens {
  const TypographyTokens();

  TextStyle get displayLarge => const TextStyle(
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
  );

  TextStyle get headlineLarge => const TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
  );

  TextStyle get titleLarge => const TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.27,
  );

  TextStyle get bodyLarge => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );

  TextStyle get bodyMedium => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
  );

  TextStyle get labelLarge => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
  );
}

/// Border radius design tokens
class BorderRadiusTokens {
  const BorderRadiusTokens();

  BorderRadius get none => BorderRadius.zero;
  BorderRadius get sm => BorderRadius.circular(2);
  BorderRadius get md => BorderRadius.circular(6);
  BorderRadius get lg => BorderRadius.circular(8);
  BorderRadius get xl => BorderRadius.circular(12);
  BorderRadius get xxl => BorderRadius.circular(16);
  BorderRadius get full => BorderRadius.circular(9999);
}

/// Shadow design tokens
class ShadowTokens {
  const ShadowTokens();

  List<BoxShadow> get sm => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 2,
      offset: const Offset(0, 1),
    ),
  ];

  List<BoxShadow> get md => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 6,
      offset: const Offset(0, 4),
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.06),
      blurRadius: 2,
      offset: const Offset(0, 2),
    ),
  ];

  List<BoxShadow> get lg => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 15,
      offset: const Offset(0, 10),
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.04),
      blurRadius: 6,
      offset: const Offset(0, 4),
    ),
  ];
}
