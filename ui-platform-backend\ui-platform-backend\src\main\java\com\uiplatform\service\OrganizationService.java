package com.uiplatform.service;

import com.uiplatform.dto.OrganizationDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.exception.DuplicateResourceException;
import com.uiplatform.mapper.OrganizationMapper;
import com.uiplatform.repository.OrganizationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for Organization management.
 * Handles business logic for organization operations.
 */
@Service
@Transactional
public class OrganizationService {

    private static final Logger logger = LoggerFactory.getLogger(OrganizationService.class);

    private final OrganizationRepository organizationRepository;
    private final OrganizationMapper organizationMapper;

    @Autowired
    public OrganizationService(OrganizationRepository organizationRepository,
                              OrganizationMapper organizationMapper) {
        this.organizationRepository = organizationRepository;
        this.organizationMapper = organizationMapper;
    }

    /**
     * Create a new organization.
     */
    public OrganizationDTO createOrganization(OrganizationDTO.CreateDTO createDTO) {
        logger.info("Creating new organization with slug: {}", createDTO.getSlug());

        // Check if slug already exists
        if (organizationRepository.findBySlugAndDeletedFalse(createDTO.getSlug()).isPresent()) {
            throw new DuplicateResourceException("Organization with slug '" + createDTO.getSlug() + "' already exists");
        }

        // Check if domain already exists (if provided)
        if (createDTO.getDomain() != null && 
            organizationRepository.findByDomainAndDeletedFalse(createDTO.getDomain()).isPresent()) {
            throw new DuplicateResourceException("Organization with domain '" + createDTO.getDomain() + "' already exists");
        }

        Organization organization = organizationMapper.toEntity(createDTO);
        organization = organizationRepository.save(organization);

        logger.info("Successfully created organization with ID: {}", organization.getId());
        return organizationMapper.toDTO(organization);
    }

    /**
     * Get organization by ID.
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "organizations", key = "#id")
    public OrganizationDTO getOrganizationById(UUID id) {
        logger.debug("Fetching organization by ID: {}", id);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        return organizationMapper.toDTO(organization);
    }

    /**
     * Get organization by slug.
     */
    @Transactional(readOnly = true)
    public OrganizationDTO getOrganizationBySlug(String slug) {
        logger.debug("Fetching organization by slug: {}", slug);

        Organization organization = organizationRepository.findBySlugAndDeletedFalse(slug)
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with slug: " + slug));

        return organizationMapper.toDTO(organization);
    }

    /**
     * Update organization.
     */
    @CachePut(value = "organizations", key = "#id")
    public OrganizationDTO updateOrganization(UUID id, OrganizationDTO.UpdateDTO updateDTO) {
        logger.info("Updating organization with ID: {}", id);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        // Check domain uniqueness if being updated
        if (updateDTO.getDomain() != null && !updateDTO.getDomain().equals(organization.getDomain())) {
            if (organizationRepository.existsByDomainAndIdNot(updateDTO.getDomain(), id)) {
                throw new DuplicateResourceException("Organization with domain '" + updateDTO.getDomain() + "' already exists");
            }
        }

        organizationMapper.updateEntityFromDTO(updateDTO, organization);
        organization = organizationRepository.save(organization);

        logger.info("Successfully updated organization with ID: {}", id);
        return organizationMapper.toDTO(organization);
    }

    /**
     * Delete organization (soft delete).
     */
    @CacheEvict(value = "organizations", key = "#id")
    public void deleteOrganization(UUID id) {
        logger.info("Deleting organization with ID: {}", id);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        organization.markAsDeleted("SYSTEM"); // TODO: Get current user
        organizationRepository.save(organization);

        logger.info("Successfully deleted organization with ID: {}", id);
    }

    /**
     * Get all organizations with pagination.
     */
    @Transactional(readOnly = true)
    public Page<OrganizationDTO> getAllOrganizations(Pageable pageable) {
        logger.debug("Fetching all organizations with pagination");

        Page<Organization> organizations = organizationRepository.findAll(pageable);
        return organizations.map(organizationMapper::toDTO);
    }

    /**
     * Search organizations by criteria.
     */
    @Transactional(readOnly = true)
    public Page<OrganizationDTO> searchOrganizations(String name, 
                                                    Organization.OrganizationStatus status,
                                                    Organization.SubscriptionPlan plan,
                                                    Pageable pageable) {
        logger.debug("Searching organizations with criteria - name: {}, status: {}, plan: {}", name, status, plan);

        Page<Organization> organizations = organizationRepository.searchOrganizations(name, status, plan, pageable);
        return organizations.map(organizationMapper::toDTO);
    }

    /**
     * Get organizations by subscription plan.
     */
    @Transactional(readOnly = true)
    public List<OrganizationDTO> getOrganizationsBySubscriptionPlan(Organization.SubscriptionPlan plan) {
        logger.debug("Fetching organizations by subscription plan: {}", plan);

        List<Organization> organizations = organizationRepository.findBySubscriptionPlanAndDeletedFalse(plan);
        return organizations.stream()
                .map(organizationMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get organizations by status.
     */
    @Transactional(readOnly = true)
    public List<OrganizationDTO> getOrganizationsByStatus(Organization.OrganizationStatus status) {
        logger.debug("Fetching organizations by status: {}", status);

        List<Organization> organizations = organizationRepository.findByStatusAndDeletedFalse(status);
        return organizations.stream()
                .map(organizationMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Update organization subscription plan.
     */
    public OrganizationDTO updateSubscriptionPlan(UUID id, Organization.SubscriptionPlan newPlan) {
        logger.info("Updating subscription plan for organization ID: {} to {}", id, newPlan);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        Organization.SubscriptionPlan oldPlan = organization.getSubscriptionPlan();
        organization.setSubscriptionPlan(newPlan);

        // Update limits based on subscription plan
        updateLimitsBasedOnPlan(organization, newPlan);

        organization = organizationRepository.save(organization);

        logger.info("Successfully updated subscription plan for organization ID: {} from {} to {}", 
                   id, oldPlan, newPlan);
        return organizationMapper.toDTO(organization);
    }

    /**
     * Update organization status.
     */
    public OrganizationDTO updateStatus(UUID id, Organization.OrganizationStatus newStatus) {
        logger.info("Updating status for organization ID: {} to {}", id, newStatus);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        Organization.OrganizationStatus oldStatus = organization.getStatus();
        organization.setStatus(newStatus);
        organization = organizationRepository.save(organization);

        logger.info("Successfully updated status for organization ID: {} from {} to {}", 
                   id, oldStatus, newStatus);
        return organizationMapper.toDTO(organization);
    }

    /**
     * Check if organization exists by slug.
     */
    @Transactional(readOnly = true)
    public boolean existsBySlug(String slug) {
        return organizationRepository.findBySlugAndDeletedFalse(slug).isPresent();
    }

    /**
     * Check if organization exists by domain.
     */
    @Transactional(readOnly = true)
    public boolean existsByDomain(String domain) {
        return organizationRepository.findByDomainAndDeletedFalse(domain).isPresent();
    }

    /**
     * Get organization statistics.
     */
    @Transactional(readOnly = true)
    public OrganizationDTO getOrganizationWithStats(UUID id) {
        logger.debug("Fetching organization with statistics for ID: {}", id);

        Organization organization = organizationRepository.findById(id)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + id));

        OrganizationDTO dto = organizationMapper.toDTO(organization);

        // TODO: Add statistics when user repository methods are available
        // dto.setUserCount(userRepository.countByOrganizationId(id));
        // dto.setProjectCount(projectRepository.countByOrganizationId(id));
        // dto.setTemplateCount(templateRepository.countByOrganizationId(id));

        return dto;
    }

    /**
     * Update limits based on subscription plan.
     */
    private void updateLimitsBasedOnPlan(Organization organization, Organization.SubscriptionPlan plan) {
        switch (plan) {
            case FREE:
                organization.setMaxUsers(10);
                organization.setMaxProjects(5);
                organization.setMaxStorageMb(1000L);
                break;
            case BASIC:
                organization.setMaxUsers(50);
                organization.setMaxProjects(25);
                organization.setMaxStorageMb(10000L);
                break;
            case PROFESSIONAL:
                organization.setMaxUsers(200);
                organization.setMaxProjects(100);
                organization.setMaxStorageMb(50000L);
                break;
            case ENTERPRISE:
                organization.setMaxUsers(1000);
                organization.setMaxProjects(500);
                organization.setMaxStorageMb(200000L);
                break;
        }
    }
}
