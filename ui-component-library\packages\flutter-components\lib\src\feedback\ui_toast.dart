import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Toast component
class UIToast extends StatelessWidget {
  const UIToast({
    super.key,
    required this.message,
    this.title,
    this.variant = UIColorVariant.info,
    this.showIcon = true,
    this.action,
    this.onDismiss,
  });

  final String message;
  final String? title;
  final UIColorVariant variant;
  final bool showIcon;
  final Widget? action;
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final backgroundColor = variant.getColor(colorScheme);
    final textColor = _getContrastColor(backgroundColor);

    return Material(
      elevation: 4,
      borderRadius: tokens.borderRadius.lg,
      color: backgroundColor,
      child: Container(
        padding: EdgeInsets.all(tokens.spacing.size4),
        child: Row(
          children: [
            if (showIcon) ...[
              Icon(
                _getVariantIcon(),
                color: textColor,
                size: 20,
              ),
              SizedBox(width: tokens.spacing.size3),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null) ...[
                    Text(
                      title!,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: tokens.spacing.size1),
                  ],
                  Text(
                    message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: textColor,
                    ),
                  ),
                ],
              ),
            ),
            if (action != null) ...[
              SizedBox(width: tokens.spacing.size3),
              action!,
            ],
            if (onDismiss != null) ...[
              SizedBox(width: tokens.spacing.size2),
              IconButton(
                onPressed: onDismiss,
                icon: Icon(Icons.close, color: textColor, size: 18),
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(minWidth: 24, minHeight: 24),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  IconData _getVariantIcon() {
    switch (variant) {
      case UIColorVariant.success:
        return Icons.check_circle;
      case UIColorVariant.warning:
        return Icons.warning;
      case UIColorVariant.error:
        return Icons.error;
      case UIColorVariant.info:
      default:
        return Icons.info;
    }
  }
}
