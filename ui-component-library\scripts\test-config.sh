#!/bin/bash

# UI Component Library Configuration Test Script
# Tests all configuration files and build processes

set -e

echo "🔍 Starting UI Component Library Configuration Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo ""
echo "📋 Phase 1: Configuration File Validation"
echo "=========================================="

# Test 1.1: Check TypeScript configurations
echo "Testing TypeScript configurations..."

# Check root tsconfig.base.json
if [ -f "tsconfig.base.json" ]; then
    npx tsc --noEmit --project tsconfig.base.json 2>/dev/null
    print_result $? "Root TypeScript configuration"
else
    print_result 1 "Root TypeScript configuration (file missing)"
fi

# Check package TypeScript configurations
for package in packages/*/; do
    package_name=$(basename "$package")
    
    if [ -f "$package/tsconfig.json" ] || [ -f "$package/tsconfig.lib.json" ]; then
        echo "  Testing $package_name TypeScript config..."
        
        # Try to compile TypeScript in package
        cd "$package"
        if [ -f "tsconfig.json" ]; then
            npx tsc --noEmit --project tsconfig.json 2>/dev/null
            result=$?
        elif [ -f "tsconfig.lib.json" ]; then
            npx tsc --noEmit --project tsconfig.lib.json 2>/dev/null
            result=$?
        fi
        cd - > /dev/null
        
        print_result $result "$package_name TypeScript compilation"
    else
        print_warning "$package_name missing TypeScript configuration"
    fi
done

echo ""
echo "📋 Phase 2: Build System Validation"
echo "==================================="

# Test 2.1: Nx workspace validation
echo "Testing Nx workspace configuration..."
npx nx list 2>/dev/null >/dev/null
print_result $? "Nx workspace configuration"

# Test 2.2: Check if all projects can be built
echo "Testing project builds..."

# Get list of buildable projects
buildable_projects=$(npx nx show projects --with-target=build 2>/dev/null || echo "")

if [ -n "$buildable_projects" ]; then
    for project in $buildable_projects; do
        echo "  Testing $project build..."
        npx nx build $project --dry-run 2>/dev/null >/dev/null
        print_result $? "$project build configuration"
    done
else
    print_warning "No buildable projects found"
fi

echo ""
echo "📋 Phase 3: Package Dependencies"
echo "==============================="

# Test 3.1: Check package.json files
echo "Testing package.json files..."

# Check root package.json
if [ -f "package.json" ]; then
    npm ls --depth=0 2>/dev/null >/dev/null
    print_result $? "Root package dependencies"
else
    print_result 1 "Root package.json (file missing)"
fi

# Check package dependencies
for package in packages/*/; do
    package_name=$(basename "$package")
    
    if [ -f "$package/package.json" ]; then
        echo "  Testing $package_name dependencies..."
        cd "$package"
        npm ls --depth=0 2>/dev/null >/dev/null
        result=$?
        cd - > /dev/null
        print_result $result "$package_name dependencies"
    else
        print_warning "$package_name missing package.json"
    fi
done

echo ""
echo "📋 Phase 4: Design Tokens"
echo "========================"

# Test 4.1: Design tokens build
echo "Testing design tokens build..."
if [ -d "packages/design-tokens" ]; then
    cd packages/design-tokens
    if [ -f "config.js" ]; then
        # Test if style-dictionary can run
        npx style-dictionary build --config config.js 2>/dev/null >/dev/null
        result=$?
        cd - > /dev/null
        print_result $result "Design tokens build"
    else
        cd - > /dev/null
        print_result 1 "Design tokens configuration (config.js missing)"
    fi
else
    print_result 1 "Design tokens package (directory missing)"
fi

echo ""
echo "📋 Phase 5: Storybook Configuration"
echo "=================================="

# Test 5.1: Storybook configuration
echo "Testing Storybook configuration..."
if [ -d "packages/react-components/.storybook" ]; then
    cd packages/react-components
    npx storybook build --dry-run 2>/dev/null >/dev/null
    result=$?
    cd - > /dev/null
    print_result $result "Storybook configuration"
else
    print_warning "Storybook configuration not found"
fi

echo ""
echo "📋 Phase 6: Flutter Configuration"
echo "================================"

# Test 6.1: Flutter package
echo "Testing Flutter package..."
if [ -d "packages/flutter-components" ]; then
    cd packages/flutter-components
    if command -v flutter &> /dev/null; then
        flutter pub get 2>/dev/null >/dev/null
        result=$?
        print_result $result "Flutter dependencies"
        
        flutter analyze --no-pub 2>/dev/null >/dev/null
        result=$?
        print_result $result "Flutter analysis"
    else
        print_warning "Flutter not installed - skipping Flutter tests"
    fi
    cd - > /dev/null
else
    print_result 1 "Flutter package (directory missing)"
fi

echo ""
echo "📊 Test Summary"
echo "==============="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Configuration is valid.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please fix the issues above.${NC}"
    exit 1
fi
