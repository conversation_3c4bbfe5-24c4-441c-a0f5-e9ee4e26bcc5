import axios from 'axios';
import { UIMetadata, ThemeConfiguration, UserInfo } from '@types/index';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API functions
export async function fetchUIMetadata(
  pageId: string,
  params?: Record<string, any>
): Promise<UIMetadata> {
  const response = await api.get(`/ui-metadata/${pageId}`, { params });
  return response.data;
}

export async function fetchTheme(themeId: string): Promise<ThemeConfiguration> {
  const response = await api.get(`/themes/${themeId}`);
  return response.data;
}

export async function fetchUserInfo(): Promise<UserInfo> {
  const response = await api.get('/auth/me');
  return response.data;
}

export async function fetchData(
  source: string,
  params?: Record<string, any>
): Promise<any> {
  const response = await api.get(`/data/${source}`, { params });
  return response.data;
}

export async function submitForm(
  formId: string,
  data: Record<string, any>
): Promise<any> {
  const response = await api.post(`/forms/${formId}/submit`, data);
  return response.data;
}

export async function updateData(
  source: string,
  id: string,
  data: Record<string, any>
): Promise<any> {
  const response = await api.put(`/data/${source}/${id}`, data);
  return response.data;
}

export async function deleteData(source: string, id: string): Promise<void> {
  await api.delete(`/data/${source}/${id}`);
}

export async function uploadFile(file: File, path?: string): Promise<{ url: string }> {
  const formData = new FormData();
  formData.append('file', file);
  if (path) {
    formData.append('path', path);
  }

  const response = await api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
}

// Authentication functions
export async function login(credentials: {
  email: string;
  password: string;
}): Promise<{ token: string; user: UserInfo }> {
  const response = await api.post('/auth/login', credentials);
  const { token, user } = response.data;
  
  localStorage.setItem('auth_token', token);
  return { token, user };
}

export async function logout(): Promise<void> {
  try {
    await api.post('/auth/logout');
  } finally {
    localStorage.removeItem('auth_token');
  }
}

export async function refreshToken(): Promise<string> {
  const response = await api.post('/auth/refresh');
  const { token } = response.data;
  
  localStorage.setItem('auth_token', token);
  return token;
}

// Cache utilities
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

export async function fetchWithCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 300000 // 5 minutes default
): Promise<T> {
  const cached = cache.get(key);
  const now = Date.now();

  if (cached && now - cached.timestamp < cached.ttl) {
    return cached.data;
  }

  const data = await fetcher();
  cache.set(key, { data, timestamp: now, ttl });
  return data;
}

export function clearCache(key?: string): void {
  if (key) {
    cache.delete(key);
  } else {
    cache.clear();
  }
}

// Error handling utilities
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export function handleAPIError(error: any): APIError {
  if (error.response) {
    const { status, data } = error.response;
    return new APIError(
      data.message || 'An error occurred',
      status,
      data.code,
      data.details
    );
  } else if (error.request) {
    return new APIError('Network error', 0, 'NETWORK_ERROR');
  } else {
    return new APIError(error.message || 'Unknown error', 0, 'UNKNOWN_ERROR');
  }
}

// Retry utility
export async function retryRequest<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        throw lastError;
      }

      // Don't retry on client errors (4xx)
      if (error instanceof APIError && error.status >= 400 && error.status < 500) {
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError!;
}

// Batch request utility
export async function batchRequests<T>(
  requests: (() => Promise<T>)[],
  batchSize: number = 5
): Promise<T[]> {
  const results: T[] = [];
  
  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(request => request()));
    results.push(...batchResults);
  }
  
  return results;
}

export default api;
