import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// Layout Management System for Flutter UI Runtime
/// 
/// Provides dynamic layout creation, responsive behavior,
/// and adaptive layouts based on screen size and orientation.
class LayoutManager {
  static final LayoutManager _instance = LayoutManager._internal();
  factory LayoutManager() => _instance;
  LayoutManager._internal();

  /// Build layout widget from configuration
  Widget buildLayout(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    switch (config.type) {
      case 'column':
        return _buildColumn(context, config, children);
      case 'row':
        return _buildRow(context, config, children);
      case 'stack':
        return _buildStack(context, config, children);
      case 'grid':
        return _buildGrid(context, config, children);
      case 'wrap':
        return _buildWrap(context, config, children);
      case 'flex':
        return _buildFlex(context, config, children);
      case 'expanded':
        return _buildExpanded(context, config, children);
      case 'positioned':
        return _buildPositioned(context, config, children);
      case 'container':
        return _buildContainer(context, config, children);
      case 'sizedBox':
        return _buildSizedBox(context, config, children);
      case 'padding':
        return _buildPadding(context, config, children);
      case 'center':
        return _buildCenter(context, config, children);
      case 'align':
        return _buildAlign(context, config, children);
      case 'scrollable':
        return _buildScrollable(context, config, children);
      case 'listView':
        return _buildListView(context, config, children);
      case 'gridView':
        return _buildGridView(context, config, children);
      case 'pageView':
        return _buildPageView(context, config, children);
      case 'tabView':
        return _buildTabView(context, config, children);
      case 'responsive':
        return _buildResponsive(context, config, children);
      default:
        return _buildColumn(context, config, children);
    }
  }

  /// Build Column layout
  Widget _buildColumn(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    return Column(
      mainAxisAlignment: _parseMainAxisAlignment(config.mainAxisAlignment),
      crossAxisAlignment: _parseCrossAxisAlignment(config.crossAxisAlignment),
      mainAxisSize: _parseMainAxisSize(config.mainAxisSize),
      children: children,
    );
  }

  /// Build Row layout
  Widget _buildRow(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    return Row(
      mainAxisAlignment: _parseMainAxisAlignment(config.mainAxisAlignment),
      crossAxisAlignment: _parseCrossAxisAlignment(config.crossAxisAlignment),
      mainAxisSize: _parseMainAxisSize(config.mainAxisSize),
      children: children,
    );
  }

  /// Build Stack layout
  Widget _buildStack(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final alignment = _parseAlignment(config.properties?['alignment']);
    final fit = _parseStackFit(config.properties?['fit']);
    final clipBehavior = _parseClipBehavior(config.properties?['clipBehavior']);

    return Stack(
      alignment: alignment,
      fit: fit,
      clipBehavior: clipBehavior,
      children: children,
    );
  }

  /// Build Grid layout
  Widget _buildGrid(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final crossAxisCount = config.properties?['crossAxisCount'] as int? ?? 2;
    final mainAxisSpacing = config.properties?['mainAxisSpacing'] as double? ?? 0.0;
    final crossAxisSpacing = config.properties?['crossAxisSpacing'] as double? ?? 0.0;
    final childAspectRatio = config.properties?['childAspectRatio'] as double? ?? 1.0;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      childAspectRatio: childAspectRatio,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: children,
    );
  }

  /// Build Wrap layout
  Widget _buildWrap(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final direction = _parseAxis(config.properties?['direction']);
    final alignment = _parseWrapAlignment(config.properties?['alignment']);
    final spacing = config.properties?['spacing'] as double? ?? 0.0;
    final runSpacing = config.properties?['runSpacing'] as double? ?? 0.0;

    return Wrap(
      direction: direction,
      alignment: alignment,
      spacing: spacing,
      runSpacing: runSpacing,
      children: children,
    );
  }

  /// Build Flex layout
  Widget _buildFlex(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final direction = _parseAxis(config.properties?['direction']);
    final mainAxisAlignment = _parseMainAxisAlignment(config.mainAxisAlignment);
    final crossAxisAlignment = _parseCrossAxisAlignment(config.crossAxisAlignment);

    return Flex(
      direction: direction,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }

  /// Build Expanded layout
  Widget _buildExpanded(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final flex = config.properties?['flex'] as int? ?? 1;
    final child = children.isNotEmpty ? children.first : const SizedBox();

    return Expanded(
      flex: flex,
      child: child,
    );
  }

  /// Build Positioned layout
  Widget _buildPositioned(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final top = config.properties?['top'] as double?;
    final right = config.properties?['right'] as double?;
    final bottom = config.properties?['bottom'] as double?;
    final left = config.properties?['left'] as double?;
    final width = config.properties?['width'] as double?;
    final height = config.properties?['height'] as double?;
    final child = children.isNotEmpty ? children.first : const SizedBox();

    return Positioned(
      top: top,
      right: right,
      bottom: bottom,
      left: left,
      width: width,
      height: height,
      child: child,
    );
  }

  /// Build Container layout
  Widget _buildContainer(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final width = config.properties?['width'] as double?;
    final height = config.properties?['height'] as double?;
    final color = _parseColor(config.properties?['color']);
    final decoration = _parseDecoration(config.properties?['decoration']);
    final alignment = _parseAlignment(config.properties?['alignment']);
    final transform = _parseMatrix4(config.properties?['transform']);
    final clipBehavior = _parseClipBehavior(config.properties?['clipBehavior']);

    final child = children.isNotEmpty ? children.first : null;

    return Container(
      width: width,
      height: height,
      padding: _parseEdgeInsets(config.padding),
      margin: _parseEdgeInsets(config.margin),
      color: decoration == null ? color : null,
      decoration: decoration,
      alignment: alignment,
      transform: transform,
      clipBehavior: clipBehavior,
      child: child,
    );
  }

  /// Build SizedBox layout
  Widget _buildSizedBox(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final width = config.properties?['width'] as double?;
    final height = config.properties?['height'] as double?;
    final child = children.isNotEmpty ? children.first : null;

    return SizedBox(
      width: width,
      height: height,
      child: child,
    );
  }

  /// Build Padding layout
  Widget _buildPadding(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final padding = _parseEdgeInsets(config.padding) ?? EdgeInsets.zero;
    final child = children.isNotEmpty ? children.first : const SizedBox();

    return Padding(
      padding: padding,
      child: child,
    );
  }

  /// Build Center layout
  Widget _buildCenter(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final widthFactor = config.properties?['widthFactor'] as double?;
    final heightFactor = config.properties?['heightFactor'] as double?;
    final child = children.isNotEmpty ? children.first : const SizedBox();

    return Center(
      widthFactor: widthFactor,
      heightFactor: heightFactor,
      child: child,
    );
  }

  /// Build Align layout
  Widget _buildAlign(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final alignment = _parseAlignment(config.properties?['alignment']);
    final widthFactor = config.properties?['widthFactor'] as double?;
    final heightFactor = config.properties?['heightFactor'] as double?;
    final child = children.isNotEmpty ? children.first : const SizedBox();

    return Align(
      alignment: alignment,
      widthFactor: widthFactor,
      heightFactor: heightFactor,
      child: child,
    );
  }

  /// Build Scrollable layout
  Widget _buildScrollable(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final scrollDirection = _parseAxis(config.properties?['scrollDirection']);
    final reverse = config.properties?['reverse'] as bool? ?? false;
    final primary = config.properties?['primary'] as bool?;
    final shrinkWrap = config.properties?['shrinkWrap'] as bool? ?? false;

    return SingleChildScrollView(
      scrollDirection: scrollDirection,
      reverse: reverse,
      primary: primary,
      padding: _parseEdgeInsets(config.padding),
      child: children.isNotEmpty ? children.first : const SizedBox(),
    );
  }

  /// Build ListView layout
  Widget _buildListView(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final scrollDirection = _parseAxis(config.properties?['scrollDirection']);
    final reverse = config.properties?['reverse'] as bool? ?? false;
    final primary = config.properties?['primary'] as bool?;
    final shrinkWrap = config.properties?['shrinkWrap'] as bool? ?? false;
    final itemExtent = config.properties?['itemExtent'] as double?;

    return ListView(
      scrollDirection: scrollDirection,
      reverse: reverse,
      primary: primary,
      shrinkWrap: shrinkWrap,
      padding: _parseEdgeInsets(config.padding),
      itemExtent: itemExtent,
      children: children,
    );
  }

  /// Build GridView layout
  Widget _buildGridView(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final crossAxisCount = config.properties?['crossAxisCount'] as int? ?? 2;
    final mainAxisSpacing = config.properties?['mainAxisSpacing'] as double? ?? 0.0;
    final crossAxisSpacing = config.properties?['crossAxisSpacing'] as double? ?? 0.0;
    final childAspectRatio = config.properties?['childAspectRatio'] as double? ?? 1.0;
    final scrollDirection = _parseAxis(config.properties?['scrollDirection']);
    final reverse = config.properties?['reverse'] as bool? ?? false;
    final primary = config.properties?['primary'] as bool?;
    final shrinkWrap = config.properties?['shrinkWrap'] as bool? ?? false;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      childAspectRatio: childAspectRatio,
      scrollDirection: scrollDirection,
      reverse: reverse,
      primary: primary,
      shrinkWrap: shrinkWrap,
      padding: _parseEdgeInsets(config.padding),
      children: children,
    );
  }

  /// Build PageView layout
  Widget _buildPageView(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final scrollDirection = _parseAxis(config.properties?['scrollDirection']);
    final reverse = config.properties?['reverse'] as bool? ?? false;
    final pageSnapping = config.properties?['pageSnapping'] as bool? ?? true;
    final allowImplicitScrolling = config.properties?['allowImplicitScrolling'] as bool? ?? false;

    return PageView(
      scrollDirection: scrollDirection,
      reverse: reverse,
      pageSnapping: pageSnapping,
      allowImplicitScrolling: allowImplicitScrolling,
      children: children,
    );
  }

  /// Build TabView layout
  Widget _buildTabView(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    final tabs = config.properties?['tabs'] as List<String>? ?? [];
    final tabPosition = config.properties?['tabPosition'] as String? ?? 'top';

    return DefaultTabController(
      length: tabs.length,
      child: Column(
        children: [
          if (tabPosition == 'top')
            TabBar(
              tabs: tabs.map((tab) => Tab(text: tab)).toList(),
            ),
          Expanded(
            child: TabBarView(
              children: children.take(tabs.length).toList(),
            ),
          ),
          if (tabPosition == 'bottom')
            TabBar(
              tabs: tabs.map((tab) => Tab(text: tab)).toList(),
            ),
        ],
      ),
    );
  }

  /// Build Responsive layout
  Widget _buildResponsive(
    BuildContext context,
    LayoutConfiguration config,
    List<Widget> children,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // Determine breakpoint
        String breakpoint;
        if (width < 600) {
          breakpoint = 'mobile';
        } else if (width < 1024) {
          breakpoint = 'tablet';
        } else {
          breakpoint = 'desktop';
        }

        // Get responsive configuration
        final responsiveConfig = config.properties?['responsive'] as Map<String, dynamic>?;
        final breakpointConfig = responsiveConfig?[breakpoint] as Map<String, dynamic>?;

        if (breakpointConfig != null) {
          // Apply breakpoint-specific configuration
          final modifiedConfig = LayoutConfiguration(
            type: breakpointConfig['type'] ?? config.type,
            mainAxisAlignment: _parseMainAxisAlignment(breakpointConfig['mainAxisAlignment']) ?? config.mainAxisAlignment,
            crossAxisAlignment: _parseCrossAxisAlignment(breakpointConfig['crossAxisAlignment']) ?? config.crossAxisAlignment,
            mainAxisSize: _parseMainAxisSize(breakpointConfig['mainAxisSize']) ?? config.mainAxisSize,
            children: config.children,
            padding: _parseEdgeInsets(breakpointConfig['padding']) ?? config.padding,
            margin: _parseEdgeInsets(breakpointConfig['margin']) ?? config.margin,
            properties: {
              ...config.properties ?? {},
              ...breakpointConfig,
            },
          );

          return buildLayout(context, modifiedConfig, children);
        }

        return buildLayout(context, config, children);
      },
    );
  }

  // Helper methods for parsing properties

  MainAxisAlignment _parseMainAxisAlignment(MainAxisAlignment? alignment) {
    return alignment ?? MainAxisAlignment.start;
  }

  CrossAxisAlignment _parseCrossAxisAlignment(CrossAxisAlignment? alignment) {
    return alignment ?? CrossAxisAlignment.center;
  }

  MainAxisSize _parseMainAxisSize(MainAxisSize? size) {
    return size ?? MainAxisSize.max;
  }

  Alignment _parseAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'topLeft': return Alignment.topLeft;
        case 'topCenter': return Alignment.topCenter;
        case 'topRight': return Alignment.topRight;
        case 'centerLeft': return Alignment.centerLeft;
        case 'center': return Alignment.center;
        case 'centerRight': return Alignment.centerRight;
        case 'bottomLeft': return Alignment.bottomLeft;
        case 'bottomCenter': return Alignment.bottomCenter;
        case 'bottomRight': return Alignment.bottomRight;
      }
    }
    return Alignment.center;
  }

  StackFit _parseStackFit(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'loose': return StackFit.loose;
        case 'expand': return StackFit.expand;
        case 'passthrough': return StackFit.passthrough;
      }
    }
    return StackFit.loose;
  }

  Clip _parseClipBehavior(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'none': return Clip.none;
        case 'hardEdge': return Clip.hardEdge;
        case 'antiAlias': return Clip.antiAlias;
        case 'antiAliasWithSaveLayer': return Clip.antiAliasWithSaveLayer;
      }
    }
    return Clip.none;
  }

  Axis _parseAxis(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'horizontal': return Axis.horizontal;
        case 'vertical': return Axis.vertical;
      }
    }
    return Axis.vertical;
  }

  WrapAlignment _parseWrapAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start': return WrapAlignment.start;
        case 'end': return WrapAlignment.end;
        case 'center': return WrapAlignment.center;
        case 'spaceBetween': return WrapAlignment.spaceBetween;
        case 'spaceAround': return WrapAlignment.spaceAround;
        case 'spaceEvenly': return WrapAlignment.spaceEvenly;
      }
    }
    return WrapAlignment.start;
  }

  EdgeInsets? _parseEdgeInsets(EdgeInsetsGeometry? value) {
    if (value == null) return null;
    
    if (value.all != null) {
      return EdgeInsets.all(value.all!);
    }
    
    if (value.horizontal != null || value.vertical != null) {
      return EdgeInsets.symmetric(
        horizontal: value.horizontal ?? 0,
        vertical: value.vertical ?? 0,
      );
    }
    
    return EdgeInsets.only(
      top: value.top ?? 0,
      right: value.right ?? 0,
      bottom: value.bottom ?? 0,
      left: value.left ?? 0,
    );
  }

  Color? _parseColor(dynamic value) {
    if (value is String && value.startsWith('#')) {
      return Color(int.parse(value.replaceFirst('#', '0xff')));
    }
    return null;
  }

  Decoration? _parseDecoration(dynamic value) {
    if (value is Map<String, dynamic>) {
      return BoxDecoration(
        color: _parseColor(value['color']),
        borderRadius: _parseBorderRadius(value['borderRadius']),
        border: _parseBorder(value['border']),
        boxShadow: _parseBoxShadow(value['boxShadow']),
      );
    }
    return null;
  }

  BorderRadius? _parseBorderRadius(dynamic value) {
    if (value is num) {
      return BorderRadius.circular(value.toDouble());
    }
    return null;
  }

  Border? _parseBorder(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Border.all(
        color: _parseColor(value['color']) ?? Colors.black,
        width: (value['width'] as num?)?.toDouble() ?? 1.0,
      );
    }
    return null;
  }

  List<BoxShadow>? _parseBoxShadow(dynamic value) {
    if (value is List) {
      return value.map((shadow) {
        if (shadow is Map<String, dynamic>) {
          return BoxShadow(
            color: _parseColor(shadow['color']) ?? Colors.black,
            offset: Offset(
              (shadow['offsetX'] as num?)?.toDouble() ?? 0,
              (shadow['offsetY'] as num?)?.toDouble() ?? 0,
            ),
            blurRadius: (shadow['blurRadius'] as num?)?.toDouble() ?? 0,
            spreadRadius: (shadow['spreadRadius'] as num?)?.toDouble() ?? 0,
          );
        }
        return const BoxShadow();
      }).toList();
    }
    return null;
  }

  Matrix4? _parseMatrix4(dynamic value) {
    if (value is List && value.length == 16) {
      return Matrix4.fromList(value.cast<double>());
    }
    return null;
  }
}

/// Riverpod provider for layout manager
final layoutManagerProvider = Provider<LayoutManager>((ref) {
  return LayoutManager();
});
