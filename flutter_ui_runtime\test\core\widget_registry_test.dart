import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_ui_runtime/core/widget_registry.dart';
import 'package:flutter_ui_runtime/models/ui_metadata.dart';

void main() {
  group('WidgetRegistry', () {
    late WidgetRegistry registry;

    setUp(() {
      registry = WidgetRegistry();
    });

    tearDown(() {
      registry.clearCache();
    });

    group('Builder Registration', () {
      testWidgets('should register and retrieve widget builder', (tester) async {
        // Arrange
        final builder = (ComponentDefinition definition, BuildContext context) {
          return Text(definition.properties['text'] ?? 'Default');
        };

        // Act
        registry.registerBuilder('test-widget', builder);

        // Assert
        expect(registry.isRegistered('test-widget'), isTrue);
        expect(registry.getBuilder('test-widget'), equals(builder));
      });

      testWidgets('should register widget factory for lazy loading', (tester) async {
        // Arrange
        final factory = () async {
          return (ComponentDefinition definition, BuildContext context) {
            return Text('Lazy loaded: ${definition.properties['text']}');
          };
        };

        // Act
        registry.registerFactory('lazy-widget', factory);

        // Assert
        expect(registry.isRegistered('lazy-widget'), isTrue);
      });

      test('should get all registered types', () {
        // Arrange
        registry.registerBuilder('widget1', (def, ctx) => Container());
        registry.registerFactory('widget2', () async => (def, ctx) => Container());

        // Act
        final types = registry.getRegisteredTypes();

        // Assert
        expect(types, contains('widget1'));
        expect(types, contains('widget2'));
        expect(types.length, equals(2));
      });
    });

    group('Widget Building', () {
      testWidgets('should build widget from registered builder', (tester) async {
        // Arrange
        registry.registerBuilder('text', (definition, context) {
          return Text(definition.properties['text'] ?? 'Default');
        });

        final definition = ComponentDefinition(
          id: 'test-1',
          type: 'text',
          properties: {'text': 'Hello World'},
        );

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => registry.buildWidget(definition, context),
              ),
            ),
          ),
        );

        // Assert
        expect(find.text('Hello World'), findsOneWidget);
      });

      testWidgets('should build lazy widget with loading state', (tester) async {
        // Arrange
        registry.registerFactory('lazy-text', () async {
          await Future.delayed(const Duration(milliseconds: 100));
          return (ComponentDefinition definition, BuildContext context) {
            return Text(definition.properties['text'] ?? 'Lazy Default');
          };
        });

        final definition = ComponentDefinition(
          id: 'test-2',
          type: 'lazy-text',
          properties: {'text': 'Lazy Hello'},
        );

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => registry.buildWidget(definition, context),
              ),
            ),
          ),
        );

        // Assert - should show loading state initially
        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // Wait for lazy loading to complete
        await tester.pump(const Duration(milliseconds: 150));

        // Assert - should show loaded widget
        expect(find.text('Lazy Hello'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });

      testWidgets('should show error widget for failed lazy loading', (tester) async {
        // Arrange
        registry.registerFactory('error-widget', () async {
          throw Exception('Loading failed');
        });

        final definition = ComponentDefinition(
          id: 'test-3',
          type: 'error-widget',
          properties: {},
        );

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => registry.buildWidget(definition, context),
              ),
            ),
          ),
        );

        // Wait for error
        await tester.pump(const Duration(milliseconds: 100));

        // Assert
        expect(find.byIcon(Icons.error), findsOneWidget);
        expect(find.text('Error loading error-widget'), findsOneWidget);
      });

      testWidgets('should show unknown widget for unregistered type', (tester) async {
        // Arrange
        final definition = ComponentDefinition(
          id: 'test-4',
          type: 'unknown-widget',
          properties: {},
        );

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => registry.buildWidget(definition, context),
              ),
            ),
          ),
        );

        // Assert
        expect(find.byIcon(Icons.help_outline), findsOneWidget);
        expect(find.text('Unknown: unknown-widget'), findsOneWidget);
      });
    });

    group('Caching', () {
      testWidgets('should cache built widgets', (tester) async {
        // Arrange
        var buildCount = 0;
        registry.registerBuilder('cached-widget', (definition, context) {
          buildCount++;
          return Text('Build count: $buildCount');
        });

        final definition = ComponentDefinition(
          id: 'test-5',
          type: 'cached-widget',
          properties: {'text': 'Same props'},
        );

        // Act - build same widget twice
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => Column(
                  children: [
                    registry.buildWidget(definition, context),
                    registry.buildWidget(definition, context),
                  ],
                ),
              ),
            ),
          ),
        );

        // Assert - should use cached version
        expect(buildCount, equals(1));
      });

      test('should clear cache', () {
        // Arrange
        registry.registerBuilder('test-widget', (def, ctx) => Container());
        final definition = ComponentDefinition(
          id: 'test-6',
          type: 'test-widget',
          properties: {},
        );

        // Build widget to populate cache
        final context = MockBuildContext();
        registry.buildWidget(definition, context);

        final statsBefore = registry.getCacheStats();
        expect(statsBefore['cachedWidgets'], greaterThan(0));

        // Act
        registry.clearCache();

        // Assert
        final statsAfter = registry.getCacheStats();
        expect(statsAfter['cachedWidgets'], equals(0));
      });
    });

    group('Preloading', () {
      test('should preload widget builders', () async {
        // Arrange
        final types = ['widget1', 'widget2'];
        registry.registerFactory('widget1', () async {
          await Future.delayed(const Duration(milliseconds: 50));
          return (def, ctx) => Text('Widget 1');
        });
        registry.registerFactory('widget2', () async {
          await Future.delayed(const Duration(milliseconds: 50));
          return (def, ctx) => Text('Widget 2');
        });

        // Act
        await registry.preloadBuilders(types);

        // Assert
        expect(registry.getBuilder('widget1'), isNotNull);
        expect(registry.getBuilder('widget2'), isNotNull);
      });
    });

    group('Unregistration', () {
      test('should unregister widget type', () {
        // Arrange
        registry.registerBuilder('temp-widget', (def, ctx) => Container());
        expect(registry.isRegistered('temp-widget'), isTrue);

        // Act
        registry.unregister('temp-widget');

        // Assert
        expect(registry.isRegistered('temp-widget'), isFalse);
        expect(registry.getBuilder('temp-widget'), isNull);
      });
    });

    group('Cache Statistics', () {
      test('should provide cache statistics', () {
        // Arrange
        registry.registerBuilder('stats-widget', (def, ctx) => Container());
        registry.registerFactory('lazy-stats', () async => (def, ctx) => Container());

        // Act
        final stats = registry.getCacheStats();

        // Assert
        expect(stats, containsPair('registeredBuilders', 1));
        expect(stats, containsPair('registeredFactories', 1));
        expect(stats, containsPair('totalRegisteredTypes', 2));
        expect(stats, containsPair('cachedWidgets', 0));
        expect(stats, containsPair('loadingBuilders', 0));
      });
    });
  });

  group('Default Widget Initialization', () {
    testWidgets('should initialize default text widget', (tester) async {
      // Arrange
      initializeDefaultWidgets();
      final registry = WidgetRegistry();

      final definition = ComponentDefinition(
        id: 'text-1',
        type: 'text',
        properties: {
          'text': 'Hello World',
          'style': {
            'fontSize': 18.0,
            'color': '#ff0000',
            'fontWeight': 'bold',
          },
        },
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => registry.buildWidget(definition, context),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Hello World'), findsOneWidget);
      
      final textWidget = tester.widget<Text>(find.text('Hello World'));
      expect(textWidget.style?.fontSize, equals(18.0));
      expect(textWidget.style?.color, equals(const Color(0xffff0000)));
      expect(textWidget.style?.fontWeight, equals(FontWeight.bold));
    });

    testWidgets('should initialize default button widget', (tester) async {
      // Arrange
      initializeDefaultWidgets();
      final registry = WidgetRegistry();

      final definition = ComponentDefinition(
        id: 'button-1',
        type: 'button',
        properties: {
          'text': 'Click Me',
          'variant': 'elevated',
          'disabled': false,
        },
        actions: {
          'onPressed': {
            'type': 'navigate',
            'params': {'route': '/dashboard'},
          },
        },
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => registry.buildWidget(definition, context),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Click Me'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNotNull);
    });

    testWidgets('should initialize default container widget', (tester) async {
      // Arrange
      initializeDefaultWidgets();
      final registry = WidgetRegistry();

      final definition = ComponentDefinition(
        id: 'container-1',
        type: 'container',
        properties: {
          'width': 200.0,
          'height': 100.0,
          'color': '#0000ff',
          'padding': {
            'top': 10.0,
            'right': 15.0,
            'bottom': 10.0,
            'left': 15.0,
          },
        },
        children: [
          ComponentDefinition(
            id: 'child-text',
            type: 'text',
            properties: {'text': 'Container Child'},
          ),
        ],
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => registry.buildWidget(definition, context),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(Container), findsOneWidget);
      expect(find.text('Container Child'), findsOneWidget);
      
      final container = tester.widget<Container>(find.byType(Container));
      expect(container.constraints?.maxWidth, equals(200.0));
      expect(container.constraints?.maxHeight, equals(100.0));
    });

    testWidgets('should initialize default input widget', (tester) async {
      // Arrange
      initializeDefaultWidgets();
      final registry = WidgetRegistry();

      final definition = ComponentDefinition(
        id: 'input-1',
        type: 'input',
        properties: {
          'placeholder': 'Enter your name',
          'value': 'Initial value',
          'type': 'text',
          'required': true,
        },
        actions: {
          'onChange': {
            'type': 'set_state',
            'params': {'key': 'inputValue'},
          },
        },
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => registry.buildWidget(definition, context),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsOneWidget);
      
      final textField = tester.widget<TextFormField>(find.byType(TextFormField));
      expect(textField.initialValue, equals('Initial value'));
      expect(textField.decoration?.hintText, equals('Enter your name'));
      expect(textField.validator, isNotNull);
    });

    testWidgets('should initialize default image widget', (tester) async {
      // Arrange
      initializeDefaultWidgets();
      final registry = WidgetRegistry();

      final definition = ComponentDefinition(
        id: 'image-1',
        type: 'image',
        properties: {
          'src': 'https://example.com/image.jpg',
          'width': 150.0,
          'height': 100.0,
          'fit': 'cover',
          'alt': 'Example image',
        },
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => registry.buildWidget(definition, context),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(Image), findsOneWidget);
      
      final image = tester.widget<Image>(find.byType(Image));
      expect(image.width, equals(150.0));
      expect(image.height, equals(100.0));
      expect(image.fit, equals(BoxFit.cover));
      expect(image.semanticLabel, equals('Example image'));
    });
  });
}

// Mock BuildContext for testing
class MockBuildContext extends BuildContext {
  @override
  bool get debugDoingBuild => false;

  @override
  InheritedWidget? dependOnInheritedElement(InheritedElement ancestor, {Object? aspect}) => null;

  @override
  T? dependOnInheritedWidgetOfExactType<T extends InheritedWidget>({Object? aspect}) => null;

  @override
  DiagnosticsNode describeElement(String name, {DiagnosticsTreeStyle style = DiagnosticsTreeStyle.errorProperty}) {
    throw UnimplementedError();
  }

  @override
  List<DiagnosticsNode> describeMissingAncestor({required Type expectedAncestorType}) {
    throw UnimplementedError();
  }

  @override
  DiagnosticsNode describeOwnershipChain(String name) {
    throw UnimplementedError();
  }

  @override
  DiagnosticsNode describeWidget(String name, {DiagnosticsTreeStyle style = DiagnosticsTreeStyle.errorProperty}) {
    throw UnimplementedError();
  }

  @override
  T? findAncestorRenderObjectOfType<T extends RenderObject>() => null;

  @override
  T? findAncestorStateOfType<T extends State<StatefulWidget>>() => null;

  @override
  T? findAncestorWidgetOfExactType<T extends Widget>() => null;

  @override
  RenderObject? findRenderObject() => null;

  @override
  T? findRootAncestorStateOfType<T extends State<StatefulWidget>>() => null;

  @override
  InheritedElement? getElementForInheritedWidgetOfExactType<T extends InheritedWidget>() => null;

  @override
  BuildOwner? get owner => null;

  @override
  Size? get size => null;

  @override
  void visitAncestorElements(bool Function(Element element) visitor) {}

  @override
  void visitChildElements(ElementVisitor visitor) {}

  @override
  Widget get widget => Container();
}
