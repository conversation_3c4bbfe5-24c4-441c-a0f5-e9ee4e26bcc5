import React, { useState, useCallback } from 'react';
import { Ta<PERSON>, Card, Button, Space, Modal, message } from 'antd';
import {
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  HighlightOutlined,
  SaveOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@store/index';
import {
  updateThemeColors,
  updateThemeTypography,
  updateThemeSpacing,
  setTheme,
  resetTheme,
} from '@store/slices/themeSlice';
import { Theme } from '@types/index';

// Theme editor components
import ColorPaletteEditor from './ColorPaletteEditor';
import TypographyEditor from './TypographyEditor';
import SpacingEditor from './SpacingEditor';
import ShadowEditor from './ShadowEditor';
import BorderEditor from './BorderEditor';
import AnimationEditor from './AnimationEditor';
import ThemePreview from './ThemePreview';

const { TabPane } = Tabs;

interface ThemeEditorProps {
  className?: string;
}

const ThemeEditor: React.FC<ThemeEditorProps> = ({ className = '' }) => {
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState('colors');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const { theme, customThemes } = useAppSelector(state => state.theme);
  const { currentConfiguration } = useAppSelector(state => state.uiBuilder);

  // Handle color updates
  const handleColorUpdate = useCallback((colorUpdates: Partial<typeof theme.colors>) => {
    dispatch(updateThemeColors(colorUpdates));
  }, [dispatch]);

  // Handle typography updates
  const handleTypographyUpdate = useCallback((typographyUpdates: Partial<typeof theme.typography>) => {
    dispatch(updateThemeTypography(typographyUpdates));
  }, [dispatch]);

  // Handle spacing updates
  const handleSpacingUpdate = useCallback((spacingUpdates: Partial<typeof theme.spacing>) => {
    dispatch(updateThemeSpacing(spacingUpdates));
  }, [dispatch]);

  // Handle theme save
  const handleSaveTheme = useCallback(async () => {
    try {
      // TODO: Implement theme saving to backend
      message.success('Theme saved successfully');
    } catch (error) {
      message.error('Failed to save theme');
    }
  }, [theme]);

  // Handle theme reset
  const handleResetTheme = useCallback(() => {
    Modal.confirm({
      title: 'Reset Theme',
      content: 'Are you sure you want to reset the theme to default? This action cannot be undone.',
      onOk: () => {
        dispatch(resetTheme());
        message.success('Theme reset to default');
      },
    });
  }, [dispatch]);

  // Handle theme export
  const handleExportTheme = useCallback(() => {
    const themeData = JSON.stringify(theme, null, 2);
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${theme.name.toLowerCase().replace(/\s+/g, '-')}-theme.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    message.success('Theme exported successfully');
  }, [theme]);

  // Handle theme import
  const handleImportTheme = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedTheme = JSON.parse(e.target?.result as string) as Theme;
        dispatch(setTheme(importedTheme));
        message.success('Theme imported successfully');
        setImportModalVisible(false);
      } catch (error) {
        message.error('Invalid theme file');
      }
    };
    reader.readAsText(file);
  }, [dispatch]);

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">Theme Editor</h3>
          
          <Space>
            <Button
              icon={<SaveOutlined />}
              type="primary"
              onClick={handleSaveTheme}
              size="small"
            >
              Save
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={handleResetTheme}
              size="small"
            >
              Reset
            </Button>
          </Space>
        </div>

        {/* Theme info */}
        <div className="text-sm text-gray-600">
          <div className="font-medium">{theme.name}</div>
          {theme.description && (
            <div className="text-xs text-gray-500 mt-1">{theme.description}</div>
          )}
        </div>

        {/* Quick actions */}
        <div className="flex items-center space-x-2 mt-3">
          <Button
            icon={<HighlightOutlined />}
            size="small"
            onClick={() => setPreviewVisible(true)}
          >
            Preview
          </Button>
          
          <Button
            icon={<ExportOutlined />}
            size="small"
            onClick={handleExportTheme}
          >
            Export
          </Button>
          
          <Button
            icon={<ImportOutlined />}
            size="small"
            onClick={() => setImportModalVisible(true)}
          >
            Import
          </Button>
        </div>
      </div>

      {/* Theme editor tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="h-full"
          tabBarStyle={{ margin: 0, padding: '0 16px' }}
        >
          <TabPane
            tab={
              <span>
                <BgColorsOutlined />
                Colors
              </span>
            }
            key="colors"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <ColorPaletteEditor
                colors={theme.colors}
                onChange={handleColorUpdate}
              />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <FontSizeOutlined />
                Typography
              </span>
            }
            key="typography"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <TypographyEditor
                typography={theme.typography}
                onChange={handleTypographyUpdate}
              />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BorderOutlined />
                Spacing
              </span>
            }
            key="spacing"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <SpacingEditor
                spacing={theme.spacing}
                onChange={handleSpacingUpdate}
              />
            </div>
          </TabPane>

          <TabPane
            tab="Shadows"
            key="shadows"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <ShadowEditor
                shadows={theme.shadows}
                onChange={(shadows) => {
                  dispatch(setTheme({ ...theme, shadows }));
                }}
              />
            </div>
          </TabPane>

          <TabPane
            tab="Borders"
            key="borders"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <BorderEditor
                borders={theme.borders}
                onChange={(borders) => {
                  dispatch(setTheme({ ...theme, borders }));
                }}
              />
            </div>
          </TabPane>

          <TabPane
            tab="Animations"
            key="animations"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <AnimationEditor
                animations={theme.animations}
                onChange={(animations) => {
                  dispatch(setTheme({ ...theme, animations }));
                }}
              />
            </div>
          </TabPane>
        </Tabs>
      </div>

      {/* Theme preview modal */}
      <Modal
        title="Theme Preview"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        <ThemePreview
          theme={theme}
          configuration={currentConfiguration}
        />
      </Modal>

      {/* Import modal */}
      <Modal
        title="Import Theme"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            Select a theme JSON file to import. This will replace the current theme.
          </div>
          
          <input
            type="file"
            accept=".json"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                handleImportTheme(file);
              }
            }}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          
          <div className="text-xs text-gray-500">
            Supported format: JSON theme files exported from this editor
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ThemeEditor;
