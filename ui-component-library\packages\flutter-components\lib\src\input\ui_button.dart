import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';

import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';
import '../utils/ui_utils.dart';

/// UI Builder Button component that provides consistent button styling
/// across Material and Cupertino platforms
class UIButton extends StatefulWidget {
  const UIButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = UIButtonVariant.primary,
    this.size = UISize.md,
    this.fullWidth = false,
    this.loading = false,
    this.disabled = false,
    this.leftIcon,
    this.rightIcon,
    this.loadingWidget,
    this.testId,
    this.semanticLabel,
    this.animationProps,
    this.platformBehavior = UIPlatformBehavior.auto,
  });

  /// Button press handler
  final VoidCallback? onPressed;

  /// Button content
  final Widget child;

  /// Button variant
  final UIButtonVariant variant;

  /// Button size
  final UISize size;

  /// Whether button takes full width
  final bool fullWidth;

  /// Loading state
  final bool loading;

  /// Disabled state
  final bool disabled;

  /// Icon to display before text
  final Widget? leftIcon;

  /// Icon to display after text
  final Widget? rightIcon;

  /// Custom loading widget
  final Widget? loadingWidget;

  /// Test identifier
  final String? testId;

  /// Semantic label for accessibility
  final String? semanticLabel;

  /// Animation properties
  final UIAnimationProps? animationProps;

  /// Platform-specific behavior
  final UIPlatformBehavior platformBehavior;

  @override
  State<UIButton> createState() => _UIButtonState();
}

class _UIButtonState extends State<UIButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationProps?.duration ?? const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationProps?.curve ?? Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  bool get _isDisabled => widget.disabled || widget.loading;

  void _handleTapDown(TapDownDetails details) {
    if (!_isDisabled) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isDisabled) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (!_isDisabled) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tokens = DesignTokens.of(context);

    return Semantics(
      label: widget.semanticLabel,
      button: true,
      enabled: !_isDisabled,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildPlatformButton(context, theme, tokens),
          );
        },
      ),
    );
  }

  Widget _buildPlatformButton(
    BuildContext context,
    ThemeData theme,
    DesignTokens tokens,
  ) {
    final shouldUseCupertino = widget.platformBehavior == UIPlatformBehavior.cupertino || (widget.platformBehavior == UIPlatformBehavior.auto && UIUtils.isCupertino(context));

    if (shouldUseCupertino) {
      return _buildCupertinoButton(context, tokens);
    } else {
      return _buildMaterialButton(context, theme, tokens);
    }
  }

  Widget _buildMaterialButton(
    BuildContext context,
    ThemeData theme,
    DesignTokens tokens,
  ) {
    final buttonStyle = _getMaterialButtonStyle(theme, tokens);
    final content = _buildButtonContent();

    Widget button;
    switch (widget.variant) {
      case UIButtonVariant.primary:
        button = ElevatedButton(
          onPressed: _isDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: content,
        );
        break;
      case UIButtonVariant.secondary:
        button = FilledButton(
          onPressed: _isDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: content,
        );
        break;
      case UIButtonVariant.outline:
        button = OutlinedButton(
          onPressed: _isDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: content,
        );
        break;
      case UIButtonVariant.ghost:
      case UIButtonVariant.link:
        button = TextButton(
          onPressed: _isDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: content,
        );
        break;
      case UIButtonVariant.destructive:
        button = ElevatedButton(
          onPressed: _isDisabled ? null : widget.onPressed,
          style: buttonStyle?.copyWith(
            backgroundColor: MaterialStateProperty.all(theme.colorScheme.error),
            foregroundColor: MaterialStateProperty.all(theme.colorScheme.onError),
          ),
          child: content,
        );
        break;
    }

    return _wrapButton(button);
  }

  Widget _buildCupertinoButton(BuildContext context, DesignTokens tokens) {
    final content = _buildButtonContent();
    final buttonStyle = _getCupertinoButtonStyle(tokens);

    return _wrapButton(
      CupertinoButton(
        onPressed: _isDisabled ? null : widget.onPressed,
        color: buttonStyle.backgroundColor,
        disabledColor: buttonStyle.disabledColor,
        padding: buttonStyle.padding,
        borderRadius: buttonStyle.borderRadius,
        child: content,
      ),
    );
  }

  Widget _buildButtonContent() {
    if (widget.loading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.loadingWidget ??
              SizedBox(
                width: _getIconSize(),
                height: _getIconSize(),
                child: const CircularProgressIndicator(strokeWidth: 2),
              ),
          const SizedBox(width: 8),
          widget.child,
        ],
      );
    }

    final children = <Widget>[];

    if (widget.leftIcon != null) {
      children.add(SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: widget.leftIcon!,
      ));
      children.add(const SizedBox(width: 8));
    }

    children.add(widget.child);

    if (widget.rightIcon != null) {
      children.add(const SizedBox(width: 8));
      children.add(SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: widget.rightIcon!,
      ));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _wrapButton(Widget button) {
    Widget wrappedButton = GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: button,
    );

    if (widget.fullWidth) {
      wrappedButton = SizedBox(
        width: double.infinity,
        child: wrappedButton,
      );
    }

    if (widget.testId != null) {
      wrappedButton = Semantics(
        identifier: widget.testId,
        child: wrappedButton,
      );
    }

    return wrappedButton;
  }

  ButtonStyle _getMaterialButtonStyle(ThemeData theme, DesignTokens tokens) {
    final size = _getButtonSize();

    return ButtonStyle(
      minimumSize: MaterialStateProperty.all(Size(size.minWidth, size.height)),
      padding: MaterialStateProperty.all(size.padding),
      textStyle: MaterialStateProperty.all(size.textStyle),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(tokens.borderRadius.md),
        ),
      ),
    );
  }

  _CupertinoButtonStyle _getCupertinoButtonStyle(DesignTokens tokens) {
    final size = _getButtonSize();

    return _CupertinoButtonStyle(
      backgroundColor: _getCupertinoBackgroundColor(tokens),
      disabledColor: tokens.colors.neutral.shade300,
      padding: size.padding,
      borderRadius: BorderRadius.circular(tokens.borderRadius.md),
    );
  }

  Color _getCupertinoBackgroundColor(DesignTokens tokens) {
    switch (widget.variant) {
      case UIButtonVariant.primary:
        return tokens.colors.primary.shade500;
      case UIButtonVariant.secondary:
        return tokens.colors.secondary.shade500;
      case UIButtonVariant.destructive:
        return tokens.colors.error.shade500;
      case UIButtonVariant.outline:
      case UIButtonVariant.ghost:
      case UIButtonVariant.link:
        return Colors.transparent;
    }
  }

  _ButtonSize _getButtonSize() {
    switch (widget.size) {
      case UISize.xs:
        return _ButtonSize(
          height: 28,
          minWidth: 56,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          textStyle: const TextStyle(fontSize: 12),
        );
      case UISize.sm:
        return _ButtonSize(
          height: 32,
          minWidth: 64,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          textStyle: const TextStyle(fontSize: 14),
        );
      case UISize.md:
        return _ButtonSize(
          height: 36,
          minWidth: 72,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: const TextStyle(fontSize: 14),
        );
      case UISize.lg:
        return _ButtonSize(
          height: 40,
          minWidth: 80,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          textStyle: const TextStyle(fontSize: 16),
        );
      case UISize.xl:
        return _ButtonSize(
          height: 44,
          minWidth: 88,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: const TextStyle(fontSize: 16),
        );
      case UISize.xxl:
        return _ButtonSize(
          height: 48,
          minWidth: 96,
          padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14),
          textStyle: const TextStyle(fontSize: 18),
        );
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case UISize.xs:
        return 12;
      case UISize.sm:
        return 14;
      case UISize.md:
        return 16;
      case UISize.lg:
        return 18;
      case UISize.xl:
        return 20;
      case UISize.xxl:
        return 22;
    }
  }
}

class _ButtonSize {
  const _ButtonSize({
    required this.height,
    required this.minWidth,
    required this.padding,
    required this.textStyle,
  });

  final double height;
  final double minWidth;
  final EdgeInsets padding;
  final TextStyle textStyle;
}

class _CupertinoButtonStyle {
  const _CupertinoButtonStyle({
    required this.backgroundColor,
    required this.disabledColor,
    required this.padding,
    required this.borderRadius,
  });

  final Color backgroundColor;
  final Color disabledColor;
  final EdgeInsets padding;
  final BorderRadius borderRadius;
}
