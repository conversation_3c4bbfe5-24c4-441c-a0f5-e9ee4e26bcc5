import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';
import '../utils/ui_utils.dart';

/// UI Builder Container component that provides consistent container styling
class UIContainer extends StatelessWidget {
  const UIContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.decoration,
    this.alignment,
    this.constraints,
    this.transform,
    this.clipBehavior = Clip.none,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.shadow,
    this.gradient,
  });

  /// Child widget
  final Widget? child;

  /// Container width
  final double? width;

  /// Container height
  final double? height;

  /// Container padding
  final EdgeInsets? padding;

  /// Container margin
  final EdgeInsets? margin;

  /// Container decoration
  final Decoration? decoration;

  /// Child alignment
  final AlignmentGeometry? alignment;

  /// Container constraints
  final BoxConstraints? constraints;

  /// Container transform
  final Matrix4? transform;

  /// Clip behavior
  final Clip clipBehavior;

  /// Background color
  final Color? backgroundColor;

  /// Border radius
  final BorderRadius? borderRadius;

  /// Border
  final Border? border;

  /// Shadow
  final List<BoxShadow>? shadow;

  /// Gradient
  final Gradient? gradient;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build decoration
    Decoration? finalDecoration = decoration;
    if (finalDecoration == null && (backgroundColor != null || borderRadius != null || border != null || shadow != null || gradient != null)) {
      finalDecoration = BoxDecoration(
        color: gradient == null ? backgroundColor : null,
        borderRadius: borderRadius,
        border: border,
        boxShadow: shadow,
        gradient: gradient,
      );
    }

    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      constraints: constraints,
      transform: transform,
      clipBehavior: clipBehavior,
      decoration: finalDecoration,
      child: child,
    );
  }
}

/// UI Builder responsive container with breakpoint-based sizing
class UIResponsiveContainer extends StatelessWidget {
  const UIResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.alignment = Alignment.center,
    this.breakpoints = const {
      UIBreakpoint.sm: 540,
      UIBreakpoint.md: 720,
      UIBreakpoint.lg: 960,
      UIBreakpoint.xl: 1140,
      UIBreakpoint.xxl: 1320,
    },
  });

  /// Child widget
  final Widget child;

  /// Maximum width override
  final double? maxWidth;

  /// Container padding
  final EdgeInsets? padding;

  /// Container margin
  final EdgeInsets? margin;

  /// Child alignment
  final AlignmentGeometry alignment;

  /// Breakpoint max widths
  final Map<UIBreakpoint, double> breakpoints;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final currentBreakpoint = UIUtils.getCurrentBreakpoint(context);
    
    double? containerMaxWidth = maxWidth;
    if (containerMaxWidth == null) {
      // Find the appropriate max width for current breakpoint
      for (final breakpoint in UIBreakpoint.values.reversed) {
        if (screenWidth >= breakpoint.minWidth && breakpoints.containsKey(breakpoint)) {
          containerMaxWidth = breakpoints[breakpoint];
          break;
        }
      }
    }

    return Container(
      width: double.infinity,
      padding: margin,
      alignment: alignment,
      child: Container(
        width: double.infinity,
        constraints: containerMaxWidth != null 
          ? BoxConstraints(maxWidth: containerMaxWidth)
          : null,
        padding: padding,
        child: child,
      ),
    );
  }
}

/// UI Builder section container with semantic styling
class UISection extends StatelessWidget {
  const UISection({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.fullWidth = true,
    this.semanticLabel,
  });

  /// Child widget
  final Widget child;

  /// Section padding
  final EdgeInsets? padding;

  /// Section margin
  final EdgeInsets? margin;

  /// Background color
  final Color? backgroundColor;

  /// Whether section should take full width
  final bool fullWidth;

  /// Semantic label for accessibility
  final String? semanticLabel;

  @override
  Widget build(BuildContext context) {
    final tokens = DesignTokens.instance;
    final defaultPadding = EdgeInsets.symmetric(
      vertical: tokens.spacing.size8,
      horizontal: tokens.spacing.size4,
    );

    Widget sectionChild = Container(
      width: fullWidth ? double.infinity : null,
      padding: padding ?? defaultPadding,
      margin: margin,
      color: backgroundColor,
      child: child,
    );

    if (semanticLabel != null) {
      sectionChild = Semantics(
        label: semanticLabel,
        child: sectionChild,
      );
    }

    return sectionChild;
  }
}

/// UI Builder card container with elevation and styling
class UICardContainer extends StatelessWidget {
  const UICardContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation = 1,
    this.borderRadius,
    this.backgroundColor,
    this.shadowColor,
    this.borderColor,
    this.borderWidth = 0,
    this.clipBehavior = Clip.antiAlias,
  });

  /// Child widget
  final Widget child;

  /// Card padding
  final EdgeInsets? padding;

  /// Card margin
  final EdgeInsets? margin;

  /// Card elevation
  final double elevation;

  /// Border radius
  final BorderRadius? borderRadius;

  /// Background color
  final Color? backgroundColor;

  /// Shadow color
  final Color? shadowColor;

  /// Border color
  final Color? borderColor;

  /// Border width
  final double borderWidth;

  /// Clip behavior
  final Clip clipBehavior;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final defaultBorderRadius = borderRadius ?? tokens.borderRadius.lg;
    final defaultPadding = padding ?? EdgeInsets.all(tokens.spacing.size4);

    return Container(
      margin: margin,
      child: Material(
        elevation: elevation,
        borderRadius: defaultBorderRadius,
        color: backgroundColor ?? colorScheme.surface,
        shadowColor: shadowColor,
        clipBehavior: clipBehavior,
        child: Container(
          decoration: borderColor != null && borderWidth > 0
            ? BoxDecoration(
                borderRadius: defaultBorderRadius,
                border: Border.all(
                  color: borderColor!,
                  width: borderWidth,
                ),
              )
            : null,
          child: Padding(
            padding: defaultPadding,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// UI Builder flexible container that adapts to content
class UIFlexibleContainer extends StatelessWidget {
  const UIFlexibleContainer({
    super.key,
    required this.child,
    this.minWidth,
    this.maxWidth,
    this.minHeight,
    this.maxHeight,
    this.aspectRatio,
    this.padding,
    this.margin,
    this.alignment,
  });

  /// Child widget
  final Widget child;

  /// Minimum width
  final double? minWidth;

  /// Maximum width
  final double? maxWidth;

  /// Minimum height
  final double? minHeight;

  /// Maximum height
  final double? maxHeight;

  /// Aspect ratio
  final double? aspectRatio;

  /// Container padding
  final EdgeInsets? padding;

  /// Container margin
  final EdgeInsets? margin;

  /// Child alignment
  final AlignmentGeometry? alignment;

  @override
  Widget build(BuildContext context) {
    Widget container = Container(
      constraints: BoxConstraints(
        minWidth: minWidth ?? 0,
        maxWidth: maxWidth ?? double.infinity,
        minHeight: minHeight ?? 0,
        maxHeight: maxHeight ?? double.infinity,
      ),
      padding: padding,
      margin: margin,
      alignment: alignment,
      child: child,
    );

    if (aspectRatio != null) {
      container = AspectRatio(
        aspectRatio: aspectRatio!,
        child: container,
      );
    }

    return container;
  }
}
