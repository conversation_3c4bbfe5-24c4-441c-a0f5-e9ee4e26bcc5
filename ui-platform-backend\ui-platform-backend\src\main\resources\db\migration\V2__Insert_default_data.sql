-- Default data for UI Platform
-- Version 2.0 - Insert default components and sample data

-- Insert default component categories and components
INSERT INTO components (id, name, display_name, description, category, type, config_schema, default_props, style_schema, react_code, flutter_code, documentation, examples, tags, is_public, version) VALUES

-- Layout Components
(uuid_generate_v4(), 'container', 'Container', 'A flexible container component for layout', 'layout', 'container', 
'{"properties": {"padding": {"type": "string", "default": "16px"}, "margin": {"type": "string", "default": "0px"}, "maxWidth": {"type": "string", "default": "100%"}}}',
'{"padding": "16px", "margin": "0px", "maxWidth": "100%"}',
'{"backgroundColor": {"type": "string", "default": "transparent"}, "borderRadius": {"type": "string", "default": "0px"}, "border": {"type": "string", "default": "none"}}',
'import React from "react"; export const Container = ({ children, padding = "16px", margin = "0px", maxWidth = "100%", style = {} }) => ( <div style={{ padding, margin, maxWidth, ...style }}> {children} </div> );',
'import "package:flutter/material.dart"; class UIContainer extends StatelessWidget { final Widget child; final EdgeInsets padding; final EdgeInsets margin; final double maxWidth; const UIContainer({Key? key, required this.child, this.padding = const EdgeInsets.all(16), this.margin = EdgeInsets.zero, this.maxWidth = double.infinity}) : super(key: key); @override Widget build(BuildContext context) { return Container( padding: padding, margin: margin, constraints: BoxConstraints(maxWidth: maxWidth), child: child, ); } }',
'A flexible container component that can hold other components and provides spacing and layout control.',
'[{"title": "Basic Container", "code": "<Container padding=\"20px\"><Text>Hello World</Text></Container>"}]',
'{"layout", "container", "wrapper"}', true, '1.0.0'),

(uuid_generate_v4(), 'row', 'Row', 'Horizontal layout component', 'layout', 'layout',
'{"properties": {"gap": {"type": "string", "default": "8px"}, "justifyContent": {"type": "string", "enum": ["flex-start", "center", "flex-end", "space-between", "space-around"], "default": "flex-start"}, "alignItems": {"type": "string", "enum": ["flex-start", "center", "flex-end", "stretch"], "default": "center"}}}',
'{"gap": "8px", "justifyContent": "flex-start", "alignItems": "center"}',
'{"backgroundColor": {"type": "string", "default": "transparent"}, "padding": {"type": "string", "default": "0px"}}',
'import React from "react"; export const Row = ({ children, gap = "8px", justifyContent = "flex-start", alignItems = "center", style = {} }) => ( <div style={{ display: "flex", flexDirection: "row", gap, justifyContent, alignItems, ...style }}> {children} </div> );',
'import "package:flutter/material.dart"; class UIRow extends StatelessWidget { final List<Widget> children; final MainAxisAlignment mainAxisAlignment; final CrossAxisAlignment crossAxisAlignment; const UIRow({Key? key, required this.children, this.mainAxisAlignment = MainAxisAlignment.start, this.crossAxisAlignment = CrossAxisAlignment.center}) : super(key: key); @override Widget build(BuildContext context) { return Row( mainAxisAlignment: mainAxisAlignment, crossAxisAlignment: crossAxisAlignment, children: children, ); } }',
'A horizontal layout component that arranges children in a row.',
'[{"title": "Basic Row", "code": "<Row><Button>Button 1</Button><Button>Button 2</Button></Row>"}]',
'{"layout", "row", "horizontal"}', true, '1.0.0'),

-- Form Components
(uuid_generate_v4(), 'text-input', 'Text Input', 'A text input field', 'form', 'input',
'{"properties": {"placeholder": {"type": "string", "default": "Enter text..."}, "value": {"type": "string", "default": ""}, "required": {"type": "boolean", "default": false}, "disabled": {"type": "boolean", "default": false}, "maxLength": {"type": "number", "default": 255}}}',
'{"placeholder": "Enter text...", "value": "", "required": false, "disabled": false, "maxLength": 255}',
'{"width": {"type": "string", "default": "100%"}, "height": {"type": "string", "default": "40px"}, "borderRadius": {"type": "string", "default": "4px"}, "border": {"type": "string", "default": "1px solid #ccc"}, "padding": {"type": "string", "default": "8px 12px"}}',
'import React, { useState } from "react"; export const TextInput = ({ placeholder = "Enter text...", value: initialValue = "", required = false, disabled = false, maxLength = 255, onChange, style = {} }) => { const [value, setValue] = useState(initialValue); const handleChange = (e) => { setValue(e.target.value); onChange?.(e.target.value); }; return ( <input type="text" placeholder={placeholder} value={value} required={required} disabled={disabled} maxLength={maxLength} onChange={handleChange} style={{ width: "100%", height: "40px", borderRadius: "4px", border: "1px solid #ccc", padding: "8px 12px", ...style }} /> ); };',
'import "package:flutter/material.dart"; class UITextInput extends StatefulWidget { final String placeholder; final String initialValue; final bool required; final bool disabled; final int maxLength; final Function(String)? onChanged; const UITextInput({Key? key, this.placeholder = "Enter text...", this.initialValue = "", this.required = false, this.disabled = false, this.maxLength = 255, this.onChanged}) : super(key: key); @override _UITextInputState createState() => _UITextInputState(); } class _UITextInputState extends State<UITextInput> { late TextEditingController _controller; @override void initState() { super.initState(); _controller = TextEditingController(text: widget.initialValue); } @override Widget build(BuildContext context) { return TextField( controller: _controller, decoration: InputDecoration( hintText: widget.placeholder, border: OutlineInputBorder(), ), enabled: !widget.disabled, maxLength: widget.maxLength, onChanged: widget.onChanged, ); } }',
'A text input field for collecting user input.',
'[{"title": "Basic Input", "code": "<TextInput placeholder=\"Enter your name\" />"}]',
'{"form", "input", "text"}', true, '1.0.0'),

(uuid_generate_v4(), 'button', 'Button', 'A clickable button component', 'form', 'button',
'{"properties": {"text": {"type": "string", "default": "Click me"}, "variant": {"type": "string", "enum": ["primary", "secondary", "outline", "ghost"], "default": "primary"}, "size": {"type": "string", "enum": ["small", "medium", "large"], "default": "medium"}, "disabled": {"type": "boolean", "default": false}, "loading": {"type": "boolean", "default": false}}}',
'{"text": "Click me", "variant": "primary", "size": "medium", "disabled": false, "loading": false}',
'{"backgroundColor": {"type": "string", "default": "#007bff"}, "color": {"type": "string", "default": "#ffffff"}, "borderRadius": {"type": "string", "default": "4px"}, "border": {"type": "string", "default": "none"}, "padding": {"type": "string", "default": "8px 16px"}, "fontSize": {"type": "string", "default": "14px"}}',
'import React from "react"; export const Button = ({ text = "Click me", variant = "primary", size = "medium", disabled = false, loading = false, onClick, style = {} }) => { const getVariantStyles = () => { switch (variant) { case "primary": return { backgroundColor: "#007bff", color: "#ffffff" }; case "secondary": return { backgroundColor: "#6c757d", color: "#ffffff" }; case "outline": return { backgroundColor: "transparent", color: "#007bff", border: "1px solid #007bff" }; case "ghost": return { backgroundColor: "transparent", color: "#007bff", border: "none" }; default: return { backgroundColor: "#007bff", color: "#ffffff" }; } }; const getSizeStyles = () => { switch (size) { case "small": return { padding: "4px 8px", fontSize: "12px" }; case "medium": return { padding: "8px 16px", fontSize: "14px" }; case "large": return { padding: "12px 24px", fontSize: "16px" }; default: return { padding: "8px 16px", fontSize: "14px" }; } }; return ( <button disabled={disabled || loading} onClick={onClick} style={{ borderRadius: "4px", border: "none", cursor: disabled ? "not-allowed" : "pointer", opacity: disabled ? 0.6 : 1, ...getVariantStyles(), ...getSizeStyles(), ...style }} > {loading ? "Loading..." : text} </button> ); };',
'import "package:flutter/material.dart"; class UIButton extends StatelessWidget { final String text; final String variant; final String size; final bool disabled; final bool loading; final VoidCallback? onPressed; const UIButton({Key? key, this.text = "Click me", this.variant = "primary", this.size = "medium", this.disabled = false, this.loading = false, this.onPressed}) : super(key: key); @override Widget build(BuildContext context) { return ElevatedButton( onPressed: disabled || loading ? null : onPressed, style: ElevatedButton.styleFrom( backgroundColor: variant == "primary" ? Colors.blue : Colors.grey, ), child: loading ? CircularProgressIndicator() : Text(text), ); } }',
'A clickable button component with various styles and states.',
'[{"title": "Primary Button", "code": "<Button text=\"Save\" variant=\"primary\" />"}]',
'{"form", "button", "action"}', true, '1.0.0'),

-- Display Components
(uuid_generate_v4(), 'text', 'Text', 'A text display component', 'display', 'text',
'{"properties": {"content": {"type": "string", "default": "Sample text"}, "variant": {"type": "string", "enum": ["h1", "h2", "h3", "h4", "h5", "h6", "body", "caption"], "default": "body"}, "align": {"type": "string", "enum": ["left", "center", "right", "justify"], "default": "left"}, "color": {"type": "string", "default": "#000000"}}}',
'{"content": "Sample text", "variant": "body", "align": "left", "color": "#000000"}',
'{"fontSize": {"type": "string", "default": "14px"}, "fontWeight": {"type": "string", "default": "normal"}, "lineHeight": {"type": "string", "default": "1.5"}, "margin": {"type": "string", "default": "0px"}}',
'import React from "react"; export const Text = ({ content = "Sample text", variant = "body", align = "left", color = "#000000", style = {} }) => { const getVariantStyles = () => { switch (variant) { case "h1": return { fontSize: "32px", fontWeight: "bold" }; case "h2": return { fontSize: "28px", fontWeight: "bold" }; case "h3": return { fontSize: "24px", fontWeight: "bold" }; case "h4": return { fontSize: "20px", fontWeight: "bold" }; case "h5": return { fontSize: "18px", fontWeight: "bold" }; case "h6": return { fontSize: "16px", fontWeight: "bold" }; case "body": return { fontSize: "14px", fontWeight: "normal" }; case "caption": return { fontSize: "12px", fontWeight: "normal" }; default: return { fontSize: "14px", fontWeight: "normal" }; } }; const Tag = variant.startsWith("h") ? variant : "p"; return ( <Tag style={{ textAlign: align, color, lineHeight: "1.5", margin: "0px", ...getVariantStyles(), ...style }} > {content} </Tag> ); };',
'import "package:flutter/material.dart"; class UIText extends StatelessWidget { final String content; final String variant; final TextAlign align; final Color color; const UIText({Key? key, this.content = "Sample text", this.variant = "body", this.align = TextAlign.left, this.color = Colors.black}) : super(key: key); @override Widget build(BuildContext context) { TextStyle style; switch (variant) { case "h1": style = TextStyle(fontSize: 32, fontWeight: FontWeight.bold); break; case "h2": style = TextStyle(fontSize: 28, fontWeight: FontWeight.bold); break; case "h3": style = TextStyle(fontSize: 24, fontWeight: FontWeight.bold); break; case "body": default: style = TextStyle(fontSize: 14, fontWeight: FontWeight.normal); break; } return Text( content, textAlign: align, style: style.copyWith(color: color), ); } }',
'A text display component with various typography styles.',
'[{"title": "Heading", "code": "<Text content=\"Welcome\" variant=\"h1\" />"}]',
'{"display", "text", "typography"}', true, '1.0.0'),

(uuid_generate_v4(), 'image', 'Image', 'An image display component', 'display', 'media',
'{"properties": {"src": {"type": "string", "default": "https://via.placeholder.com/300x200"}, "alt": {"type": "string", "default": "Image"}, "width": {"type": "string", "default": "auto"}, "height": {"type": "string", "default": "auto"}, "objectFit": {"type": "string", "enum": ["contain", "cover", "fill", "none", "scale-down"], "default": "cover"}}}',
'{"src": "https://via.placeholder.com/300x200", "alt": "Image", "width": "auto", "height": "auto", "objectFit": "cover"}',
'{"borderRadius": {"type": "string", "default": "0px"}, "border": {"type": "string", "default": "none"}, "boxShadow": {"type": "string", "default": "none"}}',
'import React from "react"; export const Image = ({ src = "https://via.placeholder.com/300x200", alt = "Image", width = "auto", height = "auto", objectFit = "cover", style = {} }) => ( <img src={src} alt={alt} style={{ width, height, objectFit, borderRadius: "0px", border: "none", ...style }} /> );',
'import "package:flutter/material.dart"; class UIImage extends StatelessWidget { final String src; final String alt; final double? width; final double? height; final BoxFit fit; const UIImage({Key? key, this.src = "https://via.placeholder.com/300x200", this.alt = "Image", this.width, this.height, this.fit = BoxFit.cover}) : super(key: key); @override Widget build(BuildContext context) { return Image.network( src, width: width, height: height, fit: fit, errorBuilder: (context, error, stackTrace) { return Container( width: width, height: height, color: Colors.grey[300], child: Icon(Icons.error), ); }, ); } }',
'An image display component with various sizing and fitting options.',
'[{"title": "Basic Image", "code": "<Image src=\"/images/sample.jpg\" alt=\"Sample\" />"}]',
'{"display", "image", "media"}', true, '1.0.0'),

-- Navigation Components
(uuid_generate_v4(), 'link', 'Link', 'A navigation link component', 'navigation', 'link',
'{"properties": {"href": {"type": "string", "default": "#"}, "text": {"type": "string", "default": "Link"}, "target": {"type": "string", "enum": ["_self", "_blank", "_parent", "_top"], "default": "_self"}, "disabled": {"type": "boolean", "default": false}}}',
'{"href": "#", "text": "Link", "target": "_self", "disabled": false}',
'{"color": {"type": "string", "default": "#007bff"}, "textDecoration": {"type": "string", "default": "underline"}, "fontSize": {"type": "string", "default": "14px"}}',
'import React from "react"; export const Link = ({ href = "#", text = "Link", target = "_self", disabled = false, style = {} }) => ( <a href={disabled ? undefined : href} target={target} style={{ color: disabled ? "#ccc" : "#007bff", textDecoration: "underline", fontSize: "14px", cursor: disabled ? "not-allowed" : "pointer", ...style }} onClick={disabled ? (e) => e.preventDefault() : undefined} > {text} </a> );',
'import "package:flutter/material.dart"; import "package:url_launcher/url_launcher.dart"; class UILink extends StatelessWidget { final String href; final String text; final bool disabled; const UILink({Key? key, this.href = "#", this.text = "Link", this.disabled = false}) : super(key: key); @override Widget build(BuildContext context) { return GestureDetector( onTap: disabled ? null : () async { if (await canLaunch(href)) { await launch(href); } }, child: Text( text, style: TextStyle( color: disabled ? Colors.grey : Colors.blue, decoration: TextDecoration.underline, ), ), ); } }',
'A navigation link component for internal and external navigation.',
'[{"title": "External Link", "code": "<Link href=\"https://example.com\" text=\"Visit Example\" target=\"_blank\" />"}]',
'{"navigation", "link", "anchor"}', true, '1.0.0');

-- Insert sample organization
INSERT INTO organizations (id, name, slug, description, subscription_plan) VALUES
(uuid_generate_v4(), 'Demo Organization', 'demo-org', 'A sample organization for demonstration purposes', 'pro');

-- Insert sample admin user
INSERT INTO users (id, email, username, password_hash, first_name, last_name, email_verified, is_active) VALUES
(uuid_generate_v4(), '<EMAIL>', 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', true, true);

-- Link admin user to demo organization
INSERT INTO user_organizations (user_id, organization_id, role) 
SELECT u.id, o.id, 'admin' 
FROM users u, organizations o 
WHERE u.username = 'admin' AND o.slug = 'demo-org';

-- Insert sample workspace
INSERT INTO workspaces (id, organization_id, name, description, created_by)
SELECT uuid_generate_v4(), o.id, 'Default Workspace', 'Default workspace for getting started', u.id
FROM organizations o, users u
WHERE o.slug = 'demo-org' AND u.username = 'admin';

-- Insert sample UI configuration
INSERT INTO ui_configs (id, workspace_id, name, description, config_data, created_by)
SELECT 
    uuid_generate_v4(),
    w.id,
    'Welcome Page',
    'A sample welcome page configuration',
    '{"components": [{"id": "header", "type": "container", "props": {"padding": "20px"}, "children": [{"id": "title", "type": "text", "props": {"content": "Welcome to UI Platform", "variant": "h1", "align": "center"}}]}, {"id": "content", "type": "container", "props": {"padding": "20px"}, "children": [{"id": "description", "type": "text", "props": {"content": "Build amazing user interfaces with our drag-and-drop builder.", "variant": "body", "align": "center"}}, {"id": "cta-button", "type": "button", "props": {"text": "Get Started", "variant": "primary", "size": "large"}}]}]}',
    u.id
FROM workspaces w, users u
WHERE w.name = 'Default Workspace' AND u.username = 'admin';
