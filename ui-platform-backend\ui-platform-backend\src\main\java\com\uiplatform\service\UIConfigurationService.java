package com.uiplatform.service;

import com.uiplatform.dto.UIConfigurationDTO;
import com.uiplatform.entity.*;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.exception.DuplicateResourceException;
import com.uiplatform.exception.BusinessException;
import com.uiplatform.mapper.UIConfigurationMapper;
import com.uiplatform.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for UIConfiguration management.
 * Handles business logic for dynamic UI configuration operations.
 */
@Service
@Transactional
public class UIConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(UIConfigurationService.class);

    private final UIConfigurationRepository uiConfigurationRepository;
    private final OrganizationRepository organizationRepository;
    private final UserRepository userRepository;
    private final ThemeRepository themeRepository;
    private final LayoutRepository layoutRepository;
    private final UIConfigurationMapper uiConfigurationMapper;

    @Autowired
    public UIConfigurationService(UIConfigurationRepository uiConfigurationRepository,
                                 OrganizationRepository organizationRepository,
                                 UserRepository userRepository,
                                 ThemeRepository themeRepository,
                                 LayoutRepository layoutRepository,
                                 UIConfigurationMapper uiConfigurationMapper) {
        this.uiConfigurationRepository = uiConfigurationRepository;
        this.organizationRepository = organizationRepository;
        this.userRepository = userRepository;
        this.themeRepository = themeRepository;
        this.layoutRepository = layoutRepository;
        this.uiConfigurationMapper = uiConfigurationMapper;
    }

    /**
     * Create a new UI configuration.
     */
    public UIConfigurationDTO createUIConfiguration(UIConfigurationDTO.CreateDTO createDTO, UUID ownerId, UUID organizationId) {
        logger.info("Creating new UI configuration with slug: {} for organization: {}", 
                   createDTO.getSlug(), organizationId);

        // Validate organization exists
        Organization organization = organizationRepository.findById(organizationId)
                .filter(org -> !org.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + organizationId));

        // Validate owner exists and belongs to organization
        User owner = userRepository.findById(ownerId)
                .filter(user -> !user.getDeleted() && user.getOrganization().getId().equals(organizationId))
                .orElseThrow(() -> new ResourceNotFoundException("User not found or doesn't belong to organization"));

        // Check slug uniqueness within organization
        if (uiConfigurationRepository.existsBySlugAndOrganizationIdAndIdNot(createDTO.getSlug(), organizationId, UUID.randomUUID())) {
            throw new DuplicateResourceException("UI Configuration with slug '" + createDTO.getSlug() + "' already exists in this organization");
        }

        UIConfiguration uiConfiguration = uiConfigurationMapper.toEntity(createDTO);
        uiConfiguration.setOrganization(organization);
        uiConfiguration.setOwner(owner);

        // Set theme if provided
        if (createDTO.getThemeId() != null) {
            Theme theme = themeRepository.findById(createDTO.getThemeId())
                    .filter(t -> !t.getDeleted() && (t.getOrganization().getId().equals(organizationId) || t.getIsPublic() || t.getIsSystem()))
                    .orElseThrow(() -> new ResourceNotFoundException("Theme not found or not accessible"));
            uiConfiguration.setTheme(theme);
        }

        // Set layout if provided
        if (createDTO.getLayoutId() != null) {
            Layout layout = layoutRepository.findById(createDTO.getLayoutId())
                    .filter(l -> !l.getDeleted() && (l.getOrganization().getId().equals(organizationId) || l.getIsPublic() || l.getIsSystem()))
                    .orElseThrow(() -> new ResourceNotFoundException("Layout not found or not accessible"));
            uiConfiguration.setLayout(layout);
        }

        // Set parent if provided
        if (createDTO.getParentId() != null) {
            UIConfiguration parent = uiConfigurationRepository.findById(createDTO.getParentId())
                    .filter(p -> !p.getDeleted() && p.getOrganization().getId().equals(organizationId))
                    .orElseThrow(() -> new ResourceNotFoundException("Parent UI Configuration not found"));
            uiConfiguration.setParent(parent);
        }

        uiConfiguration = uiConfigurationRepository.save(uiConfiguration);

        logger.info("Successfully created UI configuration with ID: {}", uiConfiguration.getId());
        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Get UI configuration by ID.
     */
    @Transactional(readOnly = true)
    public UIConfigurationDTO getUIConfigurationById(UUID id) {
        logger.debug("Fetching UI configuration by ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Get UI configuration by slug and organization.
     */
    @Transactional(readOnly = true)
    public UIConfigurationDTO getUIConfigurationBySlug(String slug, UUID organizationId) {
        logger.debug("Fetching UI configuration by slug: {} for organization: {}", slug, organizationId);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findBySlugAndOrganizationIdAndDeletedFalse(slug, organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with slug: " + slug));

        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Update UI configuration.
     */
    public UIConfigurationDTO updateUIConfiguration(UUID id, UIConfigurationDTO updateDTO) {
        logger.info("Updating UI configuration with ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        // Check slug uniqueness if being updated
        if (updateDTO.getSlug() != null && !updateDTO.getSlug().equals(uiConfiguration.getSlug())) {
            if (uiConfigurationRepository.existsBySlugAndOrganizationIdAndIdNot(
                    updateDTO.getSlug(), uiConfiguration.getOrganization().getId(), id)) {
                throw new DuplicateResourceException("UI Configuration with slug '" + updateDTO.getSlug() + "' already exists in this organization");
            }
        }

        uiConfigurationMapper.updateEntityFromDTO(updateDTO, uiConfiguration);
        uiConfiguration.incrementVersion();
        uiConfiguration = uiConfigurationRepository.save(uiConfiguration);

        logger.info("Successfully updated UI configuration with ID: {}", id);
        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Delete UI configuration (soft delete).
     */
    public void deleteUIConfiguration(UUID id) {
        logger.info("Deleting UI configuration with ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        uiConfiguration.markAsDeleted("SYSTEM"); // TODO: Get current user
        uiConfigurationRepository.save(uiConfiguration);

        logger.info("Successfully deleted UI configuration with ID: {}", id);
    }

    /**
     * Get UI configurations by organization.
     */
    @Transactional(readOnly = true)
    public Page<UIConfigurationDTO> getUIConfigurationsByOrganization(UUID organizationId, Pageable pageable) {
        logger.debug("Fetching UI configurations for organization ID: {}", organizationId);

        Page<UIConfiguration> configurations = uiConfigurationRepository.findByOrganizationIdAndDeletedFalse(organizationId, pageable);
        return configurations.map(uiConfigurationMapper::toDTO);
    }

    /**
     * Get UI configurations by owner.
     */
    @Transactional(readOnly = true)
    public Page<UIConfigurationDTO> getUIConfigurationsByOwner(UUID ownerId, Pageable pageable) {
        logger.debug("Fetching UI configurations for owner ID: {}", ownerId);

        Page<UIConfiguration> configurations = uiConfigurationRepository.findByOwnerIdAndDeletedFalse(ownerId, pageable);
        return configurations.map(uiConfigurationMapper::toDTO);
    }

    /**
     * Search UI configurations by criteria.
     */
    @Transactional(readOnly = true)
    public Page<UIConfigurationDTO> searchUIConfigurations(UUID organizationId,
                                                          UIConfiguration.UIConfigurationType type,
                                                          UIConfiguration.UIConfigurationStatus status,
                                                          UUID ownerId,
                                                          Boolean isPublished,
                                                          Pageable pageable) {
        logger.debug("Searching UI configurations with criteria for organization: {}", organizationId);

        Page<UIConfiguration> configurations = uiConfigurationRepository.findByCriteria(
                organizationId, type, status, ownerId, isPublished, pageable);
        return configurations.map(uiConfigurationMapper::toDTO);
    }

    /**
     * Publish UI configuration.
     */
    public UIConfigurationDTO publishUIConfiguration(UUID id) {
        logger.info("Publishing UI configuration with ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        if (uiConfiguration.getIsPublished()) {
            throw new BusinessException("UI Configuration is already published");
        }

        uiConfiguration.publish();
        uiConfiguration = uiConfigurationRepository.save(uiConfiguration);

        logger.info("Successfully published UI configuration with ID: {}", id);
        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Unpublish UI configuration.
     */
    public UIConfigurationDTO unpublishUIConfiguration(UUID id) {
        logger.info("Unpublishing UI configuration with ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        if (!uiConfiguration.getIsPublished()) {
            throw new BusinessException("UI Configuration is not published");
        }

        uiConfiguration.unpublish();
        uiConfiguration = uiConfigurationRepository.save(uiConfiguration);

        logger.info("Successfully unpublished UI configuration with ID: {}", id);
        return uiConfigurationMapper.toDTO(uiConfiguration);
    }

    /**
     * Clone UI configuration.
     */
    public UIConfigurationDTO cloneUIConfiguration(UUID id, String newName, String newSlug, UUID ownerId) {
        logger.info("Cloning UI configuration with ID: {} to new name: {}", id, newName);

        UIConfiguration original = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        // Validate owner
        User owner = userRepository.findById(ownerId)
                .filter(user -> !user.getDeleted() && user.getOrganization().getId().equals(original.getOrganization().getId()))
                .orElseThrow(() -> new ResourceNotFoundException("User not found or doesn't belong to organization"));

        // Check slug uniqueness
        if (uiConfigurationRepository.existsBySlugAndOrganizationIdAndIdNot(
                newSlug, original.getOrganization().getId(), UUID.randomUUID())) {
            throw new DuplicateResourceException("UI Configuration with slug '" + newSlug + "' already exists in this organization");
        }

        UIConfiguration cloned = new UIConfiguration();
        cloned.setName(newName);
        cloned.setSlug(newSlug);
        cloned.setDescription("Cloned from: " + original.getName());
        cloned.setType(original.getType());
        cloned.setOrganization(original.getOrganization());
        cloned.setOwner(owner);
        cloned.setTheme(original.getTheme());
        cloned.setLayout(original.getLayout());
        cloned.setMetadata(original.getMetadata());
        cloned.setLayoutConfig(original.getLayoutConfig());
        cloned.setStyleConfig(original.getStyleConfig());
        cloned.setResponsiveConfig(original.getResponsiveConfig());
        cloned.setValidationRules(original.getValidationRules());

        cloned = uiConfigurationRepository.save(cloned);

        logger.info("Successfully cloned UI configuration with new ID: {}", cloned.getId());
        return uiConfigurationMapper.toDTO(cloned);
    }

    /**
     * Get published UI configurations.
     */
    @Transactional(readOnly = true)
    public List<UIConfigurationDTO> getPublishedUIConfigurations(UUID organizationId) {
        logger.debug("Fetching published UI configurations for organization: {}", organizationId);

        List<UIConfiguration> configurations = uiConfigurationRepository.findByOrganizationIdAndIsPublishedTrueAndDeletedFalse(organizationId);
        return configurations.stream()
                .map(uiConfigurationMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get public UI configurations.
     */
    @Transactional(readOnly = true)
    public List<UIConfigurationDTO> getPublicUIConfigurations() {
        logger.debug("Fetching public UI configurations");

        List<UIConfiguration> configurations = uiConfigurationRepository.findByIsPublicTrueAndDeletedFalse();
        return configurations.stream()
                .map(uiConfigurationMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get UI configuration with statistics.
     */
    @Transactional(readOnly = true)
    public UIConfigurationDTO getUIConfigurationWithStats(UUID id) {
        logger.debug("Fetching UI configuration with statistics for ID: {}", id);

        UIConfiguration uiConfiguration = uiConfigurationRepository.findById(id)
                .filter(config -> !config.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("UI Configuration not found with ID: " + id));

        UIConfigurationDTO dto = uiConfigurationMapper.toDTO(uiConfiguration);
        
        // Add statistics
        dto.setComponentCount(uiConfigurationRepository.countByUiConfigurationId(id));
        // TODO: Add form field count when FormFieldService is implemented
        
        return dto;
    }
}
