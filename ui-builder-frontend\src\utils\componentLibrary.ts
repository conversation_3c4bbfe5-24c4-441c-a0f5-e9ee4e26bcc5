import { ComponentType, ComponentDefinition, ComponentCategory, ComponentLibrary } from '@types/index';

// Component categories
export const componentCategories: ComponentCategory[] = [
  {
    id: 'layout',
    name: 'Layout',
    description: 'Layout and container components',
    icon: '📐',
    order: 1,
    components: ['container', 'grid', 'flex', 'stack', 'divider', 'spacer'],
  },
  {
    id: 'text',
    name: 'Text',
    description: 'Text and typography components',
    icon: '📝',
    order: 2,
    components: ['text', 'heading', 'paragraph', 'link'],
  },
  {
    id: 'form',
    name: 'Form',
    description: 'Form input and control components',
    icon: '📋',
    order: 3,
    components: ['input', 'textarea', 'select', 'checkbox', 'radio', 'switch', 'slider', 'datePicker', 'timePicker', 'fileUpload'],
  },
  {
    id: 'interactive',
    name: 'Interactive',
    description: 'Interactive and navigation components',
    icon: '🎯',
    order: 4,
    components: ['button', 'iconButton', 'dropdown', 'menu', 'tabs', 'accordion', 'modal', 'drawer', 'tooltip', 'popover'],
  },
  {
    id: 'display',
    name: 'Display',
    description: 'Display and media components',
    icon: '🖼️',
    order: 5,
    components: ['image', 'icon', 'avatar', 'badge', 'tag', 'progress', 'spinner', 'skeleton'],
  },
  {
    id: 'data',
    name: 'Data',
    description: 'Data display and visualization components',
    icon: '📊',
    order: 6,
    components: ['table', 'list', 'card', 'timeline', 'tree'],
  },
  {
    id: 'charts',
    name: 'Charts',
    description: 'Chart and visualization components',
    icon: '📈',
    order: 7,
    components: ['lineChart', 'barChart', 'pieChart', 'areaChart'],
  },
  {
    id: 'media',
    name: 'Media',
    description: 'Media and embedded content',
    icon: '🎬',
    order: 8,
    components: ['video', 'audio', 'iframe'],
  },
];

// Component definitions
export const componentDefinitions: ComponentDefinition[] = [
  // Layout Components
  {
    id: 'container',
    type: ComponentType.CONTAINER,
    name: 'container',
    displayName: 'Container',
    description: 'A flexible container for organizing content',
    icon: '📦',
    category: 'layout',
    tags: ['layout', 'container', 'wrapper'],
    defaultProperties: {
      padding: 16,
      maxWidth: 1200,
    },
    defaultStyles: {
      backgroundColor: 'transparent',
      padding: { all: 16 },
      borderRadius: 8,
    },
    defaultSize: {
      width: 'auto',
      height: 'auto',
      minHeight: 100,
    },
    propertySchema: {
      padding: {
        type: 'number',
        label: 'Padding',
        description: 'Internal spacing',
        defaultValue: 16,
        group: 'spacing',
      },
      maxWidth: {
        type: 'number',
        label: 'Max Width',
        description: 'Maximum width of the container',
        defaultValue: 1200,
        group: 'layout',
      },
    },
    styleSchema: {
      backgroundColor: {
        type: 'color',
        label: 'Background Color',
        group: 'background',
      },
      borderRadius: {
        type: 'size',
        label: 'Border Radius',
        group: 'border',
      },
    },
    constraints: {
      resizable: true,
      draggable: true,
      deletable: true,
      duplicatable: true,
    },
  },
  
  // Text Components
  {
    id: 'text',
    type: ComponentType.TEXT,
    name: 'text',
    displayName: 'Text',
    description: 'Simple text element',
    icon: '📄',
    category: 'text',
    tags: ['text', 'content', 'typography'],
    defaultProperties: {
      text: 'Sample text',
      editable: true,
    },
    defaultStyles: {
      fontSize: 14,
      color: '#1f2937',
      fontWeight: 'normal',
    },
    defaultSize: {
      width: 'auto',
      height: 'auto',
    },
    propertySchema: {
      text: {
        type: 'string',
        label: 'Text Content',
        description: 'The text to display',
        defaultValue: 'Sample text',
        required: true,
        group: 'content',
      },
      editable: {
        type: 'boolean',
        label: 'Editable',
        description: 'Allow inline editing',
        defaultValue: true,
        group: 'behavior',
      },
    },
    styleSchema: {
      fontSize: {
        type: 'size',
        label: 'Font Size',
        group: 'typography',
      },
      color: {
        type: 'color',
        label: 'Text Color',
        group: 'typography',
      },
      fontWeight: {
        type: 'typography',
        label: 'Font Weight',
        group: 'typography',
      },
    },
    constraints: {
      resizable: true,
      draggable: true,
      deletable: true,
      duplicatable: true,
    },
  },

  // Button Component
  {
    id: 'button',
    type: ComponentType.BUTTON,
    name: 'button',
    displayName: 'Button',
    description: 'Interactive button element',
    icon: '🔘',
    category: 'interactive',
    tags: ['button', 'interactive', 'action'],
    defaultProperties: {
      text: 'Button',
      variant: 'primary',
      size: 'medium',
      disabled: false,
    },
    defaultStyles: {
      backgroundColor: '#3b82f6',
      color: '#ffffff',
      borderRadius: 8,
      padding: { top: 8, right: 16, bottom: 8, left: 16 },
    },
    defaultSize: {
      width: 'auto',
      height: 40,
      minWidth: 80,
    },
    propertySchema: {
      text: {
        type: 'string',
        label: 'Button Text',
        description: 'Text displayed on the button',
        defaultValue: 'Button',
        required: true,
        group: 'content',
      },
      variant: {
        type: 'enum',
        label: 'Variant',
        description: 'Button style variant',
        defaultValue: 'primary',
        options: [
          { label: 'Primary', value: 'primary' },
          { label: 'Secondary', value: 'secondary' },
          { label: 'Outline', value: 'outline' },
          { label: 'Ghost', value: 'ghost' },
        ],
        group: 'appearance',
      },
      size: {
        type: 'enum',
        label: 'Size',
        description: 'Button size',
        defaultValue: 'medium',
        options: [
          { label: 'Small', value: 'small' },
          { label: 'Medium', value: 'medium' },
          { label: 'Large', value: 'large' },
        ],
        group: 'appearance',
      },
      disabled: {
        type: 'boolean',
        label: 'Disabled',
        description: 'Disable button interaction',
        defaultValue: false,
        group: 'behavior',
      },
    },
    styleSchema: {
      backgroundColor: {
        type: 'color',
        label: 'Background Color',
        group: 'background',
        states: ['default', 'hover', 'active'],
      },
      color: {
        type: 'color',
        label: 'Text Color',
        group: 'typography',
      },
      borderRadius: {
        type: 'size',
        label: 'Border Radius',
        group: 'border',
      },
    },
    constraints: {
      resizable: true,
      draggable: true,
      deletable: true,
      duplicatable: true,
    },
  },

  // Input Component
  {
    id: 'input',
    type: ComponentType.INPUT,
    name: 'input',
    displayName: 'Input',
    description: 'Text input field',
    icon: '📝',
    category: 'form',
    tags: ['input', 'form', 'text'],
    defaultProperties: {
      placeholder: 'Enter text...',
      value: '',
      required: false,
      disabled: false,
      type: 'text',
    },
    defaultStyles: {
      backgroundColor: '#ffffff',
      border: { width: 1, style: 'solid', color: '#d1d5db' },
      borderRadius: 8,
      padding: { top: 8, right: 12, bottom: 8, left: 12 },
    },
    defaultSize: {
      width: 200,
      height: 40,
      minWidth: 100,
    },
    propertySchema: {
      placeholder: {
        type: 'string',
        label: 'Placeholder',
        description: 'Placeholder text',
        defaultValue: 'Enter text...',
        group: 'content',
      },
      value: {
        type: 'string',
        label: 'Default Value',
        description: 'Default input value',
        defaultValue: '',
        group: 'content',
      },
      type: {
        type: 'enum',
        label: 'Input Type',
        description: 'Type of input',
        defaultValue: 'text',
        options: [
          { label: 'Text', value: 'text' },
          { label: 'Email', value: 'email' },
          { label: 'Password', value: 'password' },
          { label: 'Number', value: 'number' },
          { label: 'URL', value: 'url' },
          { label: 'Tel', value: 'tel' },
        ],
        group: 'behavior',
      },
      required: {
        type: 'boolean',
        label: 'Required',
        description: 'Mark field as required',
        defaultValue: false,
        group: 'validation',
      },
      disabled: {
        type: 'boolean',
        label: 'Disabled',
        description: 'Disable input interaction',
        defaultValue: false,
        group: 'behavior',
      },
    },
    styleSchema: {
      backgroundColor: {
        type: 'color',
        label: 'Background Color',
        group: 'background',
      },
      borderColor: {
        type: 'color',
        label: 'Border Color',
        group: 'border',
        states: ['default', 'focus', 'error'],
      },
      borderRadius: {
        type: 'size',
        label: 'Border Radius',
        group: 'border',
      },
    },
    constraints: {
      resizable: true,
      draggable: true,
      deletable: true,
      duplicatable: true,
    },
  },
];

// Create component library
export const componentLibrary: ComponentLibrary = {
  id: 'default',
  name: 'Default Component Library',
  description: 'Standard UI components for building interfaces',
  version: '1.0.0',
  categories: componentCategories,
  components: componentDefinitions,
  isPublic: true,
  createdBy: 'system',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

// Helper functions
export function getComponentDefinition(type: ComponentType): ComponentDefinition | null {
  return componentDefinitions.find(def => def.type === type) || null;
}

export function getComponentsByCategory(categoryId: string): ComponentDefinition[] {
  return componentDefinitions.filter(def => def.category === categoryId);
}

export function searchComponents(query: string): ComponentDefinition[] {
  const lowerQuery = query.toLowerCase();
  return componentDefinitions.filter(def =>
    def.displayName.toLowerCase().includes(lowerQuery) ||
    def.description.toLowerCase().includes(lowerQuery) ||
    def.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
  );
}
