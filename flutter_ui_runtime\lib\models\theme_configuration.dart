import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'theme_configuration.g.dart';

/// Theme configuration for the Flutter UI runtime
@JsonSerializable()
class ThemeConfiguration extends Equatable {
  final String id;
  final String name;
  final ColorSchemeConfiguration colorScheme;
  final TypographyConfiguration typography;
  final SpacingConfiguration spacing;
  final BorderConfiguration borders;
  final ShadowConfiguration shadows;
  final AnimationConfiguration animations;
  final Map<String, dynamic>? customProperties;

  const ThemeConfiguration({
    required this.id,
    required this.name,
    required this.colorScheme,
    required this.typography,
    required this.spacing,
    required this.borders,
    required this.shadows,
    required this.animations,
    this.customProperties,
  });

  factory ThemeConfiguration.fromJson(Map<String, dynamic> json) =>
      _$ThemeConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$ThemeConfigurationToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        colorScheme,
        typography,
        spacing,
        borders,
        shadows,
        animations,
        customProperties,
      ];

  /// Convert to Flutter ThemeData
  ThemeData toThemeData() {
    return ThemeData(
      colorScheme: colorScheme.toColorScheme(),
      textTheme: typography.toTextTheme(),
      // Additional theme properties would be configured here
    );
  }
}

/// Color scheme configuration
@JsonSerializable()
class ColorSchemeConfiguration extends Equatable {
  final String primary;
  final String? primaryVariant;
  final String secondary;
  final String? secondaryVariant;
  final String surface;
  final String background;
  final String error;
  final String onPrimary;
  final String onSecondary;
  final String onSurface;
  final String onBackground;
  final String onError;
  final Brightness brightness;

  const ColorSchemeConfiguration({
    required this.primary,
    this.primaryVariant,
    required this.secondary,
    this.secondaryVariant,
    required this.surface,
    required this.background,
    required this.error,
    required this.onPrimary,
    required this.onSecondary,
    required this.onSurface,
    required this.onBackground,
    required this.onError,
    required this.brightness,
  });

  factory ColorSchemeConfiguration.fromJson(Map<String, dynamic> json) =>
      _$ColorSchemeConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$ColorSchemeConfigurationToJson(this);

  @override
  List<Object?> get props => [
        primary,
        primaryVariant,
        secondary,
        secondaryVariant,
        surface,
        background,
        error,
        onPrimary,
        onSecondary,
        onSurface,
        onBackground,
        onError,
        brightness,
      ];

  /// Convert to Flutter ColorScheme
  ColorScheme toColorScheme() {
    return ColorScheme(
      brightness: brightness,
      primary: Color(int.parse(primary.replaceFirst('#', '0xff'))),
      onPrimary: Color(int.parse(onPrimary.replaceFirst('#', '0xff'))),
      secondary: Color(int.parse(secondary.replaceFirst('#', '0xff'))),
      onSecondary: Color(int.parse(onSecondary.replaceFirst('#', '0xff'))),
      error: Color(int.parse(error.replaceFirst('#', '0xff'))),
      onError: Color(int.parse(onError.replaceFirst('#', '0xff'))),
      surface: Color(int.parse(surface.replaceFirst('#', '0xff'))),
      onSurface: Color(int.parse(onSurface.replaceFirst('#', '0xff'))),
      background: Color(int.parse(background.replaceFirst('#', '0xff'))),
      onBackground: Color(int.parse(onBackground.replaceFirst('#', '0xff'))),
    );
  }
}

/// Typography configuration
@JsonSerializable()
class TypographyConfiguration extends Equatable {
  final FontConfiguration primaryFont;
  final FontConfiguration? secondaryFont;
  final Map<String, TextStyleConfiguration> textStyles;

  const TypographyConfiguration({
    required this.primaryFont,
    this.secondaryFont,
    required this.textStyles,
  });

  factory TypographyConfiguration.fromJson(Map<String, dynamic> json) =>
      _$TypographyConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$TypographyConfigurationToJson(this);

  @override
  List<Object?> get props => [primaryFont, secondaryFont, textStyles];

  /// Convert to Flutter TextTheme
  TextTheme toTextTheme() {
    return TextTheme(
      displayLarge: textStyles['displayLarge']?.toTextStyle(),
      displayMedium: textStyles['displayMedium']?.toTextStyle(),
      displaySmall: textStyles['displaySmall']?.toTextStyle(),
      headlineLarge: textStyles['headlineLarge']?.toTextStyle(),
      headlineMedium: textStyles['headlineMedium']?.toTextStyle(),
      headlineSmall: textStyles['headlineSmall']?.toTextStyle(),
      titleLarge: textStyles['titleLarge']?.toTextStyle(),
      titleMedium: textStyles['titleMedium']?.toTextStyle(),
      titleSmall: textStyles['titleSmall']?.toTextStyle(),
      bodyLarge: textStyles['bodyLarge']?.toTextStyle(),
      bodyMedium: textStyles['bodyMedium']?.toTextStyle(),
      bodySmall: textStyles['bodySmall']?.toTextStyle(),
      labelLarge: textStyles['labelLarge']?.toTextStyle(),
      labelMedium: textStyles['labelMedium']?.toTextStyle(),
      labelSmall: textStyles['labelSmall']?.toTextStyle(),
    );
  }
}

/// Font configuration
@JsonSerializable()
class FontConfiguration extends Equatable {
  final String family;
  final List<FontWeight> weights;
  final String? fallback;

  const FontConfiguration({
    required this.family,
    required this.weights,
    this.fallback,
  });

  factory FontConfiguration.fromJson(Map<String, dynamic> json) =>
      _$FontConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$FontConfigurationToJson(this);

  @override
  List<Object?> get props => [family, weights, fallback];
}

/// Text style configuration
@JsonSerializable()
class TextStyleConfiguration extends Equatable {
  final double fontSize;
  final FontWeight fontWeight;
  final double? letterSpacing;
  final double? lineHeight;
  final String? color;
  final String? fontFamily;

  const TextStyleConfiguration({
    required this.fontSize,
    required this.fontWeight,
    this.letterSpacing,
    this.lineHeight,
    this.color,
    this.fontFamily,
  });

  factory TextStyleConfiguration.fromJson(Map<String, dynamic> json) =>
      _$TextStyleConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$TextStyleConfigurationToJson(this);

  @override
  List<Object?> get props => [
        fontSize,
        fontWeight,
        letterSpacing,
        lineHeight,
        color,
        fontFamily,
      ];

  /// Convert to Flutter TextStyle
  TextStyle toTextStyle() {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      letterSpacing: letterSpacing,
      height: lineHeight,
      color: color != null 
          ? Color(int.parse(color!.replaceFirst('#', '0xff')))
          : null,
      fontFamily: fontFamily,
    );
  }
}

/// Spacing configuration
@JsonSerializable()
class SpacingConfiguration extends Equatable {
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;
  final double xxl;

  const SpacingConfiguration({
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
    required this.xxl,
  });

  factory SpacingConfiguration.fromJson(Map<String, dynamic> json) =>
      _$SpacingConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$SpacingConfigurationToJson(this);

  @override
  List<Object?> get props => [xs, sm, md, lg, xl, xxl];
}

/// Border configuration
@JsonSerializable()
class BorderConfiguration extends Equatable {
  final double thin;
  final double medium;
  final double thick;
  final double radiusSmall;
  final double radiusMedium;
  final double radiusLarge;
  final double radiusRound;

  const BorderConfiguration({
    required this.thin,
    required this.medium,
    required this.thick,
    required this.radiusSmall,
    required this.radiusMedium,
    required this.radiusLarge,
    required this.radiusRound,
  });

  factory BorderConfiguration.fromJson(Map<String, dynamic> json) =>
      _$BorderConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$BorderConfigurationToJson(this);

  @override
  List<Object?> get props => [
        thin,
        medium,
        thick,
        radiusSmall,
        radiusMedium,
        radiusLarge,
        radiusRound,
      ];
}

/// Shadow configuration
@JsonSerializable()
class ShadowConfiguration extends Equatable {
  final ShadowDefinition small;
  final ShadowDefinition medium;
  final ShadowDefinition large;
  final ShadowDefinition extraLarge;

  const ShadowConfiguration({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });

  factory ShadowConfiguration.fromJson(Map<String, dynamic> json) =>
      _$ShadowConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$ShadowConfigurationToJson(this);

  @override
  List<Object?> get props => [small, medium, large, extraLarge];
}

/// Shadow definition
@JsonSerializable()
class ShadowDefinition extends Equatable {
  final double offsetX;
  final double offsetY;
  final double blurRadius;
  final double spreadRadius;
  final String color;
  final double opacity;

  const ShadowDefinition({
    required this.offsetX,
    required this.offsetY,
    required this.blurRadius,
    required this.spreadRadius,
    required this.color,
    required this.opacity,
  });

  factory ShadowDefinition.fromJson(Map<String, dynamic> json) =>
      _$ShadowDefinitionFromJson(json);

  Map<String, dynamic> toJson() => _$ShadowDefinitionToJson(this);

  @override
  List<Object?> get props => [
        offsetX,
        offsetY,
        blurRadius,
        spreadRadius,
        color,
        opacity,
      ];

  /// Convert to Flutter BoxShadow
  BoxShadow toBoxShadow() {
    return BoxShadow(
      offset: Offset(offsetX, offsetY),
      blurRadius: blurRadius,
      spreadRadius: spreadRadius,
      color: Color(int.parse(color.replaceFirst('#', '0xff')))
          .withOpacity(opacity),
    );
  }
}

/// Animation configuration
@JsonSerializable()
class AnimationConfiguration extends Equatable {
  final Duration fast;
  final Duration normal;
  final Duration slow;
  final Curve defaultCurve;

  const AnimationConfiguration({
    required this.fast,
    required this.normal,
    required this.slow,
    required this.defaultCurve,
  });

  factory AnimationConfiguration.fromJson(Map<String, dynamic> json) =>
      _$AnimationConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$AnimationConfigurationToJson(this);

  @override
  List<Object?> get props => [fast, normal, slow, defaultCurve];
}
