# UI Builder Component Library

A comprehensive, cross-platform design system and component library that provides consistent UI components across web (React) and mobile (Flutter) platforms. Built for the UI Builder platform with enterprise-grade quality, accessibility, and performance.

## 🚀 Features

### ✨ **Cross-Platform Consistency**
- **React Components**: TypeScript-based components for web applications
- **Flutter Widgets**: Dart-based widgets for mobile applications  
- **Unified API**: Consistent component APIs across platforms
- **Design Tokens**: Shared design values using Style Dictionary

### 🎨 **Comprehensive Design System**
- **50+ Components**: Foundation, layout, input, display, navigation, and feedback
- **Dynamic Theming**: Light/dark mode with custom theme support
- **Responsive Design**: Mobile-first approach with breakpoint system
- **Accessibility**: WCAG 2.1 AA compliant with comprehensive a11y testing

### 🛠️ **Developer Experience**
- **TypeScript**: Full type safety and IntelliSense support
- **Storybook**: Interactive component documentation and playground
- **Testing Utilities**: Comprehensive testing helpers and utilities
- **Internationalization**: Multi-language support with RTL languages

### 🏗️ **Enterprise Ready**
- **Monorepo Architecture**: Nx-powered workspace for scalability
- **CI/CD Pipeline**: Automated testing, building, and deployment
- **Performance Optimized**: Tree-shaking, code splitting, and bundle analysis
- **Security**: Regular security audits and vulnerability scanning

## 📦 Packages

| Package | Description | Platform | Version |
|---------|-------------|----------|---------|
| [`@ui-builder/design-tokens`](./packages/design-tokens) | Design tokens and theme definitions | Universal | [![npm](https://img.shields.io/npm/v/@ui-builder/design-tokens)](https://www.npmjs.com/package/@ui-builder/design-tokens) |
| [`@ui-builder/react-components`](./packages/react-components) | React component library | Web | [![npm](https://img.shields.io/npm/v/@ui-builder/react-components)](https://www.npmjs.com/package/@ui-builder/react-components) |
| [`ui_builder_flutter_components`](./packages/flutter-components) | Flutter widget library | Mobile | [![pub](https://img.shields.io/pub/v/ui_builder_flutter_components)](https://pub.dev/packages/ui_builder_flutter_components) |
| [`@ui-builder/icons`](./packages/icons) | Icon library with React and Flutter support | Universal | [![npm](https://img.shields.io/npm/v/@ui-builder/icons)](https://www.npmjs.com/package/@ui-builder/icons) |
| [`@ui-builder/testing-utilities`](./packages/testing-utilities) | Testing helpers and utilities | Web | [![npm](https://img.shields.io/npm/v/@ui-builder/testing-utilities)](https://www.npmjs.com/package/@ui-builder/testing-utilities) |

## 🚀 Quick Start

### React (Web)

```bash
npm install @ui-builder/react-components @ui-builder/design-tokens
```

```tsx
import { Button, ThemeProvider } from '@ui-builder/react-components';
import '@ui-builder/react-components/dist/styles.css';

function App() {
  return (
    <ThemeProvider>
      <Button variant="primary" size="md">
        Hello World
      </Button>
    </ThemeProvider>
  );
}
```

### Flutter (Mobile)

```yaml
dependencies:
  ui_builder_flutter_components: ^1.0.0
```

```dart
import 'package:ui_builder_flutter_components/ui_builder_flutter_components.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UIThemeProvider(
      child: MaterialApp(
        home: Scaffold(
          body: UIButton(
            variant: UIButtonVariant.primary,
            size: UISize.md,
            onPressed: () => print('Hello World'),
            child: Text('Hello World'),
          ),
        ),
      ),
    );
  }
}
```

## 📚 Documentation

- **[Storybook](https://components.uibuilder.dev)** - Interactive component documentation
- **[Design System Guide](https://docs.uibuilder.dev)** - Complete design system documentation
- **[API Reference](https://docs.uibuilder.dev/api)** - Detailed API documentation
- **[Migration Guide](https://docs.uibuilder.dev/migration)** - Upgrade and migration instructions

## 🎨 Component Categories

### Foundation Components
- **Typography**: Headings, body text, captions with consistent hierarchy
- **Colors**: Primary, secondary, semantic colors with dark mode support
- **Spacing**: Consistent spacing scale and layout utilities
- **Icons**: 200+ optimized SVG icons with consistent sizing

### Layout Components
- **Container**: Responsive containers with max-width constraints
- **Grid**: Flexible grid system with responsive breakpoints
- **Flex**: Flexbox utilities with gap support
- **Stack**: Vertical and horizontal stacking with spacing
- **Card**: Content containers with elevation and borders
- **Divider**: Visual separators with customizable styles

### Input Components
- **Button**: Primary, secondary, outline, ghost, and icon variants
- **Input**: Text inputs with validation, icons, and floating labels
- **Textarea**: Multi-line text inputs with auto-resize
- **Select**: Dropdown selects with search and multi-select
- **Checkbox**: Single and group checkboxes with indeterminate state
- **Radio**: Radio buttons with group management
- **Switch**: Toggle switches with labels and descriptions
- **Slider**: Range sliders with marks and custom formatting
- **DatePicker**: Date and time selection with localization
- **FileUpload**: Drag-and-drop file uploads with progress

### Display Components
- **Avatar**: User avatars with fallbacks and status indicators
- **Badge**: Status badges with variants and positioning
- **Chip**: Interactive chips with actions and removal
- **Progress**: Linear and circular progress indicators
- **Skeleton**: Loading placeholders with animation
- **Spinner**: Loading spinners with size variants
- **Table**: Data tables with sorting, filtering, and pagination
- **Image**: Optimized images with lazy loading and fallbacks

### Navigation Components
- **Breadcrumb**: Navigation breadcrumbs with separators
- **Tabs**: Horizontal and vertical tabs with lazy loading
- **Pagination**: Page navigation with customizable ranges
- **Menu**: Dropdown and context menus with keyboard navigation
- **Sidebar**: Collapsible sidebars with navigation items

### Feedback Components
- **Alert**: Contextual alerts with actions and dismissal
- **Toast**: Temporary notifications with positioning
- **Modal**: Overlay modals with focus management
- **Tooltip**: Contextual tooltips with positioning
- **Popover**: Rich popovers with custom content
- **Dialog**: Confirmation dialogs with actions

## 🌍 Internationalization

The component library supports internationalization with:

- **12 Languages**: English, Spanish, French, German, Italian, Portuguese, Japanese, Korean, Chinese, Arabic, Hebrew, Russian
- **RTL Support**: Right-to-left languages with automatic layout adjustment
- **Date/Time Formatting**: Locale-specific formatting with Intl API
- **Number Formatting**: Currency and number formatting per locale
- **Pluralization**: Smart pluralization rules for different languages

```tsx
import { I18nProvider } from '@ui-builder/react-components';

<I18nProvider locale="es" messages={spanishMessages}>
  <App />
</I18nProvider>
```

## 🎯 Accessibility

All components are built with accessibility in mind:

- **WCAG 2.1 AA Compliance**: Meets international accessibility standards
- **Keyboard Navigation**: Full keyboard support with focus management
- **Screen Reader Support**: Proper ARIA labels and semantic markup
- **Color Contrast**: Sufficient contrast ratios for all text and UI elements
- **Focus Management**: Logical focus order and visible focus indicators
- **Reduced Motion**: Respects user's motion preferences

## 🧪 Testing

Comprehensive testing utilities and examples:

```tsx
import { render, screen } from '@ui-builder/testing-utilities';
import { Button } from '@ui-builder/react-components';

test('button renders correctly', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByRole('button')).toBeInTheDocument();
});
```

### Testing Features
- **Custom Render**: Pre-configured render with providers
- **Accessibility Testing**: Automated a11y testing with jest-axe
- **Visual Regression**: Chromatic integration for visual testing
- **User Interactions**: Realistic user interaction testing
- **Mock Utilities**: Pre-built mocks for common scenarios

## 🏗️ Development

### Prerequisites
- Node.js 18+
- npm 9+
- Flutter 3.16+ (for Flutter components)

### Setup
```bash
# Clone the repository
git clone https://github.com/ui-builder/component-library.git
cd component-library

# Install dependencies
npm install

# Build design tokens
npm run tokens:build

# Start development
npm run dev
```

### Available Scripts
```bash
# Development
npm run dev              # Start development mode
npm run storybook        # Start Storybook

# Building
npm run build            # Build all packages
npm run tokens:build     # Build design tokens
npm run react:build      # Build React components
npm run flutter:build    # Build Flutter components

# Testing
npm run test             # Run all tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
npm run test:a11y        # Run accessibility tests

# Linting & Formatting
npm run lint             # Lint all packages
npm run format           # Format all files
npm run type-check       # TypeScript type checking
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Run the test suite
6. Submit a pull request

### Code Standards
- **TypeScript**: Strict mode with comprehensive types
- **ESLint**: Airbnb configuration with accessibility rules
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Semantic commit messages
- **Testing**: Minimum 90% code coverage

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.uibuilder.dev](https://docs.uibuilder.dev)
- **GitHub Issues**: [Report bugs and request features](https://github.com/ui-builder/component-library/issues)
- **Discord**: [Join our community](https://discord.gg/uibuilder)
- **Email**: [<EMAIL>](mailto:<EMAIL>)

## 🙏 Acknowledgments

Built with ❤️ by the UI Builder team and powered by:

- [React](https://reactjs.org/) - UI library for web
- [Flutter](https://flutter.dev/) - UI framework for mobile
- [Style Dictionary](https://amzn.github.io/style-dictionary/) - Design token management
- [Storybook](https://storybook.js.org/) - Component documentation
- [Nx](https://nx.dev/) - Monorepo tooling
- [Radix UI](https://www.radix-ui.com/) - Accessible component primitives

---

**[🌟 Star us on GitHub](https://github.com/ui-builder/component-library)** if you find this project useful!
