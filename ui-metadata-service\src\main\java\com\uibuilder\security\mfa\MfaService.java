package com.uibuilder.security.mfa;

import com.uibuilder.entity.User;
import com.uibuilder.entity.MfaDevice;
import com.uibuilder.repository.MfaDeviceRepository;
import com.uibuilder.security.audit.AuditService;
import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import com.warrenstrange.googleauth.GoogleAuthenticatorQRGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class MfaService {

    private final MfaDeviceRepository mfaDeviceRepository;
    private final GoogleAuthenticator googleAuthenticator;
    private final AuditService auditService;
    private final MfaProperties mfaProperties;
    private final EmailService emailService;
    private final SmsService smsService;

    @Transactional
    public MfaSetupResponse setupTotp(User user, String deviceName) {
        try {
            // Generate new secret key
            GoogleAuthenticatorKey key = googleAuthenticator.createCredentials();
            String secretKey = key.getKey();
            
            // Encrypt the secret key before storing
            String encryptedSecret = encryptSecret(secretKey);
            
            // Create MFA device record
            MfaDevice device = MfaDevice.builder()
                .user(user)
                .deviceName(deviceName)
                .deviceType(MfaDeviceType.TOTP)
                .secretKey(encryptedSecret)
                .isActive(false)
                .createdAt(LocalDateTime.now())
                .build();
            
            mfaDeviceRepository.save(device);
            
            // Generate QR code URL
            String qrCodeUrl = GoogleAuthenticatorQRGenerator.getOtpAuthURL(
                mfaProperties.getIssuer(),
                user.getEmail(),
                key
            );
            
            // Audit log
            auditService.logSecurityEvent(
                user.getId(),
                "MFA_SETUP_INITIATED",
                "TOTP device setup initiated",
                Map.of("deviceName", deviceName)
            );
            
            return MfaSetupResponse.builder()
                .deviceId(device.getId())
                .secretKey(secretKey)
                .qrCodeUrl(qrCodeUrl)
                .backupCodes(generateBackupCodes(user))
                .build();
                
        } catch (Exception e) {
            log.error("Failed to setup TOTP for user: {}", user.getId(), e);
            throw new MfaException("Failed to setup TOTP device", e);
        }
    }

    @Transactional
    public void confirmTotpSetup(User user, String deviceId, String verificationCode) {
        MfaDevice device = mfaDeviceRepository.findByIdAndUser(deviceId, user)
            .orElseThrow(() -> new MfaException("MFA device not found"));
        
        if (device.isActive()) {
            throw new MfaException("MFA device is already active");
        }
        
        // Verify the code
        String decryptedSecret = decryptSecret(device.getSecretKey());
        boolean isValid = googleAuthenticator.authorize(decryptedSecret, Integer.parseInt(verificationCode));
        
        if (!isValid) {
            auditService.logSecurityEvent(
                user.getId(),
                "MFA_SETUP_FAILED",
                "Invalid verification code during TOTP setup",
                Map.of("deviceId", deviceId)
            );
            throw new MfaException("Invalid verification code");
        }
        
        // Activate the device
        device.setActive(true);
        device.setActivatedAt(LocalDateTime.now());
        mfaDeviceRepository.save(device);
        
        // Update user MFA status
        user.setMfaEnabled(true);
        userRepository.save(user);
        
        auditService.logSecurityEvent(
            user.getId(),
            "MFA_ENABLED",
            "TOTP device activated successfully",
            Map.of("deviceId", deviceId, "deviceName", device.getDeviceName())
        );
    }

    public boolean verifyTotp(User user, String code) {
        List<MfaDevice> activeDevices = mfaDeviceRepository.findByUserAndDeviceTypeAndIsActive(
            user, MfaDeviceType.TOTP, true);
        
        for (MfaDevice device : activeDevices) {
            try {
                String decryptedSecret = decryptSecret(device.getSecretKey());
                boolean isValid = googleAuthenticator.authorize(decryptedSecret, Integer.parseInt(code));
                
                if (isValid) {
                    // Update last used timestamp
                    device.setLastUsedAt(LocalDateTime.now());
                    mfaDeviceRepository.save(device);
                    
                    auditService.logSecurityEvent(
                        user.getId(),
                        "MFA_VERIFICATION_SUCCESS",
                        "TOTP verification successful",
                        Map.of("deviceId", device.getId())
                    );
                    
                    return true;
                }
            } catch (Exception e) {
                log.warn("Failed to verify TOTP for device: {}", device.getId(), e);
            }
        }
        
        auditService.logSecurityEvent(
            user.getId(),
            "MFA_VERIFICATION_FAILED",
            "TOTP verification failed",
            Map.of("code", code.replaceAll("\\d", "*"))
        );
        
        return false;
    }

    @Transactional
    public void setupSms(User user, String phoneNumber) {
        // Validate phone number format
        if (!isValidPhoneNumber(phoneNumber)) {
            throw new MfaException("Invalid phone number format");
        }
        
        // Generate verification code
        String verificationCode = generateSmsCode();
        
        // Store temporary verification record
        MfaVerification verification = MfaVerification.builder()
            .user(user)
            .phoneNumber(phoneNumber)
            .code(verificationCode)
            .expiresAt(LocalDateTime.now().plusMinutes(5))
            .attempts(0)
            .build();
        
        mfaVerificationRepository.save(verification);
        
        // Send SMS
        smsService.sendVerificationCode(phoneNumber, verificationCode);
        
        auditService.logSecurityEvent(
            user.getId(),
            "SMS_MFA_SETUP_INITIATED",
            "SMS MFA setup initiated",
            Map.of("phoneNumber", maskPhoneNumber(phoneNumber))
        );
    }

    @Transactional
    public void confirmSmsSetup(User user, String phoneNumber, String verificationCode) {
        MfaVerification verification = mfaVerificationRepository
            .findByUserAndPhoneNumberAndExpiresAtAfter(user, phoneNumber, LocalDateTime.now())
            .orElseThrow(() -> new MfaException("Verification code expired or not found"));
        
        // Check attempt limit
        if (verification.getAttempts() >= mfaProperties.getMaxVerificationAttempts()) {
            throw new MfaException("Maximum verification attempts exceeded");
        }
        
        verification.setAttempts(verification.getAttempts() + 1);
        mfaVerificationRepository.save(verification);
        
        if (!verification.getCode().equals(verificationCode)) {
            auditService.logSecurityEvent(
                user.getId(),
                "SMS_MFA_SETUP_FAILED",
                "Invalid SMS verification code",
                Map.of("phoneNumber", maskPhoneNumber(phoneNumber), "attempts", verification.getAttempts())
            );
            throw new MfaException("Invalid verification code");
        }
        
        // Create SMS MFA device
        MfaDevice device = MfaDevice.builder()
            .user(user)
            .deviceName("SMS: " + maskPhoneNumber(phoneNumber))
            .deviceType(MfaDeviceType.SMS)
            .phoneNumber(phoneNumber)
            .isActive(true)
            .createdAt(LocalDateTime.now())
            .activatedAt(LocalDateTime.now())
            .build();
        
        mfaDeviceRepository.save(device);
        
        // Clean up verification record
        mfaVerificationRepository.delete(verification);
        
        // Update user MFA status
        user.setMfaEnabled(true);
        userRepository.save(user);
        
        auditService.logSecurityEvent(
            user.getId(),
            "SMS_MFA_ENABLED",
            "SMS MFA device activated successfully",
            Map.of("phoneNumber", maskPhoneNumber(phoneNumber))
        );
    }

    public void sendSmsCode(User user) {
        List<MfaDevice> smsDevices = mfaDeviceRepository.findByUserAndDeviceTypeAndIsActive(
            user, MfaDeviceType.SMS, true);
        
        if (smsDevices.isEmpty()) {
            throw new MfaException("No active SMS MFA devices found");
        }
        
        MfaDevice device = smsDevices.get(0); // Use first active SMS device
        String verificationCode = generateSmsCode();
        
        // Store verification code temporarily
        MfaVerification verification = MfaVerification.builder()
            .user(user)
            .phoneNumber(device.getPhoneNumber())
            .code(verificationCode)
            .expiresAt(LocalDateTime.now().plusMinutes(5))
            .attempts(0)
            .build();
        
        mfaVerificationRepository.save(verification);
        
        // Send SMS
        smsService.sendVerificationCode(device.getPhoneNumber(), verificationCode);
        
        auditService.logSecurityEvent(
            user.getId(),
            "SMS_MFA_CODE_SENT",
            "SMS MFA verification code sent",
            Map.of("deviceId", device.getId())
        );
    }

    public boolean verifySmsCode(User user, String code) {
        List<MfaDevice> smsDevices = mfaDeviceRepository.findByUserAndDeviceTypeAndIsActive(
            user, MfaDeviceType.SMS, true);
        
        for (MfaDevice device : smsDevices) {
            Optional<MfaVerification> verificationOpt = mfaVerificationRepository
                .findByUserAndPhoneNumberAndExpiresAtAfter(
                    user, device.getPhoneNumber(), LocalDateTime.now());
            
            if (verificationOpt.isPresent()) {
                MfaVerification verification = verificationOpt.get();
                
                if (verification.getCode().equals(code)) {
                    // Update device last used
                    device.setLastUsedAt(LocalDateTime.now());
                    mfaDeviceRepository.save(device);
                    
                    // Clean up verification
                    mfaVerificationRepository.delete(verification);
                    
                    auditService.logSecurityEvent(
                        user.getId(),
                        "SMS_MFA_VERIFICATION_SUCCESS",
                        "SMS MFA verification successful",
                        Map.of("deviceId", device.getId())
                    );
                    
                    return true;
                }
            }
        }
        
        auditService.logSecurityEvent(
            user.getId(),
            "SMS_MFA_VERIFICATION_FAILED",
            "SMS MFA verification failed",
            Map.of("code", code.replaceAll("\\d", "*"))
        );
        
        return false;
    }

    private List<String> generateBackupCodes(User user) {
        List<String> backupCodes = new ArrayList<>();
        SecureRandom random = new SecureRandom();
        
        for (int i = 0; i < 10; i++) {
            String code = String.format("%08d", random.nextInt(100000000));
            backupCodes.add(code);
            
            // Store encrypted backup code
            MfaBackupCode backupCode = MfaBackupCode.builder()
                .user(user)
                .code(hashBackupCode(code))
                .isUsed(false)
                .createdAt(LocalDateTime.now())
                .build();
            
            mfaBackupCodeRepository.save(backupCode);
        }
        
        return backupCodes;
    }

    private String encryptSecret(String secret) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        SecretKeySpec keySpec = new SecretKeySpec(
            mfaProperties.getEncryptionKey().getBytes(), "AES");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encrypted = cipher.doFinal(secret.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    private String decryptSecret(String encryptedSecret) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        SecretKeySpec keySpec = new SecretKeySpec(
            mfaProperties.getEncryptionKey().getBytes(), "AES");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedSecret));
        return new String(decrypted);
    }

    private String generateSmsCode() {
        SecureRandom random = new SecureRandom();
        return String.format("%06d", random.nextInt(1000000));
    }

    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber.length() <= 4) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + 
               phoneNumber.substring(phoneNumber.length() - 2);
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber.matches("^\\+?[1-9]\\d{1,14}$");
    }

    private String hashBackupCode(String code) {
        return BCrypt.hashpw(code, BCrypt.gensalt(12));
    }
}
