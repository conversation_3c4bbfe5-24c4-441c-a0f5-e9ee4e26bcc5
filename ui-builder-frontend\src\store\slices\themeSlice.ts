import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Theme } from '@types/index';

interface ThemeState {
  theme: Theme;
  isDarkMode: boolean;
  customThemes: Theme[];
  activeThemeId: string;
}

const defaultTheme: Theme = {
  id: 'default',
  name: 'Default Theme',
  description: 'Default UI Builder theme',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
    },
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981',
      600: '#059669',
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    background: {
      primary: '#ffffff',
      secondary: '#f8fafc',
      tertiary: '#f1f5f9',
    },
    text: {
      primary: '#1f2937',
      secondary: '#6b7280',
      disabled: '#9ca3af',
      inverse: '#ffffff',
    },
  },
  typography: {
    fontFamilies: {
      primary: 'Inter, system-ui, sans-serif',
      secondary: 'Inter, system-ui, sans-serif',
      monospace: 'JetBrains Mono, Monaco, Consolas, monospace',
    },
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
    },
    fontWeights: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2,
    },
    letterSpacing: {
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '2.5rem',
    '3xl': '3rem',
    '4xl': '4rem',
    '5xl': '5rem',
    '6xl': '6rem',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
  },
  borders: {
    none: '0',
    sm: '1px',
    md: '2px',
    lg: '4px',
    xl: '8px',
  },
  animations: {
    durations: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easings: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

const initialState: ThemeState = {
  theme: defaultTheme,
  isDarkMode: false,
  customThemes: [],
  activeThemeId: 'default',
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Theme>) => {
      state.theme = action.payload;
      state.activeThemeId = action.payload.id;
    },
    
    toggleDarkMode: (state) => {
      state.isDarkMode = !state.isDarkMode;
      
      // Update theme colors for dark mode
      if (state.isDarkMode) {
        state.theme.colors.background = {
          primary: '#1f2937',
          secondary: '#111827',
          tertiary: '#0f172a',
        };
        state.theme.colors.text = {
          primary: '#f9fafb',
          secondary: '#d1d5db',
          disabled: '#6b7280',
          inverse: '#1f2937',
        };
      } else {
        state.theme.colors.background = {
          primary: '#ffffff',
          secondary: '#f8fafc',
          tertiary: '#f1f5f9',
        };
        state.theme.colors.text = {
          primary: '#1f2937',
          secondary: '#6b7280',
          disabled: '#9ca3af',
          inverse: '#ffffff',
        };
      }
    },
    
    updateThemeColors: (state, action: PayloadAction<Partial<typeof defaultTheme.colors>>) => {
      state.theme.colors = { ...state.theme.colors, ...action.payload };
    },
    
    updateThemeTypography: (state, action: PayloadAction<Partial<typeof defaultTheme.typography>>) => {
      state.theme.typography = { ...state.theme.typography, ...action.payload };
    },
    
    updateThemeSpacing: (state, action: PayloadAction<Partial<typeof defaultTheme.spacing>>) => {
      state.theme.spacing = { ...state.theme.spacing, ...action.payload };
    },
    
    addCustomTheme: (state, action: PayloadAction<Theme>) => {
      const existingIndex = state.customThemes.findIndex(t => t.id === action.payload.id);
      if (existingIndex >= 0) {
        state.customThemes[existingIndex] = action.payload;
      } else {
        state.customThemes.push(action.payload);
      }
    },
    
    removeCustomTheme: (state, action: PayloadAction<string>) => {
      state.customThemes = state.customThemes.filter(t => t.id !== action.payload);
      
      // If the removed theme was active, switch to default
      if (state.activeThemeId === action.payload) {
        state.theme = defaultTheme;
        state.activeThemeId = 'default';
      }
    },
    
    resetTheme: (state) => {
      state.theme = defaultTheme;
      state.activeThemeId = 'default';
      state.isDarkMode = false;
    },
  },
});

export const {
  setTheme,
  toggleDarkMode,
  updateThemeColors,
  updateThemeTypography,
  updateThemeSpacing,
  addCustomTheme,
  removeCustomTheme,
  resetTheme,
} = themeSlice.actions;

export default themeSlice.reducer;
