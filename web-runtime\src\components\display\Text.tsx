import React from 'react';
import { cn } from '@utils/cn';
import { ComponentProps } from '@types/index';

interface TextProps extends ComponentProps {
  content: string;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'overline';
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  color?: string;
  align?: 'left' | 'center' | 'right' | 'justify';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  decoration?: 'none' | 'underline' | 'overline' | 'line-through';
  truncate?: boolean;
  wrap?: boolean;
}

const variantClasses = {
  h1: 'text-4xl font-bold',
  h2: 'text-3xl font-bold',
  h3: 'text-2xl font-semibold',
  h4: 'text-xl font-semibold',
  h5: 'text-lg font-medium',
  h6: 'text-base font-medium',
  body: 'text-base',
  caption: 'text-sm text-surface-600',
  overline: 'text-xs uppercase tracking-wide text-surface-500',
};

const sizeClasses = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
  '2xl': 'text-2xl',
  '3xl': 'text-3xl',
  '4xl': 'text-4xl',
  '5xl': 'text-5xl',
  '6xl': 'text-6xl',
};

const weightClasses = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
};

const alignClasses = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify',
};

const transformClasses = {
  none: '',
  uppercase: 'uppercase',
  lowercase: 'lowercase',
  capitalize: 'capitalize',
};

const decorationClasses = {
  none: 'no-underline',
  underline: 'underline',
  overline: 'overline',
  'line-through': 'line-through',
};

const getElementType = (variant: TextProps['variant']) => {
  switch (variant) {
    case 'h1':
    case 'h2':
    case 'h3':
    case 'h4':
    case 'h5':
    case 'h6':
      return variant;
    case 'caption':
    case 'overline':
      return 'span';
    default:
      return 'p';
  }
};

const Text: React.FC<TextProps> = ({
  content,
  variant = 'body',
  size,
  weight,
  color,
  align = 'left',
  transform = 'none',
  decoration = 'none',
  truncate = false,
  wrap = true,
  className,
  style,
  ...props
}) => {
  const Element = getElementType(variant) as keyof JSX.IntrinsicElements;

  const computedStyle = {
    ...style,
    ...(color && { color }),
  };

  return (
    <Element
      className={cn(
        variantClasses[variant],
        size && sizeClasses[size],
        weight && weightClasses[weight],
        alignClasses[align],
        transformClasses[transform],
        decorationClasses[decoration],
        truncate && 'truncate',
        !wrap && 'whitespace-nowrap',
        className
      )}
      style={computedStyle}
      {...props}
    >
      {content}
    </Element>
  );
};

export default Text;
