package com.uiplatform.repository;

import com.uiplatform.entity.ActivityLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for ActivityLog entity operations.
 */
@Repository
public interface ActivityLogRepository extends JpaRepository<ActivityLog, UUID> {

    /**
     * Find activity logs for a specific UI configuration.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.uiConfigurationId = :configId ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByUIConfigurationId(@Param("configId") UUID configId, Pageable pageable);

    /**
     * Find activity logs for an organization.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.organizationId = :orgId ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByOrganizationId(@Param("orgId") UUID organizationId, Pageable pageable);

    /**
     * Find activity logs by user.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.userId = :userId ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByUserId(@Param("userId") UUID userId, Pageable pageable);

    /**
     * Find activity logs by activity type.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.activityType = :type ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByActivityType(@Param("type") ActivityLog.ActivityType activityType, Pageable pageable);

    /**
     * Find activity logs by severity.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.severity = :severity ORDER BY a.timestamp DESC")
    Page<ActivityLog> findBySeverity(@Param("severity") ActivityLog.Severity severity, Pageable pageable);

    /**
     * Find activity logs within a time range.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.timestamp BETWEEN :startTime AND :endTime ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByTimestampBetween(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime, 
                                           Pageable pageable);

    /**
     * Find recent activity logs for an organization.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.organizationId = :orgId AND a.timestamp > :since ORDER BY a.timestamp DESC")
    List<ActivityLog> findRecentActivityByOrganization(@Param("orgId") UUID organizationId, 
                                                      @Param("since") LocalDateTime since);

    /**
     * Find recent activity logs for a UI configuration.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.uiConfigurationId = :configId AND a.timestamp > :since ORDER BY a.timestamp DESC")
    List<ActivityLog> findRecentActivityByUIConfiguration(@Param("configId") UUID configId, 
                                                         @Param("since") LocalDateTime since);

    /**
     * Find activity logs by user and organization.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.userId = :userId AND a.organizationId = :orgId ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByUserIdAndOrganizationId(@Param("userId") UUID userId, 
                                                   @Param("orgId") UUID organizationId, 
                                                   Pageable pageable);

    /**
     * Find activity logs by entity type and ID.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.entityType = :entityType AND a.entityId = :entityId ORDER BY a.timestamp DESC")
    List<ActivityLog> findByEntityTypeAndEntityId(@Param("entityType") String entityType, 
                                                 @Param("entityId") String entityId);

    /**
     * Find activity logs by action.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.action = :action ORDER BY a.timestamp DESC")
    Page<ActivityLog> findByAction(@Param("action") String action, Pageable pageable);

    /**
     * Search activity logs by description.
     */
    @Query("SELECT a FROM ActivityLog a WHERE LOWER(a.description) LIKE LOWER(CONCAT('%', :searchText, '%')) ORDER BY a.timestamp DESC")
    Page<ActivityLog> searchByDescription(@Param("searchText") String searchText, Pageable pageable);

    /**
     * Find activity logs by tags.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.tags LIKE %:tag% ORDER BY a.timestamp DESC")
    List<ActivityLog> findByTagsContaining(@Param("tag") String tag);

    /**
     * Find system activities.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.isSystemAction = true ORDER BY a.timestamp DESC")
    Page<ActivityLog> findSystemActivities(Pageable pageable);

    /**
     * Find user activities (non-system).
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.isSystemAction = false ORDER BY a.timestamp DESC")
    Page<ActivityLog> findUserActivities(Pageable pageable);

    /**
     * Count activities by type for an organization.
     */
    @Query("SELECT a.activityType, COUNT(a) FROM ActivityLog a WHERE a.organizationId = :orgId GROUP BY a.activityType")
    List<Object[]> countActivitiesByTypeForOrganization(@Param("orgId") UUID organizationId);

    /**
     * Count activities by user for an organization.
     */
    @Query("SELECT a.userId, a.username, COUNT(a) FROM ActivityLog a WHERE a.organizationId = :orgId GROUP BY a.userId, a.username ORDER BY COUNT(a) DESC")
    List<Object[]> countActivitiesByUserForOrganization(@Param("orgId") UUID organizationId);

    /**
     * Get activity statistics for a time period.
     */
    @Query("SELECT " +
           "COUNT(a) as totalActivities, " +
           "COUNT(DISTINCT a.userId) as uniqueUsers, " +
           "COUNT(DISTINCT a.uiConfigurationId) as uniqueConfigurations, " +
           "AVG(a.durationMs) as averageDuration " +
           "FROM ActivityLog a WHERE a.organizationId = :orgId AND a.timestamp BETWEEN :startTime AND :endTime")
    ActivityStatistics getActivityStatistics(@Param("orgId") UUID organizationId,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * Find activities with performance issues (long duration).
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.durationMs > :thresholdMs ORDER BY a.durationMs DESC")
    List<ActivityLog> findSlowActivities(@Param("thresholdMs") Long thresholdMs);

    /**
     * Find error activities.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.severity IN ('ERROR', 'CRITICAL') ORDER BY a.timestamp DESC")
    Page<ActivityLog> findErrorActivities(Pageable pageable);

    /**
     * Find activities by IP address.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.ipAddress = :ipAddress ORDER BY a.timestamp DESC")
    List<ActivityLog> findByIpAddress(@Param("ipAddress") String ipAddress);

    /**
     * Find activities by session ID.
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.sessionId = :sessionId ORDER BY a.timestamp ASC")
    List<ActivityLog> findBySessionId(@Param("sessionId") String sessionId);

    /**
     * Get daily activity counts for a time period.
     */
    @Query("SELECT DATE(a.timestamp) as date, COUNT(a) as count " +
           "FROM ActivityLog a WHERE a.organizationId = :orgId AND a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(a.timestamp) ORDER BY DATE(a.timestamp)")
    List<Object[]> getDailyActivityCounts(@Param("orgId") UUID organizationId,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * Get hourly activity counts for a day.
     */
    @Query("SELECT HOUR(a.timestamp) as hour, COUNT(a) as count " +
           "FROM ActivityLog a WHERE a.organizationId = :orgId AND DATE(a.timestamp) = DATE(:date) " +
           "GROUP BY HOUR(a.timestamp) ORDER BY HOUR(a.timestamp)")
    List<Object[]> getHourlyActivityCounts(@Param("orgId") UUID organizationId,
                                          @Param("date") LocalDateTime date);

    /**
     * Find most active users in a time period.
     */
    @Query("SELECT a.userId, a.username, COUNT(a) as activityCount " +
           "FROM ActivityLog a WHERE a.organizationId = :orgId AND a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY a.userId, a.username ORDER BY COUNT(a) DESC")
    List<Object[]> findMostActiveUsers(@Param("orgId") UUID organizationId,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      Pageable pageable);

    /**
     * Find most modified UI configurations.
     */
    @Query("SELECT a.uiConfigurationId, COUNT(a) as modificationCount " +
           "FROM ActivityLog a WHERE a.organizationId = :orgId AND a.uiConfigurationId IS NOT NULL " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY a.uiConfigurationId ORDER BY COUNT(a) DESC")
    List<Object[]> findMostModifiedConfigurations(@Param("orgId") UUID organizationId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 Pageable pageable);

    /**
     * Delete old activity logs (for cleanup).
     */
    @Query("DELETE FROM ActivityLog a WHERE a.timestamp < :cutoffDate")
    void deleteOldActivities(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Interface for activity statistics projection.
     */
    interface ActivityStatistics {
        long getTotalActivities();
        long getUniqueUsers();
        long getUniqueConfigurations();
        Double getAverageDuration();
    }
}
