package com.uibuilder.performance;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableCaching
@RequiredArgsConstructor
public class CacheConfig {

    private final RedisConnectionFactory redisConnectionFactory;

    @Bean
    @Primary
    public CacheManager cacheManager() {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer(objectMapper())))
            .disableCachingNullValues();

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // UI Config cache - 1 hour TTL
        cacheConfigurations.put("uiConfigs", defaultConfig
            .entryTtl(Duration.ofHours(1)));
        
        // Template cache - 2 hours TTL
        cacheConfigurations.put("templates", defaultConfig
            .entryTtl(Duration.ofHours(2)));
        
        // User permissions cache - 15 minutes TTL
        cacheConfigurations.put("userPermissions", defaultConfig
            .entryTtl(Duration.ofMinutes(15)));
        
        // Component metadata cache - 4 hours TTL
        cacheConfigurations.put("componentMetadata", defaultConfig
            .entryTtl(Duration.ofHours(4)));
        
        // Design tokens cache - 24 hours TTL
        cacheConfigurations.put("designTokens", defaultConfig
            .entryTtl(Duration.ofHours(24)));
        
        // Session cache - 30 minutes TTL
        cacheConfigurations.put("sessions", defaultConfig
            .entryTtl(Duration.ofMinutes(30)));
        
        // Rate limiting cache - 1 minute TTL
        cacheConfigurations.put("rateLimits", defaultConfig
            .entryTtl(Duration.ofMinutes(1)));
        
        // API response cache - 5 minutes TTL
        cacheConfigurations.put("apiResponses", defaultConfig
            .entryTtl(Duration.ofMinutes(5)));

        return RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }

    @Bean
    public RedisCacheManagerBuilderCustomizer redisCacheManagerBuilderCustomizer() {
        return builder -> builder
            .withCacheConfiguration("longTerm",
                RedisCacheConfiguration.defaultCacheConfig()
                    .entryTtl(Duration.ofDays(1)))
            .withCacheConfiguration("shortTerm",
                RedisCacheConfiguration.defaultCacheConfig()
                    .entryTtl(Duration.ofMinutes(5)));
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        // Use String serializer for keys
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        
        // Use JSON serializer for values
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper()));
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper()));
        
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.findAndRegisterModules();
        return mapper;
    }
}

@Service
@RequiredArgsConstructor
@Slf4j
public class CacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheManager cacheManager;

    /**
     * Cache UI configuration with hierarchical invalidation
     */
    @Cacheable(value = "uiConfigs", key = "#configId")
    public UIConfig getCachedUIConfig(String configId) {
        // This will be called only if not in cache
        return uiConfigRepository.findById(configId).orElse(null);
    }

    /**
     * Invalidate UI config cache and related caches
     */
    public void invalidateUIConfigCache(String configId, String workspaceId) {
        // Invalidate specific config
        cacheManager.getCache("uiConfigs").evict(configId);
        
        // Invalidate workspace-related caches
        String workspacePattern = "workspace:" + workspaceId + ":*";
        invalidateCachePattern(workspacePattern);
        
        log.debug("Invalidated cache for UI config: {} and workspace: {}", configId, workspaceId);
    }

    /**
     * Cache template with version support
     */
    @Cacheable(value = "templates", key = "#templateId + ':' + #version")
    public Template getCachedTemplate(String templateId, String version) {
        return templateRepository.findByIdAndVersion(templateId, version).orElse(null);
    }

    /**
     * Cache component metadata for faster rendering
     */
    @Cacheable(value = "componentMetadata", key = "#componentType")
    public ComponentMetadata getCachedComponentMetadata(String componentType) {
        return componentMetadataService.getMetadata(componentType);
    }

    /**
     * Cache design tokens with environment support
     */
    @Cacheable(value = "designTokens", key = "#environment + ':' + #theme")
    public DesignTokens getCachedDesignTokens(String environment, String theme) {
        return designTokenService.getTokens(environment, theme);
    }

    /**
     * Cache API responses with conditional caching
     */
    public <T> T cacheApiResponse(String cacheKey, Supplier<T> dataSupplier, 
                                 Duration ttl, Predicate<T> shouldCache) {
        // Try to get from cache first
        T cachedValue = (T) redisTemplate.opsForValue().get("api:" + cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }

        // Get fresh data
        T freshData = dataSupplier.get();
        
        // Cache only if condition is met
        if (shouldCache.test(freshData)) {
            redisTemplate.opsForValue().set("api:" + cacheKey, freshData, ttl);
        }

        return freshData;
    }

    /**
     * Implement cache warming for frequently accessed data
     */
    @EventListener
    @Async
    public void warmCache(ApplicationReadyEvent event) {
        log.info("Starting cache warming...");
        
        // Warm up popular templates
        List<String> popularTemplates = templateRepository.findPopularTemplateIds(50);
        popularTemplates.forEach(templateId -> {
            try {
                getCachedTemplate(templateId, "latest");
            } catch (Exception e) {
                log.warn("Failed to warm cache for template: {}", templateId, e);
            }
        });

        // Warm up component metadata
        List<String> componentTypes = componentMetadataService.getAllComponentTypes();
        componentTypes.forEach(type -> {
            try {
                getCachedComponentMetadata(type);
            } catch (Exception e) {
                log.warn("Failed to warm cache for component type: {}", type, e);
            }
        });

        // Warm up design tokens
        Arrays.asList("development", "staging", "production").forEach(env -> {
            Arrays.asList("light", "dark").forEach(theme -> {
                try {
                    getCachedDesignTokens(env, theme);
                } catch (Exception e) {
                    log.warn("Failed to warm cache for tokens: {}:{}", env, theme, e);
                }
            });
        });

        log.info("Cache warming completed");
    }

    /**
     * Invalidate cache by pattern
     */
    public void invalidateCachePattern(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.debug("Invalidated {} cache keys matching pattern: {}", keys.size(), pattern);
        }
    }

    /**
     * Get cache statistics
     */
    public CacheStatistics getCacheStatistics() {
        Map<String, CacheStats> cacheStats = new HashMap<>();
        
        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache instanceof RedisCache) {
                // Get Redis-specific statistics
                cacheStats.put(cacheName, getCacheStats(cacheName));
            }
        });

        return CacheStatistics.builder()
            .cacheStats(cacheStats)
            .totalMemoryUsage(getRedisMemoryUsage())
            .timestamp(LocalDateTime.now())
            .build();
    }

    /**
     * Implement cache eviction policies
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void performCacheEviction() {
        // Evict expired entries
        evictExpiredEntries();
        
        // Check memory usage and evict if necessary
        long memoryUsage = getRedisMemoryUsage();
        long maxMemory = getRedisMaxMemory();
        
        if (memoryUsage > maxMemory * 0.8) { // 80% threshold
            performLRUEviction();
        }
    }

    private void evictExpiredEntries() {
        // Redis handles TTL automatically, but we can clean up application-level expired data
        String pattern = "expired:*";
        Set<String> expiredKeys = redisTemplate.keys(pattern);
        if (expiredKeys != null && !expiredKeys.isEmpty()) {
            redisTemplate.delete(expiredKeys);
            log.debug("Evicted {} expired cache entries", expiredKeys.size());
        }
    }

    private void performLRUEviction() {
        // Implement custom LRU eviction for specific cache types
        log.info("Performing LRU eviction due to high memory usage");
        
        // Evict least recently used API responses
        String apiPattern = "api:*";
        Set<String> apiKeys = redisTemplate.keys(apiPattern);
        if (apiKeys != null && apiKeys.size() > 1000) {
            // Keep only the most recent 500 API responses
            List<String> sortedKeys = apiKeys.stream()
                .sorted((k1, k2) -> {
                    Long ttl1 = redisTemplate.getExpire(k1);
                    Long ttl2 = redisTemplate.getExpire(k2);
                    return ttl1.compareTo(ttl2);
                })
                .collect(Collectors.toList());
            
            List<String> keysToEvict = sortedKeys.subList(0, sortedKeys.size() - 500);
            redisTemplate.delete(keysToEvict);
            
            log.info("Evicted {} API cache entries", keysToEvict.size());
        }
    }

    private CacheStats getCacheStats(String cacheName) {
        // Implementation would depend on Redis monitoring
        return CacheStats.builder()
            .hits(0L)
            .misses(0L)
            .evictions(0L)
            .size(0L)
            .build();
    }

    private long getRedisMemoryUsage() {
        // Get Redis memory usage through Redis INFO command
        return 0L; // Placeholder
    }

    private long getRedisMaxMemory() {
        // Get Redis max memory configuration
        return Long.MAX_VALUE; // Placeholder
    }
}

@Data
@Builder
class CacheStatistics {
    private Map<String, CacheStats> cacheStats;
    private long totalMemoryUsage;
    private LocalDateTime timestamp;
}

@Data
@Builder
class CacheStats {
    private long hits;
    private long misses;
    private long evictions;
    private long size;
}
