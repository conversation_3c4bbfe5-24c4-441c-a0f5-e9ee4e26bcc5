import { ComponentMetadata, UIConfiguration } from '../types/runtime';

export interface PerformanceMetrics {
  renderTime: number;
  bundleSize: number;
  memoryUsage: number;
  networkRequests: number;
  cacheHitRate: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
}

export interface OptimizationConfig {
  enableCodeSplitting: boolean;
  enableLazyLoading: boolean;
  enableBundleOptimization: boolean;
  enableImageOptimization: boolean;
  enableCaching: boolean;
  enablePreloading: boolean;
  enableVirtualization: boolean;
  maxBundleSize: number;
  maxRenderTime: number;
}

export interface LazyLoadOptions {
  threshold: number;
  rootMargin: string;
  triggerOnce: boolean;
}

/**
 * Performance Optimizer for Web Runtime
 * 
 * Provides comprehensive performance optimizations including:
 * - Code splitting and lazy loading
 * - Bundle size optimization
 * - Image optimization and lazy loading
 * - Component virtualization
 * - Caching strategies
 * - Performance monitoring
 */
export class PerformanceOptimizer {
  private config: OptimizationConfig;
  private metrics: PerformanceMetrics;
  private observer: IntersectionObserver | null = null;
  private lazyComponents = new Map<string, () => Promise<any>>();
  private componentCache = new Map<string, any>();
  private performanceObserver: PerformanceObserver | null = null;

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = {
      enableCodeSplitting: true,
      enableLazyLoading: true,
      enableBundleOptimization: true,
      enableImageOptimization: true,
      enableCaching: true,
      enablePreloading: true,
      enableVirtualization: true,
      maxBundleSize: 250000, // 250KB
      maxRenderTime: 16, // 60fps
      ...config,
    };

    this.metrics = {
      renderTime: 0,
      bundleSize: 0,
      memoryUsage: 0,
      networkRequests: 0,
      cacheHitRate: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      cumulativeLayoutShift: 0,
    };

    this.initializeOptimizations();
  }

  /**
   * Initialize performance optimizations
   */
  private initializeOptimizations(): void {
    if (this.config.enableLazyLoading) {
      this.setupLazyLoading();
    }

    if (this.config.enablePreloading) {
      this.setupPreloading();
    }

    this.setupPerformanceMonitoring();
  }

  /**
   * Setup lazy loading with Intersection Observer
   */
  private setupLazyLoading(): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    const options: LazyLoadOptions = {
      threshold: 0.1,
      rootMargin: '50px',
      triggerOnce: true,
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          const componentId = element.dataset.componentId;
          
          if (componentId && this.lazyComponents.has(componentId)) {
            this.loadLazyComponent(componentId);
            this.observer?.unobserve(element);
          }
        }
      });
    }, options);
  }

  /**
   * Setup resource preloading
   */
  private setupPreloading(): void {
    // Preload critical resources
    this.preloadCriticalResources();
    
    // Setup prefetch for likely-to-be-needed resources
    this.setupPrefetching();
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    // Monitor Core Web Vitals
    this.performanceObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime;
            }
            break;
          case 'largest-contentful-paint':
            this.metrics.largestContentfulPaint = entry.startTime;
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              this.metrics.cumulativeLayoutShift += (entry as any).value;
            }
            break;
          case 'navigation':
            this.updateNavigationMetrics(entry as PerformanceNavigationTiming);
            break;
        }
      });
    });

    this.performanceObserver.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift', 'navigation'] });
  }

  /**
   * Register a lazy-loadable component
   */
  registerLazyComponent(componentId: string, loader: () => Promise<any>): void {
    this.lazyComponents.set(componentId, loader);
  }

  /**
   * Load a lazy component
   */
  private async loadLazyComponent(componentId: string): Promise<any> {
    const loader = this.lazyComponents.get(componentId);
    if (!loader) return null;

    try {
      const startTime = performance.now();
      const component = await loader();
      const loadTime = performance.now() - startTime;

      // Cache the loaded component
      this.componentCache.set(componentId, component);
      
      // Update metrics
      this.updateLoadMetrics(loadTime);

      return component;
    } catch (error) {
      console.error(`Failed to load lazy component ${componentId}:`, error);
      return null;
    }
  }

  /**
   * Optimize component rendering
   */
  optimizeComponentRendering(components: ComponentMetadata[]): ComponentMetadata[] {
    const startTime = performance.now();

    let optimizedComponents = components;

    // Apply virtualization for large lists
    if (this.config.enableVirtualization && components.length > 100) {
      optimizedComponents = this.virtualizeComponents(components);
    }

    // Optimize images
    if (this.config.enableImageOptimization) {
      optimizedComponents = this.optimizeImages(optimizedComponents);
    }

    // Apply lazy loading
    if (this.config.enableLazyLoading) {
      optimizedComponents = this.applyLazyLoading(optimizedComponents);
    }

    const renderTime = performance.now() - startTime;
    this.metrics.renderTime = renderTime;

    return optimizedComponents;
  }

  /**
   * Virtualize components for large lists
   */
  private virtualizeComponents(components: ComponentMetadata[]): ComponentMetadata[] {
    // Implement virtual scrolling for large component lists
    const viewportHeight = window.innerHeight;
    const itemHeight = 100; // Estimated item height
    const visibleItems = Math.ceil(viewportHeight / itemHeight) + 5; // Buffer

    // Only render visible items plus buffer
    return components.slice(0, visibleItems);
  }

  /**
   * Optimize images in components
   */
  private optimizeImages(components: ComponentMetadata[]): ComponentMetadata[] {
    return components.map(component => {
      if (component.type === 'image' && component.properties?.src) {
        const optimizedComponent = { ...component };
        
        // Add lazy loading attributes
        optimizedComponent.properties = {
          ...component.properties,
          loading: 'lazy',
          decoding: 'async',
        };

        // Generate responsive image sources
        if (this.shouldGenerateResponsiveImages(component.properties.src)) {
          optimizedComponent.properties.srcSet = this.generateResponsiveImageSources(component.properties.src);
          optimizedComponent.properties.sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
        }

        return optimizedComponent;
      }
      return component;
    });
  }

  /**
   * Apply lazy loading to components
   */
  private applyLazyLoading(components: ComponentMetadata[]): ComponentMetadata[] {
    return components.map((component, index) => {
      // Apply lazy loading to components below the fold
      if (index > 5 && this.isLazyLoadable(component)) {
        const lazyComponent = { ...component };
        lazyComponent.properties = {
          ...component.properties,
          'data-lazy': true,
          'data-component-id': component.id,
        };
        return lazyComponent;
      }
      return component;
    });
  }

  /**
   * Check if component should be lazy loaded
   */
  private isLazyLoadable(component: ComponentMetadata): boolean {
    const lazyLoadableTypes = ['image', 'video', 'iframe', 'chart', 'map'];
    return lazyLoadableTypes.includes(component.type);
  }

  /**
   * Preload critical resources
   */
  private preloadCriticalResources(): void {
    const criticalResources = [
      '/fonts/inter-var.woff2',
      '/css/critical.css',
      '/js/runtime-core.js',
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      
      if (resource.endsWith('.woff2')) {
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
      } else if (resource.endsWith('.css')) {
        link.as = 'style';
      } else if (resource.endsWith('.js')) {
        link.as = 'script';
      }
      
      document.head.appendChild(link);
    });
  }

  /**
   * Setup resource prefetching
   */
  private setupPrefetching(): void {
    // Prefetch likely-to-be-needed resources on idle
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.prefetchResources();
      });
    } else {
      setTimeout(() => this.prefetchResources(), 2000);
    }
  }

  /**
   * Prefetch resources
   */
  private prefetchResources(): void {
    const prefetchResources = [
      '/js/components-lazy.js',
      '/css/components.css',
      '/images/placeholder.webp',
    ];

    prefetchResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = resource;
      document.head.appendChild(link);
    });
  }

  /**
   * Generate responsive image sources
   */
  private generateResponsiveImageSources(src: string): string {
    const sizes = [320, 640, 768, 1024, 1280, 1920];
    const format = this.supportsWebP() ? 'webp' : 'jpg';
    
    return sizes
      .map(size => `${this.getOptimizedImageUrl(src, size, format)} ${size}w`)
      .join(', ');
  }

  /**
   * Get optimized image URL
   */
  private getOptimizedImageUrl(src: string, width: number, format: string): string {
    // This would integrate with an image optimization service
    const baseUrl = src.replace(/\.[^/.]+$/, '');
    return `${baseUrl}_${width}.${format}`;
  }

  /**
   * Check if browser supports WebP
   */
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * Check if responsive images should be generated
   */
  private shouldGenerateResponsiveImages(src: string): boolean {
    // Don't generate for SVGs or already optimized images
    return !src.endsWith('.svg') && !src.includes('_optimized');
  }

  /**
   * Update navigation metrics
   */
  private updateNavigationMetrics(entry: PerformanceNavigationTiming): void {
    this.metrics.networkRequests = performance.getEntriesByType('resource').length;
  }

  /**
   * Update load metrics
   */
  private updateLoadMetrics(loadTime: number): void {
    this.metrics.renderTime = Math.max(this.metrics.renderTime, loadTime);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    // Update memory usage if available
    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    return { ...this.metrics };
  }

  /**
   * Optimize bundle size
   */
  optimizeBundleSize(): void {
    if (!this.config.enableBundleOptimization) return;

    // Tree shake unused code
    this.treeShakeUnusedCode();
    
    // Compress assets
    this.compressAssets();
    
    // Split code by routes
    this.splitCodeByRoutes();
  }

  /**
   * Tree shake unused code
   */
  private treeShakeUnusedCode(): void {
    // This would be handled by the build system (Vite/Webpack)
    console.log('Tree shaking enabled in build configuration');
  }

  /**
   * Compress assets
   */
  private compressAssets(): void {
    // Enable gzip/brotli compression
    console.log('Asset compression enabled');
  }

  /**
   * Split code by routes
   */
  private splitCodeByRoutes(): void {
    // Implement route-based code splitting
    console.log('Route-based code splitting enabled');
  }

  /**
   * Monitor performance and trigger optimizations
   */
  monitorAndOptimize(): void {
    const metrics = this.getMetrics();
    
    // Check if performance is below thresholds
    if (metrics.renderTime > this.config.maxRenderTime) {
      console.warn('Render time exceeds threshold:', metrics.renderTime);
      this.triggerPerformanceOptimizations();
    }
    
    if (metrics.largestContentfulPaint > 2500) {
      console.warn('LCP exceeds threshold:', metrics.largestContentfulPaint);
      this.optimizeLCP();
    }
    
    if (metrics.cumulativeLayoutShift > 0.1) {
      console.warn('CLS exceeds threshold:', metrics.cumulativeLayoutShift);
      this.optimizeCLS();
    }
  }

  /**
   * Trigger performance optimizations
   */
  private triggerPerformanceOptimizations(): void {
    // Reduce component complexity
    // Enable more aggressive caching
    // Defer non-critical operations
  }

  /**
   * Optimize Largest Contentful Paint
   */
  private optimizeLCP(): void {
    // Preload LCP element
    // Optimize critical resource loading
    // Remove render-blocking resources
  }

  /**
   * Optimize Cumulative Layout Shift
   */
  private optimizeCLS(): void {
    // Set explicit dimensions for images and videos
    // Reserve space for dynamic content
    // Avoid inserting content above existing content
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    
    this.lazyComponents.clear();
    this.componentCache.clear();
  }
}

// Global performance optimizer instance
export const performanceOptimizer = new PerformanceOptimizer();
