package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.uiplatform.entity.UIConfiguration;
import jakarta.validation.constraints.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * DTO for UIConfiguration entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UIConfigurationDTO extends BaseDTO {

    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must not exceed 100 characters")
    private String name;

    @NotBlank(message = "Slug is required")
    @Size(max = 100, message = "Slug must not exceed 100 characters")
    @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
    private String slug;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @NotNull(message = "Type is required")
    private UIConfiguration.UIConfigurationType type;

    private UIConfiguration.UIConfigurationStatus status;

    private Map<String, Object> metadata;
    private Map<String, Object> layoutConfig;
    private Map<String, Object> styleConfig;
    private Map<String, Object> responsiveConfig;
    private Map<String, Object> validationRules;

    @Min(value = 1, message = "Version number must be at least 1")
    private Integer versionNumber;

    private Boolean isPublished;
    private Boolean isTemplate;
    private Boolean isPublic;

    private String tags;
    private String previewUrl;
    private String publishedUrl;

    // Related entities
    @NotNull(message = "Organization ID is required")
    private UUID organizationId;
    private String organizationName;

    @NotNull(message = "Owner ID is required")
    private UUID ownerId;
    private String ownerName;

    private UUID themeId;
    private String themeName;

    private UUID layoutId;
    private String layoutName;

    private UUID parentId;
    private String parentName;

    // Collections
    private List<ComponentDTO> components;
    private List<FormFieldDTO> formFields;
    private List<UIConfigurationDTO> children;

    // Statistics
    private Long componentCount;
    private Long formFieldCount;
    private Long childrenCount;

    // Constructors
    public UIConfigurationDTO() {}

    public UIConfigurationDTO(String name, String slug, UIConfiguration.UIConfigurationType type) {
        this.name = name;
        this.slug = slug;
        this.type = type;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UIConfiguration.UIConfigurationType getType() {
        return type;
    }

    public void setType(UIConfiguration.UIConfigurationType type) {
        this.type = type;
    }

    public UIConfiguration.UIConfigurationStatus getStatus() {
        return status;
    }

    public void setStatus(UIConfiguration.UIConfigurationStatus status) {
        this.status = status;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Map<String, Object> getLayoutConfig() {
        return layoutConfig;
    }

    public void setLayoutConfig(Map<String, Object> layoutConfig) {
        this.layoutConfig = layoutConfig;
    }

    public Map<String, Object> getStyleConfig() {
        return styleConfig;
    }

    public void setStyleConfig(Map<String, Object> styleConfig) {
        this.styleConfig = styleConfig;
    }

    public Map<String, Object> getResponsiveConfig() {
        return responsiveConfig;
    }

    public void setResponsiveConfig(Map<String, Object> responsiveConfig) {
        this.responsiveConfig = responsiveConfig;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public Boolean getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Boolean isTemplate) {
        this.isTemplate = isTemplate;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getPublishedUrl() {
        return publishedUrl;
    }

    public void setPublishedUrl(String publishedUrl) {
        this.publishedUrl = publishedUrl;
    }

    public UUID getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(UUID organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public UUID getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public UUID getThemeId() {
        return themeId;
    }

    public void setThemeId(UUID themeId) {
        this.themeId = themeId;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public UUID getLayoutId() {
        return layoutId;
    }

    public void setLayoutId(UUID layoutId) {
        this.layoutId = layoutId;
    }

    public String getLayoutName() {
        return layoutName;
    }

    public void setLayoutName(String layoutName) {
        this.layoutName = layoutName;
    }

    public UUID getParentId() {
        return parentId;
    }

    public void setParentId(UUID parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<ComponentDTO> getComponents() {
        return components;
    }

    public void setComponents(List<ComponentDTO> components) {
        this.components = components;
    }

    public List<FormFieldDTO> getFormFields() {
        return formFields;
    }

    public void setFormFields(List<FormFieldDTO> formFields) {
        this.formFields = formFields;
    }

    public List<UIConfigurationDTO> getChildren() {
        return children;
    }

    public void setChildren(List<UIConfigurationDTO> children) {
        this.children = children;
    }

    public Long getComponentCount() {
        return componentCount;
    }

    public void setComponentCount(Long componentCount) {
        this.componentCount = componentCount;
    }

    public Long getFormFieldCount() {
        return formFieldCount;
    }

    public void setFormFieldCount(Long formFieldCount) {
        this.formFieldCount = formFieldCount;
    }

    public Long getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(Long childrenCount) {
        this.childrenCount = childrenCount;
    }

    /**
     * Create DTO for UI configuration creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Name is required")
        @Size(max = 100, message = "Name must not exceed 100 characters")
        private String name;

        @NotBlank(message = "Slug is required")
        @Size(max = 100, message = "Slug must not exceed 100 characters")
        @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
        private String slug;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @NotNull(message = "Type is required")
        private UIConfiguration.UIConfigurationType type;

        private Map<String, Object> metadata;
        private UUID themeId;
        private UUID layoutId;
        private UUID parentId;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSlug() {
            return slug;
        }

        public void setSlug(String slug) {
            this.slug = slug;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public UIConfiguration.UIConfigurationType getType() {
            return type;
        }

        public void setType(UIConfiguration.UIConfigurationType type) {
            this.type = type;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public UUID getThemeId() {
            return themeId;
        }

        public void setThemeId(UUID themeId) {
            this.themeId = themeId;
        }

        public UUID getLayoutId() {
            return layoutId;
        }

        public void setLayoutId(UUID layoutId) {
            this.layoutId = layoutId;
        }

        public UUID getParentId() {
            return parentId;
        }

        public void setParentId(UUID parentId) {
            this.parentId = parentId;
        }
    }
}
