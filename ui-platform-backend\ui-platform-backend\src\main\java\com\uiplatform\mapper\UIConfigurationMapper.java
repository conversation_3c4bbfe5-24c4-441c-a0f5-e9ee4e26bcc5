package com.uiplatform.mapper;

import com.uiplatform.dto.UIConfigurationDTO;
import com.uiplatform.entity.UIConfiguration;
import org.mapstruct.*;

/**
 * Mapper interface for UIConfiguration entity and DTO conversions.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UIConfigurationMapper {

    /**
     * Convert UIConfiguration entity to DTO.
     */
    @Mapping(target = "organizationId", source = "organization.id")
    @Mapping(target = "organizationName", source = "organization.name")
    @Mapping(target = "ownerId", source = "owner.id")
    @Mapping(target = "ownerName", expression = "java(uiConfiguration.getOwner().getFirstName() + \" \" + uiConfiguration.getOwner().getLastName())")
    @Mapping(target = "themeId", source = "theme.id")
    @Mapping(target = "themeName", source = "theme.name")
    @Mapping(target = "layoutId", source = "layout.id")
    @Mapping(target = "layoutName", source = "layout.name")
    @Mapping(target = "parentId", source = "parent.id")
    @Mapping(target = "parentName", source = "parent.name")
    @Mapping(target = "components", ignore = true)
    @Mapping(target = "formFields", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "componentCount", ignore = true)
    @Mapping(target = "formFieldCount", ignore = true)
    @Mapping(target = "childrenCount", ignore = true)
    UIConfigurationDTO toDTO(UIConfiguration uiConfiguration);

    /**
     * Convert CreateDTO to UIConfiguration entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "DRAFT")
    @Mapping(target = "versionNumber", constant = "1")
    @Mapping(target = "isPublished", constant = "false")
    @Mapping(target = "isTemplate", constant = "false")
    @Mapping(target = "isPublic", constant = "false")
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "theme", ignore = true)
    @Mapping(target = "layout", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "components", ignore = true)
    @Mapping(target = "formFields", ignore = true)
    @Mapping(target = "previewUrl", ignore = true)
    @Mapping(target = "publishedUrl", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    UIConfiguration toEntity(UIConfigurationDTO.CreateDTO createDTO);

    /**
     * Update UIConfiguration entity from DTO.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "slug", ignore = true)
    @Mapping(target = "versionNumber", ignore = true)
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "theme", ignore = true)
    @Mapping(target = "layout", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "components", ignore = true)
    @Mapping(target = "formFields", ignore = true)
    @Mapping(target = "previewUrl", ignore = true)
    @Mapping(target = "publishedUrl", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void updateEntityFromDTO(UIConfigurationDTO updateDTO, @MappingTarget UIConfiguration uiConfiguration);
}
