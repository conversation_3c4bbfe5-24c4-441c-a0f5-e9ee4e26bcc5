import React, { useState, useCallback, useMemo } from 'react';
import { 
  Card, 
  Input, 
  Select, 
  Button, 
  Space, 
  Modal, 
  message, 
  Tabs, 
  Badge,
  Tooltip,
  Rate,
  Tag,
  Empty,
  Spin,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  EyeOutlined,
  DownloadOutlined,
  HeartOutlined,
  HeartFilled,
  ShareAltOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '@store/index';
import { 
  useGetTemplatesQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation,
  useDeleteTemplateMutation,
} from '@store/api/templateApi';
import { Template, TemplateCategory } from '@types/index';

// Components
import TemplateCard from './TemplateCard';
import TemplatePreview from './TemplatePreview';
import TemplateEditor from './TemplateEditor';
import TemplateFilters from './TemplateFilters';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface TemplateManagerProps {
  className?: string;
  mode?: 'browse' | 'manage';
  onTemplateSelect?: (template: Template) => void;
}

const TemplateManager: React.FC<TemplateManagerProps> = ({
  className = '',
  mode = 'browse',
  onTemplateSelect,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory | 'all'>('all');
  const [sortBy, setSortBy] = useState<'rating' | 'downloads' | 'recent'>('rating');
  const [showPremiumOnly, setShowPremiumOnly] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [editorVisible, setEditorVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  const { user } = useAppSelector(state => state.auth);

  // API hooks
  const {
    data: templatesData,
    isLoading,
    error,
    refetch,
  } = useGetTemplatesQuery({
    search: searchTerm,
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
    sortBy,
    sortOrder: 'desc',
    isPublic: mode === 'browse',
  });

  const [createTemplate] = useCreateTemplateMutation();
  const [updateTemplate] = useUpdateTemplateMutation();
  const [deleteTemplate] = useDeleteTemplateMutation();

  // Filter templates based on current tab and filters
  const filteredTemplates = useMemo(() => {
    if (!templatesData?.data) return [];

    let filtered = templatesData.data;

    // Filter by tab
    if (activeTab === 'favorites') {
      filtered = filtered.filter(template => favorites.has(template.id));
    } else if (activeTab === 'my-templates') {
      filtered = filtered.filter(template => template.createdBy === user?.id);
    } else if (activeTab === 'premium') {
      filtered = filtered.filter(template => template.isPremium);
    }

    // Filter by premium
    if (showPremiumOnly) {
      filtered = filtered.filter(template => template.isPremium);
    }

    return filtered;
  }, [templatesData?.data, activeTab, favorites, user?.id, showPremiumOnly]);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: Template) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    } else {
      setSelectedTemplate(template);
      setPreviewVisible(true);
    }
  }, [onTemplateSelect]);

  // Handle template preview
  const handleTemplatePreview = useCallback((template: Template) => {
    setSelectedTemplate(template);
    setPreviewVisible(true);
  }, []);

  // Handle template edit
  const handleTemplateEdit = useCallback((template: Template) => {
    setSelectedTemplate(template);
    setEditorVisible(true);
  }, []);

  // Handle template delete
  const handleTemplateDelete = useCallback(async (template: Template) => {
    Modal.confirm({
      title: 'Delete Template',
      content: `Are you sure you want to delete "${template.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      onOk: async () => {
        try {
          await deleteTemplate(template.id).unwrap();
          message.success('Template deleted successfully');
          refetch();
        } catch (error) {
          message.error('Failed to delete template');
        }
      },
    });
  }, [deleteTemplate, refetch]);

  // Handle favorite toggle
  const handleFavoriteToggle = useCallback((templateId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(templateId)) {
        newFavorites.delete(templateId);
      } else {
        newFavorites.add(templateId);
      }
      return newFavorites;
    });
  }, []);

  // Handle template creation
  const handleCreateTemplate = useCallback(() => {
    setSelectedTemplate(null);
    setEditorVisible(true);
  }, []);

  // Handle template save
  const handleTemplateSave = useCallback(async (templateData: Partial<Template>) => {
    try {
      if (selectedTemplate) {
        // Update existing template
        await updateTemplate({
          id: selectedTemplate.id,
          data: templateData,
        }).unwrap();
        message.success('Template updated successfully');
      } else {
        // Create new template
        await createTemplate(templateData).unwrap();
        message.success('Template created successfully');
      }
      
      setEditorVisible(false);
      refetch();
    } catch (error) {
      message.error('Failed to save template');
    }
  }, [selectedTemplate, updateTemplate, createTemplate, refetch]);

  // Get template count by category
  const getTemplateCount = (category: string) => {
    if (!templatesData?.data) return 0;
    
    if (category === 'all') return templatesData.data.length;
    if (category === 'favorites') return favorites.size;
    if (category === 'my-templates') {
      return templatesData.data.filter(t => t.createdBy === user?.id).length;
    }
    if (category === 'premium') {
      return templatesData.data.filter(t => t.isPremium).length;
    }
    
    return templatesData.data.filter(t => t.category === category).length;
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <div className="text-lg font-medium text-red-600 mb-2">
            Failed to load templates
          </div>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="p-6 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Templates</h2>
            <p className="text-gray-600 mt-1">
              Choose from our collection of professional templates
            </p>
          </div>
          
          {mode === 'manage' && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateTemplate}
            >
              Create Template
            </Button>
          )}
        </div>

        {/* Search and filters */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Search
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              allowClear
              size="large"
            />
          </div>
          
          <Select
            value={selectedCategory}
            onChange={setSelectedCategory}
            style={{ width: 200 }}
            size="large"
          >
            <Option value="all">All Categories</Option>
            {Object.values(TemplateCategory).map(category => (
              <Option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Option>
            ))}
          </Select>
          
          <Select
            value={sortBy}
            onChange={setSortBy}
            style={{ width: 150 }}
            size="large"
          >
            <Option value="rating">Top Rated</Option>
            <Option value="downloads">Most Popular</Option>
            <Option value="recent">Most Recent</Option>
          </Select>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="h-full"
          tabBarStyle={{ padding: '0 24px', backgroundColor: 'white' }}
        >
          <TabPane
            tab={
              <span>
                All Templates
                <Badge count={getTemplateCount('all')} style={{ marginLeft: 8 }} />
              </span>
            }
            key="all"
          >
            <TemplateGrid
              templates={filteredTemplates}
              isLoading={isLoading}
              favorites={favorites}
              onTemplateSelect={handleTemplateSelect}
              onTemplatePreview={handleTemplatePreview}
              onTemplateEdit={mode === 'manage' ? handleTemplateEdit : undefined}
              onTemplateDelete={mode === 'manage' ? handleTemplateDelete : undefined}
              onFavoriteToggle={handleFavoriteToggle}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                Favorites
                <Badge count={getTemplateCount('favorites')} style={{ marginLeft: 8 }} />
              </span>
            }
            key="favorites"
          >
            <TemplateGrid
              templates={filteredTemplates}
              isLoading={isLoading}
              favorites={favorites}
              onTemplateSelect={handleTemplateSelect}
              onTemplatePreview={handleTemplatePreview}
              onTemplateEdit={mode === 'manage' ? handleTemplateEdit : undefined}
              onTemplateDelete={mode === 'manage' ? handleTemplateDelete : undefined}
              onFavoriteToggle={handleFavoriteToggle}
            />
          </TabPane>

          {mode === 'manage' && (
            <TabPane
              tab={
                <span>
                  My Templates
                  <Badge count={getTemplateCount('my-templates')} style={{ marginLeft: 8 }} />
                </span>
              }
              key="my-templates"
            >
              <TemplateGrid
                templates={filteredTemplates}
                isLoading={isLoading}
                favorites={favorites}
                onTemplateSelect={handleTemplateSelect}
                onTemplatePreview={handleTemplatePreview}
                onTemplateEdit={handleTemplateEdit}
                onTemplateDelete={handleTemplateDelete}
                onFavoriteToggle={handleFavoriteToggle}
              />
            </TabPane>
          )}

          <TabPane
            tab={
              <span>
                Premium
                <Badge count={getTemplateCount('premium')} style={{ marginLeft: 8 }} />
              </span>
            }
            key="premium"
          >
            <TemplateGrid
              templates={filteredTemplates}
              isLoading={isLoading}
              favorites={favorites}
              onTemplateSelect={handleTemplateSelect}
              onTemplatePreview={handleTemplatePreview}
              onTemplateEdit={mode === 'manage' ? handleTemplateEdit : undefined}
              onTemplateDelete={mode === 'manage' ? handleTemplateDelete : undefined}
              onFavoriteToggle={handleFavoriteToggle}
            />
          </TabPane>
        </Tabs>
      </div>

      {/* Preview modal */}
      <Modal
        title={selectedTemplate?.name}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={
          <Space>
            <Button onClick={() => setPreviewVisible(false)}>
              Close
            </Button>
            {selectedTemplate && (
              <Button
                type="primary"
                onClick={() => handleTemplateSelect(selectedTemplate)}
              >
                Use Template
              </Button>
            )}
          </Space>
        }
      >
        {selectedTemplate && (
          <TemplatePreview template={selectedTemplate} />
        )}
      </Modal>

      {/* Editor modal */}
      <Modal
        title={selectedTemplate ? 'Edit Template' : 'Create Template'}
        open={editorVisible}
        onCancel={() => setEditorVisible(false)}
        width="80%"
        footer={null}
        destroyOnClose
      >
        <TemplateEditor
          template={selectedTemplate}
          onSave={handleTemplateSave}
          onCancel={() => setEditorVisible(false)}
        />
      </Modal>
    </div>
  );
};

// Template grid component
interface TemplateGridProps {
  templates: Template[];
  isLoading: boolean;
  favorites: Set<string>;
  onTemplateSelect: (template: Template) => void;
  onTemplatePreview: (template: Template) => void;
  onTemplateEdit?: (template: Template) => void;
  onTemplateDelete?: (template: Template) => void;
  onFavoriteToggle: (templateId: string) => void;
}

const TemplateGrid: React.FC<TemplateGridProps> = ({
  templates,
  isLoading,
  favorites,
  onTemplateSelect,
  onTemplatePreview,
  onTemplateEdit,
  onTemplateDelete,
  onFavoriteToggle,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (templates.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Empty
          description="No templates found"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {templates.map(template => (
          <TemplateCard
            key={template.id}
            template={template}
            isFavorite={favorites.has(template.id)}
            onSelect={() => onTemplateSelect(template)}
            onPreview={() => onTemplatePreview(template)}
            onEdit={onTemplateEdit ? () => onTemplateEdit(template) : undefined}
            onDelete={onTemplateDelete ? () => onTemplateDelete(template) : undefined}
            onFavoriteToggle={() => onFavoriteToggle(template.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default TemplateManager;
