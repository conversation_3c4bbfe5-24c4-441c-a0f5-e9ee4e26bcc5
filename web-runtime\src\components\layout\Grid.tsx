import React from 'react';
import { cn } from '@utils/cn';
import { ComponentProps } from '@types/index';

interface GridProps extends ComponentProps {
  columns?: number | 'auto' | 'none';
  rows?: number | 'auto' | 'none';
  gap?: number | string;
  columnGap?: number | string;
  rowGap?: number | string;
  autoFlow?: 'row' | 'column' | 'dense' | 'row-dense' | 'column-dense';
  alignItems?: 'start' | 'end' | 'center' | 'stretch';
  justifyItems?: 'start' | 'end' | 'center' | 'stretch';
  alignContent?: 'start' | 'end' | 'center' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
  justifyContent?: 'start' | 'end' | 'center' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
}

const getColumnClass = (columns: GridProps['columns']) => {
  if (typeof columns === 'number') {
    return `grid-cols-${columns}`;
  }
  if (columns === 'auto') {
    return 'grid-cols-auto';
  }
  if (columns === 'none') {
    return 'grid-cols-none';
  }
  return 'grid-cols-1';
};

const getRowClass = (rows: GridProps['rows']) => {
  if (typeof rows === 'number') {
    return `grid-rows-${rows}`;
  }
  if (rows === 'auto') {
    return 'grid-rows-auto';
  }
  if (rows === 'none') {
    return 'grid-rows-none';
  }
  return '';
};

const getGapClass = (gap: number | string | undefined) => {
  if (typeof gap === 'number') {
    return `gap-${gap}`;
  }
  if (typeof gap === 'string') {
    return `gap-[${gap}]`;
  }
  return 'gap-4';
};

const getColumnGapClass = (gap: number | string | undefined) => {
  if (typeof gap === 'number') {
    return `gap-x-${gap}`;
  }
  if (typeof gap === 'string') {
    return `gap-x-[${gap}]`;
  }
  return '';
};

const getRowGapClass = (gap: number | string | undefined) => {
  if (typeof gap === 'number') {
    return `gap-y-${gap}`;
  }
  if (typeof gap === 'string') {
    return `gap-y-[${gap}]`;
  }
  return '';
};

const autoFlowClasses = {
  row: 'grid-flow-row',
  column: 'grid-flow-col',
  dense: 'grid-flow-dense',
  'row-dense': 'grid-flow-row-dense',
  'column-dense': 'grid-flow-col-dense',
};

const alignItemsClasses = {
  start: 'items-start',
  end: 'items-end',
  center: 'items-center',
  stretch: 'items-stretch',
};

const justifyItemsClasses = {
  start: 'justify-items-start',
  end: 'justify-items-end',
  center: 'justify-items-center',
  stretch: 'justify-items-stretch',
};

const alignContentClasses = {
  start: 'content-start',
  end: 'content-end',
  center: 'content-center',
  stretch: 'content-stretch',
  'space-between': 'content-between',
  'space-around': 'content-around',
  'space-evenly': 'content-evenly',
};

const justifyContentClasses = {
  start: 'justify-start',
  end: 'justify-end',
  center: 'justify-center',
  stretch: 'justify-stretch',
  'space-between': 'justify-between',
  'space-around': 'justify-around',
  'space-evenly': 'justify-evenly',
};

const Grid: React.FC<GridProps> = ({
  columns = 1,
  rows,
  gap,
  columnGap,
  rowGap,
  autoFlow = 'row',
  alignItems,
  justifyItems,
  alignContent,
  justifyContent,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'grid',
        getColumnClass(columns),
        getRowClass(rows),
        gap !== undefined ? getGapClass(gap) : '',
        columnGap !== undefined ? getColumnGapClass(columnGap) : '',
        rowGap !== undefined ? getRowGapClass(rowGap) : '',
        autoFlowClasses[autoFlow],
        alignItems && alignItemsClasses[alignItems],
        justifyItems && justifyItemsClasses[justifyItems],
        alignContent && alignContentClasses[alignContent],
        justifyContent && justifyContentClasses[justifyContent],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default Grid;
