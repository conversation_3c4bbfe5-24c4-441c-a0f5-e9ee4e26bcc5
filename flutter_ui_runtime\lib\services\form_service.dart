import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// Form Management Service for Flutter UI Runtime
/// 
/// Provides comprehensive form handling including:
/// - Dynamic form generation from configuration
/// - Validation rules and error handling
/// - Form state management
/// - Data binding and submission
/// - Multi-step form support

enum FormFieldType {
  text,
  email,
  password,
  number,
  phone,
  url,
  textarea,
  select,
  multiSelect,
  checkbox,
  radio,
  date,
  time,
  dateTime,
  file,
  image,
  slider,
  switch_,
  rating,
  color,
  custom,
}

enum ValidationType {
  required,
  minLength,
  maxLength,
  pattern,
  email,
  url,
  number,
  min,
  max,
  custom,
}

class FormFieldDefinition {
  final String id;
  final String name;
  final String label;
  final FormFieldType type;
  final dynamic defaultValue;
  final String? placeholder;
  final String? helpText;
  final bool required;
  final bool disabled;
  final bool readonly;
  final List<ValidationRule> validationRules;
  final List<FormFieldOption>? options;
  final Map<String, dynamic>? properties;
  final Map<String, dynamic>? style;

  FormFieldDefinition({
    required this.id,
    required this.name,
    required this.label,
    required this.type,
    this.defaultValue,
    this.placeholder,
    this.helpText,
    this.required = false,
    this.disabled = false,
    this.readonly = false,
    this.validationRules = const [],
    this.options,
    this.properties,
    this.style,
  });

  factory FormFieldDefinition.fromJson(Map<String, dynamic> json) {
    return FormFieldDefinition(
      id: json['id'],
      name: json['name'],
      label: json['label'],
      type: FormFieldType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => FormFieldType.text,
      ),
      defaultValue: json['defaultValue'],
      placeholder: json['placeholder'],
      helpText: json['helpText'],
      required: json['required'] ?? false,
      disabled: json['disabled'] ?? false,
      readonly: json['readonly'] ?? false,
      validationRules: (json['validationRules'] as List<dynamic>?)
          ?.map((rule) => ValidationRule.fromJson(rule))
          .toList() ?? [],
      options: (json['options'] as List<dynamic>?)
          ?.map((option) => FormFieldOption.fromJson(option))
          .toList(),
      properties: json['properties'] != null 
          ? Map<String, dynamic>.from(json['properties'])
          : null,
      style: json['style'] != null 
          ? Map<String, dynamic>.from(json['style'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'label': label,
    'type': type.name,
    'defaultValue': defaultValue,
    'placeholder': placeholder,
    'helpText': helpText,
    'required': required,
    'disabled': disabled,
    'readonly': readonly,
    'validationRules': validationRules.map((rule) => rule.toJson()).toList(),
    'options': options?.map((option) => option.toJson()).toList(),
    'properties': properties,
    'style': style,
  };
}

class FormFieldOption {
  final String value;
  final String label;
  final bool disabled;
  final Map<String, dynamic>? metadata;

  FormFieldOption({
    required this.value,
    required this.label,
    this.disabled = false,
    this.metadata,
  });

  factory FormFieldOption.fromJson(Map<String, dynamic> json) {
    return FormFieldOption(
      value: json['value'],
      label: json['label'],
      disabled: json['disabled'] ?? false,
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'value': value,
    'label': label,
    'disabled': disabled,
    'metadata': metadata,
  };
}

class ValidationRule {
  final ValidationType type;
  final dynamic value;
  final String message;

  ValidationRule({
    required this.type,
    this.value,
    required this.message,
  });

  factory ValidationRule.fromJson(Map<String, dynamic> json) {
    return ValidationRule(
      type: ValidationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ValidationType.required,
      ),
      value: json['value'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() => {
    'type': type.name,
    'value': value,
    'message': message,
  };
}

class FormConfiguration {
  final String id;
  final String name;
  final String? title;
  final String? description;
  final List<FormFieldDefinition> fields;
  final List<FormStep>? steps;
  final Map<String, dynamic>? submitAction;
  final Map<String, dynamic>? validationRules;
  final Map<String, dynamic>? styling;

  FormConfiguration({
    required this.id,
    required this.name,
    this.title,
    this.description,
    required this.fields,
    this.steps,
    this.submitAction,
    this.validationRules,
    this.styling,
  });

  factory FormConfiguration.fromJson(Map<String, dynamic> json) {
    return FormConfiguration(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      description: json['description'],
      fields: (json['fields'] as List<dynamic>)
          .map((field) => FormFieldDefinition.fromJson(field))
          .toList(),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((step) => FormStep.fromJson(step))
          .toList(),
      submitAction: json['submitAction'] != null 
          ? Map<String, dynamic>.from(json['submitAction'])
          : null,
      validationRules: json['validationRules'] != null 
          ? Map<String, dynamic>.from(json['validationRules'])
          : null,
      styling: json['styling'] != null 
          ? Map<String, dynamic>.from(json['styling'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'title': title,
    'description': description,
    'fields': fields.map((field) => field.toJson()).toList(),
    'steps': steps?.map((step) => step.toJson()).toList(),
    'submitAction': submitAction,
    'validationRules': validationRules,
    'styling': styling,
  };
}

class FormStep {
  final String id;
  final String title;
  final String? description;
  final List<String> fieldIds;
  final Map<String, dynamic>? conditions;

  FormStep({
    required this.id,
    required this.title,
    this.description,
    required this.fieldIds,
    this.conditions,
  });

  factory FormStep.fromJson(Map<String, dynamic> json) {
    return FormStep(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      fieldIds: List<String>.from(json['fieldIds']),
      conditions: json['conditions'] != null 
          ? Map<String, dynamic>.from(json['conditions'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'fieldIds': fieldIds,
    'conditions': conditions,
  };
}

class FormState {
  final Map<String, dynamic> values;
  final Map<String, String> errors;
  final Map<String, bool> touched;
  final bool isValid;
  final bool isSubmitting;
  final bool isDirty;

  FormState({
    this.values = const {},
    this.errors = const {},
    this.touched = const {},
    this.isValid = true,
    this.isSubmitting = false,
    this.isDirty = false,
  });

  FormState copyWith({
    Map<String, dynamic>? values,
    Map<String, String>? errors,
    Map<String, bool>? touched,
    bool? isValid,
    bool? isSubmitting,
    bool? isDirty,
  }) {
    return FormState(
      values: values ?? this.values,
      errors: errors ?? this.errors,
      touched: touched ?? this.touched,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isDirty: isDirty ?? this.isDirty,
    );
  }
}

class FormService extends StateNotifier<FormState> {
  FormService() : super(FormState());

  FormConfiguration? _configuration;
  final Map<String, GlobalKey<FormState>> _formKeys = {};

  /// Initialize form with configuration
  void initializeForm(FormConfiguration configuration) {
    _configuration = configuration;
    
    // Set default values
    final defaultValues = <String, dynamic>{};
    for (final field in configuration.fields) {
      if (field.defaultValue != null) {
        defaultValues[field.name] = field.defaultValue;
      }
    }
    
    state = FormState(values: defaultValues);
  }

  /// Update field value
  void updateField(String fieldName, dynamic value) {
    final newValues = Map<String, dynamic>.from(state.values);
    newValues[fieldName] = value;
    
    final newTouched = Map<String, bool>.from(state.touched);
    newTouched[fieldName] = true;
    
    // Validate field
    final errors = Map<String, String>.from(state.errors);
    final fieldError = _validateField(fieldName, value);
    if (fieldError != null) {
      errors[fieldName] = fieldError;
    } else {
      errors.remove(fieldName);
    }
    
    state = state.copyWith(
      values: newValues,
      touched: newTouched,
      errors: errors,
      isValid: errors.isEmpty,
      isDirty: true,
    );
  }

  /// Validate entire form
  bool validateForm() {
    if (_configuration == null) return false;
    
    final errors = <String, String>{};
    final touched = <String, bool>{};
    
    for (final field in _configuration!.fields) {
      touched[field.name] = true;
      final value = state.values[field.name];
      final error = _validateField(field.name, value);
      if (error != null) {
        errors[field.name] = error;
      }
    }
    
    state = state.copyWith(
      errors: errors,
      touched: touched,
      isValid: errors.isEmpty,
    );
    
    return errors.isEmpty;
  }

  /// Validate single field
  String? _validateField(String fieldName, dynamic value) {
    if (_configuration == null) return null;
    
    final field = _configuration!.fields.firstWhere(
      (f) => f.name == fieldName,
      orElse: () => throw FormException('Field not found: $fieldName'),
    );
    
    for (final rule in field.validationRules) {
      final error = _applyValidationRule(rule, value, field);
      if (error != null) return error;
    }
    
    return null;
  }

  /// Apply validation rule
  String? _applyValidationRule(ValidationRule rule, dynamic value, FormFieldDefinition field) {
    switch (rule.type) {
      case ValidationType.required:
        if (value == null || value.toString().trim().isEmpty) {
          return rule.message;
        }
        break;
        
      case ValidationType.minLength:
        if (value != null && value.toString().length < (rule.value as int)) {
          return rule.message;
        }
        break;
        
      case ValidationType.maxLength:
        if (value != null && value.toString().length > (rule.value as int)) {
          return rule.message;
        }
        break;
        
      case ValidationType.pattern:
        if (value != null && !RegExp(rule.value as String).hasMatch(value.toString())) {
          return rule.message;
        }
        break;
        
      case ValidationType.email:
        if (value != null && !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value.toString())) {
          return rule.message;
        }
        break;
        
      case ValidationType.url:
        if (value != null && !Uri.tryParse(value.toString())?.hasAbsolutePath == true) {
          return rule.message;
        }
        break;
        
      case ValidationType.number:
        if (value != null && double.tryParse(value.toString()) == null) {
          return rule.message;
        }
        break;
        
      case ValidationType.min:
        if (value != null) {
          final numValue = double.tryParse(value.toString());
          if (numValue != null && numValue < (rule.value as num)) {
            return rule.message;
          }
        }
        break;
        
      case ValidationType.max:
        if (value != null) {
          final numValue = double.tryParse(value.toString());
          if (numValue != null && numValue > (rule.value as num)) {
            return rule.message;
          }
        }
        break;
        
      case ValidationType.custom:
        // Custom validation would be implemented here
        break;
    }
    
    return null;
  }

  /// Submit form
  Future<bool> submitForm() async {
    if (!validateForm()) return false;
    
    state = state.copyWith(isSubmitting: true);
    
    try {
      // Handle form submission based on configuration
      if (_configuration?.submitAction != null) {
        await _handleSubmitAction(_configuration!.submitAction!);
      }
      
      state = state.copyWith(isSubmitting: false);
      return true;
    } catch (e) {
      state = state.copyWith(isSubmitting: false);
      throw FormException('Form submission failed: $e');
    }
  }

  /// Handle submit action
  Future<void> _handleSubmitAction(Map<String, dynamic> action) async {
    final actionType = action['type'] as String?;
    
    switch (actionType) {
      case 'api':
        await _submitToApi(action);
        break;
      case 'email':
        await _submitViaEmail(action);
        break;
      case 'webhook':
        await _submitToWebhook(action);
        break;
      case 'storage':
        await _submitToStorage(action);
        break;
      default:
        throw FormException('Unknown submit action type: $actionType');
    }
  }

  Future<void> _submitToApi(Map<String, dynamic> action) async {
    // API submission implementation
    final endpoint = action['endpoint'] as String;
    final method = action['method'] as String? ?? 'POST';
    
    // This would integrate with ApiService
    print('Submitting to API: $method $endpoint');
    print('Data: ${state.values}');
  }

  Future<void> _submitViaEmail(Map<String, dynamic> action) async {
    // Email submission implementation
    final recipient = action['recipient'] as String;
    print('Submitting via email to: $recipient');
    print('Data: ${state.values}');
  }

  Future<void> _submitToWebhook(Map<String, dynamic> action) async {
    // Webhook submission implementation
    final url = action['url'] as String;
    print('Submitting to webhook: $url');
    print('Data: ${state.values}');
  }

  Future<void> _submitToStorage(Map<String, dynamic> action) async {
    // Local storage submission implementation
    final key = action['key'] as String;
    print('Submitting to storage with key: $key');
    print('Data: ${state.values}');
  }

  /// Reset form
  void resetForm() {
    if (_configuration == null) return;
    
    final defaultValues = <String, dynamic>{};
    for (final field in _configuration!.fields) {
      if (field.defaultValue != null) {
        defaultValues[field.name] = field.defaultValue;
      }
    }
    
    state = FormState(values: defaultValues);
  }

  /// Clear form
  void clearForm() {
    state = FormState();
  }

  /// Get field value
  T? getFieldValue<T>(String fieldName) {
    return state.values[fieldName] as T?;
  }

  /// Get field error
  String? getFieldError(String fieldName) {
    return state.errors[fieldName];
  }

  /// Check if field is touched
  bool isFieldTouched(String fieldName) {
    return state.touched[fieldName] ?? false;
  }

  /// Check if field has error
  bool hasFieldError(String fieldName) {
    return state.errors.containsKey(fieldName);
  }
}

/// Form Exception
class FormException implements Exception {
  final String message;
  FormException(this.message);
  
  @override
  String toString() => 'FormException: $message';
}

/// Providers
final formServiceProvider = StateNotifierProvider.family<FormService, FormState, String>((ref, formId) {
  return FormService();
});

final formConfigurationProvider = Provider.family<FormConfiguration?, String>((ref, configId) {
  // This would load form configuration from storage or API
  return null;
});

/// Helper extensions
extension FormServiceExtension on WidgetRef {
  FormService formService(String formId) => read(formServiceProvider(formId).notifier);
  FormState formState(String formId) => read(formServiceProvider(formId));
  
  void updateFormField(String formId, String fieldName, dynamic value) {
    formService(formId).updateField(fieldName, value);
  }
  
  bool validateForm(String formId) {
    return formService(formId).validateForm();
  }
  
  Future<bool> submitForm(String formId) {
    return formService(formId).submitForm();
  }
}
