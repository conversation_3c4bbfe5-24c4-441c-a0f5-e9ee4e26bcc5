#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');
const ora = require('ora');
const semver = require('semver');

/**
 * Build and Distribution Script for UI Component Library
 * 
 * Features:
 * - Multi-package build orchestration
 * - Version management and bumping
 * - TypeScript compilation
 * - Bundle optimization
 * - Documentation generation
 * - Package publishing
 * - Release automation
 */

class BuildSystem {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..');
    this.packagesDir = path.join(this.rootDir, 'packages');
    this.distDir = path.join(this.rootDir, 'dist');
    this.config = this.loadConfig();
  }

  loadConfig() {
    const configPath = path.join(this.rootDir, 'build.config.js');
    if (fs.existsSync(configPath)) {
      return require(configPath);
    }
    
    return {
      packages: [
        'design-tokens',
        'core',
        'react',
        'flutter',
        'icons',
        'testing',
      ],
      buildOrder: [
        'design-tokens',
        'core',
        'icons',
        'react',
        'flutter',
        'testing',
      ],
      registry: 'https://registry.npmjs.org/',
      publishConfig: {
        access: 'public',
        registry: 'https://registry.npmjs.org/',
      },
      bundleAnalysis: true,
      generateDocs: true,
      runTests: true,
    };
  }

  async run(command, options = {}) {
    const { version, packages, skipTests, skipDocs, dryRun } = options;
    
    console.log(chalk.blue.bold('🚀 UI Component Library Build System'));
    console.log(chalk.gray(`Command: ${command}`));
    console.log(chalk.gray(`Options: ${JSON.stringify(options, null, 2)}`));
    
    try {
      switch (command) {
        case 'build':
          await this.build(packages, { skipTests, skipDocs });
          break;
        case 'version':
          await this.version(version, packages);
          break;
        case 'publish':
          await this.publish(packages, { dryRun });
          break;
        case 'release':
          await this.release(version, { skipTests, skipDocs, dryRun });
          break;
        case 'clean':
          await this.clean(packages);
          break;
        case 'analyze':
          await this.analyze(packages);
          break;
        default:
          throw new Error(`Unknown command: ${command}`);
      }
      
      console.log(chalk.green.bold('✅ Build completed successfully!'));
    } catch (error) {
      console.error(chalk.red.bold('❌ Build failed:'), error.message);
      process.exit(1);
    }
  }

  async build(packages = this.config.packages, options = {}) {
    const { skipTests, skipDocs } = options;
    
    console.log(chalk.yellow.bold('\n📦 Building packages...'));
    
    // Clean previous builds
    await this.clean(packages);
    
    // Build packages in order
    for (const packageName of this.config.buildOrder) {
      if (packages.includes(packageName)) {
        await this.buildPackage(packageName, { skipTests, skipDocs });
      }
    }
    
    // Generate combined documentation
    if (!skipDocs && this.config.generateDocs) {
      await this.generateDocs();
    }
    
    // Run bundle analysis
    if (this.config.bundleAnalysis) {
      await this.analyze(packages);
    }
  }

  async buildPackage(packageName, options = {}) {
    const { skipTests, skipDocs } = options;
    const spinner = ora(`Building ${packageName}...`).start();
    
    try {
      const packageDir = path.join(this.packagesDir, packageName);
      const packageJson = await fs.readJson(path.join(packageDir, 'package.json'));
      
      // Install dependencies
      spinner.text = `Installing dependencies for ${packageName}...`;
      execSync('npm ci', { cwd: packageDir, stdio: 'pipe' });
      
      // Run tests
      if (!skipTests && this.config.runTests) {
        spinner.text = `Running tests for ${packageName}...`;
        try {
          execSync('npm test', { cwd: packageDir, stdio: 'pipe' });
        } catch (error) {
          throw new Error(`Tests failed for ${packageName}`);
        }
      }
      
      // Build TypeScript
      if (fs.existsSync(path.join(packageDir, 'tsconfig.json'))) {
        spinner.text = `Compiling TypeScript for ${packageName}...`;
        execSync('npx tsc', { cwd: packageDir, stdio: 'pipe' });
      }
      
      // Build with package-specific script
      if (packageJson.scripts && packageJson.scripts.build) {
        spinner.text = `Running build script for ${packageName}...`;
        execSync('npm run build', { cwd: packageDir, stdio: 'pipe' });
      }
      
      // Generate package documentation
      if (!skipDocs && packageJson.scripts && packageJson.scripts['build:docs']) {
        spinner.text = `Generating docs for ${packageName}...`;
        execSync('npm run build:docs', { cwd: packageDir, stdio: 'pipe' });
      }
      
      // Copy package to dist
      const distPackageDir = path.join(this.distDir, packageName);
      await fs.ensureDir(distPackageDir);
      
      // Copy built files
      const filesToCopy = ['package.json', 'README.md', 'LICENSE'];
      const dirsToCopy = ['lib', 'dist', 'build', 'docs'];
      
      for (const file of filesToCopy) {
        const srcPath = path.join(packageDir, file);
        if (await fs.pathExists(srcPath)) {
          await fs.copy(srcPath, path.join(distPackageDir, file));
        }
      }
      
      for (const dir of dirsToCopy) {
        const srcPath = path.join(packageDir, dir);
        if (await fs.pathExists(srcPath)) {
          await fs.copy(srcPath, path.join(distPackageDir, dir));
        }
      }
      
      spinner.succeed(`Built ${packageName} successfully`);
    } catch (error) {
      spinner.fail(`Failed to build ${packageName}: ${error.message}`);
      throw error;
    }
  }

  async version(versionType, packages = this.config.packages) {
    console.log(chalk.yellow.bold('\n🔢 Updating versions...'));
    
    if (!versionType) {
      throw new Error('Version type is required (patch, minor, major, or specific version)');
    }
    
    const rootPackageJson = await fs.readJson(path.join(this.rootDir, 'package.json'));
    const currentVersion = rootPackageJson.version;
    
    let newVersion;
    if (semver.valid(versionType)) {
      newVersion = versionType;
    } else {
      newVersion = semver.inc(currentVersion, versionType);
    }
    
    if (!newVersion) {
      throw new Error(`Invalid version type: ${versionType}`);
    }
    
    console.log(chalk.blue(`Updating from ${currentVersion} to ${newVersion}`));
    
    // Update root package.json
    rootPackageJson.version = newVersion;
    await fs.writeJson(path.join(this.rootDir, 'package.json'), rootPackageJson, { spaces: 2 });
    
    // Update package versions
    for (const packageName of packages) {
      const packageDir = path.join(this.packagesDir, packageName);
      const packageJsonPath = path.join(packageDir, 'package.json');
      
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        packageJson.version = newVersion;
        
        // Update internal dependencies
        for (const depType of ['dependencies', 'devDependencies', 'peerDependencies']) {
          if (packageJson[depType]) {
            for (const depName of Object.keys(packageJson[depType])) {
              if (depName.startsWith('@ui-builder/')) {
                packageJson[depType][depName] = `^${newVersion}`;
              }
            }
          }
        }
        
        await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });
        console.log(chalk.green(`✓ Updated ${packageName} to ${newVersion}`));
      }
    }
    
    // Create git tag
    try {
      execSync(`git add .`, { stdio: 'pipe' });
      execSync(`git commit -m "chore: bump version to ${newVersion}"`, { stdio: 'pipe' });
      execSync(`git tag v${newVersion}`, { stdio: 'pipe' });
      console.log(chalk.green(`✓ Created git tag v${newVersion}`));
    } catch (error) {
      console.warn(chalk.yellow('⚠️ Failed to create git tag'));
    }
  }

  async publish(packages = this.config.packages, options = {}) {
    const { dryRun } = options;
    
    console.log(chalk.yellow.bold('\n📤 Publishing packages...'));
    
    if (dryRun) {
      console.log(chalk.blue('🔍 Dry run mode - no actual publishing'));
    }
    
    for (const packageName of this.config.buildOrder) {
      if (packages.includes(packageName)) {
        await this.publishPackage(packageName, { dryRun });
      }
    }
  }

  async publishPackage(packageName, options = {}) {
    const { dryRun } = options;
    const spinner = ora(`Publishing ${packageName}...`).start();
    
    try {
      const packageDir = path.join(this.distDir, packageName);
      
      if (!await fs.pathExists(packageDir)) {
        throw new Error(`Package ${packageName} not found in dist directory`);
      }
      
      const packageJson = await fs.readJson(path.join(packageDir, 'package.json'));
      
      // Check if version already exists
      try {
        const publishedInfo = execSync(`npm view ${packageJson.name}@${packageJson.version} version`, {
          cwd: packageDir,
          stdio: 'pipe',
          encoding: 'utf8',
        }).trim();
        
        if (publishedInfo === packageJson.version) {
          spinner.info(`${packageName}@${packageJson.version} already published`);
          return;
        }
      } catch (error) {
        // Package doesn't exist, continue with publishing
      }
      
      // Publish package
      const publishCmd = dryRun ? 'npm publish --dry-run' : 'npm publish';
      execSync(publishCmd, { cwd: packageDir, stdio: 'pipe' });
      
      spinner.succeed(`Published ${packageName}@${packageJson.version}`);
    } catch (error) {
      spinner.fail(`Failed to publish ${packageName}: ${error.message}`);
      throw error;
    }
  }

  async release(versionType, options = {}) {
    const { skipTests, skipDocs, dryRun } = options;
    
    console.log(chalk.yellow.bold('\n🚀 Creating release...'));
    
    // Update versions
    await this.version(versionType);
    
    // Build packages
    await this.build(this.config.packages, { skipTests, skipDocs });
    
    // Publish packages
    await this.publish(this.config.packages, { dryRun });
    
    // Push git changes
    if (!dryRun) {
      try {
        execSync('git push origin main --tags', { stdio: 'pipe' });
        console.log(chalk.green('✓ Pushed changes to git'));
      } catch (error) {
        console.warn(chalk.yellow('⚠️ Failed to push to git'));
      }
    }
  }

  async clean(packages = this.config.packages) {
    console.log(chalk.yellow.bold('\n🧹 Cleaning build artifacts...'));
    
    // Clean dist directory
    await fs.remove(this.distDir);
    await fs.ensureDir(this.distDir);
    
    // Clean package build directories
    for (const packageName of packages) {
      const packageDir = path.join(this.packagesDir, packageName);
      const dirsToClean = ['lib', 'dist', 'build', 'coverage', '.nyc_output'];
      
      for (const dir of dirsToClean) {
        const dirPath = path.join(packageDir, dir);
        if (await fs.pathExists(dirPath)) {
          await fs.remove(dirPath);
        }
      }
    }
    
    console.log(chalk.green('✓ Cleaned build artifacts'));
  }

  async analyze(packages = this.config.packages) {
    console.log(chalk.yellow.bold('\n📊 Analyzing bundles...'));
    
    const analysis = {
      packages: {},
      total: {
        size: 0,
        gzipSize: 0,
        files: 0,
      },
    };
    
    for (const packageName of packages) {
      const packageDir = path.join(this.distDir, packageName);
      
      if (await fs.pathExists(packageDir)) {
        const packageAnalysis = await this.analyzePackage(packageName, packageDir);
        analysis.packages[packageName] = packageAnalysis;
        
        analysis.total.size += packageAnalysis.size;
        analysis.total.gzipSize += packageAnalysis.gzipSize;
        analysis.total.files += packageAnalysis.files;
      }
    }
    
    // Save analysis report
    const reportPath = path.join(this.distDir, 'bundle-analysis.json');
    await fs.writeJson(reportPath, analysis, { spaces: 2 });
    
    // Print summary
    console.log(chalk.blue('\n📈 Bundle Analysis Summary:'));
    console.log(`Total size: ${this.formatBytes(analysis.total.size)}`);
    console.log(`Total gzip size: ${this.formatBytes(analysis.total.gzipSize)}`);
    console.log(`Total files: ${analysis.total.files}`);
    
    for (const [packageName, packageAnalysis] of Object.entries(analysis.packages)) {
      console.log(`\n${packageName}:`);
      console.log(`  Size: ${this.formatBytes(packageAnalysis.size)}`);
      console.log(`  Gzip: ${this.formatBytes(packageAnalysis.gzipSize)}`);
      console.log(`  Files: ${packageAnalysis.files}`);
    }
  }

  async analyzePackage(packageName, packageDir) {
    const stats = await this.getDirectoryStats(packageDir);
    return {
      size: stats.size,
      gzipSize: Math.round(stats.size * 0.3), // Rough gzip estimate
      files: stats.files,
    };
  }

  async getDirectoryStats(dir) {
    let totalSize = 0;
    let totalFiles = 0;
    
    const items = await fs.readdir(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = await fs.stat(itemPath);
      
      if (stat.isDirectory()) {
        const subStats = await this.getDirectoryStats(itemPath);
        totalSize += subStats.size;
        totalFiles += subStats.files;
      } else {
        totalSize += stat.size;
        totalFiles++;
      }
    }
    
    return { size: totalSize, files: totalFiles };
  }

  async generateDocs() {
    console.log(chalk.yellow.bold('\n📚 Generating documentation...'));
    
    const docsDir = path.join(this.distDir, 'docs');
    await fs.ensureDir(docsDir);
    
    // Generate combined documentation
    try {
      execSync('npm run build:docs', { cwd: this.rootDir, stdio: 'pipe' });
      console.log(chalk.green('✓ Generated documentation'));
    } catch (error) {
      console.warn(chalk.yellow('⚠️ Failed to generate documentation'));
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const options = {};
  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith('--')) {
      const key = arg.slice(2);
      const value = args[i + 1] && !args[i + 1].startsWith('--') ? args[++i] : true;
      options[key] = value;
    }
  }
  
  const buildSystem = new BuildSystem();
  buildSystem.run(command, options).catch(console.error);
}

module.exports = BuildSystem;
