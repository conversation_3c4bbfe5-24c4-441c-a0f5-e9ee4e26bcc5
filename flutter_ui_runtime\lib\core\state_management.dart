import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// State change event
class StateChangeEvent {
  final String key;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  final String? source;

  StateChangeEvent({
    required this.key,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
    this.source,
  });
}

/// State listener function type
typedef StateListener = void Function(StateChangeEvent event);

/// State validator function type
typedef StateValidator = bool Function(String key, dynamic value);

/// State transformer function type
typedef StateTransformer = dynamic Function(dynamic value);

/// State Management System for Flutter UI Runtime
/// 
/// Provides centralized state management with reactive updates,
/// persistence, validation, and debugging capabilities.
class StateManager extends ChangeNotifier {
  static final StateManager _instance = StateManager._internal();
  factory StateManager() => _instance;
  StateManager._internal();

  final Map<String, dynamic> _state = {};
  final Map<String, List<StateListener>> _listeners = {};
  final Map<String, StateValidator> _validators = {};
  final Map<String, StateTransformer> _transformers = {};
  final List<StateChangeEvent> _history = [];
  final Set<String> _persistentKeys = {};
  
  bool _debugMode = false;
  int _maxHistorySize = 100;

  /// Get state value by key
  T? get<T>(String key) {
    return _state[key] as T?;
  }

  /// Set state value
  void set(String key, dynamic value, {String? source}) {
    final oldValue = _state[key];
    
    // Apply transformer if exists
    if (_transformers.containsKey(key)) {
      value = _transformers[key]!(value);
    }
    
    // Validate if validator exists
    if (_validators.containsKey(key)) {
      if (!_validators[key]!(key, value)) {
        if (_debugMode) {
          print('State validation failed for key: $key, value: $value');
        }
        return;
      }
    }
    
    // Update state
    _state[key] = value;
    
    // Create change event
    final event = StateChangeEvent(
      key: key,
      oldValue: oldValue,
      newValue: value,
      timestamp: DateTime.now(),
      source: source,
    );
    
    // Add to history
    _addToHistory(event);
    
    // Notify listeners
    _notifyListeners(key, event);
    
    // Persist if needed
    if (_persistentKeys.contains(key)) {
      _persistState(key, value);
    }
    
    // Notify change notifier
    notifyListeners();
    
    if (_debugMode) {
      print('State changed: $key = $value (source: $source)');
    }
  }

  /// Update state value using a function
  void update<T>(String key, T Function(T? current) updater, {String? source}) {
    final current = get<T>(key);
    final newValue = updater(current);
    set(key, newValue, source: source);
  }

  /// Remove state value
  void remove(String key, {String? source}) {
    if (_state.containsKey(key)) {
      final oldValue = _state[key];
      _state.remove(key);
      
      final event = StateChangeEvent(
        key: key,
        oldValue: oldValue,
        newValue: null,
        timestamp: DateTime.now(),
        source: source,
      );
      
      _addToHistory(event);
      _notifyListeners(key, event);
      
      if (_persistentKeys.contains(key)) {
        _removePersistentState(key);
      }
      
      notifyListeners();
      
      if (_debugMode) {
        print('State removed: $key (source: $source)');
      }
    }
  }

  /// Check if state key exists
  bool contains(String key) {
    return _state.containsKey(key);
  }

  /// Get all state keys
  Set<String> get keys => _state.keys.toSet();

  /// Get all state as map
  Map<String, dynamic> get state => Map.unmodifiable(_state);

  /// Clear all state
  void clear({String? source}) {
    final oldState = Map<String, dynamic>.from(_state);
    _state.clear();
    
    for (final entry in oldState.entries) {
      final event = StateChangeEvent(
        key: entry.key,
        oldValue: entry.value,
        newValue: null,
        timestamp: DateTime.now(),
        source: source,
      );
      
      _addToHistory(event);
      _notifyListeners(entry.key, event);
    }
    
    _clearPersistentState();
    notifyListeners();
    
    if (_debugMode) {
      print('All state cleared (source: $source)');
    }
  }

  /// Add state listener
  void addListener(String key, StateListener listener) {
    _listeners.putIfAbsent(key, () => []).add(listener);
  }

  /// Remove state listener
  void removeListener(String key, StateListener listener) {
    _listeners[key]?.remove(listener);
    if (_listeners[key]?.isEmpty == true) {
      _listeners.remove(key);
    }
  }

  /// Add state validator
  void addValidator(String key, StateValidator validator) {
    _validators[key] = validator;
  }

  /// Remove state validator
  void removeValidator(String key) {
    _validators.remove(key);
  }

  /// Add state transformer
  void addTransformer(String key, StateTransformer transformer) {
    _transformers[key] = transformer;
  }

  /// Remove state transformer
  void removeTransformer(String key) {
    _transformers.remove(key);
  }

  /// Mark key as persistent
  void setPersistent(String key, bool persistent) {
    if (persistent) {
      _persistentKeys.add(key);
      if (_state.containsKey(key)) {
        _persistState(key, _state[key]);
      }
    } else {
      _persistentKeys.remove(key);
      _removePersistentState(key);
    }
  }

  /// Get state history
  List<StateChangeEvent> get history => List.unmodifiable(_history);

  /// Get state history for specific key
  List<StateChangeEvent> getHistoryForKey(String key) {
    return _history.where((event) => event.key == key).toList();
  }

  /// Clear state history
  void clearHistory() {
    _history.clear();
  }

  /// Enable/disable debug mode
  void setDebugMode(bool enabled) {
    _debugMode = enabled;
  }

  /// Set maximum history size
  void setMaxHistorySize(int size) {
    _maxHistorySize = size;
    _trimHistory();
  }

  /// Batch state updates
  void batch(void Function() updates, {String? source}) {
    final oldNotifyListeners = notifyListeners;
    
    // Temporarily disable notifications
    notifyListeners = () {};
    
    try {
      updates();
    } finally {
      // Re-enable notifications and notify once
      notifyListeners = oldNotifyListeners;
      notifyListeners();
    }
  }

  /// Create state snapshot
  Map<String, dynamic> createSnapshot() {
    return Map<String, dynamic>.from(_state);
  }

  /// Restore from snapshot
  void restoreFromSnapshot(Map<String, dynamic> snapshot, {String? source}) {
    batch(() {
      clear(source: source);
      for (final entry in snapshot.entries) {
        set(entry.key, entry.value, source: source);
      }
    }, source: source);
  }

  /// Undo last change for key
  void undo(String key, {String? source}) {
    final keyHistory = getHistoryForKey(key);
    if (keyHistory.length >= 2) {
      final previousEvent = keyHistory[keyHistory.length - 2];
      set(key, previousEvent.newValue, source: source ?? 'undo');
    }
  }

  /// Private methods

  void _addToHistory(StateChangeEvent event) {
    _history.add(event);
    _trimHistory();
  }

  void _trimHistory() {
    while (_history.length > _maxHistorySize) {
      _history.removeAt(0);
    }
  }

  void _notifyListeners(String key, StateChangeEvent event) {
    _listeners[key]?.forEach((listener) {
      try {
        listener(event);
      } catch (e) {
        if (_debugMode) {
          print('Error in state listener for key $key: $e');
        }
      }
    });
  }

  void _persistState(String key, dynamic value) {
    // This would integrate with SharedPreferences or similar
    // For now, just log the persistence
    if (_debugMode) {
      print('Persisting state: $key = $value');
    }
  }

  void _removePersistentState(String key) {
    if (_debugMode) {
      print('Removing persistent state: $key');
    }
  }

  void _clearPersistentState() {
    if (_debugMode) {
      print('Clearing all persistent state');
    }
  }
}

/// Reactive State Provider
class ReactiveStateProvider<T> extends StateNotifier<T> {
  final String key;
  final StateManager _stateManager;

  ReactiveStateProvider(this.key, T initialValue) 
      : _stateManager = StateManager(),
        super(initialValue) {
    
    // Initialize state if not exists
    if (!_stateManager.contains(key)) {
      _stateManager.set(key, initialValue);
    } else {
      state = _stateManager.get<T>(key) ?? initialValue;
    }
    
    // Listen to state changes
    _stateManager.addListener(key, _onStateChange);
  }

  void _onStateChange(StateChangeEvent event) {
    if (event.newValue is T) {
      state = event.newValue;
    }
  }

  void updateState(T newState, {String? source}) {
    _stateManager.set(key, newState, source: source);
  }

  void updateWith(T Function(T current) updater, {String? source}) {
    _stateManager.update<T>(key, updater, source: source);
  }

  @override
  void dispose() {
    _stateManager.removeListener(key, _onStateChange);
    super.dispose();
  }
}

/// State Hook for functional components
class StateHook<T> {
  final String key;
  final StateManager _stateManager;
  late final ReactiveStateProvider<T> _provider;

  StateHook(this.key, T initialValue) : _stateManager = StateManager() {
    _provider = ReactiveStateProvider<T>(key, initialValue);
  }

  T get value => _provider.state;

  void setValue(T newValue, {String? source}) {
    _provider.updateState(newValue, source: source);
  }

  void updateValue(T Function(T current) updater, {String? source}) {
    _provider.updateWith(updater, source: source);
  }

  void dispose() {
    _provider.dispose();
  }
}

/// State Builder Widget
class StateBuilder<T> extends ConsumerWidget {
  final String stateKey;
  final T defaultValue;
  final Widget Function(BuildContext context, T value, void Function(T) setValue) builder;

  const StateBuilder({
    Key? key,
    required this.stateKey,
    required this.defaultValue,
    required this.builder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stateManager = StateManager();
    
    // Get current value
    final currentValue = stateManager.get<T>(stateKey) ?? defaultValue;
    
    // Create setter function
    void setValue(T newValue) {
      stateManager.set(stateKey, newValue, source: 'StateBuilder');
    }
    
    return AnimatedBuilder(
      animation: stateManager,
      builder: (context, child) {
        final value = stateManager.get<T>(stateKey) ?? defaultValue;
        return builder(context, value, setValue);
      },
    );
  }
}

/// State Selector Widget
class StateSelector<T, R> extends ConsumerWidget {
  final String stateKey;
  final T defaultValue;
  final R Function(T value) selector;
  final Widget Function(BuildContext context, R selected) builder;

  const StateSelector({
    Key? key,
    required this.stateKey,
    required this.defaultValue,
    required this.selector,
    required this.builder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stateManager = StateManager();
    
    return AnimatedBuilder(
      animation: stateManager,
      builder: (context, child) {
        final value = stateManager.get<T>(stateKey) ?? defaultValue;
        final selected = selector(value);
        return builder(context, selected);
      },
    );
  }
}

/// Computed State Provider
class ComputedStateProvider<T> extends StateNotifier<T> {
  final List<String> dependencies;
  final T Function(Map<String, dynamic> state) compute;
  final StateManager _stateManager;

  ComputedStateProvider({
    required this.dependencies,
    required this.compute,
  }) : _stateManager = StateManager(),
       super(_computeInitialValue(dependencies, compute, StateManager())) {
    
    // Listen to dependency changes
    for (final dep in dependencies) {
      _stateManager.addListener(dep, _onDependencyChange);
    }
  }

  static T _computeInitialValue<T>(
    List<String> dependencies,
    T Function(Map<String, dynamic> state) compute,
    StateManager stateManager,
  ) {
    final depState = <String, dynamic>{};
    for (final dep in dependencies) {
      depState[dep] = stateManager.get(dep);
    }
    return compute(depState);
  }

  void _onDependencyChange(StateChangeEvent event) {
    final depState = <String, dynamic>{};
    for (final dep in dependencies) {
      depState[dep] = _stateManager.get(dep);
    }
    state = compute(depState);
  }

  @override
  void dispose() {
    for (final dep in dependencies) {
      _stateManager.removeListener(dep, _onDependencyChange);
    }
    super.dispose();
  }
}

/// Riverpod providers for state management
final stateManagerProvider = Provider<StateManager>((ref) {
  return StateManager();
});

final stateProvider = StateNotifierProvider.family<ReactiveStateProvider<dynamic>, dynamic, String>(
  (ref, key) {
    return ReactiveStateProvider<dynamic>(key, null);
  },
);

/// State management utilities
class StateUtils {
  /// Create a form state manager
  static FormStateManager createFormState(Map<String, dynamic> initialValues) {
    return FormStateManager(initialValues);
  }

  /// Create a list state manager
  static ListStateManager<T> createListState<T>(List<T> initialItems) {
    return ListStateManager<T>(initialItems);
  }

  /// Create a loading state manager
  static LoadingStateManager createLoadingState() {
    return LoadingStateManager();
  }
}

/// Form State Manager
class FormStateManager {
  final StateManager _stateManager = StateManager();
  final String _prefix;

  FormStateManager(Map<String, dynamic> initialValues) 
      : _prefix = 'form_${DateTime.now().millisecondsSinceEpoch}' {
    
    for (final entry in initialValues.entries) {
      _stateManager.set('${_prefix}_${entry.key}', entry.value);
    }
  }

  T? getValue<T>(String field) {
    return _stateManager.get<T>('${_prefix}_$field');
  }

  void setValue(String field, dynamic value) {
    _stateManager.set('${_prefix}_$field', value, source: 'form');
  }

  Map<String, dynamic> getValues() {
    final values = <String, dynamic>{};
    for (final key in _stateManager.keys) {
      if (key.startsWith('${_prefix}_')) {
        final field = key.substring(_prefix.length + 1);
        values[field] = _stateManager.get(key);
      }
    }
    return values;
  }

  void reset(Map<String, dynamic> values) {
    for (final key in _stateManager.keys.toList()) {
      if (key.startsWith('${_prefix}_')) {
        _stateManager.remove(key);
      }
    }
    
    for (final entry in values.entries) {
      setValue(entry.key, entry.value);
    }
  }
}

/// List State Manager
class ListStateManager<T> {
  final StateManager _stateManager = StateManager();
  final String _key;

  ListStateManager(List<T> initialItems) 
      : _key = 'list_${DateTime.now().millisecondsSinceEpoch}' {
    _stateManager.set(_key, List<T>.from(initialItems));
  }

  List<T> get items => _stateManager.get<List<T>>(_key) ?? <T>[];

  void add(T item) {
    _stateManager.update<List<T>>(_key, (current) {
      final list = List<T>.from(current ?? <T>[]);
      list.add(item);
      return list;
    }, source: 'list_add');
  }

  void remove(T item) {
    _stateManager.update<List<T>>(_key, (current) {
      final list = List<T>.from(current ?? <T>[]);
      list.remove(item);
      return list;
    }, source: 'list_remove');
  }

  void removeAt(int index) {
    _stateManager.update<List<T>>(_key, (current) {
      final list = List<T>.from(current ?? <T>[]);
      if (index >= 0 && index < list.length) {
        list.removeAt(index);
      }
      return list;
    }, source: 'list_remove_at');
  }

  void insert(int index, T item) {
    _stateManager.update<List<T>>(_key, (current) {
      final list = List<T>.from(current ?? <T>[]);
      list.insert(index, item);
      return list;
    }, source: 'list_insert');
  }

  void clear() {
    _stateManager.set(_key, <T>[], source: 'list_clear');
  }

  void addListener(StateListener listener) {
    _stateManager.addListener(_key, listener);
  }

  void removeListener(StateListener listener) {
    _stateManager.removeListener(_key, listener);
  }
}

/// Loading State Manager
class LoadingStateManager {
  final StateManager _stateManager = StateManager();
  final String _key;

  LoadingStateManager() : _key = 'loading_${DateTime.now().millisecondsSinceEpoch}' {
    _stateManager.set(_key, false);
  }

  bool get isLoading => _stateManager.get<bool>(_key) ?? false;

  void setLoading(bool loading) {
    _stateManager.set(_key, loading, source: 'loading_state');
  }

  Future<T> withLoading<T>(Future<T> Function() operation) async {
    setLoading(true);
    try {
      return await operation();
    } finally {
      setLoading(false);
    }
  }

  void addListener(StateListener listener) {
    _stateManager.addListener(_key, listener);
  }

  void removeListener(StateListener listener) {
    _stateManager.removeListener(_key, listener);
  }
}
