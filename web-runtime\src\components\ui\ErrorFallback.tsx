import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { cn } from '@utils/cn';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary?: () => void;
  title?: string;
  message?: string;
  componentId?: string;
  componentType?: string;
  className?: string;
  showDetails?: boolean;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  title = 'Something went wrong',
  message,
  componentId,
  componentType,
  className,
  showDetails = false,
}) => {
  const [detailsVisible, setDetailsVisible] = React.useState(showDetails);

  const defaultMessage = componentId 
    ? `There was an error rendering the ${componentType || 'component'} with ID "${componentId}".`
    : 'An unexpected error occurred. Please try refreshing the page.';

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center p-6 bg-error-50 border border-error-200 rounded-lg',
        className
      )}
      role="alert"
    >
      <div className="flex items-center space-x-3 mb-4">
        <ExclamationTriangleIcon className="w-8 h-8 text-error-600" />
        <h2 className="text-lg font-semibold text-error-900">{title}</h2>
      </div>

      <p className="text-error-700 text-center mb-4 max-w-md">
        {message || defaultMessage}
      </p>

      {componentId && (
        <div className="text-sm text-error-600 mb-4 text-center">
          <p>Component ID: <code className="bg-error-100 px-1 rounded">{componentId}</code></p>
          {componentType && (
            <p>Component Type: <code className="bg-error-100 px-1 rounded">{componentType}</code></p>
          )}
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3">
        {resetErrorBoundary && (
          <button
            onClick={resetErrorBoundary}
            className="inline-flex items-center px-4 py-2 bg-error-600 text-white rounded-md hover:bg-error-700 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2 transition-colors"
          >
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Try Again
          </button>
        )}

        <button
          onClick={() => setDetailsVisible(!detailsVisible)}
          className="inline-flex items-center px-4 py-2 bg-white text-error-700 border border-error-300 rounded-md hover:bg-error-50 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2 transition-colors"
        >
          {detailsVisible ? 'Hide' : 'Show'} Details
        </button>
      </div>

      {detailsVisible && (
        <div className="mt-6 w-full max-w-2xl">
          <details className="bg-white border border-error-200 rounded-md">
            <summary className="px-4 py-2 cursor-pointer text-sm font-medium text-error-700 hover:bg-error-50">
              Error Details
            </summary>
            <div className="px-4 py-3 border-t border-error-200">
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-error-900 mb-1">Error Message:</h4>
                  <p className="text-sm text-error-700 font-mono bg-error-50 p-2 rounded">
                    {error.message}
                  </p>
                </div>

                {error.stack && (
                  <div>
                    <h4 className="text-sm font-medium text-error-900 mb-1">Stack Trace:</h4>
                    <pre className="text-xs text-error-600 bg-error-50 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                      {error.stack}
                    </pre>
                  </div>
                )}

                <div>
                  <h4 className="text-sm font-medium text-error-900 mb-1">Timestamp:</h4>
                  <p className="text-sm text-error-700">
                    {new Date().toISOString()}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-error-900 mb-1">User Agent:</h4>
                  <p className="text-xs text-error-600 break-all">
                    {navigator.userAgent}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-error-900 mb-1">URL:</h4>
                  <p className="text-sm text-error-700 break-all">
                    {window.location.href}
                  </p>
                </div>
              </div>
            </div>
          </details>
        </div>
      )}

      <div className="mt-4 text-xs text-error-500 text-center">
        If this problem persists, please contact support with the error details above.
      </div>
    </div>
  );
};

export default ErrorFallback;
