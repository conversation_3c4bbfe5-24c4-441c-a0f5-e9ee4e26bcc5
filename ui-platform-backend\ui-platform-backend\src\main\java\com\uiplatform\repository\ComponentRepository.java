package com.uiplatform.repository;

import com.uiplatform.entity.Component;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Repository interface for Component entity.
 * Provides CRUD operations and custom queries for component management.
 */
@Repository
public interface ComponentRepository extends JpaRepository<Component, UUID>, JpaSpecificationExecutor<Component> {

    /**
     * Find components by UI configuration.
     */
    List<Component> findByUiConfigurationIdAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find components by UI configuration with pagination.
     */
    Page<Component> findByUiConfigurationIdAndDeletedFalse(UUID uiConfigurationId, Pageable pageable);

    /**
     * Find components by type.
     */
    List<Component> findByComponentTypeAndDeletedFalse(String componentType);

    /**
     * Find components by category.
     */
    List<Component> findByCategoryAndDeletedFalse(String category);

    /**
     * Find components by type and category.
     */
    List<Component> findByComponentTypeAndCategoryAndDeletedFalse(String componentType, String category);

    /**
     * Find root components (no parent).
     */
    List<Component> findByUiConfigurationIdAndParentIsNullAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find child components.
     */
    List<Component> findByParentIdAndDeletedFalseOrderBySortOrder(UUID parentId);

    /**
     * Find reusable components.
     */
    List<Component> findByIsReusableTrueAndDeletedFalse();

    /**
     * Find visible components.
     */
    List<Component> findByUiConfigurationIdAndIsVisibleTrueAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find enabled components.
     */
    List<Component> findByUiConfigurationIdAndIsEnabledTrueAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Search components by name.
     */
    @Query("SELECT c FROM Component c WHERE c.uiConfiguration.id = :uiConfigurationId AND " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%')) AND c.deleted = false ORDER BY c.sortOrder")
    List<Component> searchByName(@Param("uiConfigurationId") UUID uiConfigurationId, @Param("name") String name);

    /**
     * Find components by multiple criteria.
     */
    @Query("SELECT c FROM Component c WHERE c.uiConfiguration.id = :uiConfigurationId AND " +
           "(:componentType IS NULL OR c.componentType = :componentType) AND " +
           "(:category IS NULL OR c.category = :category) AND " +
           "(:isVisible IS NULL OR c.isVisible = :isVisible) AND " +
           "(:isEnabled IS NULL OR c.isEnabled = :isEnabled) AND " +
           "c.deleted = false ORDER BY c.sortOrder")
    Page<Component> findByCriteria(@Param("uiConfigurationId") UUID uiConfigurationId,
                                  @Param("componentType") String componentType,
                                  @Param("category") String category,
                                  @Param("isVisible") Boolean isVisible,
                                  @Param("isEnabled") Boolean isEnabled,
                                  Pageable pageable);

    /**
     * Count components by UI configuration.
     */
    @Query("SELECT COUNT(c) FROM Component c WHERE c.uiConfiguration.id = :uiConfigurationId AND c.deleted = false")
    Long countByUiConfigurationId(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Count components by type.
     */
    @Query("SELECT COUNT(c) FROM Component c WHERE c.componentType = :componentType AND c.deleted = false")
    Long countByComponentType(@Param("componentType") String componentType);

    /**
     * Count child components.
     */
    @Query("SELECT COUNT(c) FROM Component c WHERE c.parent.id = :parentId AND c.deleted = false")
    Long countByParentId(@Param("parentId") UUID parentId);

    /**
     * Find components with children count.
     */
    @Query("SELECT c, COUNT(child) as childCount FROM Component c LEFT JOIN c.children child " +
           "WHERE c.uiConfiguration.id = :uiConfigurationId AND c.deleted = false GROUP BY c ORDER BY c.sortOrder")
    List<Object[]> findWithChildrenCount(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Find components by depth level.
     */
    @Query("SELECT c FROM Component c WHERE c.uiConfiguration.id = :uiConfigurationId AND c.deleted = false")
    List<Component> findByUiConfigurationId(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Update component visibility.
     */
    @Modifying
    @Query("UPDATE Component c SET c.isVisible = :isVisible WHERE c.id = :id")
    void updateVisibility(@Param("id") UUID id, @Param("isVisible") Boolean isVisible);

    /**
     * Update component enabled status.
     */
    @Modifying
    @Query("UPDATE Component c SET c.isEnabled = :isEnabled WHERE c.id = :id")
    void updateEnabledStatus(@Param("id") UUID id, @Param("isEnabled") Boolean isEnabled);

    /**
     * Update component sort order.
     */
    @Modifying
    @Query("UPDATE Component c SET c.sortOrder = :sortOrder WHERE c.id = :id")
    void updateSortOrder(@Param("id") UUID id, @Param("sortOrder") Integer sortOrder);

    /**
     * Find components by data source.
     */
    @Query("SELECT c FROM Component c WHERE c.dataSource = :dataSource AND c.deleted = false")
    List<Component> findByDataSource(@Param("dataSource") String dataSource);

    /**
     * Find most used component types.
     */
    @Query("SELECT c.componentType, COUNT(c) as usageCount FROM Component c WHERE c.deleted = false GROUP BY c.componentType ORDER BY usageCount DESC")
    List<Object[]> findMostUsedComponentTypes();

    /**
     * Find components by organization (through UI configuration).
     */
    @Query("SELECT c FROM Component c WHERE c.uiConfiguration.organization.id = :organizationId AND c.deleted = false")
    List<Component> findByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Find all unique component types.
     */
    @Query("SELECT DISTINCT c.componentType FROM Component c WHERE c.deleted = false ORDER BY c.componentType")
    List<String> findAllComponentTypes();

    /**
     * Find all unique categories.
     */
    @Query("SELECT DISTINCT c.category FROM Component c WHERE c.category IS NOT NULL AND c.deleted = false ORDER BY c.category")
    List<String> findAllCategories();

    /**
     * Update component parent.
     */
    @Modifying
    @Query("UPDATE Component c SET c.parent.id = :parentId WHERE c.id = :id")
    void updateParent(@Param("id") UUID id, @Param("parentId") UUID parentId);

    /**
     * Remove parent from component.
     */
    @Modifying
    @Query("UPDATE Component c SET c.parent = null WHERE c.id = :id")
    void removeParent(@Param("id") UUID id);
}
