package com.uibuilder.auth.controller;

import com.uibuilder.auth.dto.*;
import com.uibuilder.auth.service.AuthService;
import com.uibuilder.auth.service.UserService;
import com.uibuilder.auth.model.User;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * REST Controller for authentication and user management
 * 
 * Provides endpoints for user registration, login, logout, password management,
 * JWT token operations, and user profile management.
 */
@RestController
@RequestMapping("/api/auth")
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserService userService;

    /**
     * User registration
     */
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
        AuthResponse response = authService.register(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * User login
     */
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        String clientIP = getClientIP(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");
        
        AuthResponse response = authService.login(request, clientIP, userAgent);
        return ResponseEntity.ok(response);
    }

    /**
     * User logout
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, String>> logout(@RequestHeader("Authorization") String token) {
        authService.logout(token);
        return ResponseEntity.ok(Map.of("message", "Logged out successfully"));
    }

    /**
     * Refresh JWT token
     */
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        AuthResponse response = authService.refreshToken(request.getRefreshToken());
        return ResponseEntity.ok(response);
    }

    /**
     * Forgot password - send reset email
     */
    @PostMapping("/forgot-password")
    public ResponseEntity<Map<String, String>> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        authService.forgotPassword(request.getEmail());
        return ResponseEntity.ok(Map.of("message", "Password reset email sent"));
    }

    /**
     * Reset password with token
     */
    @PostMapping("/reset-password")
    public ResponseEntity<Map<String, String>> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        authService.resetPassword(request.getToken(), request.getNewPassword());
        return ResponseEntity.ok(Map.of("message", "Password reset successfully"));
    }

    /**
     * Change password (authenticated user)
     */
    @PostMapping("/change-password")
    public ResponseEntity<Map<String, String>> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        authService.changePassword(userId, request.getCurrentPassword(), request.getNewPassword());
        return ResponseEntity.ok(Map.of("message", "Password changed successfully"));
    }

    /**
     * Verify email address
     */
    @PostMapping("/verify-email")
    public ResponseEntity<Map<String, String>> verifyEmail(@Valid @RequestBody VerifyEmailRequest request) {
        authService.verifyEmail(request.getToken());
        return ResponseEntity.ok(Map.of("message", "Email verified successfully"));
    }

    /**
     * Resend email verification
     */
    @PostMapping("/resend-verification")
    public ResponseEntity<Map<String, String>> resendVerification(
            @RequestHeader("X-User-ID") String userId) {
        
        authService.resendEmailVerification(userId);
        return ResponseEntity.ok(Map.of("message", "Verification email sent"));
    }

    /**
     * Get current user profile
     */
    @GetMapping("/me")
    public ResponseEntity<UserProfileResponse> getCurrentUser(@RequestHeader("X-User-ID") String userId) {
        UserProfileResponse profile = userService.getUserProfile(userId);
        return ResponseEntity.ok(profile);
    }

    /**
     * Update user profile
     */
    @PutMapping("/profile")
    public ResponseEntity<UserProfileResponse> updateProfile(
            @Valid @RequestBody UpdateProfileRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        UserProfileResponse profile = userService.updateUserProfile(userId, request);
        return ResponseEntity.ok(profile);
    }

    /**
     * Enable two-factor authentication
     */
    @PostMapping("/2fa/enable")
    public ResponseEntity<TwoFactorResponse> enableTwoFactor(@RequestHeader("X-User-ID") String userId) {
        TwoFactorResponse response = authService.enableTwoFactor(userId);
        return ResponseEntity.ok(response);
    }

    /**
     * Verify two-factor authentication setup
     */
    @PostMapping("/2fa/verify")
    public ResponseEntity<Map<String, String>> verifyTwoFactor(
            @Valid @RequestBody VerifyTwoFactorRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        authService.verifyTwoFactor(userId, request.getCode());
        return ResponseEntity.ok(Map.of("message", "Two-factor authentication enabled"));
    }

    /**
     * Disable two-factor authentication
     */
    @PostMapping("/2fa/disable")
    public ResponseEntity<Map<String, String>> disableTwoFactor(
            @Valid @RequestBody DisableTwoFactorRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        authService.disableTwoFactor(userId, request.getPassword());
        return ResponseEntity.ok(Map.of("message", "Two-factor authentication disabled"));
    }

    /**
     * Generate backup codes for 2FA
     */
    @PostMapping("/2fa/backup-codes")
    public ResponseEntity<BackupCodesResponse> generateBackupCodes(@RequestHeader("X-User-ID") String userId) {
        BackupCodesResponse response = authService.generateBackupCodes(userId);
        return ResponseEntity.ok(response);
    }

    /**
     * Validate JWT token
     */
    @PostMapping("/validate")
    public ResponseEntity<TokenValidationResponse> validateToken(@Valid @RequestBody ValidateTokenRequest request) {
        TokenValidationResponse response = authService.validateToken(request.getToken());
        return ResponseEntity.ok(response);
    }

    /**
     * Get user sessions
     */
    @GetMapping("/sessions")
    public ResponseEntity<UserSessionsResponse> getUserSessions(@RequestHeader("X-User-ID") String userId) {
        UserSessionsResponse response = authService.getUserSessions(userId);
        return ResponseEntity.ok(response);
    }

    /**
     * Revoke user session
     */
    @DeleteMapping("/sessions/{sessionId}")
    public ResponseEntity<Map<String, String>> revokeSession(
            @PathVariable String sessionId,
            @RequestHeader("X-User-ID") String userId) {
        
        authService.revokeSession(userId, sessionId);
        return ResponseEntity.ok(Map.of("message", "Session revoked successfully"));
    }

    /**
     * Revoke all user sessions
     */
    @DeleteMapping("/sessions")
    public ResponseEntity<Map<String, String>> revokeAllSessions(@RequestHeader("X-User-ID") String userId) {
        authService.revokeAllSessions(userId);
        return ResponseEntity.ok(Map.of("message", "All sessions revoked successfully"));
    }

    /**
     * Check if email is available
     */
    @GetMapping("/check-email")
    public ResponseEntity<Map<String, Boolean>> checkEmailAvailability(@RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);
        return ResponseEntity.ok(Map.of("available", available));
    }

    /**
     * Check if username is available
     */
    @GetMapping("/check-username")
    public ResponseEntity<Map<String, Boolean>> checkUsernameAvailability(@RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);
        return ResponseEntity.ok(Map.of("available", available));
    }

    /**
     * Get password strength requirements
     */
    @GetMapping("/password-requirements")
    public ResponseEntity<PasswordRequirementsResponse> getPasswordRequirements() {
        PasswordRequirementsResponse requirements = authService.getPasswordRequirements();
        return ResponseEntity.ok(requirements);
    }

    /**
     * Validate password strength
     */
    @PostMapping("/validate-password")
    public ResponseEntity<PasswordValidationResponse> validatePassword(@Valid @RequestBody ValidatePasswordRequest request) {
        PasswordValidationResponse response = authService.validatePassword(request.getPassword());
        return ResponseEntity.ok(response);
    }

    /**
     * Get user login history
     */
    @GetMapping("/login-history")
    public ResponseEntity<LoginHistoryResponse> getLoginHistory(
            @RequestHeader("X-User-ID") String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        LoginHistoryResponse response = authService.getLoginHistory(userId, page, size);
        return ResponseEntity.ok(response);
    }

    /**
     * Report suspicious activity
     */
    @PostMapping("/report-suspicious")
    public ResponseEntity<Map<String, String>> reportSuspiciousActivity(
            @Valid @RequestBody ReportSuspiciousRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        authService.reportSuspiciousActivity(userId, request);
        return ResponseEntity.ok(Map.of("message", "Suspicious activity reported"));
    }

    /**
     * Get account security status
     */
    @GetMapping("/security-status")
    public ResponseEntity<SecurityStatusResponse> getSecurityStatus(@RequestHeader("X-User-ID") String userId) {
        SecurityStatusResponse response = authService.getSecurityStatus(userId);
        return ResponseEntity.ok(response);
    }

    // Helper methods

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0].trim();
    }
}
