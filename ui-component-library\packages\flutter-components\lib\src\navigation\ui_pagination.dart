import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Pagination component
class UIPagination extends StatelessWidget {
  const UIPagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.showFirstLast = true,
    this.showPrevNext = true,
  });

  final int currentPage;
  final int totalPages;
  final Function(int page) onPageChanged;
  final bool showFirstLast;
  final bool showPrevNext;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showFirstLast && currentPage > 1)
          IconButton(
            onPressed: () => onPageChanged(1),
            icon: Icon(Icons.first_page),
          ),
        if (showPrevNext && currentPage > 1)
          IconButton(
            onPressed: () => onPageChanged(currentPage - 1),
            icon: Icon(Icons.chevron_left),
          ),
        Text('Page $currentPage of $totalPages'),
        if (showPrevNext && currentPage < totalPages)
          IconButton(
            onPressed: () => onPageChanged(currentPage + 1),
            icon: Icon(Icons.chevron_right),
          ),
        if (showFirstLast && currentPage < totalPages)
          IconButton(
            onPressed: () => onPageChanged(totalPages),
            icon: Icon(Icons.last_page),
          ),
      ],
    );
  }
}
