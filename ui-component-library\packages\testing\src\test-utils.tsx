import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ThemeProvider, UIBuilderTheme, defaultLightTheme, defaultDarkTheme } from '@ui-builder/core';
import { axe, toHaveNoViolations } from 'jest-axe';
import { queries } from '@testing-library/dom';
import * as customQueries from './custom-queries';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Custom render options
interface CustomRenderOptions extends Omit<RenderOptions, 'queries'> {
  theme?: UIBuilderTheme;
  locale?: string;
  density?: 'compact' | 'normal' | 'comfortable';
  reducedMotion?: boolean;
  highContrast?: boolean;
}

// All queries including custom ones
const allQueries = {
  ...queries,
  ...customQueries,
};

/**
 * Custom render function that wraps components with necessary providers
 */
export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const {
    theme = defaultLightTheme,
    locale = 'en',
    density = 'normal',
    reducedMotion = false,
    highContrast = false,
    ...renderOptions
  } = options;

  const customTheme = {
    ...theme,
    accessibility: {
      ...theme.accessibility,
      reducedMotion,
      highContrast,
    },
  };

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <ThemeProvider 
        initialTheme={customTheme}
        initialAccessibility={{
          reducedMotion,
          highContrast,
        }}
      >
        <div data-testid="test-wrapper" data-density={density} lang={locale}>
          {children}
        </div>
      </ThemeProvider>
    );
  }

  return render(ui, {
    wrapper: Wrapper,
    queries: allQueries,
    ...renderOptions,
  });
}

/**
 * Render component with light theme
 */
export function renderWithLightTheme(ui: ReactElement, options?: CustomRenderOptions) {
  return renderWithProviders(ui, { ...options, theme: defaultLightTheme });
}

/**
 * Render component with dark theme
 */
export function renderWithDarkTheme(ui: ReactElement, options?: CustomRenderOptions) {
  return renderWithProviders(ui, { ...options, theme: defaultDarkTheme });
}

/**
 * Render component with high contrast mode
 */
export function renderWithHighContrast(ui: ReactElement, options?: CustomRenderOptions) {
  return renderWithProviders(ui, { ...options, highContrast: true });
}

/**
 * Render component with reduced motion
 */
export function renderWithReducedMotion(ui: ReactElement, options?: CustomRenderOptions) {
  return renderWithProviders(ui, { ...options, reducedMotion: true });
}

/**
 * Test accessibility of a component
 */
export async function testAccessibility(
  ui: ReactElement,
  options?: CustomRenderOptions
): Promise<void> {
  const { container } = renderWithProviders(ui, options);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
}

/**
 * Test component in both light and dark themes
 */
export async function testThemes(
  ui: ReactElement,
  testFn: (result: RenderResult) => void | Promise<void>
): Promise<void> {
  // Test light theme
  const lightResult = renderWithLightTheme(ui);
  await testFn(lightResult);
  lightResult.unmount();

  // Test dark theme
  const darkResult = renderWithDarkTheme(ui);
  await testFn(darkResult);
  darkResult.unmount();
}

/**
 * Test component accessibility in different modes
 */
export async function testAccessibilityModes(ui: ReactElement): Promise<void> {
  // Test normal mode
  await testAccessibility(ui);

  // Test high contrast mode
  await testAccessibility(ui, { highContrast: true });

  // Test reduced motion mode
  await testAccessibility(ui, { reducedMotion: true });
}

/**
 * Test component in different densities
 */
export async function testDensities(
  ui: ReactElement,
  testFn: (result: RenderResult, density: string) => void | Promise<void>
): Promise<void> {
  const densities = ['compact', 'normal', 'comfortable'] as const;

  for (const density of densities) {
    const result = renderWithProviders(ui, { density });
    await testFn(result, density);
    result.unmount();
  }
}

/**
 * Test component responsiveness
 */
export function testResponsiveness(
  ui: ReactElement,
  breakpoints: Record<string, { width: number; height: number }>
): void {
  Object.entries(breakpoints).forEach(([name, { width, height }]) => {
    // Mock viewport size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: width,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: height,
    });

    // Trigger resize event
    window.dispatchEvent(new Event('resize'));

    const result = renderWithProviders(ui);
    
    // Add custom assertions here based on breakpoint
    expect(result.container).toBeInTheDocument();
    
    result.unmount();
  });
}

/**
 * Mock intersection observer for testing
 */
export function mockIntersectionObserver(): void {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
}

/**
 * Mock resize observer for testing
 */
export function mockResizeObserver(): void {
  const mockResizeObserver = jest.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.ResizeObserver = mockResizeObserver;
}

/**
 * Mock matchMedia for testing
 */
export function mockMatchMedia(): void {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
}

/**
 * Setup common mocks for testing
 */
export function setupTestMocks(): void {
  mockIntersectionObserver();
  mockResizeObserver();
  mockMatchMedia();
}

/**
 * Create a mock theme for testing
 */
export function createMockTheme(overrides: Partial<UIBuilderTheme> = {}): UIBuilderTheme {
  return {
    ...defaultLightTheme,
    ...overrides,
  };
}

/**
 * Wait for next tick (useful for async operations)
 */
export function waitForNextTick(): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, 0));
}

/**
 * Simulate user interaction delay
 */
export function simulateUserDelay(ms: number = 100): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Test component with different prop combinations
 */
export async function testPropCombinations<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  propCombinations: T[],
  testFn: (result: RenderResult, props: T) => void | Promise<void>
): Promise<void> {
  for (const props of propCombinations) {
    const result = renderWithProviders(<Component {...props} />);
    await testFn(result, props);
    result.unmount();
  }
}

/**
 * Test component error boundaries
 */
export function testErrorBoundary(
  ui: ReactElement,
  errorTrigger: () => void
): void {
  const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  
  const result = renderWithProviders(ui);
  
  expect(() => {
    errorTrigger();
  }).not.toThrow();
  
  consoleSpy.mockRestore();
  result.unmount();
}

/**
 * Test component performance
 */
export function testPerformance(
  ui: ReactElement,
  maxRenderTime: number = 16
): void {
  const startTime = performance.now();
  const result = renderWithProviders(ui);
  const endTime = performance.now();
  
  const renderTime = endTime - startTime;
  expect(renderTime).toBeLessThan(maxRenderTime);
  
  result.unmount();
}

/**
 * Test component memory leaks
 */
export async function testMemoryLeaks(
  ui: ReactElement,
  iterations: number = 100
): Promise<void> {
  const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
  
  for (let i = 0; i < iterations; i++) {
    const result = renderWithProviders(ui);
    result.unmount();
  }
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  
  await waitForNextTick();
  
  const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
  const memoryIncrease = finalMemory - initialMemory;
  
  // Allow for some memory increase but flag significant leaks
  expect(memoryIncrease).toBeLessThan(1024 * 1024); // 1MB threshold
}

// Re-export everything from testing library
export * from '@testing-library/react';
export * from '@testing-library/user-event';
export { axe } from 'jest-axe';

// Export custom render as default
export { renderWithProviders as render };
