import React, { ComponentType, ReactElement } from 'react';
import { ComponentConfig, ComponentProps, ComponentValidation } from '../types/component';

export interface RegisteredComponent {
  component: ComponentType<any>;
  config: ComponentConfig;
  validation?: ComponentValidation;
  examples?: ComponentExample[];
  documentation?: string;
  version?: string;
  deprecated?: boolean;
  category?: string;
  tags?: string[];
}

export interface ComponentExample {
  name: string;
  description: string;
  props: Record<string, any>;
  code?: string;
}

export interface ComponentRegistryOptions {
  enableValidation?: boolean;
  enableCaching?: boolean;
  enableLazyLoading?: boolean;
  fallbackComponent?: ComponentType<any>;
}

/**
 * Component Registry for managing dynamic UI components
 * Provides registration, retrieval, validation, and caching of components
 */
export class ComponentRegistry {
  private static instance: ComponentRegistry;
  private components: Map<string, RegisteredComponent> = new Map();
  private componentCache: Map<string, ComponentType<any>> = new Map();
  private lazyComponents: Map<string, () => Promise<ComponentType<any>>> = new Map();
  private options: ComponentRegistryOptions;

  private constructor(options: ComponentRegistryOptions = {}) {
    this.options = {
      enableValidation: true,
      enableCaching: true,
      enableLazyLoading: true,
      ...options
    };
  }

  /**
   * Get singleton instance of ComponentRegistry
   */
  public static getInstance(options?: ComponentRegistryOptions): ComponentRegistry {
    if (!ComponentRegistry.instance) {
      ComponentRegistry.instance = new ComponentRegistry(options);
    }
    return ComponentRegistry.instance;
  }

  /**
   * Register a component with the registry
   */
  public register(
    type: string,
    component: ComponentType<any>,
    config: ComponentConfig,
    metadata?: Partial<RegisteredComponent>
  ): void {
    if (this.components.has(type)) {
      console.warn(`Component "${type}" is already registered. Overwriting...`);
    }

    const registeredComponent: RegisteredComponent = {
      component,
      config,
      validation: metadata?.validation,
      examples: metadata?.examples,
      documentation: metadata?.documentation,
      version: metadata?.version || '1.0.0',
      deprecated: metadata?.deprecated || false,
      category: metadata?.category || 'general',
      tags: metadata?.tags || []
    };

    this.components.set(type, registeredComponent);

    // Clear cache for this component if caching is enabled
    if (this.options.enableCaching) {
      this.componentCache.delete(type);
    }

    console.log(`Registered component: ${type}`);
  }

  /**
   * Register a lazy-loaded component
   */
  public registerLazy(
    type: string,
    loader: () => Promise<ComponentType<any>>,
    config: ComponentConfig,
    metadata?: Partial<RegisteredComponent>
  ): void {
    if (!this.options.enableLazyLoading) {
      throw new Error('Lazy loading is disabled in registry options');
    }

    this.lazyComponents.set(type, loader);
    
    // Register placeholder component
    this.register(type, this.createLazyWrapper(type, loader), config, metadata);
  }

  /**
   * Get a component by type
   */
  public getComponent(type: string): ComponentType<any> | null {
    // Check cache first
    if (this.options.enableCaching && this.componentCache.has(type)) {
      return this.componentCache.get(type)!;
    }

    const registered = this.components.get(type);
    if (!registered) {
      console.warn(`Component "${type}" not found in registry`);
      return this.options.fallbackComponent || null;
    }

    if (registered.deprecated) {
      console.warn(`Component "${type}" is deprecated: ${registered.documentation}`);
    }

    // Cache the component if caching is enabled
    if (this.options.enableCaching) {
      this.componentCache.set(type, registered.component);
    }

    return registered.component;
  }

  /**
   * Get component configuration
   */
  public getConfig(type: string): ComponentConfig | null {
    const registered = this.components.get(type);
    return registered?.config || null;
  }

  /**
   * Get component metadata
   */
  public getMetadata(type: string): RegisteredComponent | null {
    return this.components.get(type) || null;
  }

  /**
   * Check if a component is registered
   */
  public hasComponent(type: string): boolean {
    return this.components.has(type);
  }

  /**
   * Get all registered component types
   */
  public getComponentTypes(): string[] {
    return Array.from(this.components.keys());
  }

  /**
   * Get components by category
   */
  public getComponentsByCategory(category: string): string[] {
    return Array.from(this.components.entries())
      .filter(([, registered]) => registered.category === category)
      .map(([type]) => type);
  }

  /**
   * Get components by tag
   */
  public getComponentsByTag(tag: string): string[] {
    return Array.from(this.components.entries())
      .filter(([, registered]) => registered.tags?.includes(tag))
      .map(([type]) => type);
  }

  /**
   * Search components by name or description
   */
  public searchComponents(query: string): string[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.components.entries())
      .filter(([type, registered]) => 
        type.toLowerCase().includes(lowerQuery) ||
        registered.config.displayName?.toLowerCase().includes(lowerQuery) ||
        registered.config.description?.toLowerCase().includes(lowerQuery)
      )
      .map(([type]) => type);
  }

  /**
   * Validate component props
   */
  public validateProps(type: string, props: ComponentProps): ValidationResult {
    if (!this.options.enableValidation) {
      return { isValid: true, errors: [] };
    }

    const registered = this.components.get(type);
    if (!registered || !registered.validation) {
      return { isValid: true, errors: [] };
    }

    return this.performValidation(props, registered.validation);
  }

  /**
   * Unregister a component
   */
  public unregister(type: string): boolean {
    const deleted = this.components.delete(type);
    if (deleted) {
      this.componentCache.delete(type);
      this.lazyComponents.delete(type);
      console.log(`Unregistered component: ${type}`);
    }
    return deleted;
  }

  /**
   * Clear all registered components
   */
  public clear(): void {
    this.components.clear();
    this.componentCache.clear();
    this.lazyComponents.clear();
    console.log('Cleared all registered components');
  }

  /**
   * Get registry statistics
   */
  public getStats(): RegistryStats {
    const components = Array.from(this.components.values());
    const categories = new Set(components.map(c => c.category));
    const deprecated = components.filter(c => c.deprecated).length;
    const lazy = this.lazyComponents.size;

    return {
      totalComponents: this.components.size,
      categories: Array.from(categories),
      deprecatedComponents: deprecated,
      lazyComponents: lazy,
      cachedComponents: this.componentCache.size
    };
  }

  /**
   * Export registry configuration
   */
  public exportConfig(): RegistryConfig {
    const components: Record<string, ComponentConfig> = {};
    
    this.components.forEach((registered, type) => {
      components[type] = registered.config;
    });

    return {
      version: '1.0.0',
      components,
      options: this.options
    };
  }

  /**
   * Import registry configuration
   */
  public importConfig(config: RegistryConfig): void {
    Object.entries(config.components).forEach(([type, componentConfig]) => {
      // Note: This only imports config, actual components need to be registered separately
      console.log(`Config imported for component: ${type}`);
    });
  }

  /**
   * Create a lazy wrapper component
   */
  private createLazyWrapper(
    type: string, 
    loader: () => Promise<ComponentType<any>>
  ): ComponentType<any> {
    return React.lazy(async () => {
      try {
        const Component = await loader();
        
        // Update the registry with the loaded component
        const registered = this.components.get(type);
        if (registered) {
          registered.component = Component;
        }

        return { default: Component };
      } catch (error) {
        console.error(`Failed to load lazy component "${type}":`, error);
        
        // Return fallback component
        const FallbackComponent = this.options.fallbackComponent || (() => 
          React.createElement('div', { 
            style: { 
              padding: '16px', 
              border: '1px dashed #ccc', 
              textAlign: 'center' as const,
              color: '#999'
            } 
          }, `Failed to load component: ${type}`)
        );
        
        return { default: FallbackComponent };
      }
    });
  }

  /**
   * Perform prop validation
   */
  private performValidation(props: ComponentProps, validation: ComponentValidation): ValidationResult {
    const errors: string[] = [];

    // Required props validation
    if (validation.required) {
      validation.required.forEach(prop => {
        if (!(prop in props) || props[prop] === undefined || props[prop] === null) {
          errors.push(`Required prop "${prop}" is missing`);
        }
      });
    }

    // Type validation
    if (validation.types) {
      Object.entries(validation.types).forEach(([prop, expectedType]) => {
        if (prop in props && props[prop] !== undefined) {
          const actualType = typeof props[prop];
          if (actualType !== expectedType) {
            errors.push(`Prop "${prop}" should be of type "${expectedType}", got "${actualType}"`);
          }
        }
      });
    }

    // Custom validation
    if (validation.custom) {
      validation.custom.forEach(validator => {
        try {
          const result = validator(props);
          if (typeof result === 'string') {
            errors.push(result);
          } else if (result === false) {
            errors.push('Custom validation failed');
          }
        } catch (error) {
          errors.push(`Validation error: ${error.message}`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface RegistryStats {
  totalComponents: number;
  categories: string[];
  deprecatedComponents: number;
  lazyComponents: number;
  cachedComponents: number;
}

export interface RegistryConfig {
  version: string;
  components: Record<string, ComponentConfig>;
  options: ComponentRegistryOptions;
}

// Export singleton instance
export const componentRegistry = ComponentRegistry.getInstance();

// Export convenience methods
export const registerComponent = componentRegistry.register.bind(componentRegistry);
export const registerLazyComponent = componentRegistry.registerLazy.bind(componentRegistry);
export const getComponent = componentRegistry.getComponent.bind(componentRegistry);
export const hasComponent = componentRegistry.hasComponent.bind(componentRegistry);
export const getComponentConfig = componentRegistry.getConfig.bind(componentRegistry);
export const validateComponentProps = componentRegistry.validateProps.bind(componentRegistry);
