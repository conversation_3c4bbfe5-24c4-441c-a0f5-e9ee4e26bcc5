import 'package:flutter/material.dart';
import '../types/variant_types.dart';

/// Responsive design utilities for UI Builder components
class ResponsiveUtils {
  const ResponsiveUtils._();

  /// Breakpoint values in pixels
  static const double mobileBreakpoint = 576;
  static const double tabletBreakpoint = 768;
  static const double desktopBreakpoint = 992;
  static const double largeDesktopBreakpoint = 1200;
  static const double extraLargeBreakpoint = 1400;

  /// Get current screen breakpoint
  static UIBreakpoint getBreakpoint(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= extraLargeBreakpoint) return UIBreakpoint.xxl;
    if (width >= largeDesktopBreakpoint) return UIBreakpoint.xl;
    if (width >= desktopBreakpoint) return UIBreakpoint.lg;
    if (width >= tabletBreakpoint) return UIBreakpoint.md;
    if (width >= mobileBreakpoint) return UIBreakpoint.sm;
    return UIBreakpoint.xs;
  }

  /// Check if current screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < tabletBreakpoint;
  }

  /// Check if current screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= tabletBreakpoint && width < desktopBreakpoint;
  }

  /// Check if current screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
    T? extraLarge,
  }) {
    final breakpoint = getBreakpoint(context);
    
    switch (breakpoint) {
      case UIBreakpoint.xxl:
        return extraLarge ?? largeDesktop ?? desktop ?? tablet ?? mobile;
      case UIBreakpoint.xl:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
      case UIBreakpoint.lg:
        return desktop ?? tablet ?? mobile;
      case UIBreakpoint.md:
        return tablet ?? mobile;
      case UIBreakpoint.sm:
      case UIBreakpoint.xs:
        return mobile;
    }
  }

  /// Get responsive padding
  static EdgeInsets getResponsivePadding(
    BuildContext context, {
    EdgeInsets mobile = const EdgeInsets.all(16),
    EdgeInsets? tablet,
    EdgeInsets? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive margin
  static EdgeInsets getResponsiveMargin(
    BuildContext context, {
    EdgeInsets mobile = const EdgeInsets.all(8),
    EdgeInsets? tablet,
    EdgeInsets? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive font size
  static double getResponsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive spacing
  static double getResponsiveSpacing(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive columns for grid
  static int getResponsiveColumns(
    BuildContext context, {
    int mobile = 1,
    int? tablet,
    int? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive width
  static double? getResponsiveWidth(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive height
  static double? getResponsiveHeight(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive max width for containers
  static double getResponsiveMaxWidth(BuildContext context) {
    final breakpoint = getBreakpoint(context);
    
    switch (breakpoint) {
      case UIBreakpoint.xxl:
        return 1320;
      case UIBreakpoint.xl:
        return 1140;
      case UIBreakpoint.lg:
        return 960;
      case UIBreakpoint.md:
        return 720;
      case UIBreakpoint.sm:
        return 540;
      case UIBreakpoint.xs:
        return double.infinity;
    }
  }

  /// Check if screen width matches breakpoint
  static bool matchesBreakpoint(BuildContext context, UIBreakpoint breakpoint) {
    return getBreakpoint(context) == breakpoint;
  }

  /// Check if screen width is at least the given breakpoint
  static bool isAtLeastBreakpoint(BuildContext context, UIBreakpoint breakpoint) {
    final currentBreakpoint = getBreakpoint(context);
    return currentBreakpoint.index >= breakpoint.index;
  }

  /// Check if screen width is at most the given breakpoint
  static bool isAtMostBreakpoint(BuildContext context, UIBreakpoint breakpoint) {
    final currentBreakpoint = getBreakpoint(context);
    return currentBreakpoint.index <= breakpoint.index;
  }

  /// Get responsive aspect ratio
  static double getResponsiveAspectRatio(
    BuildContext context, {
    double mobile = 16 / 9,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive border radius
  static BorderRadius getResponsiveBorderRadius(
    BuildContext context, {
    BorderRadius mobile = const BorderRadius.all(Radius.circular(8)),
    BorderRadius? tablet,
    BorderRadius? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive elevation
  static double getResponsiveElevation(
    BuildContext context, {
    double mobile = 2,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}

/// Responsive widget builder
class ResponsiveBuilder extends StatelessWidget {
  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    this.extraLarge,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;
  final Widget? extraLarge;

  @override
  Widget build(BuildContext context) {
    return ResponsiveUtils.getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
      extraLarge: extraLarge,
    );
  }
}

/// Responsive layout widget
class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.alignment = Alignment.center,
  });

  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    final responsiveMaxWidth = maxWidth ?? ResponsiveUtils.getResponsiveMaxWidth(context);
    final responsivePadding = padding ?? ResponsiveUtils.getResponsivePadding(context);
    final responsiveMargin = margin ?? ResponsiveUtils.getResponsiveMargin(context);

    return Container(
      width: double.infinity,
      margin: responsiveMargin,
      alignment: alignment,
      child: Container(
        constraints: BoxConstraints(maxWidth: responsiveMaxWidth),
        padding: responsivePadding,
        child: child,
      ),
    );
  }
}

/// Responsive grid widget
class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16,
    this.runSpacing,
  });

  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double? runSpacing;

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.getResponsiveValue(
      context,
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing ?? spacing,
      children: children.map((child) {
        return SizedBox(
          width: (MediaQuery.of(context).size.width - (spacing * (columns - 1))) / columns,
          child: child,
        );
      }).toList(),
    );
  }
}
