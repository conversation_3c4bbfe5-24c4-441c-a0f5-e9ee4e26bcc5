import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';
import 'package:widgetbook_annotation/widgetbook_annotation.dart' as widgetbook;

// Import your widgets and components
import 'package:flutter_ui_runtime/widgets/text_widget.dart';
import 'package:flutter_ui_runtime/widgets/button_widget.dart';
import 'package:flutter_ui_runtime/widgets/container_widget.dart';
import 'package:flutter_ui_runtime/widgets/input_widget.dart';
import 'package:flutter_ui_runtime/widgets/image_widget.dart';
import 'package:flutter_ui_runtime/core/theme_manager.dart';

// This file should be generated automatically by widgetbook_generator
import 'main.directories.g.dart';

void main() {
  runApp(const WidgetbookApp());
}

@widgetbook.App()
class WidgetbookApp extends StatelessWidget {
  const WidgetbookApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Widgetbook.material(
      // Use the generated directories variable
      directories: directories,
      
      // App configuration
      appBuilder: (context, child) {
        return MaterialApp(
          title: 'Flutter UI Runtime Widgetbook',
          home: child,
          debugShowCheckedModeBanner: false,
        );
      },
      
      // Add-ons for enhanced functionality
      addOns: [
        // Device frame add-on
        DeviceFrameAddon(
          devices: [
            Devices.ios.iPhoneSE,
            Devices.ios.iPhone12,
            Devices.ios.iPhone13ProMax,
            Devices.android.samsungGalaxyS20,
            Devices.android.samsungGalaxyNote20,
            Devices.android.samsungGalaxyA50,
            Devices.generic.tablet,
            Devices.generic.desktop,
          ],
          initialDevice: Devices.ios.iPhone12,
        ),
        
        // Theme add-on
        MaterialThemeAddon(
          themes: [
            WidgetbookTheme(
              name: 'Light Theme',
              data: ThemeData.light().copyWith(
                primarySwatch: Colors.blue,
                visualDensity: VisualDensity.adaptivePlatformDensity,
              ),
            ),
            WidgetbookTheme(
              name: 'Dark Theme',
              data: ThemeData.dark().copyWith(
                primarySwatch: Colors.blue,
                visualDensity: VisualDensity.adaptivePlatformDensity,
              ),
            ),
            WidgetbookTheme(
              name: 'Custom Theme',
              data: ThemeData(
                primarySwatch: Colors.purple,
                brightness: Brightness.light,
                visualDensity: VisualDensity.adaptivePlatformDensity,
                fontFamily: 'Roboto',
              ),
            ),
          ],
        ),
        
        // Text scale add-on
        TextScaleAddon(
          scales: [0.8, 1.0, 1.2, 1.5, 2.0],
          initialScale: 1.0,
        ),
        
        // Locale add-on
        LocalizationAddon(
          locales: [
            const Locale('en', 'US'),
            const Locale('es', 'ES'),
            const Locale('fr', 'FR'),
            const Locale('de', 'DE'),
            const Locale('ja', 'JP'),
            const Locale('zh', 'CN'),
          ],
          localizationsDelegates: [
            DefaultMaterialLocalizations.delegate,
            DefaultWidgetsLocalizations.delegate,
          ],
        ),
        
        // Accessibility add-on
        AccessibilityAddon(),
        
        // Grid overlay add-on
        GridAddon(),
        
        // Inspector add-on
        InspectorAddon(enabled: true),
      ],
      
      // Initial route
      initialRoute: '/',
    );
  }
}

// Widget use cases for documentation

@widgetbook.UseCase(
  name: 'Default',
  type: DynamicTextWidget,
)
Widget textWidgetDefault(BuildContext context) {
  return const DynamicTextWidget(
    definition: ComponentDefinition(
      id: 'text-1',
      type: 'text',
      properties: {
        'text': 'Hello, World!',
        'fontSize': 16.0,
        'color': '#000000',
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Large Text',
  type: DynamicTextWidget,
)
Widget textWidgetLarge(BuildContext context) {
  return const DynamicTextWidget(
    definition: ComponentDefinition(
      id: 'text-2',
      type: 'text',
      properties: {
        'text': 'Large Text Example',
        'fontSize': 24.0,
        'fontWeight': 'bold',
        'color': '#2196F3',
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Primary Button',
  type: DynamicButtonWidget,
)
Widget buttonWidgetPrimary(BuildContext context) {
  return DynamicButtonWidget(
    definition: ComponentDefinition(
      id: 'button-1',
      type: 'button',
      properties: {
        'text': 'Primary Button',
        'variant': 'elevated',
        'disabled': false,
      },
      actions: {
        'onPressed': {
          'type': 'navigate',
          'params': {'route': '/dashboard'},
        },
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Outlined Button',
  type: DynamicButtonWidget,
)
Widget buttonWidgetOutlined(BuildContext context) {
  return DynamicButtonWidget(
    definition: ComponentDefinition(
      id: 'button-2',
      type: 'button',
      properties: {
        'text': 'Outlined Button',
        'variant': 'outlined',
        'disabled': false,
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Disabled Button',
  type: DynamicButtonWidget,
)
Widget buttonWidgetDisabled(BuildContext context) {
  return DynamicButtonWidget(
    definition: ComponentDefinition(
      id: 'button-3',
      type: 'button',
      properties: {
        'text': 'Disabled Button',
        'variant': 'elevated',
        'disabled': true,
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Basic Container',
  type: DynamicContainerWidget,
)
Widget containerWidgetBasic(BuildContext context) {
  return DynamicContainerWidget(
    definition: ComponentDefinition(
      id: 'container-1',
      type: 'container',
      properties: {
        'width': 200.0,
        'height': 100.0,
        'color': '#E3F2FD',
        'padding': {
          'top': 16.0,
          'right': 16.0,
          'bottom': 16.0,
          'left': 16.0,
        },
      },
      children: [
        ComponentDefinition(
          id: 'child-text',
          type: 'text',
          properties: {
            'text': 'Container Content',
            'fontSize': 14.0,
          },
        ),
      ],
    ),
  );
}

@widgetbook.UseCase(
  name: 'Text Input',
  type: DynamicInputWidget,
)
Widget inputWidgetText(BuildContext context) {
  return DynamicInputWidget(
    definition: ComponentDefinition(
      id: 'input-1',
      type: 'input',
      properties: {
        'placeholder': 'Enter your name',
        'type': 'text',
        'required': false,
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Email Input',
  type: DynamicInputWidget,
)
Widget inputWidgetEmail(BuildContext context) {
  return DynamicInputWidget(
    definition: ComponentDefinition(
      id: 'input-2',
      type: 'input',
      properties: {
        'placeholder': 'Enter your email',
        'type': 'email',
        'required': true,
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Network Image',
  type: DynamicImageWidget,
)
Widget imageWidgetNetwork(BuildContext context) {
  return const DynamicImageWidget(
    definition: ComponentDefinition(
      id: 'image-1',
      type: 'image',
      properties: {
        'src': 'https://picsum.photos/300/200',
        'width': 300.0,
        'height': 200.0,
        'fit': 'cover',
        'alt': 'Sample image',
      },
    ),
  );
}

@widgetbook.UseCase(
  name: 'Rounded Image',
  type: DynamicImageWidget,
)
Widget imageWidgetRounded(BuildContext context) {
  return const DynamicImageWidget(
    definition: ComponentDefinition(
      id: 'image-2',
      type: 'image',
      properties: {
        'src': 'https://picsum.photos/150/150',
        'width': 150.0,
        'height': 150.0,
        'fit': 'cover',
        'borderRadius': 75.0,
        'alt': 'Rounded image',
      },
    ),
  );
}
