import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

/// Application logger utility
/// Provides structured logging with different levels and formatting
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
    level: kDebugMode ? Level.debug : Level.info,
  );

  /// Log debug message
  static void debug(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  static void info(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  static void fatal(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log API request
  static void apiRequest(String method, String url, {Map<String, dynamic>? data}) {
    _logger.d('API Request: $method $url', error: data);
  }

  /// Log API response
  static void apiResponse(String method, String url, int statusCode, {dynamic data}) {
    _logger.d('API Response: $method $url [$statusCode]', error: data);
  }

  /// Log navigation event
  static void navigation(String from, String to) {
    _logger.i('Navigation: $from -> $to');
  }

  /// Log user action
  static void userAction(String action, {Map<String, dynamic>? data}) {
    _logger.i('User Action: $action', error: data);
  }

  /// Log performance metric
  static void performance(String metric, Duration duration, {Map<String, dynamic>? data}) {
    _logger.i('Performance: $metric took ${duration.inMilliseconds}ms', error: data);
  }
}
