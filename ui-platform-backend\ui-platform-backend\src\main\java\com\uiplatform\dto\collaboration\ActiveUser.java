package com.uiplatform.dto.collaboration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO representing an active user in a collaboration session.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActiveUser {
    
    private UUID userId;
    private String username;
    private String firstName;
    private String lastName;
    private String avatarUrl;
    private String userColor; // Color assigned for visual identification
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime connectedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastActivity;
    
    private String status; // "active", "away", "idle"
    private UUID currentConfigId;
    private String currentElementId;
    private CursorPosition currentCursor;
    
    // Constructors
    public ActiveUser() {}
    
    public ActiveUser(UUID userId, String username) {
        this.userId = userId;
        this.username = username;
        this.connectedAt = LocalDateTime.now();
        this.lastActivity = LocalDateTime.now();
        this.status = "active";
    }
    
    // Getters and Setters
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getUserColor() {
        return userColor;
    }
    
    public void setUserColor(String userColor) {
        this.userColor = userColor;
    }
    
    public LocalDateTime getConnectedAt() {
        return connectedAt;
    }
    
    public void setConnectedAt(LocalDateTime connectedAt) {
        this.connectedAt = connectedAt;
    }
    
    public LocalDateTime getLastActivity() {
        return lastActivity;
    }
    
    public void setLastActivity(LocalDateTime lastActivity) {
        this.lastActivity = lastActivity;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public UUID getCurrentConfigId() {
        return currentConfigId;
    }
    
    public void setCurrentConfigId(UUID currentConfigId) {
        this.currentConfigId = currentConfigId;
    }
    
    public String getCurrentElementId() {
        return currentElementId;
    }
    
    public void setCurrentElementId(String currentElementId) {
        this.currentElementId = currentElementId;
    }
    
    public CursorPosition getCurrentCursor() {
        return currentCursor;
    }
    
    public void setCurrentCursor(CursorPosition currentCursor) {
        this.currentCursor = currentCursor;
    }
    
    /**
     * Get user's display name (first name + last name or username).
     */
    public String getDisplayName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        }
        return username;
    }
    
    /**
     * Get user's initials for avatar display.
     */
    public String getInitials() {
        if (firstName != null && lastName != null) {
            return (firstName.substring(0, 1) + lastName.substring(0, 1)).toUpperCase();
        }
        if (username != null && username.length() >= 2) {
            return username.substring(0, 2).toUpperCase();
        }
        return "U";
    }
    
    @Override
    public String toString() {
        return "ActiveUser{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", status='" + status + '\'' +
                ", connectedAt=" + connectedAt +
                ", lastActivity=" + lastActivity +
                '}';
    }
}
