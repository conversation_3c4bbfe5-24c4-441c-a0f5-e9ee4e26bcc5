package com.uiplatform.dto.collaboration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for collaboration events in real-time communication.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CollaborationEvent {
    
    private CollaborationEventType type;
    private UUID userId;
    private String username;
    private UUID configId;
    private String elementId;
    private Object data;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;
    
    // Constructors
    public CollaborationEvent() {}
    
    public CollaborationEvent(CollaborationEventType type, UUID userId, UUID configId) {
        this.type = type;
        this.userId = userId;
        this.configId = configId;
        this.timestamp = LocalDateTime.now();
    }
    
    // Get<PERSON> and Setters
    public CollaborationEventType getType() {
        return type;
    }
    
    public void setType(CollaborationEventType type) {
        this.type = type;
    }
    
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public UUID getConfigId() {
        return configId;
    }
    
    public void setConfigId(UUID configId) {
        this.configId = configId;
    }
    
    public String getElementId() {
        return elementId;
    }
    
    public void setElementId(String elementId) {
        this.elementId = elementId;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "CollaborationEvent{" +
                "type=" + type +
                ", userId=" + userId +
                ", username='" + username + '\'' +
                ", configId=" + configId +
                ", elementId='" + elementId + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}


