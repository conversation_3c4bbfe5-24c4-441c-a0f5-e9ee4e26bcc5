import { useEffect, useCallback, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { debounce, throttle } from 'lodash';
import { RootState } from '../store';
import { updateUIConfig, setLastSaved, setSyncStatus } from '../store/slices/uiBuilderSlice';
import { useCollaboration } from './useCollaboration';
import { apiClient } from '../services/apiClient';

export interface SyncOptions {
  configId: string;
  enabled?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number;
  conflictResolution?: 'client' | 'server' | 'merge' | 'prompt';
  optimisticUpdates?: boolean;
}

export interface SyncStatus {
  isSyncing: boolean;
  lastSyncTime: Date | null;
  hasUnsavedChanges: boolean;
  syncError: string | null;
  conflictDetected: boolean;
}

export interface UseRealTimeSyncReturn {
  syncStatus: SyncStatus;
  saveConfig: () => Promise<void>;
  revertChanges: () => Promise<void>;
  resolveConflict: (resolution: 'client' | 'server' | 'merge') => Promise<void>;
  enableAutoSave: () => void;
  disableAutoSave: () => void;
}

export const useRealTimeSync = (options: SyncOptions): UseRealTimeSyncReturn => {
  const {
    configId,
    enabled = true,
    autoSave = true,
    autoSaveInterval = 30000, // 30 seconds
    conflictResolution = 'prompt',
    optimisticUpdates = true
  } = options;

  const dispatch = useDispatch();
  const { currentConfig, lastSaved, syncStatus } = useSelector((state: RootState) => state.uiBuilder);
  const { broadcastConfigChange } = useCollaboration({ configId, enabled });

  const [localSyncStatus, setLocalSyncStatus] = useState<SyncStatus>({
    isSyncing: false,
    lastSyncTime: null,
    hasUnsavedChanges: false,
    syncError: null,
    conflictDetected: false
  });

  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastServerVersionRef = useRef<number>(0);
  const pendingChangesRef = useRef<any[]>([]);
  const isManualSaveRef = useRef(false);

  // Debounced save function to avoid too frequent saves
  const debouncedSave = useCallback(
    debounce(async () => {
      if (!isManualSaveRef.current && autoSave && enabled) {
        await saveConfigToServer();
      }
    }, 2000),
    [autoSave, enabled, configId]
  );

  // Throttled broadcast function for real-time collaboration
  const throttledBroadcast = useCallback(
    throttle((changes: any) => {
      if (enabled) {
        broadcastConfigChange(changes);
      }
    }, 500),
    [enabled, broadcastConfigChange]
  );

  // Save configuration to server
  const saveConfigToServer = useCallback(async () => {
    if (!currentConfig || !enabled) return;

    setLocalSyncStatus(prev => ({ ...prev, isSyncing: true, syncError: null }));
    dispatch(setSyncStatus({ isSyncing: true, syncError: null }));

    try {
      const response = await apiClient.put(`/ui-configs/${configId}`, {
        name: currentConfig.name,
        description: currentConfig.description,
        configData: currentConfig.configData,
        metadata: currentConfig.metadata,
        version: currentConfig.version
      });

      const updatedConfig = response.data;
      
      // Check for version conflicts
      if (updatedConfig.version !== currentConfig.version + 1) {
        setLocalSyncStatus(prev => ({ 
          ...prev, 
          conflictDetected: true,
          syncError: 'Version conflict detected'
        }));
        
        if (conflictResolution === 'prompt') {
          // Let the user decide how to resolve the conflict
          return;
        } else {
          await handleConflictResolution(conflictResolution, updatedConfig);
        }
      } else {
        // Successful save
        dispatch(updateUIConfig(updatedConfig));
        dispatch(setLastSaved(new Date().toISOString()));
        lastServerVersionRef.current = updatedConfig.version;
        
        setLocalSyncStatus(prev => ({
          ...prev,
          isSyncing: false,
          lastSyncTime: new Date(),
          hasUnsavedChanges: false,
          conflictDetected: false
        }));
      }
    } catch (error: any) {
      console.error('Failed to save configuration:', error);
      setLocalSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        syncError: error.message || 'Failed to save configuration'
      }));
      dispatch(setSyncStatus({ 
        isSyncing: false, 
        syncError: error.message || 'Failed to save configuration' 
      }));
    }
  }, [currentConfig, enabled, configId, conflictResolution, dispatch]);

  // Handle conflict resolution
  const handleConflictResolution = useCallback(async (
    resolution: 'client' | 'server' | 'merge',
    serverConfig?: any
  ) => {
    try {
      switch (resolution) {
        case 'client':
          // Force save client version
          await apiClient.put(`/ui-configs/${configId}`, {
            ...currentConfig,
            version: serverConfig?.version || currentConfig.version,
            forceUpdate: true
          });
          break;

        case 'server':
          // Accept server version
          if (serverConfig) {
            dispatch(updateUIConfig(serverConfig));
            lastServerVersionRef.current = serverConfig.version;
          } else {
            // Fetch latest from server
            const response = await apiClient.get(`/ui-configs/${configId}`);
            dispatch(updateUIConfig(response.data));
            lastServerVersionRef.current = response.data.version;
          }
          break;

        case 'merge':
          // Attempt to merge changes
          const mergedConfig = await performConfigMerge(currentConfig, serverConfig);
          const response = await apiClient.put(`/ui-configs/${configId}`, {
            ...mergedConfig,
            version: serverConfig?.version || currentConfig.version
          });
          dispatch(updateUIConfig(response.data));
          lastServerVersionRef.current = response.data.version;
          break;
      }

      setLocalSyncStatus(prev => ({
        ...prev,
        conflictDetected: false,
        syncError: null,
        hasUnsavedChanges: false
      }));
    } catch (error: any) {
      setLocalSyncStatus(prev => ({
        ...prev,
        syncError: `Conflict resolution failed: ${error.message}`
      }));
    }
  }, [currentConfig, configId, dispatch]);

  // Perform intelligent merge of configurations
  const performConfigMerge = useCallback(async (clientConfig: any, serverConfig: any) => {
    // Simple merge strategy - can be enhanced with operational transforms
    const merged = {
      ...serverConfig,
      configData: {
        ...serverConfig.configData,
        components: mergeComponents(
          clientConfig.configData?.components || [],
          serverConfig.configData?.components || []
        )
      }
    };

    return merged;
  }, []);

  // Merge component arrays with conflict resolution
  const mergeComponents = useCallback((clientComponents: any[], serverComponents: any[]) => {
    const merged = [...serverComponents];
    const serverIds = new Set(serverComponents.map(c => c.id));

    // Add client components that don't exist on server
    clientComponents.forEach(clientComp => {
      if (!serverIds.has(clientComp.id)) {
        merged.push(clientComp);
      }
    });

    return merged;
  }, []);

  // Manual save function
  const saveConfig = useCallback(async () => {
    isManualSaveRef.current = true;
    await saveConfigToServer();
    isManualSaveRef.current = false;
  }, [saveConfigToServer]);

  // Revert to last saved version
  const revertChanges = useCallback(async () => {
    try {
      const response = await apiClient.get(`/ui-configs/${configId}`);
      dispatch(updateUIConfig(response.data));
      lastServerVersionRef.current = response.data.version;
      
      setLocalSyncStatus(prev => ({
        ...prev,
        hasUnsavedChanges: false,
        conflictDetected: false,
        syncError: null
      }));
    } catch (error: any) {
      setLocalSyncStatus(prev => ({
        ...prev,
        syncError: `Failed to revert changes: ${error.message}`
      }));
    }
  }, [configId, dispatch]);

  // Resolve conflict with specified strategy
  const resolveConflict = useCallback(async (resolution: 'client' | 'server' | 'merge') => {
    await handleConflictResolution(resolution);
  }, [handleConflictResolution]);

  // Enable auto-save
  const enableAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearInterval(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setInterval(() => {
      if (localSyncStatus.hasUnsavedChanges && !localSyncStatus.isSyncing) {
        debouncedSave();
      }
    }, autoSaveInterval);
  }, [autoSaveInterval, localSyncStatus.hasUnsavedChanges, localSyncStatus.isSyncing, debouncedSave]);

  // Disable auto-save
  const disableAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearInterval(autoSaveTimeoutRef.current);
      autoSaveTimeoutRef.current = null;
    }
  }, []);

  // Track configuration changes
  useEffect(() => {
    if (currentConfig && lastSaved) {
      const hasChanges = new Date(currentConfig.updatedAt || 0) > new Date(lastSaved);
      
      setLocalSyncStatus(prev => ({
        ...prev,
        hasUnsavedChanges: hasChanges
      }));

      if (hasChanges && optimisticUpdates) {
        // Broadcast changes for real-time collaboration
        throttledBroadcast({
          type: 'config-update',
          configId,
          changes: currentConfig,
          timestamp: new Date().toISOString()
        });

        // Trigger auto-save
        if (autoSave && enabled) {
          debouncedSave();
        }
      }
    }
  }, [currentConfig, lastSaved, optimisticUpdates, autoSave, enabled, configId, throttledBroadcast, debouncedSave]);

  // Initialize auto-save
  useEffect(() => {
    if (autoSave && enabled) {
      enableAutoSave();
    }

    return () => {
      disableAutoSave();
    };
  }, [autoSave, enabled, enableAutoSave, disableAutoSave]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedSave.cancel();
      throttledBroadcast.cancel();
      disableAutoSave();
    };
  }, [debouncedSave, throttledBroadcast, disableAutoSave]);

  return {
    syncStatus: localSyncStatus,
    saveConfig,
    revertChanges,
    resolveConflict,
    enableAutoSave,
    disableAutoSave
  };
};
