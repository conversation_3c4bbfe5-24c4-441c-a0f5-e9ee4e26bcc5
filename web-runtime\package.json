{"name": "ui-builder-web-runtime", "version": "1.0.0", "description": "Dynamic UI Runtime Application - Renders UI configurations from JSON metadata", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "build:analyze": "vite build --mode analyze", "serve": "vite preview --port 3001"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "immer": "^10.0.3", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-error-boundary": "^4.0.11", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.5", "recharts": "^2.8.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "workbox-window": "^7.0.0", "idb": "^7.1.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "typescript": "^5.2.2", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jsdom": "^22.1.0", "vite-plugin-pwa": "^0.17.4", "vite-bundle-analyzer": "^0.7.0", "rollup-plugin-visualizer": "^5.9.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}