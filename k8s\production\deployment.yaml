apiVersion: v1
kind: Namespace
metadata:
  name: ui-builder-production
  labels:
    name: ui-builder-production
    environment: production

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: ui-builder-production
  labels:
    app: ui-builder-backend
    version: green
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ui-builder-backend
      version: green
  template:
    metadata:
      labels:
        app: ui-builder-backend
        version: green
    spec:
      containers:
      - name: backend
        image: ghcr.io/ui-builder/backend:IMAGE_TAG
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: password
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: KAFKA_BOOTSTRAP_SERVERS
          valueFrom:
            configMapKeyRef:
              name: kafka-config
              key: bootstrap-servers
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: backend-config
      - name: logs-volume
        emptyDir: {}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  namespace: ui-builder-production
  labels:
    app: ui-builder-frontend
    version: green
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ui-builder-frontend
      version: green
  template:
    metadata:
      labels:
        app: ui-builder-frontend
        version: green
    spec:
      containers:
      - name: frontend
        image: ghcr.io/ui-builder/frontend:IMAGE_TAG
        ports:
        - containerPort: 80
          name: http
        env:
        - name: REACT_APP_API_URL
          valueFrom:
            configMapKeyRef:
              name: frontend-config
              key: api-url
        - name: REACT_APP_WS_URL
          valueFrom:
            configMapKeyRef:
              name: frontend-config
              key: ws-url
        - name: REACT_APP_ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: ui-builder-production
  labels:
    app: ui-builder-backend
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: management
  selector:
    app: ui-builder-backend
    version: green

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: ui-builder-production
  labels:
    app: ui-builder-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: ui-builder-frontend
    version: green

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ui-builder-ingress
  namespace: ui-builder-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - ui-builder.com
    - api.ui-builder.com
    secretName: ui-builder-tls
  rules:
  - host: ui-builder.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: api.ui-builder.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8080

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: ui-builder-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-hpa
  namespace: ui-builder-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-deployment
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-pdb
  namespace: ui-builder-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: ui-builder-backend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontend-pdb
  namespace: ui-builder-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: ui-builder-frontend

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: ui-builder-production
data:
  application.yml: |
    server:
      port: 8080
    management:
      server:
        port: 8081
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
    logging:
      level:
        com.uibuilder: INFO
        org.springframework.security: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file:
        name: /app/logs/application.log

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: ui-builder-production
data:
  api-url: "https://api.ui-builder.com"
  ws-url: "wss://api.ui-builder.com/ws"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-config
  namespace: ui-builder-production
data:
  bootstrap-servers: "kafka-cluster-kafka-bootstrap.kafka:9092"
