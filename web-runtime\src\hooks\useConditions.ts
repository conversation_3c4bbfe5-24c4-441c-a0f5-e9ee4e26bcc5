import { useMemo } from 'react';
import { ConditionConfiguration } from '@types/index';

interface ConditionContext {
  data: Record<string, any>;
  props: Record<string, any>;
  user: any;
  [key: string]: any;
}

interface ConditionResult {
  condition: ConditionConfiguration;
  value: boolean;
  error?: string;
}

/**
 * Hook for evaluating conditional logic
 */
export function useConditions(
  conditions: ConditionConfiguration[],
  context: ConditionContext
): ConditionResult[] {
  return useMemo(() => {
    return conditions.map(condition => {
      try {
        const value = evaluateCondition(condition, context);
        return { condition, value };
      } catch (error) {
        console.warn('Condition evaluation error:', error);
        return { 
          condition, 
          value: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });
  }, [conditions, context]);
}

/**
 * Hook for evaluating a single condition
 */
export function useCondition(
  condition: ConditionConfiguration,
  context: ConditionContext
): boolean {
  return useMemo(() => {
    try {
      return evaluateCondition(condition, context);
    } catch (error) {
      console.warn('Condition evaluation error:', error);
      return false;
    }
  }, [condition, context]);
}

/**
 * Hook for conditional visibility
 */
export function useConditionalVisibility(
  conditions: ConditionConfiguration[],
  context: ConditionContext
): boolean {
  const results = useConditions(conditions, context);
  
  return useMemo(() => {
    const showConditions = results.filter(r => r.condition.type === 'show');
    const hideConditions = results.filter(r => r.condition.type === 'hide');
    
    // If any hide condition is true, don't show
    if (hideConditions.some(r => r.value)) {
      return false;
    }
    
    // If there are show conditions, at least one must be true
    if (showConditions.length > 0) {
      return showConditions.some(r => r.value);
    }
    
    // Default to visible if no conditions
    return true;
  }, [results]);
}

/**
 * Hook for conditional styling
 */
export function useConditionalStyles(
  conditions: ConditionConfiguration[],
  context: ConditionContext
): React.CSSProperties {
  const results = useConditions(conditions, context);
  
  return useMemo(() => {
    const styleConditions = results.filter(r => 
      r.condition.type === 'style' && r.value && r.condition.style
    );
    
    return styleConditions.reduce((styles, result) => ({
      ...styles,
      ...result.condition.style,
    }), {});
  }, [results]);
}

/**
 * Hook for conditional classes
 */
export function useConditionalClasses(
  conditions: Array<{
    condition: ConditionConfiguration;
    className: string;
  }>,
  context: ConditionContext
): string[] {
  return useMemo(() => {
    const classes: string[] = [];
    
    conditions.forEach(({ condition, className }) => {
      try {
        if (evaluateCondition(condition, context)) {
          classes.push(className);
        }
      } catch (error) {
        console.warn('Conditional class evaluation error:', error);
      }
    });
    
    return classes;
  }, [conditions, context]);
}

// Core condition evaluation function
function evaluateCondition(
  condition: ConditionConfiguration,
  context: ConditionContext
): boolean {
  const { expression, value } = condition;
  
  if (!expression) {
    return false;
  }
  
  // Create evaluation context with helper functions
  const evalContext = {
    ...context,
    // Comparison helpers
    equals: (a: any, b: any) => a === b,
    notEquals: (a: any, b: any) => a !== b,
    greaterThan: (a: number, b: number) => a > b,
    lessThan: (a: number, b: number) => a < b,
    greaterThanOrEqual: (a: number, b: number) => a >= b,
    lessThanOrEqual: (a: number, b: number) => a <= b,
    
    // String helpers
    contains: (str: string, substr: string) => str.includes(substr),
    startsWith: (str: string, prefix: string) => str.startsWith(prefix),
    endsWith: (str: string, suffix: string) => str.endsWith(suffix),
    matches: (str: string, pattern: string) => new RegExp(pattern).test(str),
    
    // Array helpers
    includes: (array: any[], item: any) => array.includes(item),
    isEmpty: (value: any) => {
      if (Array.isArray(value)) return value.length === 0;
      if (typeof value === 'object') return Object.keys(value).length === 0;
      if (typeof value === 'string') return value.trim() === '';
      return !value;
    },
    isNotEmpty: (value: any) => !evalContext.isEmpty(value),
    length: (value: any) => {
      if (Array.isArray(value) || typeof value === 'string') {
        return value.length;
      }
      if (typeof value === 'object') {
        return Object.keys(value).length;
      }
      return 0;
    },
    
    // Type checking helpers
    isString: (value: any) => typeof value === 'string',
    isNumber: (value: any) => typeof value === 'number' && !isNaN(value),
    isBoolean: (value: any) => typeof value === 'boolean',
    isArray: (value: any) => Array.isArray(value),
    isObject: (value: any) => typeof value === 'object' && value !== null && !Array.isArray(value),
    isNull: (value: any) => value === null,
    isUndefined: (value: any) => value === undefined,
    isDefined: (value: any) => value !== undefined && value !== null,
    
    // Date helpers
    isToday: (date: string | Date) => {
      const d = new Date(date);
      const today = new Date();
      return d.toDateString() === today.toDateString();
    },
    isBefore: (date1: string | Date, date2: string | Date) => {
      return new Date(date1) < new Date(date2);
    },
    isAfter: (date1: string | Date, date2: string | Date) => {
      return new Date(date1) > new Date(date2);
    },
    daysBetween: (date1: string | Date, date2: string | Date) => {
      const d1 = new Date(date1);
      const d2 = new Date(date2);
      const diffTime = Math.abs(d2.getTime() - d1.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    },
    
    // Permission helpers
    hasPermission: (permission: string) => context.user?.permissions?.includes(permission) || false,
    hasRole: (role: string) => context.user?.roles?.includes(role) || false,
    
    // Utility helpers
    random: () => Math.random(),
    now: () => new Date(),
    timestamp: () => Date.now(),
    
    // Value reference for comparison
    value,
  };
  
  try {
    // Create function with context
    const contextKeys = Object.keys(evalContext);
    const contextValues = Object.values(evalContext);
    
    // Wrap expression in return statement if needed
    const wrappedExpression = expression.trim().startsWith('return ') 
      ? expression 
      : `return ${expression}`;
    
    const fn = new Function(...contextKeys, wrappedExpression);
    const result = fn(...contextValues);
    
    // Ensure boolean result
    return Boolean(result);
  } catch (error) {
    console.warn('Condition expression evaluation failed:', expression, error);
    return false;
  }
}

/**
 * Helper function to get nested value from object
 */
function getNestedValue(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  
  return path.split('.').reduce((current, key) => {
    if (current === null || current === undefined) return undefined;
    
    // Handle array indices
    if (key.includes('[') && key.includes(']')) {
      const [arrayKey, indexStr] = key.split('[');
      const index = parseInt(indexStr.replace(']', ''), 10);
      
      if (arrayKey) {
        current = current[arrayKey];
      }
      
      if (Array.isArray(current) && !isNaN(index)) {
        return current[index];
      }
      
      return undefined;
    }
    
    return current[key];
  }, obj);
}

/**
 * Predefined condition templates for common use cases
 */
export const conditionTemplates = {
  // User conditions
  isLoggedIn: (): ConditionConfiguration => ({
    type: 'show',
    expression: 'isDefined(user)',
  }),
  
  isLoggedOut: (): ConditionConfiguration => ({
    type: 'show',
    expression: 'isNull(user) || isUndefined(user)',
  }),
  
  hasPermission: (permission: string): ConditionConfiguration => ({
    type: 'show',
    expression: `hasPermission('${permission}')`,
  }),
  
  hasRole: (role: string): ConditionConfiguration => ({
    type: 'show',
    expression: `hasRole('${role}')`,
  }),
  
  // Data conditions
  dataExists: (path: string): ConditionConfiguration => ({
    type: 'show',
    expression: `isDefined(data.${path})`,
  }),
  
  dataEquals: (path: string, value: any): ConditionConfiguration => ({
    type: 'show',
    expression: `equals(data.${path}, ${JSON.stringify(value)})`,
  }),
  
  arrayNotEmpty: (path: string): ConditionConfiguration => ({
    type: 'show',
    expression: `isArray(data.${path}) && isNotEmpty(data.${path})`,
  }),
  
  // Form conditions
  fieldRequired: (fieldName: string): ConditionConfiguration => ({
    type: 'show',
    expression: `isNotEmpty(props.${fieldName})`,
  }),
  
  fieldEquals: (fieldName: string, value: any): ConditionConfiguration => ({
    type: 'show',
    expression: `equals(props.${fieldName}, ${JSON.stringify(value)})`,
  }),
  
  // State conditions
  isLoading: (): ConditionConfiguration => ({
    type: 'show',
    expression: 'props.loading === true',
  }),
  
  hasError: (): ConditionConfiguration => ({
    type: 'show',
    expression: 'isDefined(props.error)',
  }),
};
