# Flutter UI Runtime

A powerful cross-platform mobile application built with Flutter that dynamically renders UI configurations from JSON metadata. This mobile runtime provides native performance while maintaining perfect synchronization with the web platform.

## 🚀 Features

### Core Capabilities
- **Dynamic Widget Rendering**: Convert JSON metadata to native Flutter widgets
- **Real-time Synchronization**: Live UI updates via WebSocket connections
- **Cross-Platform**: Single codebase for iOS and Android
- **Native Performance**: Optimized for mobile devices with 60fps animations
- **Offline-First**: Works seamlessly without internet connectivity
- **Platform Integration**: Camera, GPS, push notifications, haptic feedback

### Advanced Features
- **Adaptive UI**: Material Design for Android, Cupertino for iOS
- **State Management**: Riverpod for reactive state management
- **Local Storage**: Hive for efficient offline data caching
- **Error Boundaries**: Graceful error handling and recovery
- **Performance Monitoring**: Real-time metrics and optimization
- **Security**: Secure storage and authentication integration

## 🏗️ Architecture

### Technology Stack
- **Flutter 3.x** + **Dart** - Cross-platform mobile framework
- **Riverpod** - Reactive state management
- **Dio + Retrofit** - HTTP client with automatic retries
- **Socket.io** - Real-time communication
- **Hive** - Fast, lightweight local database
- **GoRouter** - Declarative navigation
- **Flutter Hooks** - Reactive component logic

### Core Systems

#### Widget Registry
Maps JSON component types to Flutter widgets with lazy loading:
```dart
WidgetRegistry.instance.register('Button', (config) => ButtonWidget(config));
final widget = WidgetRegistry.instance.build(config);
```

#### Dynamic Renderer
Converts JSON metadata to Flutter widget trees:
```dart
DynamicWidgetRenderer(
  config: widgetConfiguration,
  parentId: 'parent-widget',
)
```

#### State Management
Riverpod providers for reactive state:
```dart
final appState = ref.watch(appStateProvider);
final themeConfig = ref.watch(themeConfigProvider);
```

#### Real-time Sync
WebSocket integration for live updates:
```dart
final realtimeService = ref.read(realtimeServiceProvider);
realtimeService.connect();
realtimeService.joinPage(pageId);
```

## 📁 Project Structure

```
flutter_ui_runtime/
├── lib/
│   ├── core/                   # Core framework
│   │   ├── config/             # App configuration
│   │   ├── models/             # Data models
│   │   ├── providers/          # Riverpod providers
│   │   ├── services/           # Business logic services
│   │   ├── utils/              # Utility functions
│   │   └── widgets/            # Core widget system
│   ├── widgets/                # UI widget implementations
│   │   ├── display/            # Display widgets (Text, Image, etc.)
│   │   ├── input/              # Input widgets (Button, TextField, etc.)
│   │   ├── layout/             # Layout widgets (Column, Row, etc.)
│   │   └── navigation/         # Navigation widgets
│   ├── pages/                  # App pages/screens
│   └── main.dart               # App entry point
├── test/                       # Unit and widget tests
├── integration_test/           # Integration tests
├── android/                    # Android-specific code
├── ios/                        # iOS-specific code
└── assets/                     # Static assets
```

## 🚦 Getting Started

### Prerequisites
- Flutter 3.10.0 or higher
- Dart 3.0.0 or higher
- Android Studio / Xcode for platform development
- Device or emulator for testing

### Installation
```bash
cd flutter_ui_runtime
flutter pub get
```

### Development
```bash
# Run on connected device/emulator
flutter run

# Run with hot reload
flutter run --hot

# Run on specific platform
flutter run -d android
flutter run -d ios
```

### Building
```bash
# Build APK for Android
flutter build apk --release

# Build App Bundle for Google Play
flutter build appbundle --release

# Build for iOS
flutter build ios --release
```

### Testing
```bash
flutter test                    # Unit tests
flutter test integration_test/  # Integration tests
flutter test --coverage        # With coverage
```

## 🔧 Configuration

### Environment Variables
```dart
// lib/core/config/app_config.dart
static const String baseUrl = String.fromEnvironment(
  'BASE_URL',
  defaultValue: 'https://api.uibuilder.dev/v1',
);
```

### Build Configuration
```bash
# Development
flutter run --dart-define=BASE_URL=http://localhost:8080/api/v1

# Production
flutter build apk --dart-define=BASE_URL=https://api.uibuilder.dev/v1
```

## 📊 Performance

### Optimization Features
- **Widget Caching**: Intelligent widget instance caching
- **Lazy Loading**: Components loaded on demand
- **Memory Management**: Automatic cleanup and optimization
- **Image Caching**: Efficient image loading and caching
- **Background Sync**: Offline data synchronization

### Performance Metrics
- **60fps Animations**: Smooth native animations
- **Fast Startup**: < 2 seconds cold start time
- **Memory Efficient**: < 100MB RAM usage
- **Battery Optimized**: Minimal background processing

## 🔌 API Integration

### Fetching UI Metadata
```dart
final apiService = ref.read(apiServiceProvider);
final metadata = await apiService.fetchUIMetadata(pageId);
```

### Real-time Updates
```dart
final realtimeService = ref.read(realtimeServiceProvider);
realtimeService.messageStream.listen((message) {
  // Handle real-time updates
});
```

### Offline Storage
```dart
final storageService = ref.read(storageServiceProvider);
await storageService.cacheUIMetadata(pageId, metadata);
```

## 🎨 Theming

### Dynamic Theme Application
```dart
final themeConfig = ref.watch(themeConfigProvider);
final themeData = themeConfig?.toThemeData(isDark: isDarkMode);

MaterialApp(
  theme: themeData,
  // ...
)
```

### Platform-Specific Themes
```dart
// Material Design for Android
MaterialApp(theme: materialTheme)

// Cupertino for iOS  
CupertinoApp(theme: cupertinoTheme)
```

## 📱 Platform Features

### Native Integrations
```dart
// Camera access
final image = await ImagePicker().pickImage(source: ImageSource.camera);

// Location services
final position = await Geolocator.getCurrentPosition();

// Haptic feedback
await Vibration.vibrate(duration: 100);

// Push notifications
await FirebaseMessaging.instance.requestPermission();
```

### Platform-Specific UI
- **Android**: Material Design 3 components
- **iOS**: Cupertino design system
- **Adaptive**: Automatically switches based on platform

## 🔄 Real-time Features

### WebSocket Connection
```dart
final realtimeService = ref.read(realtimeServiceProvider);
await realtimeService.connect();
```

### Live Updates
- UI configuration changes
- Theme updates  
- Data synchronization
- User presence and collaboration

## 💾 Offline Support

### Offline-First Architecture
```dart
// Data automatically cached locally
final metadata = await apiService.fetchUIMetadata(pageId);

// Works offline with cached data
final cachedMetadata = await storageService.getCachedUIMetadata(pageId);
```

### Background Sync
- Automatic sync when connection restored
- Conflict resolution for concurrent edits
- Offline queue for pending changes

## 🧪 Testing

### Test Types
- **Unit Tests**: Core logic and utilities
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end workflows
- **Golden Tests**: Visual regression testing

### Running Tests
```bash
flutter test                           # All tests
flutter test test/widgets/             # Widget tests only
flutter test integration_test/         # Integration tests
flutter test --coverage               # With coverage report
```

## 🚀 Deployment

### Android Deployment
```bash
# Build release APK
flutter build apk --release

# Build App Bundle for Play Store
flutter build appbundle --release

# Sign and upload to Google Play Console
```

### iOS Deployment
```bash
# Build for iOS
flutter build ios --release

# Archive in Xcode
# Upload to App Store Connect
```

### CI/CD Integration
- GitHub Actions workflows
- Automated testing and building
- Code signing and deployment
- Performance monitoring

## 🔒 Security

### Data Protection
- Secure local storage with encryption
- Certificate pinning for API calls
- Biometric authentication support
- Runtime application self-protection

### Privacy
- Minimal data collection
- User consent management
- GDPR compliance
- Secure data transmission

## 📱 Platform Support

### Supported Platforms
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 11.0+
- **Tablets**: Responsive design for all screen sizes

### Device Features
- Camera and photo library access
- GPS and location services
- Push notifications
- Haptic feedback and vibration
- Biometric authentication
- Background app refresh

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run linting and tests
5. Submit a pull request

### Code Standards
- Dart/Flutter best practices
- Comprehensive test coverage
- Documentation for public APIs
- Performance considerations

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:
- GitHub Issues for bug reports
- Documentation for guides
- Community Discord for discussions

---

Built with ❤️ for native mobile experiences
