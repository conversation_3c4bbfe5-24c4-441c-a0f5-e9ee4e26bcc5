package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.UIConfigurationDTO;
import com.uiplatform.entity.UIConfiguration;
import com.uiplatform.service.UIConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST Controller for UI Configuration management operations.
 */
@RestController
@RequestMapping("/api/v1/ui-configurations")
@Tag(name = "UI Configurations", description = "Dynamic UI configuration management endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UIConfigurationController {

    private static final Logger logger = LoggerFactory.getLogger(UIConfigurationController.class);

    private final UIConfigurationService uiConfigurationService;

    @Autowired
    public UIConfigurationController(UIConfigurationService uiConfigurationService) {
        this.uiConfigurationService = uiConfigurationService;
    }

    /**
     * Create new UI configuration.
     */
    @PostMapping
    @Operation(summary = "Create UI configuration", description = "Create a new UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "UI configuration created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid UI configuration data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "UI configuration already exists")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'CREATE')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> createUIConfiguration(
            @Valid @RequestBody UIConfigurationDTO.CreateDTO createDTO,
            @RequestParam UUID organizationId,
            Authentication authentication) {
        
        UUID ownerId = UUID.fromString(authentication.getName());
        logger.info("Creating UI configuration '{}' for organization: {}", createDTO.getName(), organizationId);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.createUIConfiguration(createDTO, ownerId, organizationId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("UI configuration created successfully", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to create UI configuration", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to create UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Get UI configuration by ID.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get UI configuration", description = "Get UI configuration by ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> getUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id) {
        
        logger.debug("Fetching UI configuration with ID: {}", id);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.getUIConfigurationById(id);
            return ResponseEntity.ok(ApiResponse.success("UI configuration found", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to fetch UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("UI configuration not found: " + e.getMessage()));
        }
    }

    /**
     * Get UI configuration by slug.
     */
    @GetMapping("/slug/{slug}")
    @Operation(summary = "Get UI configuration by slug", description = "Get UI configuration by slug and organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> getUIConfigurationBySlug(
            @Parameter(description = "UI Configuration slug") @PathVariable String slug,
            @Parameter(description = "Organization ID") @RequestParam UUID organizationId) {
        
        logger.debug("Fetching UI configuration with slug: {} for organization: {}", slug, organizationId);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.getUIConfigurationBySlug(slug, organizationId);
            return ResponseEntity.ok(ApiResponse.success("UI configuration found", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to fetch UI configuration with slug: {}", slug, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("UI configuration not found: " + e.getMessage()));
        }
    }

    /**
     * Update UI configuration.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update UI configuration", description = "Update UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid UI configuration data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> updateUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id,
            @Valid @RequestBody UIConfigurationDTO updateDTO) {
        
        logger.info("Updating UI configuration with ID: {}", id);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.updateUIConfiguration(id, updateDTO);
            return ResponseEntity.ok(ApiResponse.success("UI configuration updated successfully", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to update UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to update UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Delete UI configuration.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete UI configuration", description = "Delete UI configuration (soft delete)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id) {
        
        logger.info("Deleting UI configuration with ID: {}", id);
        
        try {
            uiConfigurationService.deleteUIConfiguration(id);
            return ResponseEntity.ok(ApiResponse.success("UI configuration deleted successfully"));
        } catch (Exception e) {
            logger.error("Failed to delete UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to delete UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Get UI configurations by organization.
     */
    @GetMapping("/organization/{organizationId}")
    @Operation(summary = "Get UI configurations by organization", description = "Get all UI configurations for an organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configurations retrieved successfully")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public ResponseEntity<ApiResponse<Page<UIConfigurationDTO>>> getUIConfigurationsByOrganization(
            @Parameter(description = "Organization ID") @PathVariable UUID organizationId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Fetching UI configurations for organization: {}", organizationId);
        
        try {
            Page<UIConfigurationDTO> uiConfigurations = uiConfigurationService.getUIConfigurationsByOrganization(organizationId, pageable);
            
            ApiResponse<Page<UIConfigurationDTO>> response = ApiResponse.success("UI configurations retrieved successfully", uiConfigurations);
            response.setPagination(new ApiResponse.PaginationInfo(
                    uiConfigurations.getNumber(),
                    uiConfigurations.getSize(),
                    uiConfigurations.getTotalElements(),
                    uiConfigurations.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch UI configurations for organization: {}", organizationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch UI configurations: " + e.getMessage()));
        }
    }

    /**
     * Search UI configurations.
     */
    @GetMapping("/search")
    @Operation(summary = "Search UI configurations", description = "Search UI configurations by criteria")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public ResponseEntity<ApiResponse<Page<UIConfigurationDTO>>> searchUIConfigurations(
            @Parameter(description = "Organization ID") @RequestParam UUID organizationId,
            @Parameter(description = "UI Configuration type") @RequestParam(required = false) UIConfiguration.UIConfigurationType type,
            @Parameter(description = "UI Configuration status") @RequestParam(required = false) UIConfiguration.UIConfigurationStatus status,
            @Parameter(description = "Owner ID") @RequestParam(required = false) UUID ownerId,
            @Parameter(description = "Is published") @RequestParam(required = false) Boolean isPublished,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Searching UI configurations for organization: {}", organizationId);
        
        try {
            Page<UIConfigurationDTO> uiConfigurations = uiConfigurationService.searchUIConfigurations(
                    organizationId, type, status, ownerId, isPublished, pageable);
            
            ApiResponse<Page<UIConfigurationDTO>> response = ApiResponse.success("Search completed successfully", uiConfigurations);
            response.setPagination(new ApiResponse.PaginationInfo(
                    uiConfigurations.getNumber(),
                    uiConfigurations.getSize(),
                    uiConfigurations.getTotalElements(),
                    uiConfigurations.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search UI configurations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to search UI configurations: " + e.getMessage()));
        }
    }

    /**
     * Publish UI configuration.
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "Publish UI configuration", description = "Publish UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration published successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> publishUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id) {
        
        logger.info("Publishing UI configuration with ID: {}", id);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.publishUIConfiguration(id);
            return ResponseEntity.ok(ApiResponse.success("UI configuration published successfully", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to publish UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to publish UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Unpublish UI configuration.
     */
    @PostMapping("/{id}/unpublish")
    @Operation(summary = "Unpublish UI configuration", description = "Unpublish UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "UI configuration unpublished successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> unpublishUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id) {
        
        logger.info("Unpublishing UI configuration with ID: {}", id);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.unpublishUIConfiguration(id);
            return ResponseEntity.ok(ApiResponse.success("UI configuration unpublished successfully", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to unpublish UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to unpublish UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Clone UI configuration.
     */
    @PostMapping("/{id}/clone")
    @Operation(summary = "Clone UI configuration", description = "Clone UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "UI configuration cloned successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "UI configuration not found")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'CREATE')")
    public ResponseEntity<ApiResponse<UIConfigurationDTO>> cloneUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID id,
            @Parameter(description = "New name") @RequestParam String newName,
            @Parameter(description = "New slug") @RequestParam String newSlug,
            Authentication authentication) {
        
        UUID ownerId = UUID.fromString(authentication.getName());
        logger.info("Cloning UI configuration with ID: {} to new name: {}", id, newName);
        
        try {
            UIConfigurationDTO uiConfiguration = uiConfigurationService.cloneUIConfiguration(id, newName, newSlug, ownerId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("UI configuration cloned successfully", uiConfiguration));
        } catch (Exception e) {
            logger.error("Failed to clone UI configuration with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to clone UI configuration: " + e.getMessage()));
        }
    }

    /**
     * Get published UI configurations.
     */
    @GetMapping("/published")
    @Operation(summary = "Get published UI configurations", description = "Get all published UI configurations for an organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Published UI configurations retrieved successfully")
    })
    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'READ')")
    public ResponseEntity<ApiResponse<List<UIConfigurationDTO>>> getPublishedUIConfigurations(
            @Parameter(description = "Organization ID") @RequestParam UUID organizationId) {
        
        logger.debug("Fetching published UI configurations for organization: {}", organizationId);
        
        try {
            List<UIConfigurationDTO> uiConfigurations = uiConfigurationService.getPublishedUIConfigurations(organizationId);
            return ResponseEntity.ok(ApiResponse.success("Published UI configurations retrieved successfully", uiConfigurations));
        } catch (Exception e) {
            logger.error("Failed to fetch published UI configurations for organization: {}", organizationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch published UI configurations: " + e.getMessage()));
        }
    }
}
