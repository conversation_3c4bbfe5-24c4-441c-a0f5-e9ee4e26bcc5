import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Heart, Download, Plus, Loader2 } from 'lucide-react';

import { Button, ButtonGroup, IconButton, FAB } from './Button';

const meta = {
  title: 'Components/Input/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A versatile button component with multiple variants, sizes, and states.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'destructive', 'outline', 'ghost', 'link'],
      description: 'The visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
      description: 'The size of the button',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the button should take the full width of its container',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button is in a loading state',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    asChild: {
      control: 'boolean',
      description: 'Render as a child component (using Slot)',
    },
  },
  args: {
    onClick: fn(),
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic button stories
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Delete Account',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Link Button',
  },
};

// Size variations
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="xs">Extra Small</Button>
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons come in different sizes to fit various use cases.',
      },
    },
  },
};

// State variations
export const States: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Button>Normal</Button>
        <Button loading>Loading</Button>
        <Button disabled>Disabled</Button>
      </div>
      <div className="flex items-center gap-4">
        <Button variant="outline">Normal</Button>
        <Button variant="outline" loading>Loading</Button>
        <Button variant="outline" disabled>Disabled</Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons can be in different states: normal, loading, or disabled.',
      },
    },
  },
};

// With icons
export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Button leftIcon={<Plus className="h-4 w-4" />}>Add Item</Button>
        <Button rightIcon={<Download className="h-4 w-4" />}>Download</Button>
        <Button 
          leftIcon={<Heart className="h-4 w-4" />}
          rightIcon={<Plus className="h-4 w-4" />}
        >
          Like & Add
        </Button>
      </div>
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          leftIcon={<Plus className="h-4 w-4" />}
        >
          Create New
        </Button>
        <Button 
          variant="ghost" 
          rightIcon={<Download className="h-4 w-4" />}
        >
          Export
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons can include icons on the left or right side of the text.',
      },
    },
  },
};

// Loading states
export const LoadingStates: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Button loading>Loading...</Button>
        <Button loading loadingText="Saving...">Save</Button>
        <Button 
          loading 
          loadingIcon={<Loader2 className="h-4 w-4 animate-spin" />}
        >
          Custom Loader
        </Button>
      </div>
      <div className="flex items-center gap-4">
        <Button variant="outline" loading>Processing</Button>
        <Button variant="ghost" loading>Uploading</Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Loading buttons show a spinner and can have custom loading text.',
      },
    },
  },
};

// Full width
export const FullWidth: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <Button fullWidth>Full Width Primary</Button>
      <Button variant="outline" fullWidth>Full Width Outline</Button>
      <Button variant="ghost" fullWidth>Full Width Ghost</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons can take the full width of their container.',
      },
    },
    layout: 'padded',
  },
};

// Button Group
export const ButtonGroupStory: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="mb-2 text-sm font-medium">Horizontal Group</h3>
        <ButtonGroup>
          <Button variant="outline">Left</Button>
          <Button variant="outline">Center</Button>
          <Button variant="outline">Right</Button>
        </ButtonGroup>
      </div>
      
      <div>
        <h3 className="mb-2 text-sm font-medium">Attached Group</h3>
        <ButtonGroup attached>
          <Button variant="outline">First</Button>
          <Button variant="outline">Second</Button>
          <Button variant="outline">Third</Button>
        </ButtonGroup>
      </div>
      
      <div>
        <h3 className="mb-2 text-sm font-medium">Vertical Group</h3>
        <ButtonGroup orientation="vertical">
          <Button variant="outline">Top</Button>
          <Button variant="outline">Middle</Button>
          <Button variant="outline">Bottom</Button>
        </ButtonGroup>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'ButtonGroup allows you to group related buttons together.',
      },
    },
    layout: 'padded',
  },
};

// Icon Button
export const IconButtonStory: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="mb-2 text-sm font-medium">Icon Button Variants</h3>
        <div className="flex items-center gap-4">
          <IconButton 
            icon={<Heart className="h-4 w-4" />} 
            aria-label="Like"
          />
          <IconButton 
            variant="outline"
            icon={<Download className="h-4 w-4" />} 
            aria-label="Download"
          />
          <IconButton 
            variant="solid"
            icon={<Plus className="h-4 w-4" />} 
            aria-label="Add"
          />
        </div>
      </div>
      
      <div>
        <h3 className="mb-2 text-sm font-medium">Icon Button Sizes</h3>
        <div className="flex items-center gap-4">
          <IconButton 
            size="xs"
            icon={<Heart className="h-3 w-3" />} 
            aria-label="Like"
          />
          <IconButton 
            size="sm"
            icon={<Heart className="h-4 w-4" />} 
            aria-label="Like"
          />
          <IconButton 
            size="md"
            icon={<Heart className="h-4 w-4" />} 
            aria-label="Like"
          />
          <IconButton 
            size="lg"
            icon={<Heart className="h-5 w-5" />} 
            aria-label="Like"
          />
        </div>
      </div>
      
      <div>
        <h3 className="mb-2 text-sm font-medium">Circle Shape</h3>
        <div className="flex items-center gap-4">
          <IconButton 
            shape="circle"
            icon={<Heart className="h-4 w-4" />} 
            aria-label="Like"
          />
          <IconButton 
            variant="outline"
            shape="circle"
            icon={<Download className="h-4 w-4" />} 
            aria-label="Download"
          />
          <IconButton 
            variant="solid"
            shape="circle"
            icon={<Plus className="h-4 w-4" />} 
            aria-label="Add"
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'IconButton is optimized for displaying just an icon.',
      },
    },
    layout: 'padded',
  },
};

// Floating Action Button
export const FABStory: Story = {
  render: () => (
    <div className="relative h-96 w-96 border border-dashed border-gray-300 rounded-lg">
      <p className="absolute top-4 left-4 text-sm text-gray-600">
        FAB positioned in different corners
      </p>
      
      <FAB 
        position="bottom-right"
        icon={<Plus className="h-6 w-6" />}
        aria-label="Add"
      />
      
      <FAB 
        position="bottom-left"
        icon={<Heart className="h-6 w-6" />}
        aria-label="Favorite"
      />
      
      <FAB 
        position="top-right"
        extended
        leftIcon={<Download className="h-5 w-5" />}
        aria-label="Download"
      >
        Download
      </FAB>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Floating Action Button (FAB) for primary actions.',
      },
    },
    layout: 'padded',
  },
};
