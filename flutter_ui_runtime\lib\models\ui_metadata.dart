import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'ui_metadata.g.dart';

/// Core UI metadata model for Flutter runtime
@JsonSerializable()
class UIMetadata extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String version;
  final UIConfiguration configuration;
  final ThemeConfiguration theme;
  final List<ComponentDefinition> components;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UIMetadata({
    required this.id,
    required this.name,
    this.description,
    required this.version,
    required this.configuration,
    required this.theme,
    required this.components,
    this.data,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UIMetadata.fromJson(Map<String, dynamic> json) =>
      _$UIMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$UIMetadataToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        version,
        configuration,
        theme,
        components,
        data,
        createdAt,
        updatedAt,
      ];

  UIMetadata copyWith({
    String? id,
    String? name,
    String? description,
    String? version,
    UIConfiguration? configuration,
    ThemeConfiguration? theme,
    List<ComponentDefinition>? components,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UIMetadata(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      version: version ?? this.version,
      configuration: configuration ?? this.configuration,
      theme: theme ?? this.theme,
      components: components ?? this.components,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// UI configuration containing layout and behavior settings
@JsonSerializable()
class UIConfiguration extends Equatable {
  final LayoutConfiguration layout;
  final ResponsiveConfiguration responsive;
  final NavigationConfiguration? navigation;
  final Map<String, dynamic>? settings;

  const UIConfiguration({
    required this.layout,
    required this.responsive,
    this.navigation,
    this.settings,
  });

  factory UIConfiguration.fromJson(Map<String, dynamic> json) =>
      _$UIConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$UIConfigurationToJson(this);

  @override
  List<Object?> get props => [layout, responsive, navigation, settings];
}

/// Layout configuration for the UI
@JsonSerializable()
class LayoutConfiguration extends Equatable {
  final String type; // 'column', 'row', 'stack', 'grid', 'custom'
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;
  final MainAxisSize? mainAxisSize;
  final List<ComponentDefinition> children;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Map<String, dynamic>? properties;

  const LayoutConfiguration({
    required this.type,
    this.mainAxisAlignment,
    this.crossAxisAlignment,
    this.mainAxisSize,
    required this.children,
    this.padding,
    this.margin,
    this.properties,
  });

  factory LayoutConfiguration.fromJson(Map<String, dynamic> json) =>
      _$LayoutConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$LayoutConfigurationToJson(this);

  @override
  List<Object?> get props => [
        type,
        mainAxisAlignment,
        crossAxisAlignment,
        mainAxisSize,
        children,
        padding,
        margin,
        properties,
      ];
}

/// Responsive configuration for different screen sizes
@JsonSerializable()
class ResponsiveConfiguration extends Equatable {
  final Map<String, BreakpointConfiguration> breakpoints;
  final String defaultBreakpoint;

  const ResponsiveConfiguration({
    required this.breakpoints,
    required this.defaultBreakpoint,
  });

  factory ResponsiveConfiguration.fromJson(Map<String, dynamic> json) =>
      _$ResponsiveConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$ResponsiveConfigurationToJson(this);

  @override
  List<Object?> get props => [breakpoints, defaultBreakpoint];
}

/// Breakpoint configuration for responsive design
@JsonSerializable()
class BreakpointConfiguration extends Equatable {
  final double minWidth;
  final double? maxWidth;
  final int columns;
  final double spacing;
  final Map<String, dynamic>? overrides;

  const BreakpointConfiguration({
    required this.minWidth,
    this.maxWidth,
    required this.columns,
    required this.spacing,
    this.overrides,
  });

  factory BreakpointConfiguration.fromJson(Map<String, dynamic> json) =>
      _$BreakpointConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$BreakpointConfigurationToJson(this);

  @override
  List<Object?> get props => [minWidth, maxWidth, columns, spacing, overrides];
}

/// Navigation configuration
@JsonSerializable()
class NavigationConfiguration extends Equatable {
  final String type; // 'bottom', 'drawer', 'rail', 'tabs'
  final List<NavigationItem> items;
  final Map<String, dynamic>? properties;

  const NavigationConfiguration({
    required this.type,
    required this.items,
    this.properties,
  });

  factory NavigationConfiguration.fromJson(Map<String, dynamic> json) =>
      _$NavigationConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$NavigationConfigurationToJson(this);

  @override
  List<Object?> get props => [type, items, properties];
}

/// Navigation item
@JsonSerializable()
class NavigationItem extends Equatable {
  final String id;
  final String label;
  final String? icon;
  final String route;
  final bool enabled;

  const NavigationItem({
    required this.id,
    required this.label,
    this.icon,
    required this.route,
    this.enabled = true,
  });

  factory NavigationItem.fromJson(Map<String, dynamic> json) =>
      _$NavigationItemFromJson(json);

  Map<String, dynamic> toJson() => _$NavigationItemToJson(this);

  @override
  List<Object?> get props => [id, label, icon, route, enabled];
}

/// Component definition for widgets
@JsonSerializable()
class ComponentDefinition extends Equatable {
  final String id;
  final String type;
  final String? name;
  final Map<String, dynamic> properties;
  final List<ComponentDefinition>? children;
  final List<ActionDefinition>? actions;
  final Map<String, dynamic>? styling;
  final ConditionalConfiguration? conditional;

  const ComponentDefinition({
    required this.id,
    required this.type,
    this.name,
    required this.properties,
    this.children,
    this.actions,
    this.styling,
    this.conditional,
  });

  factory ComponentDefinition.fromJson(Map<String, dynamic> json) =>
      _$ComponentDefinitionFromJson(json);

  Map<String, dynamic> toJson() => _$ComponentDefinitionToJson(this);

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        properties,
        children,
        actions,
        styling,
        conditional,
      ];
}

/// Action definition for user interactions
@JsonSerializable()
class ActionDefinition extends Equatable {
  final String type;
  final String? trigger;
  final Map<String, dynamic> parameters;
  final bool? preventDefault;
  final bool? stopPropagation;

  const ActionDefinition({
    required this.type,
    this.trigger,
    required this.parameters,
    this.preventDefault,
    this.stopPropagation,
  });

  factory ActionDefinition.fromJson(Map<String, dynamic> json) =>
      _$ActionDefinitionFromJson(json);

  Map<String, dynamic> toJson() => _$ActionDefinitionToJson(this);

  @override
  List<Object?> get props => [
        type,
        trigger,
        parameters,
        preventDefault,
        stopPropagation,
      ];
}

/// Conditional configuration for dynamic visibility
@JsonSerializable()
class ConditionalConfiguration extends Equatable {
  final String field;
  final String operator; // 'equals', 'not_equals', 'contains', 'greater_than', etc.
  final dynamic value;
  final bool? hideWhenFalse;

  const ConditionalConfiguration({
    required this.field,
    required this.operator,
    required this.value,
    this.hideWhenFalse,
  });

  factory ConditionalConfiguration.fromJson(Map<String, dynamic> json) =>
      _$ConditionalConfigurationFromJson(json);

  Map<String, dynamic> toJson() => _$ConditionalConfigurationToJson(this);

  @override
  List<Object?> get props => [field, operator, value, hideWhenFalse];
}

/// Enums for layout properties
enum MainAxisAlignment {
  start,
  end,
  center,
  spaceBetween,
  spaceAround,
  spaceEvenly,
}

enum CrossAxisAlignment {
  start,
  end,
  center,
  stretch,
  baseline,
}

enum MainAxisSize {
  min,
  max,
}

/// Edge insets geometry for padding and margin
@JsonSerializable()
class EdgeInsetsGeometry extends Equatable {
  final double? top;
  final double? right;
  final double? bottom;
  final double? left;
  final double? all;
  final double? horizontal;
  final double? vertical;

  const EdgeInsetsGeometry({
    this.top,
    this.right,
    this.bottom,
    this.left,
    this.all,
    this.horizontal,
    this.vertical,
  });

  factory EdgeInsetsGeometry.fromJson(Map<String, dynamic> json) =>
      _$EdgeInsetsGeometryFromJson(json);

  Map<String, dynamic> toJson() => _$EdgeInsetsGeometryToJson(this);

  @override
  List<Object?> get props => [
        top,
        right,
        bottom,
        left,
        all,
        horizontal,
        vertical,
      ];
}
