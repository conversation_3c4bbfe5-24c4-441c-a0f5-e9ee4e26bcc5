package com.uibuilder.config.controller;

import com.uibuilder.config.dto.ConfigDto;
import com.uibuilder.config.dto.ConfigCreateRequest;
import com.uibuilder.config.dto.ConfigUpdateRequest;
import com.uibuilder.config.dto.ConfigSearchRequest;
import com.uibuilder.config.service.ConfigService;
import com.uibuilder.config.model.Config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for managing UI configurations
 * 
 * Provides endpoints for CRUD operations on UI configurations,
 * including versioning, validation, and search functionality.
 */
@RestController
@RequestMapping("/api/configs")
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class ConfigController {

    @Autowired
    private ConfigService configService;

    /**
     * Get all configurations for the current user/organization
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Page<ConfigDto>> getAllConfigs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) List<String> tags,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigSearchRequest searchRequest = ConfigSearchRequest.builder()
            .search(search)
            .category(category)
            .tags(tags)
            .userId(UUID.fromString(userId))
            .organizationId(UUID.fromString(organizationId))
            .build();
            
        Page<ConfigDto> configs = configService.getAllConfigs(searchRequest, page, size, sortBy, sortDir);
        return ResponseEntity.ok(configs);
    }

    /**
     * Get a specific configuration by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> getConfigById(
            @PathVariable @NotBlank String id,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.getConfigById(
            UUID.fromString(id), 
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(config);
    }

    /**
     * Get a specific version of a configuration
     */
    @GetMapping("/{id}/versions/{version}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> getConfigVersion(
            @PathVariable @NotBlank String id,
            @PathVariable int version,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.getConfigVersion(
            UUID.fromString(id), 
            version,
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(config);
    }

    /**
     * Get all versions of a configuration
     */
    @GetMapping("/{id}/versions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<ConfigDto>> getConfigVersions(
            @PathVariable @NotBlank String id,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        List<ConfigDto> versions = configService.getConfigVersions(
            UUID.fromString(id),
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(versions);
    }

    /**
     * Create a new configuration
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> createConfig(
            @Valid @RequestBody ConfigCreateRequest request,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        request.setCreatedBy(UUID.fromString(userId));
        request.setOrganizationId(UUID.fromString(organizationId));
        
        ConfigDto config = configService.createConfig(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(config);
    }

    /**
     * Update an existing configuration
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> updateConfig(
            @PathVariable @NotBlank String id,
            @Valid @RequestBody ConfigUpdateRequest request,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        request.setUpdatedBy(UUID.fromString(userId));
        
        ConfigDto config = configService.updateConfig(
            UUID.fromString(id), 
            request,
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(config);
    }

    /**
     * Delete a configuration
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Void> deleteConfig(
            @PathVariable @NotBlank String id,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        configService.deleteConfig(
            UUID.fromString(id),
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.noContent().build();
    }

    /**
     * Duplicate a configuration
     */
    @PostMapping("/{id}/duplicate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> duplicateConfig(
            @PathVariable @NotBlank String id,
            @RequestParam(required = false) String name,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.duplicateConfig(
            UUID.fromString(id),
            name,
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(config);
    }

    /**
     * Validate a configuration
     */
    @PostMapping("/validate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Object> validateConfig(
            @Valid @RequestBody ConfigDto config) {

        Object result = configService.validateConfig(config);
        return ResponseEntity.ok(result);
    }

    /**
     * Export a configuration
     */
    @GetMapping("/{id}/export")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<String> exportConfig(
            @PathVariable @NotBlank String id,
            @RequestParam(defaultValue = "json") String format,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        String exportData = configService.exportConfig(
            UUID.fromString(id),
            format,
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        
        return ResponseEntity.ok()
            .header("Content-Disposition", "attachment; filename=config-" + id + "." + format)
            .header("Content-Type", "application/" + format)
            .body(exportData);
    }

    /**
     * Import a configuration
     */
    @PostMapping("/import")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> importConfig(
            @RequestParam String data,
            @RequestParam(defaultValue = "json") String format,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.importConfig(
            data,
            format,
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        
        return ResponseEntity.status(HttpStatus.CREATED).body(config);
    }

    /**
     * Publish a configuration
     */
    @PostMapping("/{id}/publish")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> publishConfig(
            @PathVariable @NotBlank String id,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.publishConfig(
            UUID.fromString(id),
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(config);
    }

    /**
     * Unpublish a configuration
     */
    @PostMapping("/{id}/unpublish")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ConfigDto> unpublishConfig(
            @PathVariable @NotBlank String id,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        ConfigDto config = configService.unpublishConfig(
            UUID.fromString(id),
            UUID.fromString(userId), 
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(config);
    }

    /**
     * Get configuration statistics
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Object> getConfigStats(
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {

        Object stats = configService.getConfigStats(
            UUID.fromString(userId),
            UUID.fromString(organizationId)
        );
        return ResponseEntity.ok(stats);
    }

    /**
     * Search configurations
     */
    @PostMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Page<ConfigDto>> searchConfigs(
            @Valid @RequestBody ConfigSearchRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestHeader("X-User-ID") String userId,
            @RequestHeader("X-Organization-ID") String organizationId) {
        
        request.setUserId(UUID.fromString(userId));
        request.setOrganizationId(UUID.fromString(organizationId));
        
        Page<ConfigDto> configs = configService.searchConfigs(request, page, size, sortBy, sortDir);
        return ResponseEntity.ok(configs);
    }
}
