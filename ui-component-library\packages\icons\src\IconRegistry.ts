import React from 'react';

export type IconComponent = React.ComponentType<React.SVGProps<SVGSVGElement>>;
export type IconVariant = 'outline' | 'filled' | 'duotone' | 'light' | 'bold';

export interface IconDefinition {
  name: string;
  variants: Partial<Record<IconVariant, IconComponent>>;
  category?: string;
  tags?: string[];
  keywords?: string[];
}

export interface IconRegistryOptions {
  defaultVariant?: IconVariant;
  fallbackIcon?: IconComponent;
  enableLazyLoading?: boolean;
}

/**
 * Icon Registry for managing and organizing icon components
 * 
 * Provides centralized management of icon components with support for:
 * - Multiple variants per icon
 * - Lazy loading
 * - Categorization and tagging
 * - Search and filtering
 * - Dynamic registration
 */
export class IconRegistry {
  private icons: Map<string, IconDefinition> = new Map();
  private categories: Map<string, Set<string>> = new Map();
  private lazyLoaders: Map<string, () => Promise<IconComponent>> = new Map();
  private loadedIcons: Map<string, IconComponent> = new Map();
  private options: IconRegistryOptions;

  constructor(options: IconRegistryOptions = {}) {
    this.options = {
      defaultVariant: 'outline',
      enableLazyLoading: true,
      ...options,
    };
  }

  /**
   * Register an icon with its variants
   */
  register(definition: IconDefinition): void {
    this.icons.set(definition.name, definition);
    
    // Add to category
    if (definition.category) {
      if (!this.categories.has(definition.category)) {
        this.categories.set(definition.category, new Set());
      }
      this.categories.get(definition.category)!.add(definition.name);
    }
  }

  /**
   * Register multiple icons at once
   */
  registerBatch(definitions: IconDefinition[]): void {
    definitions.forEach(definition => this.register(definition));
  }

  /**
   * Register a lazy-loaded icon
   */
  registerLazy(
    name: string,
    loader: () => Promise<IconComponent>,
    options: {
      variant?: IconVariant;
      category?: string;
      tags?: string[];
      keywords?: string[];
    } = {}
  ): void {
    const key = `${name}-${options.variant || this.options.defaultVariant}`;
    this.lazyLoaders.set(key, loader);
    
    // Register placeholder definition
    const definition: IconDefinition = {
      name,
      variants: {},
      category: options.category,
      tags: options.tags,
      keywords: options.keywords,
    };
    
    this.register(definition);
  }

  /**
   * Get an icon component
   */
  async getIcon(name: string, variant?: IconVariant): Promise<IconComponent | null> {
    const iconVariant = variant || this.options.defaultVariant!;
    const definition = this.icons.get(name);
    
    if (!definition) {
      return this.options.fallbackIcon || null;
    }

    // Check if variant exists in definition
    if (definition.variants[iconVariant]) {
      return definition.variants[iconVariant]!;
    }

    // Try lazy loading
    const lazyKey = `${name}-${iconVariant}`;
    if (this.lazyLoaders.has(lazyKey)) {
      if (this.loadedIcons.has(lazyKey)) {
        return this.loadedIcons.get(lazyKey)!;
      }

      try {
        const loader = this.lazyLoaders.get(lazyKey)!;
        const component = await loader();
        this.loadedIcons.set(lazyKey, component);
        
        // Update definition with loaded component
        definition.variants[iconVariant] = component;
        
        return component;
      } catch (error) {
        console.warn(`Failed to load icon: ${name} (${iconVariant})`, error);
        return this.options.fallbackIcon || null;
      }
    }

    // Fallback to default variant if requested variant not found
    if (iconVariant !== this.options.defaultVariant) {
      return this.getIcon(name, this.options.defaultVariant);
    }

    return this.options.fallbackIcon || null;
  }

  /**
   * Get icon synchronously (only works for pre-loaded icons)
   */
  getIconSync(name: string, variant?: IconVariant): IconComponent | null {
    const iconVariant = variant || this.options.defaultVariant!;
    const definition = this.icons.get(name);
    
    if (!definition) {
      return this.options.fallbackIcon || null;
    }

    return definition.variants[iconVariant] || this.options.fallbackIcon || null;
  }

  /**
   * Check if an icon exists
   */
  hasIcon(name: string, variant?: IconVariant): boolean {
    const definition = this.icons.get(name);
    if (!definition) return false;
    
    if (!variant) return true;
    
    const iconVariant = variant || this.options.defaultVariant!;
    return !!definition.variants[iconVariant] || this.lazyLoaders.has(`${name}-${iconVariant}`);
  }

  /**
   * Get all registered icon names
   */
  getIconNames(): string[] {
    return Array.from(this.icons.keys());
  }

  /**
   * Get icons by category
   */
  getIconsByCategory(category: string): string[] {
    return Array.from(this.categories.get(category) || []);
  }

  /**
   * Get all categories
   */
  getCategories(): string[] {
    return Array.from(this.categories.keys());
  }

  /**
   * Search icons by name, tags, or keywords
   */
  searchIcons(query: string): string[] {
    const lowerQuery = query.toLowerCase();
    const results: string[] = [];

    for (const [name, definition] of this.icons) {
      // Search in name
      if (name.toLowerCase().includes(lowerQuery)) {
        results.push(name);
        continue;
      }

      // Search in tags
      if (definition.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))) {
        results.push(name);
        continue;
      }

      // Search in keywords
      if (definition.keywords?.some(keyword => keyword.toLowerCase().includes(lowerQuery))) {
        results.push(name);
        continue;
      }
    }

    return results;
  }

  /**
   * Get icon definition
   */
  getIconDefinition(name: string): IconDefinition | null {
    return this.icons.get(name) || null;
  }

  /**
   * Get available variants for an icon
   */
  getIconVariants(name: string): IconVariant[] {
    const definition = this.icons.get(name);
    if (!definition) return [];
    
    const variants = Object.keys(definition.variants) as IconVariant[];
    
    // Add lazy-loaded variants
    for (const [key] of this.lazyLoaders) {
      const [iconName, variant] = key.split('-');
      if (iconName === name && !variants.includes(variant as IconVariant)) {
        variants.push(variant as IconVariant);
      }
    }
    
    return variants;
  }

  /**
   * Preload icons for better performance
   */
  async preloadIcons(names: string[], variant?: IconVariant): Promise<void> {
    const promises = names.map(name => this.getIcon(name, variant));
    await Promise.all(promises);
  }

  /**
   * Preload icons by category
   */
  async preloadCategory(category: string, variant?: IconVariant): Promise<void> {
    const iconNames = this.getIconsByCategory(category);
    await this.preloadIcons(iconNames, variant);
  }

  /**
   * Clear loaded icons cache
   */
  clearCache(): void {
    this.loadedIcons.clear();
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalIcons: number;
    totalCategories: number;
    loadedIcons: number;
    lazyIcons: number;
    variantCounts: Record<IconVariant, number>;
  } {
    const variantCounts: Record<IconVariant, number> = {
      outline: 0,
      filled: 0,
      duotone: 0,
      light: 0,
      bold: 0,
    };

    for (const definition of this.icons.values()) {
      for (const variant of Object.keys(definition.variants) as IconVariant[]) {
        variantCounts[variant]++;
      }
    }

    return {
      totalIcons: this.icons.size,
      totalCategories: this.categories.size,
      loadedIcons: this.loadedIcons.size,
      lazyIcons: this.lazyLoaders.size,
      variantCounts,
    };
  }

  /**
   * Export icon definitions for backup or transfer
   */
  exportDefinitions(): IconDefinition[] {
    return Array.from(this.icons.values());
  }

  /**
   * Import icon definitions
   */
  importDefinitions(definitions: IconDefinition[]): void {
    this.registerBatch(definitions);
  }

  /**
   * Remove an icon from registry
   */
  unregister(name: string): boolean {
    const definition = this.icons.get(name);
    if (!definition) return false;

    this.icons.delete(name);
    
    // Remove from category
    if (definition.category) {
      const categorySet = this.categories.get(definition.category);
      if (categorySet) {
        categorySet.delete(name);
        if (categorySet.size === 0) {
          this.categories.delete(definition.category);
        }
      }
    }

    // Remove lazy loaders
    const variants = this.getIconVariants(name);
    variants.forEach(variant => {
      const key = `${name}-${variant}`;
      this.lazyLoaders.delete(key);
      this.loadedIcons.delete(key);
    });

    return true;
  }

  /**
   * Update icon definition
   */
  updateDefinition(name: string, updates: Partial<IconDefinition>): boolean {
    const definition = this.icons.get(name);
    if (!definition) return false;

    const updatedDefinition = { ...definition, ...updates };
    this.register(updatedDefinition);
    
    return true;
  }
}

// Global icon registry instance
export const globalIconRegistry = new IconRegistry();

// Convenience functions for global registry
export const registerIcon = (definition: IconDefinition) => 
  globalIconRegistry.register(definition);

export const registerIcons = (definitions: IconDefinition[]) => 
  globalIconRegistry.registerBatch(definitions);

export const registerLazyIcon = (
  name: string,
  loader: () => Promise<IconComponent>,
  options?: Parameters<IconRegistry['registerLazy']>[2]
) => globalIconRegistry.registerLazy(name, loader, options);

export const getIcon = (name: string, variant?: IconVariant) => 
  globalIconRegistry.getIcon(name, variant);

export const getIconSync = (name: string, variant?: IconVariant) => 
  globalIconRegistry.getIconSync(name, variant);

export const hasIcon = (name: string, variant?: IconVariant) => 
  globalIconRegistry.hasIcon(name, variant);

export const searchIcons = (query: string) => 
  globalIconRegistry.searchIcons(query);

export const getIconsByCategory = (category: string) => 
  globalIconRegistry.getIconsByCategory(category);

export const preloadIcons = (names: string[], variant?: IconVariant) => 
  globalIconRegistry.preloadIcons(names, variant);

export default IconRegistry;
