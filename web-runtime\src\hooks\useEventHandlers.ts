import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRuntimeStore } from '@stores/runtimeStore';
import { EventConfiguration, ActionConfiguration } from '@types/index';

/**
 * Hook for creating event handlers from configuration
 */
export function useEventHandlers(
  events: EventConfiguration[]
): Record<string, (...args: any[]) => void> {
  const navigate = useNavigate();
  const { setData, updateData, setLoading, setError } = useRuntimeStore();

  const executeAction = useCallback(async (action: ActionConfiguration, eventData?: any) => {
    try {
      switch (action.type) {
        case 'navigate':
          if (action.target) {
            navigate(action.target, { state: action.params });
          }
          break;

        case 'api':
          await handleApiAction(action, eventData);
          break;

        case 'data':
          handleDataAction(action, eventData, { setData, updateData });
          break;

        case 'form':
          await handleFormAction(action, eventData);
          break;

        case 'custom':
          await handleCustomAction(action, eventData);
          break;

        default:
          console.warn('Unknown action type:', action.type);
      }

      // Execute success action if defined
      if (action.success) {
        await executeAction(action.success, eventData);
      }
    } catch (error) {
      console.error('Action execution failed:', error);
      
      // Execute error action if defined
      if (action.error) {
        await executeAction(action.error, { ...eventData, error });
      }
      
      setError({
        code: 'ACTION_FAILED',
        message: error instanceof Error ? error.message : 'Action execution failed',
        timestamp: new Date().toISOString(),
      });
    }
  }, [navigate, setData, updateData, setLoading, setError]);

  const handlers = useMemo(() => {
    const eventHandlers: Record<string, (...args: any[]) => void> = {};

    events.forEach(event => {
      const handlerName = event.type;
      
      eventHandlers[handlerName] = async (...args: any[]) => {
        // Show confirmation if required
        if (event.action.confirmation) {
          const confirmed = await showConfirmation(event.action.confirmation);
          if (!confirmed) return;
        }

        // Prepare event data
        const eventData = {
          type: event.type,
          args,
          timestamp: new Date().toISOString(),
        };

        await executeAction(event.action, eventData);
      };
    });

    return eventHandlers;
  }, [events, executeAction]);

  return handlers;
}

// API action handler
async function handleApiAction(action: ActionConfiguration, eventData?: any) {
  const { setLoading, setError, setData } = useRuntimeStore.getState();
  
  if (!action.target) {
    throw new Error('API action requires a target URL');
  }

  setLoading(true);

  try {
    const response = await fetch(action.target, {
      method: action.params?.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...action.params?.headers,
      },
      body: action.params?.body ? JSON.stringify(action.params.body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Store response data if key is specified
    if (action.params?.dataKey) {
      setData(action.params.dataKey, data);
    }

    return data;
  } finally {
    setLoading(false);
  }
}

// Data action handler
function handleDataAction(
  action: ActionConfiguration,
  eventData: any,
  { setData, updateData }: { setData: Function; updateData: Function }
) {
  const { params } = action;
  
  if (!params) return;

  switch (params.operation) {
    case 'set':
      if (params.key && params.value !== undefined) {
        setData(params.key, params.value);
      }
      break;

    case 'update':
      if (params.updates) {
        updateData(params.updates);
      }
      break;

    case 'append':
      if (params.key && params.value !== undefined) {
        const { data } = useRuntimeStore.getState();
        const currentValue = data[params.key];
        if (Array.isArray(currentValue)) {
          setData(params.key, [...currentValue, params.value]);
        }
      }
      break;

    case 'remove':
      if (params.key && params.index !== undefined) {
        const { data } = useRuntimeStore.getState();
        const currentValue = data[params.key];
        if (Array.isArray(currentValue)) {
          const newValue = [...currentValue];
          newValue.splice(params.index, 1);
          setData(params.key, newValue);
        }
      }
      break;

    case 'clear':
      if (params.key) {
        setData(params.key, null);
      }
      break;

    default:
      console.warn('Unknown data operation:', params.operation);
  }
}

// Form action handler
async function handleFormAction(action: ActionConfiguration, eventData?: any) {
  const { params } = action;
  
  if (!params || !params.formId) {
    throw new Error('Form action requires formId parameter');
  }

  switch (params.operation) {
    case 'submit':
      await submitForm(params.formId, params.data);
      break;

    case 'reset':
      resetForm(params.formId);
      break;

    case 'validate':
      await validateForm(params.formId);
      break;

    default:
      console.warn('Unknown form operation:', params.operation);
  }
}

// Custom action handler
async function handleCustomAction(action: ActionConfiguration, eventData?: any) {
  const { params } = action;
  
  if (!params || !params.handler) {
    throw new Error('Custom action requires handler parameter');
  }

  // Execute custom handler function
  if (typeof params.handler === 'function') {
    await params.handler(eventData, params);
  } else if (typeof params.handler === 'string') {
    // Execute handler by name from global registry
    const handler = (window as any)[params.handler];
    if (typeof handler === 'function') {
      await handler(eventData, params);
    } else {
      throw new Error(`Custom handler '${params.handler}' not found`);
    }
  }
}

// Form utilities
async function submitForm(formId: string, data?: any) {
  const form = document.getElementById(formId) as HTMLFormElement;
  if (!form) {
    throw new Error(`Form with id '${formId}' not found`);
  }

  // Create form data
  const formData = new FormData(form);
  
  // Add additional data if provided
  if (data) {
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, String(value));
    });
  }

  // Submit form
  const action = form.action || window.location.href;
  const method = form.method || 'POST';

  const response = await fetch(action, {
    method,
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Form submission failed: ${response.statusText}`);
  }

  return response;
}

function resetForm(formId: string) {
  const form = document.getElementById(formId) as HTMLFormElement;
  if (form) {
    form.reset();
  }
}

async function validateForm(formId: string): Promise<boolean> {
  const form = document.getElementById(formId) as HTMLFormElement;
  if (!form) {
    throw new Error(`Form with id '${formId}' not found`);
  }

  return form.checkValidity();
}

// Confirmation dialog
async function showConfirmation(confirmation: {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}): Promise<boolean> {
  // Use browser confirm for now - in a real app, you'd use a custom modal
  return window.confirm(`${confirmation.title}\n\n${confirmation.message}`);
}

/**
 * Hook for creating specific event handlers
 */
export function useClickHandler(action: ActionConfiguration) {
  const handlers = useEventHandlers([{ type: 'onClick', action }]);
  return handlers.onClick;
}

export function useChangeHandler(action: ActionConfiguration) {
  const handlers = useEventHandlers([{ type: 'onChange', action }]);
  return handlers.onChange;
}

export function useSubmitHandler(action: ActionConfiguration) {
  const handlers = useEventHandlers([{ type: 'onSubmit', action }]);
  return handlers.onSubmit;
}

/**
 * Hook for creating navigation handlers
 */
export function useNavigationHandler() {
  const navigate = useNavigate();

  return useCallback((path: string, options?: { replace?: boolean; state?: any }) => {
    navigate(path, options);
  }, [navigate]);
}

/**
 * Hook for creating API call handlers
 */
export function useApiHandler() {
  const { setLoading, setError, setData } = useRuntimeStore();

  return useCallback(async (config: {
    url: string;
    method?: string;
    headers?: Record<string, string>;
    body?: any;
    dataKey?: string;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(config.url, {
        method: config.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers,
        },
        body: config.body ? JSON.stringify(config.body) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (config.dataKey) {
        setData(config.dataKey, data);
      }

      return data;
    } catch (error) {
      const errorObj = {
        code: 'API_ERROR',
        message: error instanceof Error ? error.message : 'API call failed',
        timestamp: new Date().toISOString(),
      };
      setError(errorObj);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError, setData]);
}
