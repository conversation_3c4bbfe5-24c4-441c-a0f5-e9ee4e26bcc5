package com.uibuilder.security.rbac;

import com.uibuilder.entity.User;
import com.uibuilder.entity.Role;
import com.uibuilder.entity.Permission;
import com.uibuilder.entity.Workspace;
import com.uibuilder.repository.UserRepository;
import com.uibuilder.repository.RoleRepository;
import com.uibuilder.repository.PermissionRepository;
import com.uibuilder.security.audit.AuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleBasedAccessControl {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final AuditService auditService;

    /**
     * Check if current user has permission for a specific action on a resource
     */
    public boolean hasPermission(String resource, String action) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null || !auth.isAuthenticated()) {
            return false;
        }

        String userId = auth.getName();
        return hasPermission(userId, resource, action, null);
    }

    /**
     * Check if user has permission for a specific action on a resource within a workspace
     */
    public boolean hasPermission(String userId, String resource, String action, String workspaceId) {
        try {
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new AccessDeniedException("User not found"));

            // System admin has all permissions
            if (hasSystemRole(user, "SYSTEM_ADMIN")) {
                return true;
            }

            // Get user permissions for the workspace
            Set<String> userPermissions = getUserPermissions(userId, workspaceId);
            
            // Check specific permission
            String permissionKey = resource + ":" + action;
            boolean hasPermission = userPermissions.contains(permissionKey) || 
                                  userPermissions.contains(resource + ":*") ||
                                  userPermissions.contains("*:*");

            // Audit permission check
            auditService.logAccessControl(
                userId,
                resource,
                action,
                workspaceId,
                hasPermission ? "GRANTED" : "DENIED"
            );

            return hasPermission;

        } catch (Exception e) {
            log.error("Error checking permission for user: {}, resource: {}, action: {}", 
                     userId, resource, action, e);
            return false;
        }
    }

    /**
     * Get all permissions for a user in a specific workspace
     */
    @Cacheable(value = "userPermissions", key = "#userId + ':' + #workspaceId")
    public Set<String> getUserPermissions(String userId, String workspaceId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new AccessDeniedException("User not found"));

        Set<String> permissions = new HashSet<>();

        // Get global roles
        user.getGlobalRoles().forEach(role -> {
            permissions.addAll(role.getPermissions().stream()
                .map(Permission::getKey)
                .collect(Collectors.toSet()));
        });

        // Get workspace-specific roles
        if (workspaceId != null) {
            user.getWorkspaceRoles().stream()
                .filter(wr -> wr.getWorkspace().getId().equals(workspaceId))
                .forEach(workspaceRole -> {
                    permissions.addAll(workspaceRole.getRole().getPermissions().stream()
                        .map(Permission::getKey)
                        .collect(Collectors.toSet()));
                });
        }

        return permissions;
    }

    /**
     * Assign role to user in a workspace
     */
    public void assignWorkspaceRole(String userId, String roleId, String workspaceId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new AccessDeniedException("User not found"));
        
        Role role = roleRepository.findById(roleId)
            .orElseThrow(() -> new AccessDeniedException("Role not found"));

        Workspace workspace = workspaceRepository.findById(workspaceId)
            .orElseThrow(() -> new AccessDeniedException("Workspace not found"));

        // Check if current user has permission to assign roles
        if (!hasPermission("workspace", "assign_roles", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to assign roles");
        }

        // Create workspace role assignment
        WorkspaceRole workspaceRole = WorkspaceRole.builder()
            .user(user)
            .role(role)
            .workspace(workspace)
            .assignedBy(getCurrentUserId())
            .assignedAt(LocalDateTime.now())
            .build();

        workspaceRoleRepository.save(workspaceRole);

        // Clear cache
        cacheManager.getCache("userPermissions").evict(userId + ":" + workspaceId);

        auditService.logRoleAssignment(
            getCurrentUserId(),
            userId,
            roleId,
            workspaceId,
            "ASSIGNED"
        );
    }

    /**
     * Remove role from user in a workspace
     */
    public void removeWorkspaceRole(String userId, String roleId, String workspaceId) {
        // Check permission
        if (!hasPermission("workspace", "remove_roles", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to remove roles");
        }

        WorkspaceRole workspaceRole = workspaceRoleRepository
            .findByUserIdAndRoleIdAndWorkspaceId(userId, roleId, workspaceId)
            .orElseThrow(() -> new AccessDeniedException("Workspace role not found"));

        workspaceRoleRepository.delete(workspaceRole);

        // Clear cache
        cacheManager.getCache("userPermissions").evict(userId + ":" + workspaceId);

        auditService.logRoleAssignment(
            getCurrentUserId(),
            userId,
            roleId,
            workspaceId,
            "REMOVED"
        );
    }

    /**
     * Create custom role with permissions
     */
    public Role createCustomRole(String name, String description, List<String> permissionKeys, 
                                String workspaceId) {
        // Check permission to create roles
        if (!hasPermission("workspace", "create_roles", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to create roles");
        }

        // Validate permissions exist
        List<Permission> permissions = permissionRepository.findByKeyIn(permissionKeys);
        if (permissions.size() != permissionKeys.size()) {
            throw new IllegalArgumentException("Some permissions do not exist");
        }

        Role role = Role.builder()
            .name(name)
            .description(description)
            .isCustom(true)
            .workspaceId(workspaceId)
            .permissions(new HashSet<>(permissions))
            .createdBy(getCurrentUserId())
            .createdAt(LocalDateTime.now())
            .build();

        role = roleRepository.save(role);

        auditService.logRoleManagement(
            getCurrentUserId(),
            role.getId(),
            "ROLE_CREATED",
            Map.of("name", name, "permissions", permissionKeys)
        );

        return role;
    }

    /**
     * Get available permissions grouped by resource
     */
    public Map<String, List<Permission>> getAvailablePermissions() {
        List<Permission> allPermissions = permissionRepository.findAll();
        
        return allPermissions.stream()
            .collect(Collectors.groupingBy(Permission::getResource));
    }

    /**
     * Get user's effective roles in a workspace
     */
    public List<RoleInfo> getUserRoles(String userId, String workspaceId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new AccessDeniedException("User not found"));

        List<RoleInfo> roles = new ArrayList<>();

        // Add global roles
        user.getGlobalRoles().forEach(role -> {
            roles.add(RoleInfo.builder()
                .id(role.getId())
                .name(role.getName())
                .description(role.getDescription())
                .scope("GLOBAL")
                .permissions(role.getPermissions().stream()
                    .map(Permission::getKey)
                    .collect(Collectors.toList()))
                .build());
        });

        // Add workspace roles
        if (workspaceId != null) {
            user.getWorkspaceRoles().stream()
                .filter(wr -> wr.getWorkspace().getId().equals(workspaceId))
                .forEach(workspaceRole -> {
                    Role role = workspaceRole.getRole();
                    roles.add(RoleInfo.builder()
                        .id(role.getId())
                        .name(role.getName())
                        .description(role.getDescription())
                        .scope("WORKSPACE")
                        .permissions(role.getPermissions().stream()
                            .map(Permission::getKey)
                            .collect(Collectors.toList()))
                        .build());
                });
        }

        return roles;
    }

    /**
     * Check if user has a specific system role
     */
    private boolean hasSystemRole(User user, String roleName) {
        return user.getGlobalRoles().stream()
            .anyMatch(role -> role.getName().equals(roleName));
    }

    /**
     * Get current authenticated user ID
     */
    private String getCurrentUserId() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null ? auth.getName() : null;
    }

    /**
     * Validate resource access based on ownership and permissions
     */
    public void validateResourceAccess(String resourceType, String resourceId, String action) {
        String userId = getCurrentUserId();
        if (userId == null) {
            throw new AccessDeniedException("User not authenticated");
        }

        // Check if user owns the resource
        if (isResourceOwner(userId, resourceType, resourceId)) {
            return; // Owner has full access
        }

        // Check workspace permissions
        String workspaceId = getResourceWorkspace(resourceType, resourceId);
        if (!hasPermission(userId, resourceType, action, workspaceId)) {
            throw new AccessDeniedException(
                String.format("Access denied for %s:%s on resource %s", resourceType, action, resourceId)
            );
        }
    }

    private boolean isResourceOwner(String userId, String resourceType, String resourceId) {
        switch (resourceType) {
            case "ui_config":
                return uiConfigRepository.existsByIdAndCreatedBy(resourceId, userId);
            case "template":
                return templateRepository.existsByIdAndCreatedBy(resourceId, userId);
            case "workspace":
                return workspaceRepository.existsByIdAndOwnerId(resourceId, userId);
            default:
                return false;
        }
    }

    private String getResourceWorkspace(String resourceType, String resourceId) {
        switch (resourceType) {
            case "ui_config":
                return uiConfigRepository.findById(resourceId)
                    .map(config -> config.getWorkspace().getId())
                    .orElse(null);
            case "template":
                return templateRepository.findById(resourceId)
                    .map(template -> template.getWorkspace().getId())
                    .orElse(null);
            case "workspace":
                return resourceId;
            default:
                return null;
        }
    }
}

@Data
@Builder
class RoleInfo {
    private String id;
    private String name;
    private String description;
    private String scope;
    private List<String> permissions;
}

// Predefined system permissions
@Component
class PermissionInitializer implements ApplicationRunner {
    
    private final PermissionRepository permissionRepository;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        initializeSystemPermissions();
    }
    
    private void initializeSystemPermissions() {
        List<Permission> permissions = Arrays.asList(
            // UI Config permissions
            Permission.builder().key("ui_config:create").resource("ui_config").action("create").build(),
            Permission.builder().key("ui_config:read").resource("ui_config").action("read").build(),
            Permission.builder().key("ui_config:update").resource("ui_config").action("update").build(),
            Permission.builder().key("ui_config:delete").resource("ui_config").action("delete").build(),
            Permission.builder().key("ui_config:publish").resource("ui_config").action("publish").build(),
            
            // Template permissions
            Permission.builder().key("template:create").resource("template").action("create").build(),
            Permission.builder().key("template:read").resource("template").action("read").build(),
            Permission.builder().key("template:update").resource("template").action("update").build(),
            Permission.builder().key("template:delete").resource("template").action("delete").build(),
            Permission.builder().key("template:share").resource("template").action("share").build(),
            
            // Workspace permissions
            Permission.builder().key("workspace:create").resource("workspace").action("create").build(),
            Permission.builder().key("workspace:read").resource("workspace").action("read").build(),
            Permission.builder().key("workspace:update").resource("workspace").action("update").build(),
            Permission.builder().key("workspace:delete").resource("workspace").action("delete").build(),
            Permission.builder().key("workspace:invite_users").resource("workspace").action("invite_users").build(),
            Permission.builder().key("workspace:assign_roles").resource("workspace").action("assign_roles").build(),
            Permission.builder().key("workspace:remove_roles").resource("workspace").action("remove_roles").build(),
            Permission.builder().key("workspace:create_roles").resource("workspace").action("create_roles").build(),
            
            // Collaboration permissions
            Permission.builder().key("collaboration:comment").resource("collaboration").action("comment").build(),
            Permission.builder().key("collaboration:review").resource("collaboration").action("review").build(),
            Permission.builder().key("collaboration:approve").resource("collaboration").action("approve").build(),
            
            // Admin permissions
            Permission.builder().key("admin:user_management").resource("admin").action("user_management").build(),
            Permission.builder().key("admin:system_settings").resource("admin").action("system_settings").build(),
            Permission.builder().key("admin:audit_logs").resource("admin").action("audit_logs").build()
        );
        
        permissions.forEach(permission -> {
            if (!permissionRepository.existsByKey(permission.getKey())) {
                permissionRepository.save(permission);
            }
        });
    }
}
