package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.uiplatform.entity.Template;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * DTO for Template entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TemplateDTO extends BaseDTO {

    @NotBlank(message = "Template name is required")
    @Size(max = 100, message = "Template name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @Size(max = 2000, message = "Long description must not exceed 2000 characters")
    private String longDescription;

    @NotBlank(message = "Category is required")
    @Size(max = 50, message = "Category must not exceed 50 characters")
    private String category;

    @Size(max = 100, message = "Subcategory must not exceed 100 characters")
    private String subcategory;

    private String tags;

    private Map<String, Object> templateData;
    private Map<String, Object> previewImages;

    @Pattern(regexp = "^https?://.*", message = "Demo URL must be a valid HTTP/HTTPS URL")
    private String demoUrl;

    @Pattern(regexp = "^https?://.*", message = "Documentation URL must be a valid HTTP/HTTPS URL")
    private String documentationUrl;

    private Template.TemplateStatus status;

    private Boolean isPublic;
    private Boolean isPremium;
    private Boolean isFeatured;

    @DecimalMin(value = "0.0", message = "Price must be non-negative")
    @DecimalMax(value = "9999.99", message = "Price cannot exceed 9999.99")
    private BigDecimal price;

    @Size(max = 50, message = "License type must not exceed 50 characters")
    private String licenseType;

    @NotBlank(message = "Version is required")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Version must be in format: x.y.z")
    private String version;

    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Min platform version must be in format: x.y.z")
    private String minPlatformVersion;

    private Long downloadCount;

    @DecimalMin(value = "0.0", message = "Rating must be non-negative")
    @DecimalMax(value = "5.0", message = "Rating cannot exceed 5.0")
    private BigDecimal rating;

    private Long ratingCount;
    private Long viewCount;

    private Map<String, Object> compatibility;
    private Map<String, Object> requirements;
    private Map<String, Object> installationGuide;

    // Related entities
    private UUID organizationId;
    private String organizationName;

    @NotNull(message = "Author ID is required")
    private UUID authorId;
    private String authorName;

    // Collections
    private List<TemplateReviewDTO> reviews;

    // Statistics
    private Long reviewCount;
    private BigDecimal averageRating;

    // Constructors
    public TemplateDTO() {}

    public TemplateDTO(String name, String category) {
        this.name = name;
        this.category = category;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLongDescription() {
        return longDescription;
    }

    public void setLongDescription(String longDescription) {
        this.longDescription = longDescription;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Map<String, Object> getTemplateData() {
        return templateData;
    }

    public void setTemplateData(Map<String, Object> templateData) {
        this.templateData = templateData;
    }

    public Map<String, Object> getPreviewImages() {
        return previewImages;
    }

    public void setPreviewImages(Map<String, Object> previewImages) {
        this.previewImages = previewImages;
    }

    public String getDemoUrl() {
        return demoUrl;
    }

    public void setDemoUrl(String demoUrl) {
        this.demoUrl = demoUrl;
    }

    public String getDocumentationUrl() {
        return documentationUrl;
    }

    public void setDocumentationUrl(String documentationUrl) {
        this.documentationUrl = documentationUrl;
    }

    public Template.TemplateStatus getStatus() {
        return status;
    }

    public void setStatus(Template.TemplateStatus status) {
        this.status = status;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsPremium() {
        return isPremium;
    }

    public void setIsPremium(Boolean isPremium) {
        this.isPremium = isPremium;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMinPlatformVersion() {
        return minPlatformVersion;
    }

    public void setMinPlatformVersion(String minPlatformVersion) {
        this.minPlatformVersion = minPlatformVersion;
    }

    public Long getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Long getRatingCount() {
        return ratingCount;
    }

    public void setRatingCount(Long ratingCount) {
        this.ratingCount = ratingCount;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Map<String, Object> getCompatibility() {
        return compatibility;
    }

    public void setCompatibility(Map<String, Object> compatibility) {
        this.compatibility = compatibility;
    }

    public Map<String, Object> getRequirements() {
        return requirements;
    }

    public void setRequirements(Map<String, Object> requirements) {
        this.requirements = requirements;
    }

    public Map<String, Object> getInstallationGuide() {
        return installationGuide;
    }

    public void setInstallationGuide(Map<String, Object> installationGuide) {
        this.installationGuide = installationGuide;
    }

    public UUID getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(UUID organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public UUID getAuthorId() {
        return authorId;
    }

    public void setAuthorId(UUID authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public List<TemplateReviewDTO> getReviews() {
        return reviews;
    }

    public void setReviews(List<TemplateReviewDTO> reviews) {
        this.reviews = reviews;
    }

    public Long getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Long reviewCount) {
        this.reviewCount = reviewCount;
    }

    public BigDecimal getAverageRating() {
        return averageRating;
    }

    public void setAverageRating(BigDecimal averageRating) {
        this.averageRating = averageRating;
    }

    // Utility methods
    public boolean isFree() {
        return price != null && price.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Create DTO for template creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Template name is required")
        @Size(max = 100, message = "Template name must not exceed 100 characters")
        private String name;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @NotBlank(message = "Category is required")
        @Size(max = 50, message = "Category must not exceed 50 characters")
        private String category;

        @Size(max = 100, message = "Subcategory must not exceed 100 characters")
        private String subcategory;

        private String tags;

        private Map<String, Object> templateData;

        @DecimalMin(value = "0.0", message = "Price must be non-negative")
        @DecimalMax(value = "9999.99", message = "Price cannot exceed 9999.99")
        private BigDecimal price = BigDecimal.ZERO;

        @Size(max = 50, message = "License type must not exceed 50 characters")
        private String licenseType = "MIT";

        @NotBlank(message = "Version is required")
        @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Version must be in format: x.y.z")
        private String version = "1.0.0";

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getSubcategory() {
            return subcategory;
        }

        public void setSubcategory(String subcategory) {
            this.subcategory = subcategory;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }

        public Map<String, Object> getTemplateData() {
            return templateData;
        }

        public void setTemplateData(Map<String, Object> templateData) {
            this.templateData = templateData;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public String getLicenseType() {
            return licenseType;
        }

        public void setLicenseType(String licenseType) {
            this.licenseType = licenseType;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
