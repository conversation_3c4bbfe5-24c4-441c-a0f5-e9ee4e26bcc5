import React, { useMemo, useCallback, useState, useEffect, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { componentRegistry } from '../../utils/ComponentRegistry';
import { ComponentConfig, ComponentProps, DeviceInfo } from '../../types/component';
import { evaluateExpression, processConditionals, processLoops } from '../../utils/expressionEvaluator';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import { ErrorFallback } from '../ErrorFallback/ErrorFallback';
import './DynamicRenderer.scss';

export interface DynamicRendererProps {
  config: any;
  context?: Record<string, any>;
  mode?: 'design' | 'preview' | 'interactive';
  debugMode?: boolean;
  interactive?: boolean;
  deviceInfo?: DeviceInfo;
  onComponentError?: (error: Error, componentType: string) => void;
  onComponentMount?: (componentId: string, componentType: string) => void;
  onComponentUnmount?: (componentId: string, componentType: string) => void;
  className?: string;
}

interface RenderContext {
  data: Record<string, any>;
  functions: Record<string, Function>;
  components: Record<string, any>;
  deviceInfo?: DeviceInfo;
  mode: 'design' | 'preview' | 'interactive';
  debugMode: boolean;
}

export const DynamicRenderer: React.FC<DynamicRendererProps> = ({
  config,
  context = {},
  mode = 'preview',
  debugMode = false,
  interactive = true,
  deviceInfo,
  onComponentError,
  onComponentMount,
  onComponentUnmount,
  className
}) => {
  const [renderContext, setRenderContext] = useState<RenderContext>({
    data: context,
    functions: {},
    components: {},
    deviceInfo,
    mode,
    debugMode
  });

  const [componentStates, setComponentStates] = useState<Record<string, any>>({});

  // Update render context when props change
  useEffect(() => {
    setRenderContext(prev => ({
      ...prev,
      data: { ...prev.data, ...context },
      deviceInfo,
      mode,
      debugMode
    }));
  }, [context, deviceInfo, mode, debugMode]);

  // Handle component state updates
  const updateComponentState = useCallback((componentId: string, state: any) => {
    setComponentStates(prev => ({
      ...prev,
      [componentId]: { ...prev[componentId], ...state }
    }));
  }, []);

  // Render a single component
  const renderComponent = useCallback((
    componentConfig: any,
    index: number = 0,
    parentContext: RenderContext = renderContext
  ): React.ReactElement | null => {
    if (!componentConfig || typeof componentConfig !== 'object') {
      if (debugMode) {
        return (
          <div className="debug-error" data-testid="malformed-configuration">
            Invalid configuration: {JSON.stringify(componentConfig)}
          </div>
        );
      }
      return null;
    }

    const { id, type, props = {}, children, conditions, loop, style } = componentConfig;

    if (!type) {
      if (debugMode) {
        return (
          <div className="debug-error" data-testid="malformed-configuration">
            Missing component type
          </div>
        );
      }
      return null;
    }

    // Check if component exists in registry
    if (!componentRegistry.hasComponent(type)) {
      if (debugMode) {
        return (
          <div className="debug-error" data-testid="unknown-component-fallback">
            Unknown component: {type}
          </div>
        );
      }
      return null;
    }

    // Process conditionals
    if (conditions && !processConditionals(conditions, parentContext.data)) {
      return null;
    }

    // Process loops
    if (loop) {
      return renderLoop(componentConfig, parentContext);
    }

    // Get component from registry
    const Component = componentRegistry.getComponent(type);
    if (!Component) {
      if (debugMode) {
        return (
          <div className="debug-error" data-testid="component-error-boundary">
            Error rendering component: {type}
          </div>
        );
      }
      return null;
    }

    // Process props with expression evaluation
    const processedProps = useMemo(() => {
      const processed: ComponentProps = {};
      
      Object.entries(props).forEach(([key, value]) => {
        try {
          if (typeof value === 'string' && value.includes('{{')) {
            processed[key] = evaluateExpression(value, parentContext.data);
          } else {
            processed[key] = value;
          }
        } catch (error) {
          console.warn(`Failed to evaluate expression for prop ${key}:`, error);
          processed[key] = value;
        }
      });

      // Add component state to props
      if (id && componentStates[id]) {
        processed.state = componentStates[id];
        processed.setState = (state: any) => updateComponentState(id, state);
      }

      // Add interaction handlers if in interactive mode
      if (interactive && mode === 'interactive') {
        processed.onClick = processed.onClick || (() => {});
        processed.onChange = processed.onChange || (() => {});
        processed.onFocus = processed.onFocus || (() => {});
        processed.onBlur = processed.onBlur || (() => {});
      }

      // Add device info
      if (deviceInfo) {
        processed.deviceInfo = deviceInfo;
      }

      // Apply custom styles
      if (style) {
        processed.style = { ...processed.style, ...style };
      }

      return processed;
    }, [props, parentContext.data, id, componentStates, interactive, mode, deviceInfo, style]);

    // Validate props if validation is enabled
    if (debugMode) {
      const validation = componentRegistry.validateProps(type, processedProps);
      if (!validation.isValid) {
        return (
          <div className="debug-error" data-testid="validation-error">
            Component validation failed: {validation.errors.join(', ')}
          </div>
        );
      }
    }

    // Render children
    const renderedChildren = useMemo(() => {
      if (!children || !Array.isArray(children)) {
        return undefined;
      }

      return children.map((child, childIndex) => 
        renderComponent(child, childIndex, parentContext)
      ).filter(Boolean);
    }, [children, parentContext]);

    // Component lifecycle tracking
    useEffect(() => {
      if (id && onComponentMount) {
        onComponentMount(id, type);
      }

      return () => {
        if (id && onComponentUnmount) {
          onComponentUnmount(id, type);
        }
      };
    }, [id, type, onComponentMount, onComponentUnmount]);

    // Render component with error boundary
    return (
      <ErrorBoundary
        key={id || `${type}-${index}`}
        FallbackComponent={({ error }) => (
          <ErrorFallback
            error={error}
            componentType={type}
            componentId={id}
            onRetry={() => window.location.reload()}
          />
        )}
        onError={(error) => {
          console.error(`Error in component ${type}:`, error);
          onComponentError?.(error, type);
        }}
      >
        <Suspense fallback={<LoadingSpinner size="small" />}>
          <Component {...processedProps}>
            {renderedChildren}
          </Component>
        </Suspense>
      </ErrorBoundary>
    );
  }, [renderContext, componentStates, updateComponentState, interactive, mode, deviceInfo, debugMode, onComponentError, onComponentMount, onComponentUnmount]);

  // Render loop components
  const renderLoop = useCallback((
    componentConfig: any,
    parentContext: RenderContext
  ): React.ReactElement[] => {
    const { loop, ...baseConfig } = componentConfig;
    const { data: dataPath, variable = 'item', index: indexVariable = 'index' } = loop;

    try {
      const loopData = evaluateExpression(`{{${dataPath}}}`, parentContext.data);
      
      if (!Array.isArray(loopData)) {
        console.warn('Loop data is not an array:', loopData);
        return [];
      }

      return loopData.map((item, index) => {
        const loopContext: RenderContext = {
          ...parentContext,
          data: {
            ...parentContext.data,
            [variable]: item,
            [indexVariable]: index
          }
        };

        return renderComponent(
          {
            ...baseConfig,
            id: `${baseConfig.id || baseConfig.type}-${index}`
          },
          index,
          loopContext
        );
      }).filter(Boolean) as React.ReactElement[];
    } catch (error) {
      console.error('Error processing loop:', error);
      return [];
    }
  }, [renderComponent]);

  // Handle empty or null configuration
  if (!config) {
    return (
      <div className="dynamic-renderer-empty" data-testid="empty-configuration">
        No configuration provided
      </div>
    );
  }

  // Render the root component or components
  const renderedContent = useMemo(() => {
    if (Array.isArray(config)) {
      return config.map((componentConfig, index) => 
        renderComponent(componentConfig, index)
      ).filter(Boolean);
    } else {
      return renderComponent(config);
    }
  }, [config, renderComponent]);

  return (
    <div className={`dynamic-renderer ${className || ''}`} data-mode={mode}>
      {debugMode && (
        <div className="debug-info">
          <div className="debug-header">Debug Mode</div>
          <div className="debug-details">
            <div>Mode: {mode}</div>
            <div>Interactive: {interactive ? 'Yes' : 'No'}</div>
            <div>Device: {deviceInfo?.category || 'Unknown'}</div>
            <div>Components: {Array.isArray(config) ? config.length : 1}</div>
          </div>
        </div>
      )}
      
      <div className="dynamic-renderer-content">
        {renderedContent}
      </div>
    </div>
  );
};
