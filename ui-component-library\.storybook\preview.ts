import type { Preview } from '@storybook/react';
import { ThemeProvider } from '@ui-builder/core';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport';
import '../packages/core/src/styles/globals.css';

// Import design tokens
import '@ui-builder/tokens/css/variables.css';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
      expanded: true,
      sort: 'requiredFirst',
    },
    docs: {
      toc: {
        contentsSelector: '.sbdocs-content',
        headingSelector: 'h1, h2, h3',
        ignoreSelector: '#primary',
        title: 'Table of Contents',
        disable: false,
        unsafeTocbotOptions: {
          orderedList: false,
        },
      },
      source: {
        state: 'open',
      },
    },
    viewport: {
      viewports: {
        ...INITIAL_VIEWPORTS,
        mobile1: {
          name: 'Small Mobile',
          styles: {
            width: '320px',
            height: '568px',
          },
        },
        mobile2: {
          name: 'Large Mobile',
          styles: {
            width: '414px',
            height: '896px',
          },
        },
        tablet: {
          name: 'Tablet',
          styles: {
            width: '768px',
            height: '1024px',
          },
        },
        desktop: {
          name: 'Desktop',
          styles: {
            width: '1024px',
            height: '768px',
          },
        },
        wide: {
          name: 'Wide Screen',
          styles: {
            width: '1440px',
            height: '900px',
          },
        },
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'dark',
          value: '#1a1a1a',
        },
        {
          name: 'gray',
          value: '#f5f5f5',
        },
      ],
    },
    layout: 'centered',
    options: {
      storySort: {
        order: [
          'Introduction',
          'Design System',
          ['Overview', 'Colors', 'Typography', 'Spacing', 'Icons'],
          'Foundation',
          ['Button', 'Input', 'Select', 'Checkbox', 'Radio'],
          'Layout',
          ['Container', 'Grid', 'Stack', 'Divider'],
          'Navigation',
          ['Breadcrumb', 'Menu', 'Pagination', 'Tabs'],
          'Data Display',
          ['Avatar', 'Badge', 'Card', 'List', 'Table', 'Tag'],
          'Feedback',
          ['Alert', 'Loading', 'Modal', 'Notification', 'Progress'],
          'Other',
          '*',
        ],
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'focus-order-semantics',
            enabled: true,
          },
          {
            id: 'keyboard-navigation',
            enabled: true,
          },
        ],
      },
    },
  },
  
  decorators: [
    withThemeFromJSXProvider({
      themes: {
        light: { mode: 'light', brand: 'default' },
        dark: { mode: 'dark', brand: 'default' },
        corporate: { mode: 'light', brand: 'corporate' },
        creative: { mode: 'light', brand: 'creative' },
      },
      defaultTheme: 'light',
      Provider: ThemeProvider,
      GlobalStyles: () => null,
    }),
  ],
  
  globalTypes: {
    theme: {
      description: 'Global theme for components',
      defaultValue: 'light',
      toolbar: {
        title: 'Theme',
        icon: 'paintbrush',
        items: [
          { value: 'light', title: 'Light', icon: 'sun' },
          { value: 'dark', title: 'Dark', icon: 'moon' },
          { value: 'corporate', title: 'Corporate', icon: 'building' },
          { value: 'creative', title: 'Creative', icon: 'star' },
        ],
        dynamicTitle: true,
      },
    },
    locale: {
      description: 'Internationalization locale',
      defaultValue: 'en',
      toolbar: {
        icon: 'globe',
        items: [
          { value: 'en', title: 'English' },
          { value: 'es', title: 'Español' },
          { value: 'fr', title: 'Français' },
          { value: 'de', title: 'Deutsch' },
          { value: 'ja', title: '日本語' },
          { value: 'zh', title: '中文' },
        ],
        showName: true,
      },
    },
    density: {
      description: 'Component density',
      defaultValue: 'normal',
      toolbar: {
        title: 'Density',
        icon: 'component',
        items: [
          { value: 'compact', title: 'Compact' },
          { value: 'normal', title: 'Normal' },
          { value: 'comfortable', title: 'Comfortable' },
        ],
        dynamicTitle: true,
      },
    },
  },
  
  tags: ['autodocs'],
};

export default preview;
