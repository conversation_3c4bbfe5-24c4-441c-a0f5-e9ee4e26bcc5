import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Sidebar component
class UISidebar extends StatelessWidget {
  const UISidebar({
    super.key,
    required this.children,
    this.width = 250,
    this.backgroundColor,
    this.elevation = 1,
  });

  final List<Widget> children;
  final double width;
  final Color? backgroundColor;
  final double elevation;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Material(
      elevation: elevation,
      color: backgroundColor ?? colorScheme.surface,
      child: Container(
        width: width,
        child: Column(
          children: children,
        ),
      ),
    );
  }
}
