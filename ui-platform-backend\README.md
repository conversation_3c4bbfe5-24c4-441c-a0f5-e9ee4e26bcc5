# UI Platform Backend

A comprehensive backend system for a dynamic UI configuration platform that enables users to create, manage, and deploy user interfaces through a visual interface.

## 🚀 Features

### Core Features
- **Multi-tenant Organization Management** - Complete organization and user management with role-based access control
- **Dynamic UI Configuration** - Create and manage UI configurations with real-time preview
- **Component Library** - Extensive library of reusable UI components with customization options
- **Template Marketplace** - Browse, purchase, and share UI templates
- **Theme & Layout Management** - Comprehensive theming and layout system
- **Form Builder** - Advanced form building capabilities with validation
- **Real-time Collaboration** - Multi-user editing and real-time updates

### Technical Features
- **RESTful API** - Comprehensive REST API with OpenAPI documentation
- **GraphQL Support** - Flexible GraphQL API for efficient data fetching
- **JWT Authentication** - Secure authentication with role-based permissions
- **Redis Caching** - High-performance caching for improved response times
- **Database Migrations** - Liquibase-managed database schema versioning
- **Comprehensive Testing** - Unit and integration tests with high coverage
- **Docker Support** - Containerized deployment with Docker Compose
- **Monitoring & Metrics** - Built-in health checks and Prometheus metrics

## 🏗️ Architecture

### Technology Stack
- **Framework**: Spring Boot 3.2
- **Database**: PostgreSQL with JPA/Hibernate
- **Caching**: Redis
- **Authentication**: JWT with Spring Security
- **API Documentation**: OpenAPI 3.0 (Swagger)
- **GraphQL**: GraphQL Java Kickstart
- **Testing**: JUnit 5, Mockito, TestContainers
- **Build Tool**: Maven
- **Java Version**: 17+

### Project Structure
```
ui-platform-backend/
├── src/main/java/com/uiplatform/
│   ├── config/          # Configuration classes
│   ├── controller/      # REST controllers
│   ├── dto/            # Data Transfer Objects
│   ├── entity/         # JPA entities
│   ├── exception/      # Custom exceptions
│   ├── graphql/        # GraphQL resolvers
│   ├── mapper/         # MapStruct mappers
│   ├── repository/     # JPA repositories
│   ├── security/       # Security configuration
│   └── service/        # Business logic services
├── src/main/resources/
│   ├── db/changelog/   # Liquibase migrations
│   ├── graphql/        # GraphQL schemas
│   └── application.yml # Configuration
└── src/test/           # Test classes
```

## 🚀 Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+
- Redis 6+
- Docker (optional)

### Local Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd ui-platform-backend
```

2. **Setup PostgreSQL Database**
```sql
CREATE DATABASE ui_platform;
CREATE USER ui_platform_user WITH PASSWORD 'ui_platform_password';
GRANT ALL PRIVILEGES ON DATABASE ui_platform TO ui_platform_user;
```

3. **Setup Redis**
```bash
# Using Docker
docker run -d -p 6379:6379 redis:7-alpine

# Or install locally
# Follow Redis installation guide for your OS
```

4. **Configure Environment Variables**
```bash
export JWT_SECRET=your-secret-key-here
export DATABASE_URL=********************************************
export REDIS_URL=redis://localhost:6379
```

5. **Run the Application**
```bash
# Development mode
mvn spring-boot:run -Dspring-boot.run.profiles=development

# Or build and run
mvn clean package
java -jar target/ui-platform-backend-1.0.0.jar
```

6. **Access the Application**
- API Documentation: http://localhost:8080/swagger-ui.html
- GraphQL Playground: http://localhost:8080/graphiql
- Health Check: http://localhost:8080/actuator/health

### Docker Setup

1. **Using Docker Compose**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

2. **Environment Configuration**
Create a `.env` file:
```env
JWT_SECRET=your-super-secret-jwt-key-here
POSTGRES_DB=ui_platform
POSTGRES_USER=ui_platform_user
POSTGRES_PASSWORD=ui_platform_password
REDIS_PASSWORD=your-redis-password
```

## 📚 API Documentation

### REST API
The REST API is fully documented using OpenAPI 3.0. Access the interactive documentation at:
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs

### GraphQL API
GraphQL schema and playground available at:
- **GraphQL Endpoint**: http://localhost:8080/graphql
- **GraphQL Playground**: http://localhost:8080/graphiql

### Key Endpoints

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `POST /api/v1/auth/logout` - User logout

#### Organizations
- `GET /api/v1/organizations` - List organizations
- `POST /api/v1/organizations` - Create organization
- `GET /api/v1/organizations/{id}` - Get organization
- `PUT /api/v1/organizations/{id}` - Update organization

#### UI Configurations
- `GET /api/v1/ui-configurations` - List UI configurations
- `POST /api/v1/ui-configurations` - Create UI configuration
- `GET /api/v1/ui-configurations/{id}` - Get UI configuration
- `PUT /api/v1/ui-configurations/{id}` - Update UI configuration

#### Templates
- `GET /api/v1/templates/marketplace` - Browse marketplace
- `GET /api/v1/templates/search` - Search templates
- `POST /api/v1/templates` - Create template
- `GET /api/v1/templates/{id}` - Get template

## 🔧 Configuration

### Application Properties
Key configuration options in `application.yml`:

```yaml
app:
  jwt:
    secret: ${JWT_SECRET}
    access-token-expiration-ms: 86400000  # 24 hours
    refresh-token-expiration-ms: 604800000  # 7 days
  
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000}
  
  file-upload:
    max-file-size: 10MB
    upload-dir: ./uploads
```

### Environment Variables
- `JWT_SECRET` - Secret key for JWT token signing
- `DATABASE_URL` - PostgreSQL connection URL
- `REDIS_URL` - Redis connection URL
- `CORS_ALLOWED_ORIGINS` - Allowed CORS origins
- `EMAIL_ENABLED` - Enable email functionality
- `UPLOAD_DIR` - File upload directory

## 🧪 Testing

### Running Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=OrganizationServiceTest

# Run with coverage
mvn test jacoco:report
```

### Test Categories
- **Unit Tests** - Service and component testing
- **Integration Tests** - API endpoint testing
- **Security Tests** - Authentication and authorization testing

## 🚀 Deployment

### Production Deployment

1. **Build Production JAR**
```bash
mvn clean package -Pprod
```

2. **Docker Production Build**
```bash
docker build -t ui-platform-backend:latest .
```

3. **Environment Setup**
- Configure production database
- Set up Redis cluster
- Configure load balancer
- Set environment variables

### Health Checks
- **Application Health**: `/actuator/health`
- **Database Health**: `/actuator/health/db`
- **Redis Health**: `/actuator/health/redis`

### Monitoring
- **Metrics**: `/actuator/metrics`
- **Prometheus**: `/actuator/prometheus`
- **Info**: `/actuator/info`

## 🔒 Security

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Permission-based authorization
- Multi-tenant security isolation

### Security Features
- Password encryption with BCrypt
- JWT token expiration and refresh
- CORS configuration
- Rate limiting
- Input validation and sanitization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Spring Boot best practices
- Write comprehensive tests
- Update documentation
- Follow code style guidelines
- Use conventional commits

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API documentation

## 🗺️ Roadmap

### Phase 1 (Current) ✅
- Core backend infrastructure
- Authentication and authorization
- Basic CRUD operations
- API documentation

### Phase 2 (Next)
- Real-time collaboration features
- Advanced component library
- Template marketplace enhancements
- Performance optimizations

### Phase 3 (Future)
- AI-powered UI suggestions
- Advanced analytics
- Plugin system
- Mobile app support
