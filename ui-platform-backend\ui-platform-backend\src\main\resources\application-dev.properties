# Development Environment Configuration

# Database Configuration
spring.datasource.url=***************************************************
spring.datasource.username=ui_platform_dev_user
spring.datasource.password=ui_platform_dev_pass

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.com.uiplatform=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Security Configuration (Development only - use stronger secrets in production)
app.jwt.secret=devSecretKey123456789012345678901234567890123456789012345678901234567890
app.jwt.expiration=86400000

# CORS Configuration for Development
app.cors.allowed-origins=http://localhost:3000,http://localhost:4200,http://localhost:8080

# GraphQL Configuration
spring.graphql.graphiql.enabled=true

# Actuator Configuration
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# Cache Configuration
spring.cache.redis.time-to-live=60000

# Development specific settings
app.dev.mock-external-services=true
app.dev.enable-test-data=true
