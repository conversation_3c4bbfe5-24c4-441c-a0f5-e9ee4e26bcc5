@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Global styles */
* {
  box-sizing: border-box;
}

html {
  font-family: 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f8fafc;
  color: #1f2937;
  line-height: 1.6;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Ant Design customizations */
.ant-layout {
  background: transparent !important;
}

.ant-layout-sider {
  background: #ffffff !important;
  border-right: 1px solid #e2e8f0 !important;
}

.ant-menu {
  background: transparent !important;
  border: none !important;
}

.ant-menu-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  width: calc(100% - 16px) !important;
}

.ant-menu-item-selected {
  background-color: #eff6ff !important;
  color: #2563eb !important;
}

.ant-menu-item:hover {
  background-color: #f8fafc !important;
}

/* Canvas specific styles */
.ui-builder-canvas {
  background: #ffffff;
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  position: relative;
  overflow: auto;
}

.ui-builder-canvas.grid-hidden {
  background-image: none;
}

/* Component drag preview */
.component-drag-preview {
  opacity: 0.8;
  transform: rotate(5deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border: 2px dashed #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Drop zone styles */
.drop-zone {
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.2s ease;
}

.drop-zone.active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.drop-zone.invalid {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

/* Selection styles */
.component-selected {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.component-hover {
  outline: 1px solid #93c5fd;
  outline-offset: 1px;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  background: #3b82f6;
  border: 2px solid #ffffff;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  z-index: 10;
}

.resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }
.resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

/* Collaboration styles */
.user-cursor {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.1s ease;
}

.user-cursor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 8px solid currentColor;
  border-right: 8px solid transparent;
  border-bottom: 12px solid currentColor;
  border-top: 8px solid transparent;
}

.user-cursor-label {
  position: absolute;
  top: 20px;
  left: 8px;
  background: currentColor;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .ui-builder-canvas {
    background-size: 15px 15px;
  }
  
  .resize-handle {
    width: 12px;
    height: 12px;
  }
  
  .resize-handle.nw { top: -6px; left: -6px; }
  .resize-handle.ne { top: -6px; right: -6px; }
  .resize-handle.sw { bottom: -6px; left: -6px; }
  .resize-handle.se { bottom: -6px; right: -6px; }
  .resize-handle.n { top: -6px; }
  .resize-handle.s { bottom: -6px; }
  .resize-handle.w { left: -6px; }
  .resize-handle.e { right: -6px; }
}

/* Print styles */
@media print {
  .ui-builder-canvas {
    background: white !important;
  }
  
  .resize-handle,
  .user-cursor,
  .component-selected,
  .component-hover {
    display: none !important;
  }
}
