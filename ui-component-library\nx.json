{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "ui-builder", "affected": {"defaultBase": "main"}, "cli": {"packageManager": "npm"}, "implicitDependencies": {"package.json": {"dependencies": "*", "devDependencies": "*"}, ".eslintrc.json": "*"}, "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"]}}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)"], "sharedGlobals": []}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint", "unitTestRunner": "jest"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}, "defaultProject": "design-tokens"}