import 'package:flutter/material.dart';

/// UI Builder typography system
class UIBuilderTypography {
  const UIBuilderTypography._();

  /// Base font family
  static const String fontFamily = 'Inter';

  /// Monospace font family
  static const String monospaceFontFamily = 'JetBrainsMono';

  /// Text theme for the UI Builder component library
  static const TextTheme textTheme = TextTheme(
    // Display styles
    displayLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      height: 1.12,
    ),
    displayMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.16,
    ),
    displaySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.22,
    ),

    // Headline styles
    headlineLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.29,
    ),
    headlineSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.33,
    ),

    // Title styles
    titleLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.27,
    ),
    titleMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      height: 1.5,
    ),
    titleSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),

    // Body styles
    bodyLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      height: 1.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.33,
    ),

    // Label styles
    labelLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    labelMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.33,
    ),
    labelSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.45,
    ),
  );

  /// Monospace text styles for code
  static const TextTheme monospaceTextTheme = TextTheme(
    bodyLarge: TextStyle(
      fontFamily: monospaceFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: monospaceFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontFamily: monospaceFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.33,
    ),
  );

  /// Get text style by name and size
  static TextStyle? getTextStyle(String styleName, {String? size}) {
    final normalizedName = styleName.toLowerCase();
    final normalizedSize = size?.toLowerCase() ?? 'medium';

    switch (normalizedName) {
      case 'display':
        switch (normalizedSize) {
          case 'large': return textTheme.displayLarge;
          case 'medium': return textTheme.displayMedium;
          case 'small': return textTheme.displaySmall;
          default: return textTheme.displayMedium;
        }
      case 'headline':
        switch (normalizedSize) {
          case 'large': return textTheme.headlineLarge;
          case 'medium': return textTheme.headlineMedium;
          case 'small': return textTheme.headlineSmall;
          default: return textTheme.headlineMedium;
        }
      case 'title':
        switch (normalizedSize) {
          case 'large': return textTheme.titleLarge;
          case 'medium': return textTheme.titleMedium;
          case 'small': return textTheme.titleSmall;
          default: return textTheme.titleMedium;
        }
      case 'body':
        switch (normalizedSize) {
          case 'large': return textTheme.bodyLarge;
          case 'medium': return textTheme.bodyMedium;
          case 'small': return textTheme.bodySmall;
          default: return textTheme.bodyMedium;
        }
      case 'label':
        switch (normalizedSize) {
          case 'large': return textTheme.labelLarge;
          case 'medium': return textTheme.labelMedium;
          case 'small': return textTheme.labelSmall;
          default: return textTheme.labelMedium;
        }
      case 'code':
      case 'monospace':
        switch (normalizedSize) {
          case 'large': return monospaceTextTheme.bodyLarge;
          case 'medium': return monospaceTextTheme.bodyMedium;
          case 'small': return monospaceTextTheme.bodySmall;
          default: return monospaceTextTheme.bodyMedium;
        }
      default:
        return textTheme.bodyMedium;
    }
  }

  /// Font weight utilities
  static FontWeight getFontWeight(String weight) {
    switch (weight.toLowerCase()) {
      case 'thin': return FontWeight.w100;
      case 'extralight': return FontWeight.w200;
      case 'light': return FontWeight.w300;
      case 'normal': case 'regular': return FontWeight.w400;
      case 'medium': return FontWeight.w500;
      case 'semibold': return FontWeight.w600;
      case 'bold': return FontWeight.w700;
      case 'extrabold': return FontWeight.w800;
      case 'black': return FontWeight.w900;
      default: return FontWeight.w400;
    }
  }
}
