import { RuntimeEngine } from '../../core/RuntimeEngine';
import { createMockConfiguration, createMockComponent } from '../setup';
import { mockConfiguration } from '../mocks/server';

describe('RuntimeEngine', () => {
  let engine: RuntimeEngine;

  beforeEach(() => {
    engine = new RuntimeEngine();
  });

  afterEach(() => {
    engine.cleanup();
  });

  describe('initialization', () => {
    it('should initialize with default configuration', () => {
      expect(engine.isInitialized()).toBe(false);
      expect(engine.getCurrentConfiguration()).toBeNull();
    });

    it('should initialize with provided configuration', async () => {
      const config = createMockConfiguration();
      
      await engine.initialize(config);
      
      expect(engine.isInitialized()).toBe(true);
      expect(engine.getCurrentConfiguration()).toEqual(config);
    });

    it('should handle initialization errors gracefully', async () => {
      const invalidConfig = { ...createMockConfiguration(), components: null };
      
      await expect(engine.initialize(invalidConfig as any)).rejects.toThrow();
      expect(engine.isInitialized()).toBe(false);
    });
  });

  describe('configuration management', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should load configuration successfully', async () => {
      const newConfig = createMockConfiguration({ id: 'new-config' });
      
      await engine.loadConfiguration(newConfig);
      
      expect(engine.getCurrentConfiguration()?.id).toBe('new-config');
    });

    it('should update configuration', async () => {
      const updates = { name: 'Updated Configuration' };
      
      await engine.updateConfiguration(updates);
      
      expect(engine.getCurrentConfiguration()?.name).toBe('Updated Configuration');
    });

    it('should validate configuration before loading', async () => {
      const invalidConfig = { id: 'invalid' }; // Missing required fields
      
      await expect(engine.loadConfiguration(invalidConfig as any)).rejects.toThrow();
    });

    it('should emit configuration change events', async () => {
      const listener = jest.fn();
      engine.on('configurationChanged', listener);
      
      const newConfig = createMockConfiguration({ id: 'new-config' });
      await engine.loadConfiguration(newConfig);
      
      expect(listener).toHaveBeenCalledWith(newConfig);
    });
  });

  describe('component management', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should render components', () => {
      const component = createMockComponent();
      
      const result = engine.renderComponent(component);
      
      expect(result).toBeDefined();
      expect(result.type).toBe('text');
    });

    it('should handle unknown component types', () => {
      const unknownComponent = createMockComponent({ type: 'unknown' });
      
      const result = engine.renderComponent(unknownComponent);
      
      expect(result).toBeDefined();
      expect(result.type).toBe('div'); // Fallback
    });

    it('should apply component styles', () => {
      const component = createMockComponent({
        style: {
          color: 'red',
          fontSize: '18px',
        },
      });
      
      const result = engine.renderComponent(component);
      
      expect(result.props.style).toMatchObject({
        color: 'red',
        fontSize: '18px',
      });
    });

    it('should handle component errors gracefully', () => {
      const errorComponent = createMockComponent({
        properties: { text: null }, // Invalid property
      });
      
      expect(() => engine.renderComponent(errorComponent)).not.toThrow();
    });
  });

  describe('theme management', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should apply theme to components', () => {
      const theme = {
        colors: { primary: '#ff0000' },
        typography: { fontSize: { base: '20px' } },
      };
      
      engine.setTheme(theme);
      
      expect(engine.getTheme()).toEqual(theme);
    });

    it('should merge theme with existing theme', () => {
      const existingTheme = engine.getTheme();
      const newTheme = { colors: { primary: '#ff0000' } };
      
      engine.setTheme(newTheme);
      
      expect(engine.getTheme()).toMatchObject({
        ...existingTheme,
        colors: {
          ...existingTheme?.colors,
          primary: '#ff0000',
        },
      });
    });

    it('should emit theme change events', () => {
      const listener = jest.fn();
      engine.on('themeChanged', listener);
      
      const theme = { colors: { primary: '#ff0000' } };
      engine.setTheme(theme);
      
      expect(listener).toHaveBeenCalledWith(expect.objectContaining(theme));
    });
  });

  describe('state management', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should manage component state', () => {
      engine.setState('testKey', 'testValue');
      
      expect(engine.getState('testKey')).toBe('testValue');
    });

    it('should update state', () => {
      engine.setState('counter', 0);
      engine.updateState('counter', (current: number) => current + 1);
      
      expect(engine.getState('counter')).toBe(1);
    });

    it('should emit state change events', () => {
      const listener = jest.fn();
      engine.on('stateChanged', listener);
      
      engine.setState('testKey', 'testValue');
      
      expect(listener).toHaveBeenCalledWith({
        key: 'testKey',
        value: 'testValue',
        previousValue: undefined,
      });
    });

    it('should handle state persistence', () => {
      engine.setState('persistentKey', 'persistentValue', { persist: true });
      
      // Simulate page reload
      const newEngine = new RuntimeEngine();
      newEngine.initialize(createMockConfiguration());
      
      expect(newEngine.getState('persistentKey')).toBe('persistentValue');
    });
  });

  describe('action handling', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should execute actions', async () => {
      const action = {
        type: 'setState',
        params: { key: 'actionTest', value: 'actionValue' },
      };
      
      await engine.executeAction(action);
      
      expect(engine.getState('actionTest')).toBe('actionValue');
    });

    it('should handle action errors', async () => {
      const invalidAction = {
        type: 'unknownAction',
        params: {},
      };
      
      await expect(engine.executeAction(invalidAction)).rejects.toThrow();
    });

    it('should emit action events', async () => {
      const listener = jest.fn();
      engine.on('actionExecuted', listener);
      
      const action = {
        type: 'setState',
        params: { key: 'test', value: 'test' },
      };
      
      await engine.executeAction(action);
      
      expect(listener).toHaveBeenCalledWith(action);
    });
  });

  describe('performance monitoring', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should track render performance', () => {
      const component = createMockComponent();
      
      engine.renderComponent(component);
      
      const metrics = engine.getPerformanceMetrics();
      expect(metrics.renderTime).toBeGreaterThan(0);
      expect(metrics.componentCount).toBe(1);
    });

    it('should track memory usage', () => {
      const metrics = engine.getPerformanceMetrics();
      
      expect(metrics.memoryUsage).toBeGreaterThanOrEqual(0);
    });

    it('should emit performance warnings', () => {
      const listener = jest.fn();
      engine.on('performanceWarning', listener);
      
      // Simulate slow render
      jest.spyOn(performance, 'now')
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(100); // 100ms render time
      
      const component = createMockComponent();
      engine.renderComponent(component);
      
      expect(listener).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should handle component render errors', () => {
      const errorComponent = createMockComponent({
        type: 'error-component', // This will cause an error
      });
      
      expect(() => engine.renderComponent(errorComponent)).not.toThrow();
      
      const errors = engine.getErrors();
      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe('render_error');
    });

    it('should handle action execution errors', async () => {
      const errorAction = {
        type: 'error-action',
        params: {},
      };
      
      await expect(engine.executeAction(errorAction)).rejects.toThrow();
      
      const errors = engine.getErrors();
      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe('action_error');
    });

    it('should emit error events', () => {
      const listener = jest.fn();
      engine.on('error', listener);
      
      const errorComponent = createMockComponent({ type: 'error-component' });
      engine.renderComponent(errorComponent);
      
      expect(listener).toHaveBeenCalled();
    });

    it('should clear errors', () => {
      const errorComponent = createMockComponent({ type: 'error-component' });
      engine.renderComponent(errorComponent);
      
      expect(engine.getErrors()).toHaveLength(1);
      
      engine.clearErrors();
      
      expect(engine.getErrors()).toHaveLength(0);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', async () => {
      await engine.initialize(createMockConfiguration());
      
      const listener = jest.fn();
      engine.on('configurationChanged', listener);
      
      engine.cleanup();
      
      expect(engine.isInitialized()).toBe(false);
      expect(engine.getCurrentConfiguration()).toBeNull();
      
      // Should not emit events after cleanup
      await engine.loadConfiguration(createMockConfiguration());
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('real-time updates', () => {
    beforeEach(async () => {
      await engine.initialize(createMockConfiguration());
    });

    it('should handle real-time configuration updates', () => {
      const listener = jest.fn();
      engine.on('configurationChanged', listener);
      
      const update = { name: 'Real-time Update' };
      engine.handleRealtimeUpdate('configuration', update);
      
      expect(listener).toHaveBeenCalled();
      expect(engine.getCurrentConfiguration()?.name).toBe('Real-time Update');
    });

    it('should handle real-time component updates', () => {
      const listener = jest.fn();
      engine.on('componentChanged', listener);
      
      const componentUpdate = {
        id: 'comp-1',
        properties: { text: 'Updated Text' },
      };
      
      engine.handleRealtimeUpdate('component', componentUpdate);
      
      expect(listener).toHaveBeenCalledWith(componentUpdate);
    });

    it('should handle real-time theme updates', () => {
      const listener = jest.fn();
      engine.on('themeChanged', listener);
      
      const themeUpdate = {
        colors: { primary: '#00ff00' },
      };
      
      engine.handleRealtimeUpdate('theme', themeUpdate);
      
      expect(listener).toHaveBeenCalled();
      expect(engine.getTheme()?.colors?.primary).toBe('#00ff00');
    });
  });
});
