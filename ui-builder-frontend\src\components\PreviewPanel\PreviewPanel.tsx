import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Card, Select, Button, Tooltip, Space, Slider, Switch, Dropdown, Menu } from 'antd';
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  BugOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { DynamicRenderer } from '../DynamicRenderer/DynamicRenderer';
import { setPreviewDevice, setPreviewZoom, setPreviewMode } from '../../store/slices/previewSlice';
import { useRealTimeSync } from '../../hooks/useRealTimeSync';
import { useAnalytics } from '../../hooks/useAnalytics';
import './PreviewPanel.scss';

const { Option } = Select;

export interface PreviewPanelProps {
  className?: string;
  showControls?: boolean;
  allowFullscreen?: boolean;
  enableDeviceSimulation?: boolean;
  enableInteractiveMode?: boolean;
}

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
  category: 'mobile' | 'tablet' | 'desktop';
}

const DEVICE_PRESETS: DevicePreset[] = [
  // Mobile devices
  { name: 'iPhone 14', width: 390, height: 844, icon: <MobileOutlined />, category: 'mobile' },
  { name: 'iPhone 14 Plus', width: 428, height: 926, icon: <MobileOutlined />, category: 'mobile' },
  { name: 'Samsung Galaxy S23', width: 360, height: 780, icon: <MobileOutlined />, category: 'mobile' },
  { name: 'Google Pixel 7', width: 412, height: 915, icon: <MobileOutlined />, category: 'mobile' },
  
  // Tablet devices
  { name: 'iPad Air', width: 820, height: 1180, icon: <TabletOutlined />, category: 'tablet' },
  { name: 'iPad Pro 11"', width: 834, height: 1194, icon: <TabletOutlined />, category: 'tablet' },
  { name: 'iPad Pro 12.9"', width: 1024, height: 1366, icon: <TabletOutlined />, category: 'tablet' },
  { name: 'Surface Pro', width: 912, height: 1368, icon: <TabletOutlined />, category: 'tablet' },
  
  // Desktop devices
  { name: 'MacBook Air', width: 1280, height: 832, icon: <DesktopOutlined />, category: 'desktop' },
  { name: 'MacBook Pro 14"', width: 1512, height: 982, icon: <DesktopOutlined />, category: 'desktop' },
  { name: 'MacBook Pro 16"', width: 1728, height: 1117, icon: <DesktopOutlined />, category: 'desktop' },
  { name: 'Desktop 1080p', width: 1920, height: 1080, icon: <DesktopOutlined />, category: 'desktop' },
  { name: 'Desktop 1440p', width: 2560, height: 1440, icon: <DesktopOutlined />, category: 'desktop' },
  { name: 'Desktop 4K', width: 3840, height: 2160, icon: <DesktopOutlined />, category: 'desktop' }
];

export const PreviewPanel: React.FC<PreviewPanelProps> = ({
  className,
  showControls = true,
  allowFullscreen = true,
  enableDeviceSimulation = true,
  enableInteractiveMode = true
}) => {
  const dispatch = useDispatch();
  const { trackUserAction } = useAnalytics();
  
  const { currentConfig } = useSelector((state: RootState) => state.uiBuilder);
  const { device, zoom, mode, isFullscreen } = useSelector((state: RootState) => state.preview);
  
  const [isLoading, setIsLoading] = useState(false);
  const [showDeviceFrame, setShowDeviceFrame] = useState(true);
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [customDimensions, setCustomDimensions] = useState({ width: 1200, height: 800 });
  const [showRulers, setShowRulers] = useState(false);
  const [showGrid, setShowGrid] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  
  const previewRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  const { syncStatus } = useRealTimeSync({
    configId: currentConfig?.id || '',
    enabled: !!currentConfig?.id
  });

  // Get current device preset
  const currentDevice = useMemo(() => {
    return DEVICE_PRESETS.find(d => d.name === device) || DEVICE_PRESETS[0];
  }, [device]);

  // Calculate preview dimensions
  const previewDimensions = useMemo(() => {
    if (device === 'custom') {
      return customDimensions;
    }
    
    const devicePreset = currentDevice;
    if (orientation === 'landscape') {
      return {
        width: Math.max(devicePreset.width, devicePreset.height),
        height: Math.min(devicePreset.width, devicePreset.height)
      };
    }
    
    return {
      width: Math.min(devicePreset.width, devicePreset.height),
      height: Math.max(devicePreset.width, devicePreset.height)
    };
  }, [device, currentDevice, orientation, customDimensions]);

  // Handle device change
  const handleDeviceChange = useCallback((deviceName: string) => {
    dispatch(setPreviewDevice(deviceName));
    trackUserAction('preview_device_changed', { device: deviceName });
  }, [dispatch, trackUserAction]);

  // Handle zoom change
  const handleZoomChange = useCallback((newZoom: number) => {
    dispatch(setPreviewZoom(newZoom));
    trackUserAction('preview_zoom_changed', { zoom: newZoom });
  }, [dispatch, trackUserAction]);

  // Handle mode change
  const handleModeChange = useCallback((newMode: 'design' | 'preview' | 'interactive') => {
    dispatch(setPreviewMode(newMode));
    trackUserAction('preview_mode_changed', { mode: newMode });
  }, [dispatch, trackUserAction]);

  // Handle orientation toggle
  const handleOrientationToggle = useCallback(() => {
    const newOrientation = orientation === 'portrait' ? 'landscape' : 'portrait';
    setOrientation(newOrientation);
    trackUserAction('preview_orientation_changed', { orientation: newOrientation });
  }, [orientation, trackUserAction]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    if (!allowFullscreen) return;
    
    if (!isFullscreen && previewRef.current) {
      previewRef.current.requestFullscreen?.();
    } else if (document.fullscreenElement) {
      document.exitFullscreen?.();
    }
  }, [isFullscreen, allowFullscreen]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsLoading(true);
    
    // Simulate refresh delay
    setTimeout(() => {
      setIsLoading(false);
      if (iframeRef.current) {
        iframeRef.current.src = iframeRef.current.src;
      }
    }, 500);
    
    trackUserAction('preview_refreshed');
  }, [trackUserAction]);

  // Handle share
  const handleShare = useCallback(async () => {
    if (!currentConfig) return;
    
    try {
      const shareUrl = `${window.location.origin}/preview/${currentConfig.id}`;
      await navigator.clipboard.writeText(shareUrl);
      // Show success message
      trackUserAction('preview_shared', { configId: currentConfig.id });
    } catch (error) {
      console.error('Failed to copy share URL:', error);
    }
  }, [currentConfig, trackUserAction]);

  // Settings menu
  const settingsMenu = (
    <Menu>
      <Menu.Item key="device-frame">
        <Space>
          <Switch 
            size="small" 
            checked={showDeviceFrame} 
            onChange={setShowDeviceFrame} 
          />
          Show Device Frame
        </Space>
      </Menu.Item>
      <Menu.Item key="rulers">
        <Space>
          <Switch 
            size="small" 
            checked={showRulers} 
            onChange={setShowRulers} 
          />
          Show Rulers
        </Space>
      </Menu.Item>
      <Menu.Item key="grid">
        <Space>
          <Switch 
            size="small" 
            checked={showGrid} 
            onChange={setShowGrid} 
          />
          Show Grid
        </Space>
      </Menu.Item>
      <Menu.Item key="debug">
        <Space>
          <Switch 
            size="small" 
            checked={debugMode} 
            onChange={setDebugMode} 
          />
          Debug Mode
        </Space>
      </Menu.Item>
    </Menu>
  );

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      // Update fullscreen state in store if needed
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  if (!currentConfig) {
    return (
      <div className={`preview-panel preview-panel--empty ${className || ''}`}>
        <div className="preview-empty-state">
          <EyeOutlined className="preview-empty-icon" />
          <h3>No Configuration Selected</h3>
          <p>Select a UI configuration to see the preview</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={previewRef}
      className={`preview-panel ${className || ''} ${isFullscreen ? 'preview-panel--fullscreen' : ''}`}
    >
      {/* Controls */}
      {showControls && (
        <Card className="preview-controls" size="small">
          <Space split={<div className="control-divider" />}>
            {/* Device Selection */}
            {enableDeviceSimulation && (
              <Space>
                <Select
                  value={device}
                  onChange={handleDeviceChange}
                  style={{ width: 150 }}
                  size="small"
                >
                  <Option value="custom">Custom</Option>
                  {DEVICE_PRESETS.map(preset => (
                    <Option key={preset.name} value={preset.name}>
                      <Space>
                        {preset.icon}
                        {preset.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
                
                {device !== 'custom' && (
                  <Button
                    size="small"
                    onClick={handleOrientationToggle}
                    title={`Switch to ${orientation === 'portrait' ? 'landscape' : 'portrait'}`}
                  >
                    {orientation === 'portrait' ? '📱' : '📱↻'}
                  </Button>
                )}
              </Space>
            )}

            {/* Zoom Control */}
            <Space>
              <span>Zoom:</span>
              <Slider
                min={25}
                max={200}
                step={25}
                value={zoom}
                onChange={handleZoomChange}
                style={{ width: 100 }}
                tooltip={{ formatter: (value) => `${value}%` }}
              />
              <span>{zoom}%</span>
            </Space>

            {/* Mode Selection */}
            {enableInteractiveMode && (
              <Select
                value={mode}
                onChange={handleModeChange}
                size="small"
                style={{ width: 120 }}
              >
                <Option value="design">Design</Option>
                <Option value="preview">Preview</Option>
                <Option value="interactive">Interactive</Option>
              </Select>
            )}

            {/* Action Buttons */}
            <Space>
              <Tooltip title="Refresh Preview">
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={isLoading}
                />
              </Tooltip>
              
              <Tooltip title="Share Preview">
                <Button
                  size="small"
                  icon={<ShareAltOutlined />}
                  onClick={handleShare}
                />
              </Tooltip>
              
              <Dropdown overlay={settingsMenu} trigger={['click']}>
                <Button size="small" icon={<SettingOutlined />} />
              </Dropdown>
              
              {allowFullscreen && (
                <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}>
                  <Button
                    size="small"
                    icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                    onClick={handleFullscreenToggle}
                  />
                </Tooltip>
              )}
            </Space>
          </Space>
          
          {/* Sync Status */}
          {syncStatus.hasUnsavedChanges && (
            <div className="sync-status">
              <span className="sync-indicator">●</span>
              Unsaved changes
            </div>
          )}
        </Card>
      )}

      {/* Preview Area */}
      <div className="preview-area">
        {showRulers && (
          <>
            <div className="preview-ruler preview-ruler--horizontal" />
            <div className="preview-ruler preview-ruler--vertical" />
          </>
        )}
        
        <div 
          className={`preview-viewport ${showDeviceFrame ? 'preview-viewport--framed' : ''}`}
          style={{
            width: previewDimensions.width * (zoom / 100),
            height: previewDimensions.height * (zoom / 100),
            transform: `scale(${zoom / 100})`,
            transformOrigin: 'top left'
          }}
        >
          {showGrid && <div className="preview-grid" />}
          
          <div className="preview-content">
            <DynamicRenderer
              config={currentConfig.configData}
              mode={mode}
              debugMode={debugMode}
              interactive={mode === 'interactive'}
              deviceInfo={{
                width: previewDimensions.width,
                height: previewDimensions.height,
                category: currentDevice.category,
                orientation
              }}
            />
          </div>
          
          {debugMode && (
            <div className="preview-debug-overlay">
              <div className="debug-info">
                <div>Device: {device}</div>
                <div>Size: {previewDimensions.width}×{previewDimensions.height}</div>
                <div>Zoom: {zoom}%</div>
                <div>Mode: {mode}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
