import React, { createContext, useContext, useState, useEffect } from 'react';

// Supported locales
export type Locale = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ja' | 'ko' | 'zh' | 'ar' | 'he' | 'ru';

// Text direction
export type TextDirection = 'ltr' | 'rtl';

// Translation function type
export type TranslationFunction = (key: string, params?: Record<string, any>) => string;

// Locale configuration
export interface LocaleConfig {
  code: Locale;
  name: string;
  nativeName: string;
  direction: TextDirection;
  dateFormat: string;
  timeFormat: string;
  numberFormat: Intl.NumberFormatOptions;
  currencyFormat: Intl.NumberFormatOptions;
}

// Translation messages
export interface TranslationMessages {
  [key: string]: string | TranslationMessages;
}

// I18n context value
export interface I18nContextValue {
  locale: Locale;
  direction: TextDirection;
  messages: TranslationMessages;
  t: TranslationFunction;
  setLocale: (locale: Locale) => void;
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatTime: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
  formatCurrency: (amount: number, currency: string, options?: Intl.NumberFormatOptions) => string;
  formatRelativeTime: (value: number, unit: Intl.RelativeTimeFormatUnit) => string;
  isRTL: boolean;
}

// Locale configurations
export const localeConfigs: Record<Locale, LocaleConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: 'h:mm a',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'USD' },
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    direction: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'EUR' },
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    direction: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'EUR' },
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    direction: 'ltr',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'EUR' },
  },
  it: {
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    direction: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'EUR' },
  },
  pt: {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    direction: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'EUR' },
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    direction: 'ltr',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'JPY' },
  },
  ko: {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    direction: 'ltr',
    dateFormat: 'yyyy. MM. dd.',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'KRW' },
  },
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    direction: 'ltr',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'CNY' },
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    direction: 'rtl',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'SAR' },
  },
  he: {
    code: 'he',
    name: 'Hebrew',
    nativeName: 'עברית',
    direction: 'rtl',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'ILS' },
  },
  ru: {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    direction: 'ltr',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: { notation: 'standard' },
    currencyFormat: { style: 'currency', currency: 'RUB' },
  },
};

// Default messages (English)
const defaultMessages: TranslationMessages = {
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    remove: 'Remove',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
  },
  form: {
    required: 'This field is required',
    invalid: 'Invalid value',
    email: 'Please enter a valid email address',
    password: 'Password must be at least 8 characters',
    confirm: 'Passwords do not match',
    submit: 'Submit',
    reset: 'Reset',
  },
  navigation: {
    home: 'Home',
    about: 'About',
    contact: 'Contact',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    menu: 'Menu',
  },
  accessibility: {
    close: 'Close',
    menu: 'Menu',
    search: 'Search',
    loading: 'Loading',
    error: 'Error',
    required: 'Required',
    optional: 'Optional',
  },
};

const I18nContext = createContext<I18nContextValue | undefined>(undefined);

export interface I18nProviderProps {
  children: React.ReactNode;
  locale?: Locale;
  messages?: Record<Locale, TranslationMessages>;
  onLocaleChange?: (locale: Locale) => void;
  storageKey?: string;
}

export function I18nProvider({
  children,
  locale: initialLocale,
  messages = {},
  onLocaleChange,
  storageKey = 'ui-builder-locale',
}: I18nProviderProps) {
  const [locale, setLocaleState] = useState<Locale>(() => {
    if (initialLocale) return initialLocale;
    
    // Try to get from localStorage
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored && stored in localeConfigs) {
          return stored as Locale;
        }
      } catch (error) {
        console.warn('Failed to load locale from storage:', error);
      }
    }
    
    // Detect from browser
    if (typeof navigator !== 'undefined') {
      const browserLocale = navigator.language.split('-')[0] as Locale;
      if (browserLocale in localeConfigs) {
        return browserLocale;
      }
    }
    
    return 'en';
  });

  const config = localeConfigs[locale];
  const currentMessages = { ...defaultMessages, ...messages[locale] };

  // Save locale to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, locale);
      } catch (error) {
        console.warn('Failed to save locale to storage:', error);
      }
    }
  }, [locale, storageKey]);

  // Apply direction to document
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.dir = config.direction;
      document.documentElement.lang = locale;
    }
  }, [locale, config.direction]);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    onLocaleChange?.(newLocale);
  };

  // Translation function
  const t: TranslationFunction = (key: string, params = {}) => {
    const keys = key.split('.');
    let value: any = currentMessages;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string: ${key}`);
      return key;
    }
    
    // Replace parameters
    return value.replace(/\{\{(\w+)\}\}/g, (match, param) => {
      return params[param] || match;
    });
  };

  // Formatting functions
  const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    return new Intl.DateTimeFormat(locale, options).format(date);
  };

  const formatTime = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    return new Intl.DateTimeFormat(locale, {
      timeStyle: 'short',
      ...options,
    }).format(date);
  };

  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(locale, {
      ...config.numberFormat,
      ...options,
    }).format(number);
  };

  const formatCurrency = (
    amount: number,
    currency: string,
    options?: Intl.NumberFormatOptions
  ) => {
    return new Intl.NumberFormat(locale, {
      ...config.currencyFormat,
      currency,
      ...options,
    }).format(amount);
  };

  const formatRelativeTime = (value: number, unit: Intl.RelativeTimeFormatUnit) => {
    return new Intl.RelativeTimeFormat(locale, { numeric: 'auto' }).format(value, unit);
  };

  const value: I18nContextValue = {
    locale,
    direction: config.direction,
    messages: currentMessages,
    t,
    setLocale,
    formatDate,
    formatTime,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
    isRTL: config.direction === 'rtl',
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// Hook for translation only
export function useTranslation() {
  const { t } = useI18n();
  return { t };
}

// Hook for formatting
export function useFormatting() {
  const {
    formatDate,
    formatTime,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
  } = useI18n();
  
  return {
    formatDate,
    formatTime,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
  };
}

// Hook for locale information
export function useLocale() {
  const { locale, direction, isRTL, setLocale } = useI18n();
  
  return {
    locale,
    direction,
    isRTL,
    setLocale,
    config: localeConfigs[locale],
  };
}
