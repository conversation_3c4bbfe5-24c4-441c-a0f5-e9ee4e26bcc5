import { ComponentMetadata, ActionContext } from '../types/runtime';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  organizationId?: string;
  metadata?: Record<string, any>;
}

export interface AuthConfig {
  apiUrl: string;
  tokenKey?: string;
  refreshTokenKey?: string;
  autoRefresh?: boolean;
  redirectUrl?: string;
  loginUrl?: string;
  logoutUrl?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  token: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

/**
 * Authentication Integration for Web Runtime
 * 
 * Provides authentication services including login, logout, token management,
 * user session handling, and permission-based access control.
 */
export class AuthenticationIntegration {
  private config: AuthConfig;
  private state: AuthState;
  private listeners: Set<(state: AuthState) => void> = new Set();
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor(config: AuthConfig) {
    this.config = {
      tokenKey: 'auth_token',
      refreshTokenKey: 'refresh_token',
      autoRefresh: true,
      ...config,
    };

    this.state = {
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      loading: false,
      error: null,
    };

    this.initializeFromStorage();
  }

  /**
   * Initialize authentication state from storage
   */
  private initializeFromStorage(): void {
    try {
      const token = localStorage.getItem(this.config.tokenKey!);
      const refreshToken = localStorage.getItem(this.config.refreshTokenKey!);

      if (token) {
        this.state.token = token;
        this.state.refreshToken = refreshToken;
        
        // Validate token and get user info
        this.validateToken().catch(() => {
          this.logout();
        });
      }
    } catch (error) {
      console.error('Failed to initialize auth from storage:', error);
    }
  }

  /**
   * Login with credentials
   */
  async login(credentials: LoginCredentials): Promise<AuthUser> {
    this.setState({ loading: true, error: null });

    try {
      const response = await fetch(`${this.config.apiUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const authResponse: AuthResponse = await response.json();
      
      this.setAuthData(authResponse);
      this.setState({
        isAuthenticated: true,
        user: authResponse.user,
        loading: false,
        error: null,
      });

      // Setup auto-refresh if enabled
      if (this.config.autoRefresh && authResponse.expiresIn) {
        this.setupTokenRefresh(authResponse.expiresIn);
      }

      return authResponse.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      this.setState({
        loading: false,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    this.setState({ loading: true });

    try {
      // Call logout endpoint if configured
      if (this.config.logoutUrl && this.state.token) {
        await fetch(`${this.config.apiUrl}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.state.token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearAuthData();
      this.setState({
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null,
        loading: false,
        error: null,
      });

      // Clear refresh timer
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }

      // Redirect if configured
      if (this.config.redirectUrl) {
        window.location.href = this.config.redirectUrl;
      }
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<void> {
    if (!this.state.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${this.config.apiUrl}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: this.state.refreshToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const authResponse: AuthResponse = await response.json();
      
      this.setAuthData(authResponse);
      this.setState({
        user: authResponse.user,
        token: authResponse.token,
        refreshToken: authResponse.refreshToken || this.state.refreshToken,
      });

      // Setup next refresh
      if (this.config.autoRefresh && authResponse.expiresIn) {
        this.setupTokenRefresh(authResponse.expiresIn);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.logout();
      throw error;
    }
  }

  /**
   * Validate current token
   */
  async validateToken(): Promise<AuthUser> {
    if (!this.state.token) {
      throw new Error('No token to validate');
    }

    try {
      const response = await fetch(`${this.config.apiUrl}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${this.state.token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Token validation failed');
      }

      const user: AuthUser = await response.json();
      
      this.setState({
        isAuthenticated: true,
        user,
        error: null,
      });

      return user;
    } catch (error) {
      this.setState({
        isAuthenticated: false,
        user: null,
        error: 'Token validation failed',
      });
      throw error;
    }
  }

  /**
   * Check if user has permission
   */
  hasPermission(permission: string): boolean {
    if (!this.state.user) return false;
    return this.state.user.permissions.includes(permission);
  }

  /**
   * Check if user has role
   */
  hasRole(role: string): boolean {
    if (!this.state.user) return false;
    return this.state.user.roles.includes(role);
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roles: string[]): boolean {
    if (!this.state.user) return false;
    return roles.some(role => this.state.user!.roles.includes(role));
  }

  /**
   * Check if user has all specified permissions
   */
  hasAllPermissions(permissions: string[]): boolean {
    if (!this.state.user) return false;
    return permissions.every(permission => this.state.user!.permissions.includes(permission));
  }

  /**
   * Get current authentication state
   */
  getState(): AuthState {
    return { ...this.state };
  }

  /**
   * Get current user
   */
  getCurrentUser(): AuthUser | null {
    return this.state.user;
  }

  /**
   * Get current token
   */
  getToken(): string | null {
    return this.state.token;
  }

  /**
   * Subscribe to authentication state changes
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Make authenticated API request
   */
  async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    if (!this.state.token) {
      throw new Error('No authentication token available');
    }

    const headers = {
      'Authorization': `Bearer ${this.state.token}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle token expiration
    if (response.status === 401) {
      if (this.config.autoRefresh && this.state.refreshToken) {
        try {
          await this.refreshToken();
          // Retry request with new token
          return this.authenticatedFetch(url, options);
        } catch (error) {
          this.logout();
          throw new Error('Authentication expired');
        }
      } else {
        this.logout();
        throw new Error('Authentication required');
      }
    }

    return response;
  }

  /**
   * Create authentication context for actions
   */
  createAuthContext(): Partial<ActionContext> {
    return {
      user: this.state.user,
      isAuthenticated: this.state.isAuthenticated,
      hasPermission: this.hasPermission.bind(this),
      hasRole: this.hasRole.bind(this),
      logout: this.logout.bind(this),
    };
  }

  // Private methods

  private setState(updates: Partial<AuthState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Auth state listener error:', error);
      }
    });
  }

  private setAuthData(authResponse: AuthResponse): void {
    try {
      localStorage.setItem(this.config.tokenKey!, authResponse.token);
      if (authResponse.refreshToken) {
        localStorage.setItem(this.config.refreshTokenKey!, authResponse.refreshToken);
      }
    } catch (error) {
      console.error('Failed to store auth data:', error);
    }

    this.state.token = authResponse.token;
    this.state.refreshToken = authResponse.refreshToken || this.state.refreshToken;
  }

  private clearAuthData(): void {
    try {
      localStorage.removeItem(this.config.tokenKey!);
      localStorage.removeItem(this.config.refreshTokenKey!);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  private setupTokenRefresh(expiresIn: number): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Refresh token 5 minutes before expiration
    const refreshTime = Math.max(0, (expiresIn - 300) * 1000);
    
    this.refreshTimer = setTimeout(() => {
      this.refreshToken().catch(error => {
        console.error('Auto token refresh failed:', error);
      });
    }, refreshTime);
  }
}

/**
 * Authentication Guard Component
 * 
 * Protects components based on authentication and permissions
 */
export class AuthGuard {
  constructor(private auth: AuthenticationIntegration) {}

  /**
   * Check if component should be rendered based on auth requirements
   */
  canRender(component: ComponentMetadata): boolean {
    const authRequirements = component.authRequirements;
    
    if (!authRequirements) {
      return true; // No auth requirements
    }

    // Check authentication
    if (authRequirements.requireAuth && !this.auth.getState().isAuthenticated) {
      return false;
    }

    // Check roles
    if (authRequirements.roles && authRequirements.roles.length > 0) {
      if (!this.auth.hasAnyRole(authRequirements.roles)) {
        return false;
      }
    }

    // Check permissions
    if (authRequirements.permissions && authRequirements.permissions.length > 0) {
      if (!this.auth.hasAllPermissions(authRequirements.permissions)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get fallback component for unauthorized access
   */
  getFallbackComponent(component: ComponentMetadata): ComponentMetadata | null {
    const authRequirements = component.authRequirements;
    
    if (authRequirements?.fallback) {
      return authRequirements.fallback;
    }

    // Default unauthorized component
    return {
      id: 'unauthorized',
      type: 'text',
      properties: {
        text: 'You do not have permission to view this content.',
        style: { color: '#ef4444', textAlign: 'center' },
      },
    };
  }
}

// Global authentication instance
let authInstance: AuthenticationIntegration | null = null;

/**
 * Initialize authentication system
 */
export function initializeAuth(config: AuthConfig): AuthenticationIntegration {
  authInstance = new AuthenticationIntegration(config);
  return authInstance;
}

/**
 * Get global authentication instance
 */
export function getAuth(): AuthenticationIntegration | null {
  return authInstance;
}

/**
 * Authentication hook for React components
 */
export function useAuth() {
  if (!authInstance) {
    throw new Error('Authentication not initialized');
  }

  const [state, setState] = React.useState(authInstance.getState());

  React.useEffect(() => {
    const unsubscribe = authInstance!.subscribe(setState);
    return unsubscribe;
  }, []);

  return {
    ...state,
    login: authInstance.login.bind(authInstance),
    logout: authInstance.logout.bind(authInstance),
    refreshToken: authInstance.refreshToken.bind(authInstance),
    hasPermission: authInstance.hasPermission.bind(authInstance),
    hasRole: authInstance.hasRole.bind(authInstance),
    authenticatedFetch: authInstance.authenticatedFetch.bind(authInstance),
  };
}
