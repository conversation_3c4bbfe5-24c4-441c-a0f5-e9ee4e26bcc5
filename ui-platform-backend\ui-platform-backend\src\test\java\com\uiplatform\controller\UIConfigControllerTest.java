package com.uiplatform.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.UIConfigDTO;
import com.uiplatform.dto.UIConfigCreateDTO;
import com.uiplatform.dto.UIConfigUpdateDTO;
import com.uiplatform.service.UIConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@WebMvcTest(UIConfigController.class)
class UIConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UIConfigService uiConfigService;

    @Autowired
    private ObjectMapper objectMapper;

    private UIConfigDTO sampleUIConfig;
    private UIConfigCreateDTO createDTO;
    private UIConfigUpdateDTO updateDTO;

    @BeforeEach
    void setUp() {
        sampleUIConfig = UIConfigDTO.builder()
                .id(UUID.randomUUID())
                .name("Sample Dashboard")
                .description("A sample dashboard configuration")
                .configData("{\"components\": []}")
                .version(1)
                .status("DRAFT")
                .isTemplate(false)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        createDTO = UIConfigCreateDTO.builder()
                .name("New Dashboard")
                .description("A new dashboard")
                .configData("{\"components\": []}")
                .workspaceId(UUID.randomUUID())
                .build();

        updateDTO = UIConfigUpdateDTO.builder()
                .name("Updated Dashboard")
                .description("An updated dashboard")
                .configData("{\"components\": [{\"type\": \"text\"}]}")
                .build();
    }

    @Test
    @WithMockUser(roles = "USER")
    void getAllUIConfigs_ShouldReturnPagedResults() throws Exception {
        // Given
        List<UIConfigDTO> configs = Arrays.asList(sampleUIConfig);
        Page<UIConfigDTO> page = new PageImpl<>(configs, PageRequest.of(0, 20), 1);
        
        when(uiConfigService.getAllUIConfigs(any(Pageable.class), anyString(), anyString()))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].name").value("Sample Dashboard"))
                .andExpect(jsonPath("$.totalElements").value(1))
                .andExpect(jsonPath("$.totalPages").value(1));

        verify(uiConfigService).getAllUIConfigs(any(Pageable.class), isNull(), isNull());
    }

    @Test
    @WithMockUser(roles = "USER")
    void getAllUIConfigs_WithFilters_ShouldReturnFilteredResults() throws Exception {
        // Given
        List<UIConfigDTO> configs = Arrays.asList(sampleUIConfig);
        Page<UIConfigDTO> page = new PageImpl<>(configs, PageRequest.of(0, 20), 1);
        
        when(uiConfigService.getAllUIConfigs(any(Pageable.class), eq("org-123"), eq("dashboard")))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs")
                        .param("organizationId", "org-123")
                        .param("search", "dashboard"))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].name").value("Sample Dashboard"));

        verify(uiConfigService).getAllUIConfigs(any(Pageable.class), eq("org-123"), eq("dashboard"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void getUIConfigById_ShouldReturnConfig() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        when(uiConfigService.getUIConfigById(configId.toString())).thenReturn(sampleUIConfig);

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs/{id}", configId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(configId.toString()))
                .andExpect(jsonPath("$.name").value("Sample Dashboard"))
                .andExpect(jsonPath("$.description").value("A sample dashboard configuration"));

        verify(uiConfigService).getUIConfigById(configId.toString());
    }

    @Test
    @WithMockUser(roles = "USER")
    void createUIConfig_ShouldReturnCreatedConfig() throws Exception {
        // Given
        when(uiConfigService.createUIConfig(any(UIConfigCreateDTO.class))).thenReturn(sampleUIConfig);

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDTO)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Sample Dashboard"))
                .andExpect(jsonPath("$.description").value("A sample dashboard configuration"));

        verify(uiConfigService).createUIConfig(any(UIConfigCreateDTO.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void createUIConfig_WithInvalidData_ShouldReturnBadRequest() throws Exception {
        // Given
        UIConfigCreateDTO invalidDTO = UIConfigCreateDTO.builder()
                .name("") // Invalid: empty name
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidDTO)))
                .andExpect(status().isBadRequest());

        verify(uiConfigService, never()).createUIConfig(any(UIConfigCreateDTO.class));
    }

    @Test
    @WithMockUser(roles = "USER")
    void updateUIConfig_ShouldReturnUpdatedConfig() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        UIConfigDTO updatedConfig = sampleUIConfig.toBuilder()
                .name("Updated Dashboard")
                .description("An updated dashboard")
                .build();
        
        when(uiConfigService.updateUIConfig(eq(configId.toString()), any(UIConfigUpdateDTO.class)))
                .thenReturn(updatedConfig);

        // When & Then
        mockMvc.perform(put("/api/v1/ui-configs/{id}", configId)
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Updated Dashboard"))
                .andExpect(jsonPath("$.description").value("An updated dashboard"));

        verify(uiConfigService).updateUIConfig(eq(configId.toString()), any(UIConfigUpdateDTO.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void deleteUIConfig_ShouldReturnNoContent() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        doNothing().when(uiConfigService).deleteUIConfig(configId.toString());

        // When & Then
        mockMvc.perform(delete("/api/v1/ui-configs/{id}", configId)
                        .with(csrf()))
                .andExpect(status().isNoContent());

        verify(uiConfigService).deleteUIConfig(configId.toString());
    }

    @Test
    @WithMockUser(roles = "USER")
    void deleteUIConfig_WithoutAdminRole_ShouldReturnForbidden() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();

        // When & Then
        mockMvc.perform(delete("/api/v1/ui-configs/{id}", configId)
                        .with(csrf()))
                .andExpect(status().isForbidden());

        verify(uiConfigService, never()).deleteUIConfig(anyString());
    }

    @Test
    @WithMockUser(roles = "USER")
    void duplicateUIConfig_ShouldReturnDuplicatedConfig() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        UIConfigDTO duplicatedConfig = sampleUIConfig.toBuilder()
                .id(UUID.randomUUID())
                .name("Sample Dashboard (Copy)")
                .build();
        
        when(uiConfigService.duplicateUIConfig(eq(configId.toString()), eq("Sample Dashboard (Copy)")))
                .thenReturn(duplicatedConfig);

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/{id}/duplicate", configId)
                        .with(csrf())
                        .param("newName", "Sample Dashboard (Copy)"))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Sample Dashboard (Copy)"))
                .andExpect(jsonPath("$.id").value(duplicatedConfig.getId().toString()));

        verify(uiConfigService).duplicateUIConfig(eq(configId.toString()), eq("Sample Dashboard (Copy)"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void publishUIConfig_ShouldReturnPublishedConfig() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        UIConfigDTO publishedConfig = sampleUIConfig.toBuilder()
                .status("PUBLISHED")
                .publishedAt(LocalDateTime.now())
                .build();
        
        when(uiConfigService.publishUIConfig(configId.toString())).thenReturn(publishedConfig);

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/{id}/publish", configId)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("PUBLISHED"))
                .andExpect(jsonPath("$.publishedAt").exists());

        verify(uiConfigService).publishUIConfig(configId.toString());
    }

    @Test
    @WithMockUser(roles = "USER")
    void exportUIConfig_ShouldReturnExportData() throws Exception {
        // Given
        UUID configId = sampleUIConfig.getId();
        String exportData = "{\"config\": \"exported data\"}";
        
        when(uiConfigService.exportUIConfig(configId.toString(), "json")).thenReturn(exportData);

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs/{id}/export", configId)
                        .param("format", "json"))
                .andExpect(status().isOk())
                .andExpect(content().string(exportData))
                .andExpect(header().string("Content-Disposition", 
                        "attachment; filename=ui-config-" + configId + ".json"));

        verify(uiConfigService).exportUIConfig(configId.toString(), "json");
    }

    @Test
    @WithMockUser(roles = "USER")
    void importUIConfig_ShouldReturnImportedConfig() throws Exception {
        // Given
        String importData = "{\"config\": \"imported data\"}";
        when(uiConfigService.importUIConfig(importData, "json")).thenReturn(sampleUIConfig);

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/import")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(importData)
                        .param("format", "json"))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Sample Dashboard"));

        verify(uiConfigService).importUIConfig(importData, "json");
    }

    @Test
    void getAllUIConfigs_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs"))
                .andExpect(status().isUnauthorized());

        verify(uiConfigService, never()).getAllUIConfigs(any(Pageable.class), anyString(), anyString());
    }
}
