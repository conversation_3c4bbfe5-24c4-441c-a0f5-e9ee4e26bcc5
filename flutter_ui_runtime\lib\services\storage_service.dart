import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// Offline Storage Service for Flutter UI Runtime
/// 
/// Provides local storage functionality using Hive for:
/// - UI configurations caching
/// - Theme preferences
/// - User data and settings
/// - Offline data synchronization
/// - Application state persistence

class StorageService {
  static const String configurationsBox = 'configurations';
  static const String themesBox = 'themes';
  static const String userDataBox = 'user_data';
  static const String settingsBox = 'settings';
  static const String cacheBox = 'cache';
  static const String offlineQueueBox = 'offline_queue';

  late Box<String> _configurationsBox;
  late Box<String> _themesBox;
  late Box<String> _userDataBox;
  late Box<String> _settingsBox;
  late Box<String> _cacheBox;
  late Box<String> _offlineQueueBox;

  bool _isInitialized = false;

  /// Initialize Hive and open all boxes
  Future<void> initialize() async {
    if (_isInitialized) return;

    await Hive.initFlutter();
    
    // Register adapters if needed
    _registerAdapters();

    // Open all boxes
    _configurationsBox = await Hive.openBox<String>(configurationsBox);
    _themesBox = await Hive.openBox<String>(themesBox);
    _userDataBox = await Hive.openBox<String>(userDataBox);
    _settingsBox = await Hive.openBox<String>(settingsBox);
    _cacheBox = await Hive.openBox<String>(cacheBox);
    _offlineQueueBox = await Hive.openBox<String>(offlineQueueBox);

    _isInitialized = true;
  }

  void _registerAdapters() {
    // Register custom type adapters if needed
    // Hive.registerAdapter(UIConfigurationAdapter());
  }

  /// Configuration Storage Methods
  
  /// Save UI configuration
  Future<void> saveConfiguration(UIConfiguration config) async {
    await _ensureInitialized();
    final json = jsonEncode(config.toJson());
    await _configurationsBox.put(config.id, json);
  }

  /// Get UI configuration by ID
  Future<UIConfiguration?> getConfiguration(String configId) async {
    await _ensureInitialized();
    final json = _configurationsBox.get(configId);
    if (json != null) {
      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        return UIConfiguration.fromJson(data);
      } catch (e) {
        print('Error parsing configuration: $e');
        return null;
      }
    }
    return null;
  }

  /// Get all stored configurations
  Future<List<UIConfiguration>> getAllConfigurations() async {
    await _ensureInitialized();
    final configurations = <UIConfiguration>[];
    
    for (final key in _configurationsBox.keys) {
      final config = await getConfiguration(key);
      if (config != null) {
        configurations.add(config);
      }
    }
    
    return configurations;
  }

  /// Delete configuration
  Future<void> deleteConfiguration(String configId) async {
    await _ensureInitialized();
    await _configurationsBox.delete(configId);
  }

  /// Check if configuration exists
  Future<bool> hasConfiguration(String configId) async {
    await _ensureInitialized();
    return _configurationsBox.containsKey(configId);
  }

  /// Theme Storage Methods
  
  /// Save theme configuration
  Future<void> saveTheme(String themeId, Map<String, dynamic> themeData) async {
    await _ensureInitialized();
    final json = jsonEncode(themeData);
    await _themesBox.put(themeId, json);
  }

  /// Get theme configuration
  Future<Map<String, dynamic>?> getTheme(String themeId) async {
    await _ensureInitialized();
    final json = _themesBox.get(themeId);
    if (json != null) {
      try {
        return jsonDecode(json) as Map<String, dynamic>;
      } catch (e) {
        print('Error parsing theme: $e');
        return null;
      }
    }
    return null;
  }

  /// Get all themes
  Future<Map<String, Map<String, dynamic>>> getAllThemes() async {
    await _ensureInitialized();
    final themes = <String, Map<String, dynamic>>{};
    
    for (final key in _themesBox.keys) {
      final theme = await getTheme(key);
      if (theme != null) {
        themes[key] = theme;
      }
    }
    
    return themes;
  }

  /// User Data Storage Methods
  
  /// Save user data
  Future<void> saveUserData(String key, Map<String, dynamic> data) async {
    await _ensureInitialized();
    final json = jsonEncode(data);
    await _userDataBox.put(key, json);
  }

  /// Get user data
  Future<Map<String, dynamic>?> getUserData(String key) async {
    await _ensureInitialized();
    final json = _userDataBox.get(key);
    if (json != null) {
      try {
        return jsonDecode(json) as Map<String, dynamic>;
      } catch (e) {
        print('Error parsing user data: $e');
        return null;
      }
    }
    return null;
  }

  /// Settings Storage Methods
  
  /// Save setting
  Future<void> saveSetting(String key, dynamic value) async {
    await _ensureInitialized();
    final json = jsonEncode(value);
    await _settingsBox.put(key, json);
  }

  /// Get setting
  Future<T?> getSetting<T>(String key) async {
    await _ensureInitialized();
    final json = _settingsBox.get(key);
    if (json != null) {
      try {
        return jsonDecode(json) as T;
      } catch (e) {
        print('Error parsing setting: $e');
        return null;
      }
    }
    return null;
  }

  /// Cache Storage Methods
  
  /// Save to cache with TTL
  Future<void> saveToCache(String key, dynamic data, {Duration? ttl}) async {
    await _ensureInitialized();
    final cacheData = CacheEntry(
      data: data,
      timestamp: DateTime.now(),
      ttl: ttl,
    );
    final json = jsonEncode(cacheData.toJson());
    await _cacheBox.put(key, json);
  }

  /// Get from cache
  Future<T?> getFromCache<T>(String key) async {
    await _ensureInitialized();
    final json = _cacheBox.get(key);
    if (json != null) {
      try {
        final cacheData = CacheEntry.fromJson(jsonDecode(json));
        if (!cacheData.isExpired) {
          return cacheData.data as T;
        } else {
          // Remove expired entry
          await _cacheBox.delete(key);
        }
      } catch (e) {
        print('Error parsing cache entry: $e');
      }
    }
    return null;
  }

  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    await _ensureInitialized();
    final keysToDelete = <String>[];
    
    for (final key in _cacheBox.keys) {
      final json = _cacheBox.get(key);
      if (json != null) {
        try {
          final cacheData = CacheEntry.fromJson(jsonDecode(json));
          if (cacheData.isExpired) {
            keysToDelete.add(key);
          }
        } catch (e) {
          // Invalid entry, mark for deletion
          keysToDelete.add(key);
        }
      }
    }
    
    for (final key in keysToDelete) {
      await _cacheBox.delete(key);
    }
  }

  /// Offline Queue Methods
  
  /// Add operation to offline queue
  Future<void> addToOfflineQueue(OfflineOperation operation) async {
    await _ensureInitialized();
    final json = jsonEncode(operation.toJson());
    await _offlineQueueBox.put(operation.id, json);
  }

  /// Get all offline operations
  Future<List<OfflineOperation>> getOfflineQueue() async {
    await _ensureInitialized();
    final operations = <OfflineOperation>[];
    
    for (final key in _offlineQueueBox.keys) {
      final json = _offlineQueueBox.get(key);
      if (json != null) {
        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          operations.add(OfflineOperation.fromJson(data));
        } catch (e) {
          print('Error parsing offline operation: $e');
        }
      }
    }
    
    // Sort by timestamp
    operations.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return operations;
  }

  /// Remove operation from offline queue
  Future<void> removeFromOfflineQueue(String operationId) async {
    await _ensureInitialized();
    await _offlineQueueBox.delete(operationId);
  }

  /// Clear offline queue
  Future<void> clearOfflineQueue() async {
    await _ensureInitialized();
    await _offlineQueueBox.clear();
  }

  /// Utility Methods
  
  /// Get storage statistics
  Future<StorageStats> getStorageStats() async {
    await _ensureInitialized();
    
    return StorageStats(
      configurationsCount: _configurationsBox.length,
      themesCount: _themesBox.length,
      userDataCount: _userDataBox.length,
      settingsCount: _settingsBox.length,
      cacheCount: _cacheBox.length,
      offlineQueueCount: _offlineQueueBox.length,
      totalSize: await _calculateTotalSize(),
    );
  }

  /// Clear all data
  Future<void> clearAllData() async {
    await _ensureInitialized();
    await Future.wait([
      _configurationsBox.clear(),
      _themesBox.clear(),
      _userDataBox.clear(),
      _settingsBox.clear(),
      _cacheBox.clear(),
      _offlineQueueBox.clear(),
    ]);
  }

  /// Export data for backup
  Future<Map<String, dynamic>> exportData() async {
    await _ensureInitialized();
    
    return {
      'configurations': _configurationsBox.toMap(),
      'themes': _themesBox.toMap(),
      'userData': _userDataBox.toMap(),
      'settings': _settingsBox.toMap(),
      'cache': _cacheBox.toMap(),
      'offlineQueue': _offlineQueueBox.toMap(),
      'exportTimestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Import data from backup
  Future<void> importData(Map<String, dynamic> data) async {
    await _ensureInitialized();
    
    try {
      if (data['configurations'] != null) {
        final configs = Map<String, String>.from(data['configurations']);
        await _configurationsBox.putAll(configs);
      }
      
      if (data['themes'] != null) {
        final themes = Map<String, String>.from(data['themes']);
        await _themesBox.putAll(themes);
      }
      
      if (data['userData'] != null) {
        final userData = Map<String, String>.from(data['userData']);
        await _userDataBox.putAll(userData);
      }
      
      if (data['settings'] != null) {
        final settings = Map<String, String>.from(data['settings']);
        await _settingsBox.putAll(settings);
      }
      
      // Don't import cache and offline queue as they're temporary
    } catch (e) {
      throw StorageException('Failed to import data: $e');
    }
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  Future<int> _calculateTotalSize() async {
    // This is an approximation
    int totalSize = 0;
    
    for (final box in [_configurationsBox, _themesBox, _userDataBox, _settingsBox, _cacheBox, _offlineQueueBox]) {
      for (final value in box.values) {
        totalSize += value.length * 2; // Rough estimate for UTF-16 encoding
      }
    }
    
    return totalSize;
  }

  /// Close all boxes
  Future<void> dispose() async {
    if (_isInitialized) {
      await Future.wait([
        _configurationsBox.close(),
        _themesBox.close(),
        _userDataBox.close(),
        _settingsBox.close(),
        _cacheBox.close(),
        _offlineQueueBox.close(),
      ]);
      _isInitialized = false;
    }
  }
}

/// Cache Entry model
class CacheEntry {
  final dynamic data;
  final DateTime timestamp;
  final Duration? ttl;

  CacheEntry({
    required this.data,
    required this.timestamp,
    this.ttl,
  });

  bool get isExpired {
    if (ttl == null) return false;
    return DateTime.now().difference(timestamp) > ttl!;
  }

  Map<String, dynamic> toJson() => {
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'ttl': ttl?.inMilliseconds,
  };

  factory CacheEntry.fromJson(Map<String, dynamic> json) => CacheEntry(
    data: json['data'],
    timestamp: DateTime.parse(json['timestamp']),
    ttl: json['ttl'] != null ? Duration(milliseconds: json['ttl']) : null,
  );
}

/// Offline Operation model
class OfflineOperation {
  final String id;
  final String type;
  final String method;
  final String endpoint;
  final Map<String, dynamic>? data;
  final Map<String, String>? headers;
  final DateTime timestamp;
  final int retryCount;

  OfflineOperation({
    required this.id,
    required this.type,
    required this.method,
    required this.endpoint,
    this.data,
    this.headers,
    required this.timestamp,
    this.retryCount = 0,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'method': method,
    'endpoint': endpoint,
    'data': data,
    'headers': headers,
    'timestamp': timestamp.toIso8601String(),
    'retryCount': retryCount,
  };

  factory OfflineOperation.fromJson(Map<String, dynamic> json) => OfflineOperation(
    id: json['id'],
    type: json['type'],
    method: json['method'],
    endpoint: json['endpoint'],
    data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
    headers: json['headers'] != null ? Map<String, String>.from(json['headers']) : null,
    timestamp: DateTime.parse(json['timestamp']),
    retryCount: json['retryCount'] ?? 0,
  );
}

/// Storage Statistics model
class StorageStats {
  final int configurationsCount;
  final int themesCount;
  final int userDataCount;
  final int settingsCount;
  final int cacheCount;
  final int offlineQueueCount;
  final int totalSize;

  StorageStats({
    required this.configurationsCount,
    required this.themesCount,
    required this.userDataCount,
    required this.settingsCount,
    required this.cacheCount,
    required this.offlineQueueCount,
    required this.totalSize,
  });
}

/// Storage Exception
class StorageException implements Exception {
  final String message;
  StorageException(this.message);
  
  @override
  String toString() => 'StorageException: $message';
}

/// Providers
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

final storageStatsProvider = FutureProvider<StorageStats>((ref) async {
  final storage = ref.read(storageServiceProvider);
  return storage.getStorageStats();
});

/// Helper extensions
extension StorageServiceExtension on WidgetRef {
  StorageService get storage => read(storageServiceProvider);
  
  Future<void> saveConfig(UIConfiguration config) {
    return storage.saveConfiguration(config);
  }
  
  Future<UIConfiguration?> getConfig(String configId) {
    return storage.getConfiguration(configId);
  }
  
  Future<void> saveSetting(String key, dynamic value) {
    return storage.saveSetting(key, value);
  }
  
  Future<T?> getSetting<T>(String key) {
    return storage.getSetting<T>(key);
  }
}
