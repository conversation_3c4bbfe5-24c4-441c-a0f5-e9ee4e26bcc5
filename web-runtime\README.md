# UI Builder Web Runtime

A lightweight, high-performance React application that dynamically renders UI configurations from JSON metadata. This is the end-user facing application that brings UI Builder configurations to life.

## 🚀 Features

### Core Capabilities
- **Dynamic Component Rendering**: Converts JSON metadata to functional React components
- **Real-time Updates**: Live UI updates via WebSocket connections
- **Theme System**: Dynamic theme application with CSS variables
- **Data Binding**: Connect components to live data sources
- **Responsive Design**: Automatic adaptation to different screen sizes
- **Performance Optimized**: Code splitting, lazy loading, and efficient re-rendering

### Advanced Features
- **PWA Support**: Offline functionality with service workers
- **Error Boundaries**: Graceful error handling and recovery
- **Performance Monitoring**: Real-time performance metrics and Core Web Vitals
- **Authentication**: JWT-based security integration
- **Collaboration**: Real-time cursor tracking and presence
- **Accessibility**: ARIA support and keyboard navigation

## 🏗️ Architecture

### Technology Stack
- **React 18** + **TypeScript** - Modern, type-safe foundation
- **Vite** - Lightning-fast build system
- **Zustand** - Lightweight state management
- **TanStack Query** - Powerful data fetching and caching
- **Headless UI** + **Tailwind CSS** - Accessible, customizable components
- **Socket.io** - Real-time communication
- **Framer Motion** - Smooth animations
- **Workbox** - PWA and offline support

### Core Systems

#### Component Registry
Maps JSON component types to React components with dynamic loading:
```typescript
componentRegistry.register({
  type: 'Button',
  component: ButtonComponent,
  props: [...],
  category: 'actions'
});
```

#### Dynamic Renderer
Converts JSON metadata to React components:
```typescript
<DynamicRenderer configuration={componentConfig} />
```

#### Theme Provider
Applies dynamic themes using CSS variables:
```typescript
<ThemeProvider defaultTheme={theme}>
  <App />
</ThemeProvider>
```

#### Data Binding
Connects components to live data:
```typescript
const resolvedProps = useDataBinding(props, bindings);
```

## 📁 Project Structure

```
web-runtime/
├── src/
│   ├── components/          # React components
│   │   ├── registry/        # Component registry system
│   │   ├── renderer/        # Dynamic rendering engine
│   │   ├── theme/           # Theme provider
│   │   ├── layout/          # Layout components
│   │   ├── display/         # Display components
│   │   ├── forms/           # Form components
│   │   ├── actions/         # Action components
│   │   └── ui/              # UI utilities
│   ├── stores/              # Zustand stores
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API services
│   ├── utils/               # Utility functions
│   ├── types/               # TypeScript definitions
│   └── styles/              # Global styles
├── public/                  # Static assets
└── dist/                    # Build output
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- npm 9+

### Installation
```bash
cd web-runtime
npm install
```

### Development
```bash
npm run dev
```
Starts development server at `http://localhost:3001`

### Building
```bash
npm run build
```

### Testing
```bash
npm run test          # Run tests
npm run test:ui       # Run tests with UI
npm run test:coverage # Run with coverage
```

## 🔧 Configuration

### Environment Variables
```env
VITE_API_URL=http://localhost:8080/api/v1
VITE_WS_URL=ws://localhost:8080/ws
```

### Vite Configuration
- PWA support with Workbox
- Code splitting and lazy loading
- Bundle analysis
- Proxy configuration for API calls

## 📊 Performance

### Optimization Features
- **Code Splitting**: Automatic route and component-based splitting
- **Lazy Loading**: Components loaded on demand
- **Bundle Analysis**: Built-in bundle size monitoring
- **Caching**: Intelligent API and asset caching
- **Performance Monitoring**: Real-time metrics collection

### Performance Metrics
- Render time tracking
- Memory usage monitoring
- Bundle size analysis
- Core Web Vitals (FCP, LCP, FID, CLS)

## 🔌 API Integration

### Fetching UI Metadata
```typescript
const metadata = await fetchUIMetadata(pageId, params);
```

### Real-time Updates
```typescript
const { connect, sendMessage } = useRealtimeConnection();
```

### Data Operations
```typescript
const data = await fetchData(source, params);
await updateData(source, id, updates);
```

## 🎨 Theming

### Dynamic Theme Application
```typescript
const theme = {
  colors: { primary: { 500: '#3b82f6' } },
  typography: { fontFamilies: { sans: 'Inter' } },
  spacing: { md: '1rem' },
  // ...
};

applyTheme(theme);
```

### CSS Variables
Themes are applied using CSS custom properties:
```css
:root {
  --color-primary-500: 59 130 246;
  --font-family-sans: Inter, sans-serif;
  --spacing-md: 1rem;
}
```

## 🔄 Real-time Features

### WebSocket Connection
```typescript
const connection = useRealtimeConnection({
  autoConnect: true,
  reconnectAttempts: 5
});
```

### Live Updates
- UI configuration changes
- Theme updates
- Data synchronization
- User presence and cursors

## 🧪 Testing

### Test Setup
- Vitest for unit testing
- React Testing Library for component testing
- Mock implementations for external dependencies
- Coverage reporting with v8

### Running Tests
```bash
npm run test                    # Run all tests
npm run test:watch             # Watch mode
npm run test:coverage          # With coverage
```

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### PWA Features
- Service worker for offline support
- App manifest for installation
- Background sync for data updates
- Push notifications (optional)

### Performance Optimization
- Automatic code splitting
- Asset optimization
- Gzip compression
- CDN-ready static assets

## 🔒 Security

### Authentication
- JWT token-based authentication
- Automatic token refresh
- Secure storage practices

### Data Protection
- Input sanitization
- XSS prevention
- CSRF protection
- Secure API communication

## 📱 Browser Support

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Progressive Enhancement
- Core functionality works without JavaScript
- Enhanced features with modern browser APIs
- Graceful degradation for older browsers

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run linting and tests
5. Submit a pull request

### Code Standards
- TypeScript for type safety
- ESLint for code quality
- Prettier for formatting
- Conventional commits

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:
- GitHub Issues for bug reports
- Documentation for guides
- Community Discord for discussions

---

Built with ❤️ for dynamic UI experiences
