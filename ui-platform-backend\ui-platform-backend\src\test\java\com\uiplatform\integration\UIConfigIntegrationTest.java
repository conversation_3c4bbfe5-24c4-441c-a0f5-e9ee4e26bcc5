package com.uiplatform.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.UIConfigCreateDTO;
import com.uiplatform.dto.UIConfigUpdateDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.entity.User;
import com.uiplatform.entity.Workspace;
import com.uiplatform.repository.OrganizationRepository;
import com.uiplatform.repository.UIConfigRepository;
import com.uiplatform.repository.UserRepository;
import com.uiplatform.repository.WorkspaceRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.flyway.enabled=false"
})
@Transactional
class UIConfigIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UIConfigRepository uiConfigRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private WorkspaceRepository workspaceRepository;

    private User testUser;
    private Organization testOrganization;
    private Workspace testWorkspace;

    @BeforeEach
    void setUp() {
        // Clean up repositories
        uiConfigRepository.deleteAll();
        workspaceRepository.deleteAll();
        organizationRepository.deleteAll();
        userRepository.deleteAll();

        // Create test data
        testOrganization = Organization.builder()
                .id(UUID.randomUUID())
                .name("Test Organization")
                .slug("test-org")
                .description("A test organization")
                .subscriptionPlan("pro")
                .subscriptionStatus("active")
                .build();
        testOrganization = organizationRepository.save(testOrganization);

        testUser = User.builder()
                .id(UUID.randomUUID())
                .username("testuser")
                .email("<EMAIL>")
                .passwordHash("$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi")
                .firstName("Test")
                .lastName("User")
                .emailVerified(true)
                .isActive(true)
                .build();
        testUser = userRepository.save(testUser);

        testWorkspace = Workspace.builder()
                .id(UUID.randomUUID())
                .name("Test Workspace")
                .description("A test workspace")
                .organization(testOrganization)
                .createdBy(testUser)
                .isPublic(false)
                .build();
        testWorkspace = workspaceRepository.save(testWorkspace);
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void createUIConfig_ShouldCreateAndReturnConfig() throws Exception {
        // Given
        UIConfigCreateDTO createDTO = UIConfigCreateDTO.builder()
                .name("Integration Test Dashboard")
                .description("A dashboard created in integration test")
                .configData("{\"components\": [{\"type\": \"text\", \"props\": {\"content\": \"Hello World\"}}]}")
                .workspaceId(testWorkspace.getId())
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDTO)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Integration Test Dashboard"))
                .andExpect(jsonPath("$.description").value("A dashboard created in integration test"))
                .andExpect(jsonPath("$.configData").value(containsString("Hello World")))
                .andExpect(jsonPath("$.version").value(1))
                .andExpect(jsonPath("$.status").value("DRAFT"))
                .andExpect(jsonPath("$.isTemplate").value(false))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.createdAt").exists())
                .andExpect(jsonPath("$.updatedAt").exists());

        // Verify in database
        long count = uiConfigRepository.count();
        assert count == 1;
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void getUIConfigs_ShouldReturnPagedResults() throws Exception {
        // Given - Create some test configs
        createTestUIConfig("Dashboard 1", "First dashboard");
        createTestUIConfig("Dashboard 2", "Second dashboard");
        createTestUIConfig("Form Builder", "A form builder");

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(3)))
                .andExpect(jsonPath("$.totalElements").value(3))
                .andExpect(jsonPath("$.totalPages").value(1))
                .andExpect(jsonPath("$.size").value(10))
                .andExpect(jsonPath("$.number").value(0));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void getUIConfigs_WithSearch_ShouldReturnFilteredResults() throws Exception {
        // Given
        createTestUIConfig("Dashboard Analytics", "Analytics dashboard");
        createTestUIConfig("User Form", "User registration form");
        createTestUIConfig("Dashboard Reports", "Reports dashboard");

        // When & Then - Search for "dashboard"
        mockMvc.perform(get("/api/v1/ui-configs")
                        .param("search", "dashboard"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(2)))
                .andExpect(jsonPath("$.content[*].name", hasItems("Dashboard Analytics", "Dashboard Reports")));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void updateUIConfig_ShouldUpdateAndReturnConfig() throws Exception {
        // Given
        String configId = createTestUIConfig("Original Dashboard", "Original description");
        
        UIConfigUpdateDTO updateDTO = UIConfigUpdateDTO.builder()
                .name("Updated Dashboard")
                .description("Updated description")
                .configData("{\"components\": [{\"type\": \"button\", \"props\": {\"text\": \"Click me\"}}]}")
                .build();

        // When & Then
        mockMvc.perform(put("/api/v1/ui-configs/{id}", configId)
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(configId))
                .andExpect(jsonPath("$.name").value("Updated Dashboard"))
                .andExpect(jsonPath("$.description").value("Updated description"))
                .andExpect(jsonPath("$.configData").value(containsString("Click me")))
                .andExpect(jsonPath("$.version").value(2));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void duplicateUIConfig_ShouldCreateCopy() throws Exception {
        // Given
        String originalConfigId = createTestUIConfig("Original Dashboard", "Original description");

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/{id}/duplicate", originalConfigId)
                        .with(csrf())
                        .param("newName", "Copied Dashboard"))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Copied Dashboard"))
                .andExpect(jsonPath("$.description").value("Original description"))
                .andExpect(jsonPath("$.version").value(1))
                .andExpect(jsonPath("$.id").value(not(originalConfigId)));

        // Verify we now have 2 configs
        long count = uiConfigRepository.count();
        assert count == 2;
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void publishUIConfig_ShouldUpdateStatus() throws Exception {
        // Given
        String configId = createTestUIConfig("Dashboard to Publish", "Ready for publishing");

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/{id}/publish", configId)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(configId))
                .andExpect(jsonPath("$.status").value("PUBLISHED"))
                .andExpect(jsonPath("$.publishedAt").exists());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "ADMIN")
    void deleteUIConfig_ShouldRemoveConfig() throws Exception {
        // Given
        String configId = createTestUIConfig("Dashboard to Delete", "Will be deleted");

        // When & Then
        mockMvc.perform(delete("/api/v1/ui-configs/{id}", configId)
                        .with(csrf()))
                .andExpect(status().isNoContent());

        // Verify deletion
        mockMvc.perform(get("/api/v1/ui-configs/{id}", configId))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void exportUIConfig_ShouldReturnExportData() throws Exception {
        // Given
        String configId = createTestUIConfig("Dashboard to Export", "Export test");

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs/{id}/export", configId)
                        .param("format", "json"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Disposition", 
                        containsString("attachment; filename=ui-config-" + configId + ".json")))
                .andExpect(content().string(containsString("Dashboard to Export")));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void importUIConfig_ShouldCreateConfigFromImport() throws Exception {
        // Given
        String importData = """
            {
                "name": "Imported Dashboard",
                "description": "Imported from JSON",
                "configData": {"components": [{"type": "text", "props": {"content": "Imported"}}]},
                "workspaceId": "%s"
            }
            """.formatted(testWorkspace.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs/import")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(importData)
                        .param("format", "json"))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Imported Dashboard"))
                .andExpect(jsonPath("$.description").value("Imported from JSON"))
                .andExpect(jsonPath("$.configData").value(containsString("Imported")));
    }

    @Test
    @WithMockUser(username = "otheruser", roles = "USER")
    void updateUIConfig_AsNonOwner_ShouldReturnForbidden() throws Exception {
        // Given
        String configId = createTestUIConfig("Protected Dashboard", "Owner only");
        
        UIConfigUpdateDTO updateDTO = UIConfigUpdateDTO.builder()
                .name("Hacked Dashboard")
                .build();

        // When & Then
        mockMvc.perform(put("/api/v1/ui-configs/{id}", configId)
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDTO)))
                .andExpect(status().isForbidden());
    }

    @Test
    void getUIConfigs_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void createUIConfig_WithInvalidData_ShouldReturnBadRequest() throws Exception {
        // Given
        UIConfigCreateDTO invalidDTO = UIConfigCreateDTO.builder()
                .name("") // Invalid: empty name
                .workspaceId(testWorkspace.getId())
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/ui-configs")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void getUIConfig_WithNonExistentId_ShouldReturnNotFound() throws Exception {
        // Given
        String nonExistentId = UUID.randomUUID().toString();

        // When & Then
        mockMvc.perform(get("/api/v1/ui-configs/{id}", nonExistentId))
                .andExpect(status().isNotFound());
    }

    private String createTestUIConfig(String name, String description) {
        UIConfigCreateDTO createDTO = UIConfigCreateDTO.builder()
                .name(name)
                .description(description)
                .configData("{\"components\": []}")
                .workspaceId(testWorkspace.getId())
                .build();

        try {
            String response = mockMvc.perform(post("/api/v1/ui-configs")
                            .with(csrf())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(createDTO)))
                    .andExpect(status().isCreated())
                    .andReturn()
                    .getResponse()
                    .getContentAsString();

            return objectMapper.readTree(response).get("id").asText();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test UI config", e);
        }
    }
}
