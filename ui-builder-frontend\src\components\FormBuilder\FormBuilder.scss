.form-builder {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;

  .form-builder-header {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #d9d9d9;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .form-builder-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .form-builder-main {
    flex: 1;
    padding: 16px;
    overflow: auto;
  }

  .form-builder-sidebar {
    width: 300px;
    padding: 16px;
    background: #fff;
    border-left: 1px solid #d9d9d9;
    overflow: auto;
  }
}

.form-fields-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.field-types-card {
  .field-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .field-type-button {
    height: auto;
    padding: 12px 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border: 1px dashed #d9d9d9;
    background: #fafafa;
    transition: all 0.2s;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    .field-type-icon {
      font-size: 20px;
    }

    .field-type-label {
      font-size: 11px;
      text-align: center;
      line-height: 1.2;
    }
  }
}

.form-fields-card {
  flex: 1;

  .form-fields-list {
    min-height: 200px;
  }

  .form-field-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    }

    &.selected {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    &.dragging {
      opacity: 0.8;
      transform: rotate(2deg);
    }

    .field-drag-handle {
      margin-right: 8px;
      color: #999;
      cursor: grab;
      font-size: 12px;
      line-height: 1;

      &:active {
        cursor: grabbing;
      }
    }

    .field-info {
      flex: 1;

      .field-label {
        font-weight: 500;
        margin-bottom: 2px;
      }

      .field-type {
        font-size: 12px;
        color: #666;
        text-transform: capitalize;
      }
    }

    .field-actions {
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover .field-actions {
      opacity: 1;
    }
  }

  .empty-fields-message {
    text-align: center;
    color: #999;
    padding: 40px 20px;
    font-style: italic;
  }
}

.form-code-preview {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.form-preview-placeholder {
  padding: 40px;
  text-align: center;
  background: #fff;
  border-radius: 8px;
  margin: 16px;

  h3 {
    margin-bottom: 16px;
    color: #666;
  }

  p {
    margin-bottom: 20px;
    color: #999;
  }

  pre {
    text-align: left;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 16px;
    font-size: 12px;
    max-height: 300px;
    overflow: auto;
  }
}

.field-properties-placeholder {
  padding: 20px;
  text-align: center;

  p {
    margin-bottom: 16px;
    color: #666;
  }

  pre {
    text-align: left;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    font-size: 11px;
    max-height: 200px;
    overflow: auto;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .form-builder {
    .form-builder-content {
      flex-direction: column;
    }

    .form-builder-sidebar {
      width: 100%;
      border-left: none;
      border-top: 1px solid #d9d9d9;
    }
  }
}

@media (max-width: 768px) {
  .form-builder {
    .form-builder-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .field-types-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .field-type-button {
      padding: 8px 4px;

      .field-type-label {
        font-size: 10px;
      }
    }
  }
}

// Dark theme support
.dark-theme {
  .form-builder {
    background: #1f1f1f;

    .form-builder-header {
      background: #2a2a2a;
      border-bottom-color: #444;
    }

    .form-builder-sidebar {
      background: #2a2a2a;
      border-left-color: #444;
    }
  }

  .field-type-button {
    background: #2a2a2a;
    border-color: #444;
    color: #fff;

    &:hover {
      background: #333;
      border-color: #1890ff;
    }
  }

  .form-field-item {
    background: #2a2a2a;
    border-color: #444;
    color: #fff;

    &:hover {
      background: #333;
    }

    &.selected {
      background: #1a3a5c;
    }
  }

  .form-code-preview,
  .form-preview-placeholder pre,
  .field-properties-placeholder pre {
    background: #2a2a2a;
    border-color: #444;
    color: #fff;
  }
}
