apiVersion: v1
kind: Namespace
metadata:
  name: ui-builder
  labels:
    name: ui-builder
    istio-injection: enabled

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-metadata-service-config
  namespace: ui-builder
data:
  application.yml: |
    server:
      port: 8080
      servlet:
        context-path: /api/v1
    
    spring:
      application:
        name: ui-metadata-service
      
      datasource:
        url: jdbc:postgresql://${DB_HOST}:5432/${DB_NAME}
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        hikari:
          maximum-pool-size: 20
          minimum-idle: 5
          connection-timeout: 30000
          idle-timeout: 600000
          max-lifetime: 1800000
      
      jpa:
        hibernate:
          ddl-auto: validate
        show-sql: false
        properties:
          hibernate:
            dialect: org.hibernate.dialect.PostgreSQLDialect
            format_sql: true
      
      redis:
        host: ${REDIS_HOST}
        port: 6379
        password: ${REDIS_PASSWORD}
        timeout: 2000ms
        lettuce:
          pool:
            max-active: 20
            max-idle: 10
            min-idle: 5
      
      kafka:
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
        consumer:
          group-id: ui-metadata-service
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
      metrics:
        export:
          prometheus:
            enabled: true
    
    logging:
      level:
        com.uibuilder: INFO
        org.springframework.security: DEBUG
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
apiVersion: v1
kind: Secret
metadata:
  name: ui-metadata-service-secrets
  namespace: ui-builder
type: Opaque
data:
  DB_PASSWORD: # Base64 encoded database password
  REDIS_PASSWORD: # Base64 encoded Redis password
  JWT_SECRET: # Base64 encoded JWT secret
  MFA_ENCRYPTION_KEY: # Base64 encoded MFA encryption key

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ui-metadata-service
  namespace: ui-builder
  labels:
    app: ui-metadata-service
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ui-metadata-service
  template:
    metadata:
      labels:
        app: ui-metadata-service
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: ui-metadata-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: ui-metadata-service
        image: ui-builder/ui-metadata-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_NAME
          value: "uibuilder"
        - name: DB_USERNAME
          value: "uibuilder"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ui-metadata-service-secrets
              key: DB_PASSWORD
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ui-metadata-service-secrets
              key: REDIS_PASSWORD
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ui-metadata-service-secrets
              key: JWT_SECRET
        - name: MFA_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: ui-metadata-service-secrets
              key: MFA_ENCRYPTION_KEY
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config
        configMap:
          name: ui-metadata-service-config
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "spot-instance"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ui-metadata-service
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: ui-metadata-service
  namespace: ui-builder
  labels:
    app: ui-metadata-service
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: ui-metadata-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ui-metadata-service
  namespace: ui-builder
  labels:
    app: ui-metadata-service

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ui-metadata-service-netpol
  namespace: ui-builder
spec:
  podSelector:
    matchLabels:
      app: ui-metadata-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - namespaceSelector:
        matchLabels:
          name: ui-builder
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: ui-builder
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 9092  # Kafka
  - to: []  # Allow all outbound for external APIs
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ui-metadata-service-hpa
  namespace: ui-builder
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ui-metadata-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ui-metadata-service-pdb
  namespace: ui-builder
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: ui-metadata-service
