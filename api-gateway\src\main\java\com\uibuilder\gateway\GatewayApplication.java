package com.uibuilder.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RouterFunctions.route;
import static org.springframework.web.reactive.function.server.RequestPredicates.GET;

/**
 * Main application class for the UI Builder API Gateway
 * 
 * This gateway serves as the single entry point for all UI Builder services,
 * providing routing, load balancing, authentication, rate limiting, and monitoring.
 */
@SpringBootApplication
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }

    /**
     * Configure routes for all UI Builder services
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // Authentication Service
            .route("auth-service", r -> r
                .path("/api/auth/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("auth-service-cb")
                        .setFallbackUri("forward:/fallback/auth"))
                    .retry(config -> config
                        .setRetries(3)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://auth-service"))

            // Configuration Service
            .route("config-service", r -> r
                .path("/api/configs/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("config-service-cb")
                        .setFallbackUri("forward:/fallback/config"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://config-service"))

            // Template Service
            .route("template-service", r -> r
                .path("/api/templates/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("template-service-cb")
                        .setFallbackUri("forward:/fallback/template"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://template-service"))

            // Analytics Service
            .route("analytics-service", r -> r
                .path("/api/analytics/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("analytics-service-cb")
                        .setFallbackUri("forward:/fallback/analytics"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://analytics-service"))

            // Collaboration Service (WebSocket)
            .route("collaboration-ws", r -> r
                .path("/ws/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway"))
                .uri("lb://collaboration-service"))

            // File Upload Service
            .route("upload-service", r -> r
                .path("/api/uploads/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("upload-service-cb")
                        .setFallbackUri("forward:/fallback/upload"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://upload-service"))

            // Component Library Service
            .route("component-service", r -> r
                .path("/api/components/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway")
                    .circuitBreaker(config -> config
                        .setName("component-service-cb")
                        .setFallbackUri("forward:/fallback/component"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://component-service"))

            // Health Check Endpoint
            .route("health", r -> r
                .path("/health")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway"))
                .uri("forward:/actuator/health"))

            // API Documentation
            .route("api-docs", r -> r
                .path("/api-docs/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Source", "ui-builder-gateway"))
                .uri("forward:/swagger-ui.html"))

            .build();
    }

    /**
     * Configure CORS for cross-origin requests
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        corsConfig.addAllowedOriginPattern("*");
        corsConfig.addAllowedHeader("*");
        corsConfig.addAllowedMethod("*");
        corsConfig.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }

    /**
     * Health check endpoint
     */
    @Bean
    public RouterFunction<ServerResponse> healthRouter() {
        return route(GET("/health"), 
            request -> ServerResponse.ok()
                .bodyValue(java.util.Map.of(
                    "status", "UP",
                    "service", "ui-builder-gateway",
                    "timestamp", java.time.Instant.now().toString(),
                    "version", getClass().getPackage().getImplementationVersion()
                )));
    }

    /**
     * Fallback endpoints for circuit breaker
     */
    @Bean
    public RouterFunction<ServerResponse> fallbackRouter() {
        return route(GET("/fallback/auth"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Authentication service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )))
            .andRoute(GET("/fallback/config"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Configuration service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )))
            .andRoute(GET("/fallback/template"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Template service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )))
            .andRoute(GET("/fallback/analytics"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Analytics service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )))
            .andRoute(GET("/fallback/upload"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Upload service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )))
            .andRoute(GET("/fallback/component"), 
                request -> ServerResponse.status(503)
                    .bodyValue(java.util.Map.of(
                        "error", "Component service temporarily unavailable",
                        "message", "Please try again later",
                        "timestamp", java.time.Instant.now().toString()
                    )));
    }

    /**
     * Redis rate limiter configuration
     */
    @Bean
    public org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter redisRateLimiter() {
        return new org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter(
            10, // replenishRate: tokens per second
            20, // burstCapacity: maximum tokens in bucket
            1   // requestedTokens: tokens per request
        );
    }

    /**
     * User-based key resolver for rate limiting
     */
    @Bean
    public org.springframework.cloud.gateway.filter.ratelimit.KeyResolver userKeyResolver() {
        return exchange -> {
            // Extract user ID from JWT token or use IP address as fallback
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-ID");
            if (userId != null) {
                return reactor.core.publisher.Mono.just(userId);
            }
            
            String clientIP = exchange.getRequest().getRemoteAddress() != null 
                ? exchange.getRequest().getRemoteAddress().getAddress().getHostAddress()
                : "unknown";
            return reactor.core.publisher.Mono.just(clientIP);
        };
    }
}
