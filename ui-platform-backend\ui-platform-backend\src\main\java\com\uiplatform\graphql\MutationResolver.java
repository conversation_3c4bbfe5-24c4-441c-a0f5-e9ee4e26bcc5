package com.uiplatform.graphql;

import com.uiplatform.dto.*;
import com.uiplatform.graphql.input.*;
import com.uiplatform.service.*;
import graphql.kickstart.tools.GraphQLMutationResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * GraphQL Mutation Resolver for handling all mutation operations.
 */
@Component
public class MutationResolver implements GraphQLMutationResolver {

    private static final Logger logger = LoggerFactory.getLogger(MutationResolver.class);

    private final OrganizationService organizationService;
    private final UserService userService;
    private final AuthenticationService authenticationService;
    private final UIConfigurationService uiConfigurationService;
    private final ComponentService componentService;
    private final TemplateService templateService;

    @Autowired
    public MutationResolver(OrganizationService organizationService,
                           UserService userService,
                           AuthenticationService authenticationService,
                           UIConfigurationService uiConfigurationService,
                           ComponentService componentService,
                           TemplateService templateService) {
        this.organizationService = organizationService;
        this.userService = userService;
        this.authenticationService = authenticationService;
        this.uiConfigurationService = uiConfigurationService;
        this.componentService = componentService;
        this.templateService = templateService;
    }

    // Organization Mutations

    @PreAuthorize("hasPermission('ORGANIZATION', 'CREATE')")
    public OrganizationDTO createOrganization(CreateOrganizationInput input) {
        logger.info("GraphQL mutation: createOrganization(input: {})", input);
        
        OrganizationDTO.CreateDTO createDTO = new OrganizationDTO.CreateDTO();
        createDTO.setName(input.getName());
        createDTO.setSlug(input.getSlug());
        createDTO.setDescription(input.getDescription());
        createDTO.setDomain(input.getDomain());
        
        return organizationService.createOrganization(createDTO);
    }

    @PreAuthorize("hasPermission('ORGANIZATION', 'UPDATE')")
    public OrganizationDTO updateOrganization(UUID id, UpdateOrganizationInput input) {
        logger.info("GraphQL mutation: updateOrganization(id: {}, input: {})", id, input);
        
        OrganizationDTO.UpdateDTO updateDTO = new OrganizationDTO.UpdateDTO();
        updateDTO.setName(input.getName());
        updateDTO.setDescription(input.getDescription());
        updateDTO.setDomain(input.getDomain());
        updateDTO.setLogoUrl(input.getLogoUrl());
        updateDTO.setWebsiteUrl(input.getWebsiteUrl());
        
        return organizationService.updateOrganization(id, updateDTO);
    }

    @PreAuthorize("hasPermission('ORGANIZATION', 'DELETE')")
    public Boolean deleteOrganization(UUID id) {
        logger.info("GraphQL mutation: deleteOrganization(id: {})", id);
        organizationService.deleteOrganization(id);
        return true;
    }

    // User Mutations

    @PreAuthorize("hasPermission('USER', 'CREATE')")
    public UserDTO createUser(CreateUserInput input) {
        logger.info("GraphQL mutation: createUser(input: {})", input);
        
        UserDTO.CreateDTO createDTO = new UserDTO.CreateDTO();
        createDTO.setUsername(input.getUsername());
        createDTO.setEmail(input.getEmail());
        createDTO.setPassword(input.getPassword());
        createDTO.setFirstName(input.getFirstName());
        createDTO.setLastName(input.getLastName());
        createDTO.setOrganizationId(input.getOrganizationId());
        
        return userService.createUser(createDTO);
    }

    @PreAuthorize("hasPermission('USER', 'UPDATE')")
    public UserDTO updateUser(UUID id, UpdateUserInput input) {
        logger.info("GraphQL mutation: updateUser(id: {}, input: {})", id, input);
        
        UserDTO.UpdateDTO updateDTO = new UserDTO.UpdateDTO();
        updateDTO.setFirstName(input.getFirstName());
        updateDTO.setLastName(input.getLastName());
        updateDTO.setAvatarUrl(input.getAvatarUrl());
        updateDTO.setPhoneNumber(input.getPhoneNumber());
        updateDTO.setTimezone(input.getTimezone());
        updateDTO.setLocale(input.getLocale());
        
        return userService.updateUser(id, updateDTO);
    }

    @PreAuthorize("hasPermission('USER', 'DELETE')")
    public Boolean deleteUser(UUID id) {
        logger.info("GraphQL mutation: deleteUser(id: {})", id);
        userService.deleteUser(id);
        return true;
    }

    // Authentication Mutations

    public AuthDTO.LoginResponse login(LoginInput input) {
        logger.info("GraphQL mutation: login(input: {})", input);
        
        AuthDTO.LoginRequest loginRequest = new AuthDTO.LoginRequest();
        loginRequest.setUsernameOrEmail(input.getUsernameOrEmail());
        loginRequest.setPassword(input.getPassword());
        loginRequest.setRememberMe(input.getRememberMe());
        
        return authenticationService.login(loginRequest);
    }

    public AuthDTO.LoginResponse register(RegisterInput input) {
        logger.info("GraphQL mutation: register(input: {})", input);
        
        AuthDTO.RegisterRequest registerRequest = new AuthDTO.RegisterRequest();
        registerRequest.setUsername(input.getUsername());
        registerRequest.setEmail(input.getEmail());
        registerRequest.setPassword(input.getPassword());
        registerRequest.setFirstName(input.getFirstName());
        registerRequest.setLastName(input.getLastName());
        registerRequest.setOrganizationName(input.getOrganizationName());
        registerRequest.setOrganizationSlug(input.getOrganizationSlug());
        
        return authenticationService.register(registerRequest);
    }

    public AuthDTO.LoginResponse refreshToken(String refreshToken) {
        logger.info("GraphQL mutation: refreshToken");
        
        AuthDTO.RefreshTokenRequest refreshRequest = new AuthDTO.RefreshTokenRequest();
        refreshRequest.setRefreshToken(refreshToken);
        
        return authenticationService.refreshToken(refreshRequest);
    }

    public Boolean changePassword(ChangePasswordInput input) {
        logger.info("GraphQL mutation: changePassword");
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UUID userId = UUID.fromString(authentication.getName());
        
        AuthDTO.ChangePasswordRequest changePasswordRequest = new AuthDTO.ChangePasswordRequest();
        changePasswordRequest.setCurrentPassword(input.getCurrentPassword());
        changePasswordRequest.setNewPassword(input.getNewPassword());
        
        authenticationService.changePassword(userId, changePasswordRequest);
        return true;
    }

    // UI Configuration Mutations

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'CREATE')")
    public UIConfigurationDTO createUIConfiguration(CreateUIConfigurationInput input) {
        logger.info("GraphQL mutation: createUIConfiguration(input: {})", input);
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UUID ownerId = UUID.fromString(authentication.getName());
        
        // TODO: Get organization ID from context or input
        UUID organizationId = input.getOrganizationId(); // This should be added to input
        
        UIConfigurationDTO.CreateDTO createDTO = new UIConfigurationDTO.CreateDTO();
        createDTO.setName(input.getName());
        createDTO.setSlug(input.getSlug());
        createDTO.setDescription(input.getDescription());
        createDTO.setType(input.getType());
        createDTO.setMetadata(input.getMetadata());
        createDTO.setThemeId(input.getThemeId());
        createDTO.setLayoutId(input.getLayoutId());
        createDTO.setParentId(input.getParentId());
        
        return uiConfigurationService.createUIConfiguration(createDTO, ownerId, organizationId);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public UIConfigurationDTO updateUIConfiguration(UUID id, UpdateUIConfigurationInput input) {
        logger.info("GraphQL mutation: updateUIConfiguration(id: {}, input: {})", id, input);
        
        UIConfigurationDTO updateDTO = new UIConfigurationDTO();
        updateDTO.setName(input.getName());
        updateDTO.setDescription(input.getDescription());
        updateDTO.setMetadata(input.getMetadata());
        updateDTO.setLayoutConfig(input.getLayoutConfig());
        updateDTO.setStyleConfig(input.getStyleConfig());
        updateDTO.setResponsiveConfig(input.getResponsiveConfig());
        updateDTO.setValidationRules(input.getValidationRules());
        updateDTO.setTags(input.getTags());
        
        return uiConfigurationService.updateUIConfiguration(id, updateDTO);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'DELETE')")
    public Boolean deleteUIConfiguration(UUID id) {
        logger.info("GraphQL mutation: deleteUIConfiguration(id: {})", id);
        uiConfigurationService.deleteUIConfiguration(id);
        return true;
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public UIConfigurationDTO publishUIConfiguration(UUID id) {
        logger.info("GraphQL mutation: publishUIConfiguration(id: {})", id);
        return uiConfigurationService.publishUIConfiguration(id);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'UPDATE')")
    public UIConfigurationDTO unpublishUIConfiguration(UUID id) {
        logger.info("GraphQL mutation: unpublishUIConfiguration(id: {})", id);
        return uiConfigurationService.unpublishUIConfiguration(id);
    }

    @PreAuthorize("hasPermission('UI_CONFIGURATION', 'CREATE')")
    public UIConfigurationDTO cloneUIConfiguration(UUID id, String newName, String newSlug) {
        logger.info("GraphQL mutation: cloneUIConfiguration(id: {}, newName: {}, newSlug: {})", id, newName, newSlug);
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UUID ownerId = UUID.fromString(authentication.getName());
        
        return uiConfigurationService.cloneUIConfiguration(id, newName, newSlug, ownerId);
    }

    // Component Mutations

    @PreAuthorize("hasPermission('COMPONENT', 'CREATE')")
    public ComponentDTO createComponent(CreateComponentInput input) {
        logger.info("GraphQL mutation: createComponent(input: {})", input);
        
        ComponentDTO.CreateDTO createDTO = new ComponentDTO.CreateDTO();
        createDTO.setName(input.getName());
        createDTO.setDescription(input.getDescription());
        createDTO.setComponentType(input.getComponentType());
        createDTO.setCategory(input.getCategory());
        createDTO.setProperties(input.getProperties());
        createDTO.setStyleProperties(input.getStyleProperties());
        createDTO.setSortOrder(input.getSortOrder());
        createDTO.setIsVisible(input.getIsVisible());
        createDTO.setIsEnabled(input.getIsEnabled());
        createDTO.setIsRequired(input.getIsRequired());
        createDTO.setIsReusable(input.getIsReusable());
        createDTO.setParentId(input.getParentId());
        
        return componentService.createComponent(createDTO, input.getUiConfigurationId());
    }

    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ComponentDTO updateComponent(UUID id, UpdateComponentInput input) {
        logger.info("GraphQL mutation: updateComponent(id: {}, input: {})", id, input);
        
        ComponentDTO updateDTO = new ComponentDTO();
        updateDTO.setName(input.getName());
        updateDTO.setDescription(input.getDescription());
        updateDTO.setProperties(input.getProperties());
        updateDTO.setStyleProperties(input.getStyleProperties());
        updateDTO.setEventHandlers(input.getEventHandlers());
        updateDTO.setValidationRules(input.getValidationRules());
        updateDTO.setResponsiveSettings(input.getResponsiveSettings());
        updateDTO.setIsVisible(input.getIsVisible());
        updateDTO.setIsEnabled(input.getIsEnabled());
        updateDTO.setIsRequired(input.getIsRequired());
        updateDTO.setCssClasses(input.getCssClasses());
        updateDTO.setCustomCss(input.getCustomCss());
        updateDTO.setDataSource(input.getDataSource());
        updateDTO.setDataBinding(input.getDataBinding());
        
        return componentService.updateComponent(id, updateDTO);
    }

    @PreAuthorize("hasPermission('COMPONENT', 'DELETE')")
    public Boolean deleteComponent(UUID id) {
        logger.info("GraphQL mutation: deleteComponent(id: {})", id);
        componentService.deleteComponent(id);
        return true;
    }

    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ComponentDTO moveComponent(UUID id, UUID newParentId, Integer newSortOrder) {
        logger.info("GraphQL mutation: moveComponent(id: {}, newParentId: {}, newSortOrder: {})", 
                   id, newParentId, newSortOrder);
        return componentService.moveComponent(id, newParentId, newSortOrder);
    }

    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public Boolean reorderComponents(UUID uiConfigurationId, UUID parentId, List<UUID> componentIds) {
        logger.info("GraphQL mutation: reorderComponents(uiConfigurationId: {}, parentId: {}, componentIds: {})", 
                   uiConfigurationId, parentId, componentIds);
        componentService.reorderComponents(uiConfigurationId, parentId, componentIds);
        return true;
    }

    // Template Mutations

    @PreAuthorize("hasPermission('TEMPLATE', 'CREATE')")
    public TemplateDTO createTemplate(CreateTemplateInput input) {
        logger.info("GraphQL mutation: createTemplate(input: {})", input);
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UUID authorId = UUID.fromString(authentication.getName());
        
        TemplateDTO.CreateDTO createDTO = new TemplateDTO.CreateDTO();
        createDTO.setName(input.getName());
        createDTO.setDescription(input.getDescription());
        createDTO.setCategory(input.getCategory());
        createDTO.setSubcategory(input.getSubcategory());
        createDTO.setTags(input.getTags());
        createDTO.setTemplateData(input.getTemplateData());
        createDTO.setPrice(input.getPrice());
        createDTO.setLicenseType(input.getLicenseType());
        createDTO.setVersion(input.getVersion());
        
        return templateService.createTemplate(createDTO, authorId, input.getOrganizationId());
    }

    @PreAuthorize("hasPermission('TEMPLATE', 'UPDATE')")
    public TemplateDTO updateTemplate(UUID id, UpdateTemplateInput input) {
        logger.info("GraphQL mutation: updateTemplate(id: {}, input: {})", id, input);
        
        TemplateDTO updateDTO = new TemplateDTO();
        updateDTO.setName(input.getName());
        updateDTO.setDescription(input.getDescription());
        updateDTO.setLongDescription(input.getLongDescription());
        updateDTO.setSubcategory(input.getSubcategory());
        updateDTO.setTags(input.getTags());
        updateDTO.setTemplateData(input.getTemplateData());
        updateDTO.setPreviewImages(input.getPreviewImages());
        updateDTO.setDemoUrl(input.getDemoUrl());
        updateDTO.setDocumentationUrl(input.getDocumentationUrl());
        updateDTO.setPrice(input.getPrice());
        updateDTO.setLicenseType(input.getLicenseType());
        updateDTO.setCompatibility(input.getCompatibility());
        updateDTO.setRequirements(input.getRequirements());
        updateDTO.setInstallationGuide(input.getInstallationGuide());
        
        return templateService.updateTemplate(id, updateDTO);
    }

    @PreAuthorize("hasPermission('TEMPLATE', 'DELETE')")
    public Boolean deleteTemplate(UUID id) {
        logger.info("GraphQL mutation: deleteTemplate(id: {})", id);
        templateService.deleteTemplate(id);
        return true;
    }

    @PreAuthorize("hasPermission('TEMPLATE', 'UPDATE')")
    public TemplateDTO publishTemplate(UUID id) {
        logger.info("GraphQL mutation: publishTemplate(id: {})", id);
        return templateService.publishTemplate(id);
    }

    @PreAuthorize("hasPermission('TEMPLATE', 'CREATE')")
    public TemplateDTO cloneTemplate(UUID id, String newName) {
        logger.info("GraphQL mutation: cloneTemplate(id: {}, newName: {})", id, newName);
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UUID userId = UUID.fromString(authentication.getName());
        
        return templateService.cloneTemplate(id, newName, userId);
    }
}
