import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';
import 'widget_registry.dart';
import 'state_management.dart';
import 'action_handler.dart';

/// Dynamic Widget Renderer for Flutter UI Runtime
/// 
/// Core rendering engine that converts JSON metadata to Flutter widget trees
/// with support for nested components, state management, and error handling.
class DynamicRenderer {
  static final DynamicRenderer _instance = DynamicRenderer._internal();
  factory DynamicRenderer() => _instance;
  DynamicRenderer._internal();

  final WidgetRegistry _widgetRegistry = WidgetRegistry();
  final StateManager _stateManager = StateManager();
  final ActionHandlerSystem _actionHandler = ActionHandlerSystem();
  
  bool _debugMode = false;
  final Map<String, Widget> _renderCache = {};
  final Set<String> _renderingComponents = {};

  /// Enable or disable debug mode
  void setDebugMode(bool enabled) {
    _debugMode = enabled;
  }

  /// Render a complete UI configuration
  Widget renderConfiguration(UIConfiguration configuration, BuildContext context) {
    try {
      if (_debugMode) {
        print('Rendering configuration: ${configuration.id}');
      }

      // Apply global theme if available
      if (configuration.theme != null) {
        return _wrapWithTheme(
          configuration.theme!,
          _renderLayout(configuration.layout, context),
        );
      }

      return _renderLayout(configuration.layout, context);
    } catch (e) {
      if (_debugMode) {
        print('Error rendering configuration: $e');
      }
      return _buildErrorWidget('Configuration Error', e.toString());
    }
  }

  /// Render a layout definition
  Widget _renderLayout(LayoutDefinition layout, BuildContext context) {
    switch (layout.type) {
      case 'container':
        return _renderContainer(layout, context);
      case 'column':
        return _renderColumn(layout, context);
      case 'row':
        return _renderRow(layout, context);
      case 'stack':
        return _renderStack(layout, context);
      case 'grid':
        return _renderGrid(layout, context);
      case 'wrap':
        return _renderWrap(layout, context);
      case 'scroll':
        return _renderScrollView(layout, context);
      default:
        return _renderContainer(layout, context);
    }
  }

  /// Render a container layout
  Widget _renderContainer(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    
    return Container(
      width: _parseDouble(properties['width']),
      height: _parseDouble(properties['height']),
      padding: _parseEdgeInsets(properties['padding']),
      margin: _parseEdgeInsets(properties['margin']),
      decoration: _parseDecoration(properties['decoration']),
      alignment: _parseAlignment(properties['alignment']),
      child: layout.children?.isNotEmpty == true
          ? _renderChildren(layout.children!, context).first
          : null,
    );
  }

  /// Render a column layout
  Widget _renderColumn(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    
    return Column(
      mainAxisAlignment: _parseMainAxisAlignment(properties['mainAxisAlignment']),
      crossAxisAlignment: _parseCrossAxisAlignment(properties['crossAxisAlignment']),
      mainAxisSize: _parseMainAxisSize(properties['mainAxisSize']),
      children: _renderChildren(layout.children ?? [], context),
    );
  }

  /// Render a row layout
  Widget _renderRow(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    
    return Row(
      mainAxisAlignment: _parseMainAxisAlignment(properties['mainAxisAlignment']),
      crossAxisAlignment: _parseCrossAxisAlignment(properties['crossAxisAlignment']),
      mainAxisSize: _parseMainAxisSize(properties['mainAxisSize']),
      children: _renderChildren(layout.children ?? [], context),
    );
  }

  /// Render a stack layout
  Widget _renderStack(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    
    return Stack(
      alignment: _parseAlignment(properties['alignment']) ?? Alignment.topLeft,
      fit: _parseStackFit(properties['fit']),
      children: _renderChildren(layout.children ?? [], context),
    );
  }

  /// Render a grid layout
  Widget _renderGrid(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    final crossAxisCount = _parseInt(properties['crossAxisCount']) ?? 2;
    final childAspectRatio = _parseDouble(properties['childAspectRatio']) ?? 1.0;
    final mainAxisSpacing = _parseDouble(properties['mainAxisSpacing']) ?? 0.0;
    final crossAxisSpacing = _parseDouble(properties['crossAxisSpacing']) ?? 0.0;
    
    return GridView.count(
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: _renderChildren(layout.children ?? [], context),
    );
  }

  /// Render a wrap layout
  Widget _renderWrap(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    
    return Wrap(
      direction: _parseAxis(properties['direction']),
      alignment: _parseWrapAlignment(properties['alignment']),
      spacing: _parseDouble(properties['spacing']) ?? 0.0,
      runSpacing: _parseDouble(properties['runSpacing']) ?? 0.0,
      children: _renderChildren(layout.children ?? [], context),
    );
  }

  /// Render a scroll view layout
  Widget _renderScrollView(LayoutDefinition layout, BuildContext context) {
    final properties = layout.properties ?? {};
    final direction = _parseAxis(properties['scrollDirection']);
    
    if (direction == Axis.horizontal) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _renderChildren(layout.children ?? [], context),
        ),
      );
    } else {
      return SingleChildScrollView(
        child: Column(
          children: _renderChildren(layout.children ?? [], context),
        ),
      );
    }
  }

  /// Render child components
  List<Widget> _renderChildren(List<dynamic> children, BuildContext context) {
    return children.map((child) {
      if (child is ComponentDefinition) {
        return renderComponent(child, context);
      } else if (child is LayoutDefinition) {
        return _renderLayout(child, context);
      } else if (child is Map<String, dynamic>) {
        // Try to parse as component or layout
        if (child.containsKey('type')) {
          final component = ComponentDefinition.fromJson(child);
          return renderComponent(component, context);
        } else if (child.containsKey('layout')) {
          final layout = LayoutDefinition.fromJson(child);
          return _renderLayout(layout, context);
        }
      }
      return const SizedBox.shrink();
    }).toList();
  }

  /// Render a single component
  Widget renderComponent(ComponentDefinition component, BuildContext context) {
    try {
      // Check for circular rendering
      if (_renderingComponents.contains(component.id)) {
        return _buildErrorWidget('Circular Reference', 'Component ${component.id} has circular reference');
      }

      // Check cache
      final cacheKey = _generateCacheKey(component);
      if (_renderCache.containsKey(cacheKey)) {
        return _renderCache[cacheKey]!;
      }

      _renderingComponents.add(component.id);

      if (_debugMode) {
        print('Rendering component: ${component.id} (${component.type})');
      }

      // Get widget from registry
      Widget widget = _widgetRegistry.buildWidget(component, context);

      // Apply component-level styling
      widget = _applyComponentStyling(widget, component);

      // Wrap with gesture detector for actions
      widget = _wrapWithActions(widget, component, context);

      // Wrap with conditional rendering
      widget = _wrapWithConditionalRendering(widget, component);

      // Cache the result
      _renderCache[cacheKey] = widget;
      _renderingComponents.remove(component.id);

      return widget;
    } catch (e) {
      _renderingComponents.remove(component.id);
      if (_debugMode) {
        print('Error rendering component ${component.id}: $e');
      }
      return _buildErrorWidget('Component Error', 'Failed to render ${component.type}: $e');
    }
  }

  /// Apply component-level styling
  Widget _applyComponentStyling(Widget widget, ComponentDefinition component) {
    if (component.style == null || component.style!.isEmpty) {
      return widget;
    }

    final style = component.style!;
    
    return Container(
      width: _parseDouble(style['width']),
      height: _parseDouble(style['height']),
      padding: _parseEdgeInsets(style['padding']),
      margin: _parseEdgeInsets(style['margin']),
      decoration: _parseDecoration(style),
      transform: _parseTransform(style['transform']),
      child: widget,
    );
  }

  /// Wrap widget with action handlers
  Widget _wrapWithActions(Widget widget, ComponentDefinition component, BuildContext context) {
    if (component.actions == null || component.actions!.isEmpty) {
      return widget;
    }

    return GestureDetector(
      onTap: component.actions!.containsKey('onTap')
          ? () => _handleAction(component.actions!['onTap'], context, component)
          : null,
      onDoubleTap: component.actions!.containsKey('onDoubleTap')
          ? () => _handleAction(component.actions!['onDoubleTap'], context, component)
          : null,
      onLongPress: component.actions!.containsKey('onLongPress')
          ? () => _handleAction(component.actions!['onLongPress'], context, component)
          : null,
      child: widget,
    );
  }

  /// Wrap widget with conditional rendering
  Widget _wrapWithConditionalRendering(Widget widget, ComponentDefinition component) {
    final condition = component.properties?['condition'];
    if (condition == null) {
      return widget;
    }

    // Evaluate condition
    final shouldRender = _evaluateCondition(condition);
    
    return shouldRender ? widget : const SizedBox.shrink();
  }

  /// Handle component actions
  void _handleAction(dynamic action, BuildContext context, ComponentDefinition component) {
    if (action is Map<String, dynamic>) {
      final actionContext = ActionContext(
        buildContext: context,
        stateManager: _stateManager,
        componentProps: component.properties ?? {},
        globalData: {},
        componentId: component.id,
      );

      _actionHandler.executeAction(
        action['type'] ?? 'unknown',
        actionContext,
        action['params'] ?? {},
      );
    }
  }

  /// Evaluate conditional rendering
  bool _evaluateCondition(dynamic condition) {
    if (condition is bool) {
      return condition;
    }
    
    if (condition is String) {
      // Simple state-based condition
      if (condition.startsWith('state.')) {
        final stateKey = condition.substring(6);
        return _stateManager.get<bool>(stateKey) ?? false;
      }
      
      // Simple property-based condition
      return condition.toLowerCase() == 'true';
    }
    
    if (condition is Map<String, dynamic>) {
      // Complex condition evaluation
      return _evaluateComplexCondition(condition);
    }
    
    return true;
  }

  /// Evaluate complex conditions
  bool _evaluateComplexCondition(Map<String, dynamic> condition) {
    final operator = condition['operator'] as String?;
    final left = condition['left'];
    final right = condition['right'];
    
    switch (operator) {
      case 'equals':
        return _resolveValue(left) == _resolveValue(right);
      case 'notEquals':
        return _resolveValue(left) != _resolveValue(right);
      case 'greaterThan':
        return (_resolveValue(left) as num) > (_resolveValue(right) as num);
      case 'lessThan':
        return (_resolveValue(left) as num) < (_resolveValue(right) as num);
      case 'and':
        return _evaluateCondition(left) && _evaluateCondition(right);
      case 'or':
        return _evaluateCondition(left) || _evaluateCondition(right);
      default:
        return true;
    }
  }

  /// Resolve dynamic values
  dynamic _resolveValue(dynamic value) {
    if (value is String && value.startsWith('state.')) {
      final stateKey = value.substring(6);
      return _stateManager.get(stateKey);
    }
    return value;
  }

  /// Wrap widget with theme
  Widget _wrapWithTheme(ThemeDefinition theme, Widget child) {
    return Theme(
      data: _buildThemeData(theme),
      child: child,
    );
  }

  /// Build ThemeData from theme definition
  ThemeData _buildThemeData(ThemeDefinition theme) {
    return ThemeData(
      primarySwatch: _parseColor(theme.colors?['primary']) as MaterialColor? ?? Colors.blue,
      brightness: theme.mode == 'dark' ? Brightness.dark : Brightness.light,
      fontFamily: theme.typography?['fontFamily'] as String?,
      textTheme: _buildTextTheme(theme.typography),
      colorScheme: _buildColorScheme(theme.colors),
    );
  }

  /// Build text theme
  TextTheme _buildTextTheme(Map<String, dynamic>? typography) {
    if (typography == null) return const TextTheme();
    
    return TextTheme(
      displayLarge: _buildTextStyle(typography['displayLarge']),
      displayMedium: _buildTextStyle(typography['displayMedium']),
      displaySmall: _buildTextStyle(typography['displaySmall']),
      headlineLarge: _buildTextStyle(typography['headlineLarge']),
      headlineMedium: _buildTextStyle(typography['headlineMedium']),
      headlineSmall: _buildTextStyle(typography['headlineSmall']),
      titleLarge: _buildTextStyle(typography['titleLarge']),
      titleMedium: _buildTextStyle(typography['titleMedium']),
      titleSmall: _buildTextStyle(typography['titleSmall']),
      bodyLarge: _buildTextStyle(typography['bodyLarge']),
      bodyMedium: _buildTextStyle(typography['bodyMedium']),
      bodySmall: _buildTextStyle(typography['bodySmall']),
      labelLarge: _buildTextStyle(typography['labelLarge']),
      labelMedium: _buildTextStyle(typography['labelMedium']),
      labelSmall: _buildTextStyle(typography['labelSmall']),
    );
  }

  /// Build text style
  TextStyle? _buildTextStyle(dynamic style) {
    if (style is Map<String, dynamic>) {
      return TextStyle(
        fontSize: _parseDouble(style['fontSize']),
        fontWeight: _parseFontWeight(style['fontWeight']),
        color: _parseColor(style['color']),
        letterSpacing: _parseDouble(style['letterSpacing']),
        height: _parseDouble(style['lineHeight']),
      );
    }
    return null;
  }

  /// Build color scheme
  ColorScheme _buildColorScheme(Map<String, dynamic>? colors) {
    if (colors == null) return const ColorScheme.light();
    
    return ColorScheme.light(
      primary: _parseColor(colors['primary']) ?? Colors.blue,
      secondary: _parseColor(colors['secondary']) ?? Colors.blueAccent,
      surface: _parseColor(colors['surface']) ?? Colors.white,
      background: _parseColor(colors['background']) ?? Colors.white,
      error: _parseColor(colors['error']) ?? Colors.red,
      onPrimary: _parseColor(colors['onPrimary']) ?? Colors.white,
      onSecondary: _parseColor(colors['onSecondary']) ?? Colors.white,
      onSurface: _parseColor(colors['onSurface']) ?? Colors.black,
      onBackground: _parseColor(colors['onBackground']) ?? Colors.black,
      onError: _parseColor(colors['onError']) ?? Colors.white,
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String title, String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: Colors.red.shade800,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (_debugMode) ...[
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Generate cache key for component
  String _generateCacheKey(ComponentDefinition component) {
    return '${component.id}_${component.type}_${component.properties.hashCode}_${component.style.hashCode}';
  }

  /// Clear render cache
  void clearCache() {
    _renderCache.clear();
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'cachedComponents': _renderCache.length,
      'renderingComponents': _renderingComponents.length,
    };
  }

  // Parsing utility methods
  double? _parseDouble(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  int? _parseInt(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  Color? _parseColor(dynamic value) {
    if (value is String && value.startsWith('#')) {
      return Color(int.parse(value.replaceFirst('#', '0xff')));
    }
    return null;
  }

  EdgeInsets? _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map<String, dynamic>) {
      return EdgeInsets.only(
        top: _parseDouble(value['top']) ?? 0,
        right: _parseDouble(value['right']) ?? 0,
        bottom: _parseDouble(value['bottom']) ?? 0,
        left: _parseDouble(value['left']) ?? 0,
      );
    }
    return null;
  }

  Decoration? _parseDecoration(Map<String, dynamic>? style) {
    if (style == null) return null;
    
    return BoxDecoration(
      color: _parseColor(style['backgroundColor']),
      borderRadius: _parseBorderRadius(style['borderRadius']),
      border: _parseBorder(style['border']),
      boxShadow: _parseBoxShadow(style['boxShadow']),
    );
  }

  BorderRadius? _parseBorderRadius(dynamic value) {
    if (value is num) {
      return BorderRadius.circular(value.toDouble());
    }
    return null;
  }

  Border? _parseBorder(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Border.all(
        color: _parseColor(value['color']) ?? Colors.black,
        width: _parseDouble(value['width']) ?? 1.0,
      );
    }
    return null;
  }

  List<BoxShadow>? _parseBoxShadow(dynamic value) {
    if (value is Map<String, dynamic>) {
      return [
        BoxShadow(
          color: _parseColor(value['color']) ?? Colors.black26,
          offset: Offset(
            _parseDouble(value['offsetX']) ?? 0,
            _parseDouble(value['offsetY']) ?? 0,
          ),
          blurRadius: _parseDouble(value['blurRadius']) ?? 0,
          spreadRadius: _parseDouble(value['spreadRadius']) ?? 0,
        ),
      ];
    }
    return null;
  }

  Matrix4? _parseTransform(dynamic value) {
    if (value is Map<String, dynamic>) {
      final matrix = Matrix4.identity();
      
      if (value.containsKey('translateX') || value.containsKey('translateY')) {
        matrix.translate(
          _parseDouble(value['translateX']) ?? 0,
          _parseDouble(value['translateY']) ?? 0,
        );
      }
      
      if (value.containsKey('scaleX') || value.containsKey('scaleY')) {
        matrix.scale(
          _parseDouble(value['scaleX']) ?? 1,
          _parseDouble(value['scaleY']) ?? 1,
        );
      }
      
      if (value.containsKey('rotateZ')) {
        matrix.rotateZ(_parseDouble(value['rotateZ']) ?? 0);
      }
      
      return matrix;
    }
    return null;
  }

  Alignment? _parseAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'topLeft': return Alignment.topLeft;
        case 'topCenter': return Alignment.topCenter;
        case 'topRight': return Alignment.topRight;
        case 'centerLeft': return Alignment.centerLeft;
        case 'center': return Alignment.center;
        case 'centerRight': return Alignment.centerRight;
        case 'bottomLeft': return Alignment.bottomLeft;
        case 'bottomCenter': return Alignment.bottomCenter;
        case 'bottomRight': return Alignment.bottomRight;
      }
    }
    return null;
  }

  MainAxisAlignment _parseMainAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start': return MainAxisAlignment.start;
        case 'end': return MainAxisAlignment.end;
        case 'center': return MainAxisAlignment.center;
        case 'spaceBetween': return MainAxisAlignment.spaceBetween;
        case 'spaceAround': return MainAxisAlignment.spaceAround;
        case 'spaceEvenly': return MainAxisAlignment.spaceEvenly;
      }
    }
    return MainAxisAlignment.start;
  }

  CrossAxisAlignment _parseCrossAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start': return CrossAxisAlignment.start;
        case 'end': return CrossAxisAlignment.end;
        case 'center': return CrossAxisAlignment.center;
        case 'stretch': return CrossAxisAlignment.stretch;
        case 'baseline': return CrossAxisAlignment.baseline;
      }
    }
    return CrossAxisAlignment.center;
  }

  MainAxisSize _parseMainAxisSize(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'min': return MainAxisSize.min;
        case 'max': return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  StackFit _parseStackFit(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'loose': return StackFit.loose;
        case 'expand': return StackFit.expand;
        case 'passthrough': return StackFit.passthrough;
      }
    }
    return StackFit.loose;
  }

  Axis _parseAxis(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'horizontal': return Axis.horizontal;
        case 'vertical': return Axis.vertical;
      }
    }
    return Axis.vertical;
  }

  WrapAlignment _parseWrapAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start': return WrapAlignment.start;
        case 'end': return WrapAlignment.end;
        case 'center': return WrapAlignment.center;
        case 'spaceBetween': return WrapAlignment.spaceBetween;
        case 'spaceAround': return WrapAlignment.spaceAround;
        case 'spaceEvenly': return WrapAlignment.spaceEvenly;
      }
    }
    return WrapAlignment.start;
  }

  FontWeight? _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'normal': return FontWeight.normal;
        case 'bold': return FontWeight.bold;
        case '100': return FontWeight.w100;
        case '200': return FontWeight.w200;
        case '300': return FontWeight.w300;
        case '400': return FontWeight.w400;
        case '500': return FontWeight.w500;
        case '600': return FontWeight.w600;
        case '700': return FontWeight.w700;
        case '800': return FontWeight.w800;
        case '900': return FontWeight.w900;
      }
    }
    return null;
  }
}

/// Riverpod provider for dynamic renderer
final dynamicRendererProvider = Provider<DynamicRenderer>((ref) {
  return DynamicRenderer();
});
