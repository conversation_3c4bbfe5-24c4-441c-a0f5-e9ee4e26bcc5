package com.uibuilder.collaboration;

import com.uibuilder.entity.UIConfiguration;
import com.uibuilder.entity.User;
import com.uibuilder.entity.Organization;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Advanced Collaboration Service
 * 
 * Provides enterprise-grade collaboration features including:
 * - Comment system with threading and mentions
 * - Approval workflows and review processes
 * - Version control with branching and merging
 * - Team permissions and role-based access
 * - Activity feeds and notifications
 * - Real-time collaboration tracking
 */
@Service
@Transactional
public class AdvancedCollaborationService {

    private final CommentRepository commentRepository;
    private final ApprovalWorkflowRepository workflowRepository;
    private final VersionControlRepository versionRepository;
    private final TeamPermissionRepository permissionRepository;
    private final ActivityFeedRepository activityRepository;
    private final NotificationService notificationService;
    private final WebSocketService webSocketService;

    public AdvancedCollaborationService(
            CommentRepository commentRepository,
            ApprovalWorkflowRepository workflowRepository,
            VersionControlRepository versionRepository,
            TeamPermissionRepository permissionRepository,
            ActivityFeedRepository activityRepository,
            NotificationService notificationService,
            WebSocketService webSocketService) {
        this.commentRepository = commentRepository;
        this.workflowRepository = workflowRepository;
        this.versionRepository = versionRepository;
        this.permissionRepository = permissionRepository;
        this.activityRepository = activityRepository;
        this.notificationService = notificationService;
        this.webSocketService = webSocketService;
    }

    // Comment System
    
    public Comment createComment(CreateCommentRequest request) {
        validateCommentPermissions(request.getConfigurationId(), request.getAuthorId());
        
        Comment comment = Comment.builder()
                .id(UUID.randomUUID().toString())
                .configurationId(request.getConfigurationId())
                .componentId(request.getComponentId())
                .authorId(request.getAuthorId())
                .content(request.getContent())
                .type(request.getType())
                .position(request.getPosition())
                .parentCommentId(request.getParentCommentId())
                .mentions(extractMentions(request.getContent()))
                .attachments(request.getAttachments())
                .status(CommentStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .build();
        
        comment = commentRepository.save(comment);
        
        // Send notifications for mentions
        sendMentionNotifications(comment);
        
        // Broadcast to real-time subscribers
        webSocketService.broadcastComment(comment);
        
        // Record activity
        recordActivity(ActivityType.COMMENT_CREATED, comment.getAuthorId(), 
                      comment.getConfigurationId(), comment.getId());
        
        return comment;
    }

    public Comment updateComment(String commentId, UpdateCommentRequest request) {
        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new CommentNotFoundException(commentId));
        
        validateCommentEditPermissions(comment, request.getEditorId());
        
        comment.setContent(request.getContent());
        comment.setMentions(extractMentions(request.getContent()));
        comment.setUpdatedAt(LocalDateTime.now());
        comment.setEditedBy(request.getEditorId());
        
        comment = commentRepository.save(comment);
        
        // Broadcast update
        webSocketService.broadcastCommentUpdate(comment);
        
        return comment;
    }

    public void deleteComment(String commentId, String deleterId) {
        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new CommentNotFoundException(commentId));
        
        validateCommentDeletePermissions(comment, deleterId);
        
        comment.setStatus(CommentStatus.DELETED);
        comment.setDeletedAt(LocalDateTime.now());
        comment.setDeletedBy(deleterId);
        
        commentRepository.save(comment);
        
        // Broadcast deletion
        webSocketService.broadcastCommentDeletion(commentId);
    }

    public Page<Comment> getComments(String configurationId, CommentFilter filter, Pageable pageable) {
        validateReadPermissions(configurationId, filter.getRequesterId());
        return commentRepository.findByConfigurationIdAndFilter(configurationId, filter, pageable);
    }

    public List<Comment> getCommentThread(String parentCommentId) {
        return commentRepository.findByParentCommentIdOrderByCreatedAt(parentCommentId);
    }

    // Approval Workflows
    
    public ApprovalWorkflow createApprovalWorkflow(CreateWorkflowRequest request) {
        validateWorkflowPermissions(request.getConfigurationId(), request.getCreatorId());
        
        ApprovalWorkflow workflow = ApprovalWorkflow.builder()
                .id(UUID.randomUUID().toString())
                .configurationId(request.getConfigurationId())
                .name(request.getName())
                .description(request.getDescription())
                .creatorId(request.getCreatorId())
                .steps(request.getSteps())
                .requiredApprovals(request.getRequiredApprovals())
                .autoMerge(request.isAutoMerge())
                .status(WorkflowStatus.PENDING)
                .createdAt(LocalDateTime.now())
                .build();
        
        workflow = workflowRepository.save(workflow);
        
        // Notify approvers
        notifyApprovers(workflow);
        
        // Record activity
        recordActivity(ActivityType.WORKFLOW_CREATED, workflow.getCreatorId(), 
                      workflow.getConfigurationId(), workflow.getId());
        
        return workflow;
    }

    public ApprovalWorkflow approveWorkflow(String workflowId, ApprovalRequest request) {
        ApprovalWorkflow workflow = workflowRepository.findById(workflowId)
                .orElseThrow(() -> new WorkflowNotFoundException(workflowId));
        
        validateApprovalPermissions(workflow, request.getApproverId());
        
        Approval approval = Approval.builder()
                .id(UUID.randomUUID().toString())
                .workflowId(workflowId)
                .approverId(request.getApproverId())
                .decision(request.getDecision())
                .comment(request.getComment())
                .createdAt(LocalDateTime.now())
                .build();
        
        workflow.getApprovals().add(approval);
        
        // Check if workflow is complete
        if (isWorkflowComplete(workflow)) {
            if (hasRequiredApprovals(workflow)) {
                workflow.setStatus(WorkflowStatus.APPROVED);
                if (workflow.isAutoMerge()) {
                    mergeConfiguration(workflow);
                }
            } else {
                workflow.setStatus(WorkflowStatus.REJECTED);
            }
            workflow.setCompletedAt(LocalDateTime.now());
        }
        
        workflow = workflowRepository.save(workflow);
        
        // Notify stakeholders
        notifyWorkflowUpdate(workflow, approval);
        
        return workflow;
    }

    // Version Control
    
    public ConfigurationVersion createBranch(CreateBranchRequest request) {
        validateVersionControlPermissions(request.getConfigurationId(), request.getCreatorId());
        
        UIConfiguration baseConfig = getConfiguration(request.getConfigurationId());
        
        ConfigurationVersion branch = ConfigurationVersion.builder()
                .id(UUID.randomUUID().toString())
                .configurationId(request.getConfigurationId())
                .branchName(request.getBranchName())
                .description(request.getDescription())
                .creatorId(request.getCreatorId())
                .baseVersion(request.getBaseVersion())
                .content(baseConfig.getContent())
                .status(VersionStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .build();
        
        branch = versionRepository.save(branch);
        
        // Record activity
        recordActivity(ActivityType.BRANCH_CREATED, branch.getCreatorId(), 
                      branch.getConfigurationId(), branch.getId());
        
        return branch;
    }

    public MergeResult mergeBranch(MergeRequest request) {
        validateMergePermissions(request.getSourceBranchId(), request.getTargetBranchId(), 
                               request.getMergerId());
        
        ConfigurationVersion sourceBranch = versionRepository.findById(request.getSourceBranchId())
                .orElseThrow(() -> new BranchNotFoundException(request.getSourceBranchId()));
        
        ConfigurationVersion targetBranch = versionRepository.findById(request.getTargetBranchId())
                .orElseThrow(() -> new BranchNotFoundException(request.getTargetBranchId()));
        
        // Perform three-way merge
        MergeResult result = performThreeWayMerge(sourceBranch, targetBranch, request);
        
        if (result.hasConflicts()) {
            // Create merge conflict resolution workflow
            createConflictResolutionWorkflow(result, request);
        } else {
            // Auto-merge successful
            applyMergeResult(result, targetBranch);
            
            // Record activity
            recordActivity(ActivityType.BRANCH_MERGED, request.getMergerId(), 
                          targetBranch.getConfigurationId(), result.getId());
        }
        
        return result;
    }

    // Team Permissions
    
    public TeamPermission grantPermission(GrantPermissionRequest request) {
        validatePermissionGrantPermissions(request.getConfigurationId(), request.getGranterId());
        
        TeamPermission permission = TeamPermission.builder()
                .id(UUID.randomUUID().toString())
                .configurationId(request.getConfigurationId())
                .userId(request.getUserId())
                .teamId(request.getTeamId())
                .role(request.getRole())
                .permissions(request.getPermissions())
                .grantedBy(request.getGranterId())
                .grantedAt(LocalDateTime.now())
                .expiresAt(request.getExpiresAt())
                .build();
        
        permission = permissionRepository.save(permission);
        
        // Notify user of permission grant
        notificationService.sendPermissionGrantNotification(permission);
        
        return permission;
    }

    public void revokePermission(String permissionId, String revokerId) {
        TeamPermission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new PermissionNotFoundException(permissionId));
        
        validatePermissionRevokePermissions(permission, revokerId);
        
        permission.setRevokedAt(LocalDateTime.now());
        permission.setRevokedBy(revokerId);
        permission.setStatus(PermissionStatus.REVOKED);
        
        permissionRepository.save(permission);
        
        // Notify user of permission revocation
        notificationService.sendPermissionRevokeNotification(permission);
    }

    // Activity Feed
    
    public Page<ActivityFeedItem> getActivityFeed(String configurationId, ActivityFilter filter, 
                                                 Pageable pageable) {
        validateReadPermissions(configurationId, filter.getRequesterId());
        return activityRepository.findByConfigurationIdAndFilter(configurationId, filter, pageable);
    }

    public Page<ActivityFeedItem> getUserActivityFeed(String userId, ActivityFilter filter, 
                                                     Pageable pageable) {
        return activityRepository.findByUserIdAndFilter(userId, filter, pageable);
    }

    // Helper Methods
    
    private void validateCommentPermissions(String configurationId, String userId) {
        if (!hasPermission(configurationId, userId, Permission.COMMENT)) {
            throw new InsufficientPermissionsException("User does not have comment permissions");
        }
    }

    private void validateWorkflowPermissions(String configurationId, String userId) {
        if (!hasPermission(configurationId, userId, Permission.CREATE_WORKFLOW)) {
            throw new InsufficientPermissionsException("User does not have workflow creation permissions");
        }
    }

    private void validateVersionControlPermissions(String configurationId, String userId) {
        if (!hasPermission(configurationId, userId, Permission.VERSION_CONTROL)) {
            throw new InsufficientPermissionsException("User does not have version control permissions");
        }
    }

    private boolean hasPermission(String configurationId, String userId, Permission permission) {
        return permissionRepository.hasUserPermission(configurationId, userId, permission);
    }

    private Set<String> extractMentions(String content) {
        // Extract @username mentions from content
        return MentionExtractor.extractMentions(content);
    }

    private void sendMentionNotifications(Comment comment) {
        for (String mentionedUserId : comment.getMentions()) {
            notificationService.sendMentionNotification(comment, mentionedUserId);
        }
    }

    private void recordActivity(ActivityType type, String userId, String configurationId, String entityId) {
        ActivityFeedItem activity = ActivityFeedItem.builder()
                .id(UUID.randomUUID().toString())
                .type(type)
                .userId(userId)
                .configurationId(configurationId)
                .entityId(entityId)
                .timestamp(LocalDateTime.now())
                .build();
        
        activityRepository.save(activity);
        
        // Broadcast to activity feed subscribers
        webSocketService.broadcastActivity(activity);
    }

    private UIConfiguration getConfiguration(String configurationId) {
        // This would be injected in a real implementation
        return new UIConfiguration(); // Placeholder
    }

    private MergeResult performThreeWayMerge(ConfigurationVersion source, ConfigurationVersion target, 
                                           MergeRequest request) {
        // Implement three-way merge algorithm
        return new MergeResult(); // Placeholder
    }

    private void createConflictResolutionWorkflow(MergeResult result, MergeRequest request) {
        // Create workflow for resolving merge conflicts
    }

    private void applyMergeResult(MergeResult result, ConfigurationVersion targetBranch) {
        // Apply merge changes to target branch
    }

    private boolean isWorkflowComplete(ApprovalWorkflow workflow) {
        return workflow.getApprovals().size() >= workflow.getRequiredApprovals();
    }

    private boolean hasRequiredApprovals(ApprovalWorkflow workflow) {
        long approvalCount = workflow.getApprovals().stream()
                .filter(approval -> approval.getDecision() == ApprovalDecision.APPROVED)
                .count();
        return approvalCount >= workflow.getRequiredApprovals();
    }

    private void mergeConfiguration(ApprovalWorkflow workflow) {
        // Auto-merge approved configuration changes
    }

    private void notifyApprovers(ApprovalWorkflow workflow) {
        // Send notifications to required approvers
    }

    private void notifyWorkflowUpdate(ApprovalWorkflow workflow, Approval approval) {
        // Send notifications about workflow status changes
    }
}
