version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ui-platform-postgres
    environment:
      POSTGRES_DB: uiplatform
      POSTGRES_USER: uiplatform
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U uiplatform -d uiplatform"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ui-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: ui-platform-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: ui-platform-kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # UI Platform Backend
  ui-platform-backend:
    build:
      context: ./ui-platform-backend/ui-platform-backend
      dockerfile: Dockerfile
    container_name: ui-platform-backend
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DATABASE_URL: ******************************************
      DATABASE_USERNAME: uiplatform
      DATABASE_PASSWORD: password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      JWT_SECRET: mySecretKeyForDevelopment
      CORS_ALLOWED_ORIGINS: http://localhost:3000,http://localhost:3001
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # UI Metadata Service
  ui-metadata-service:
    build:
      context: ./ui-metadata-service
      dockerfile: Dockerfile
    container_name: ui-metadata-service
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    ports:
      - "8081:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DATABASE_URL: ******************************************
      DATABASE_USERNAME: uiplatform
      DATABASE_PASSWORD: password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      JWT_SECRET: mySecretKeyForDevelopment
      CORS_ALLOWED_ORIGINS: http://localhost:3000,http://localhost:3001
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - ui-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # UI Builder Frontend
  ui-builder-frontend:
    build:
      context: ./ui-builder-frontend
      dockerfile: Dockerfile
    container_name: ui-builder-frontend
    depends_on:
      - ui-platform-backend
    ports:
      - "3000:3000"
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api/v1
      VITE_WEBSOCKET_URL: ws://localhost:8080/ws
      VITE_NODE_ENV: development
    volumes:
      - ./ui-builder-frontend/src:/app/src
      - ./ui-builder-frontend/public:/app/public
    networks:
      - ui-platform-network

  # Web Runtime
  web-runtime:
    build:
      context: ./web-runtime
      dockerfile: Dockerfile
    container_name: web-runtime
    depends_on:
      - ui-platform-backend
    ports:
      - "3001:3000"
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api/v1
      VITE_WEBSOCKET_URL: ws://localhost:8080/ws
      VITE_NODE_ENV: development
    volumes:
      - ./web-runtime/src:/app/src
      - ./web-runtime/public:/app/public
    networks:
      - ui-platform-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ui-platform-nginx
    depends_on:
      - ui-platform-backend
      - ui-metadata-service
      - ui-builder-frontend
      - web-runtime
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - ui-platform-network

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ui-platform-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ui-platform-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: ui-platform-grafana
    depends_on:
      - prometheus
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - ui-platform-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ui-platform-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
