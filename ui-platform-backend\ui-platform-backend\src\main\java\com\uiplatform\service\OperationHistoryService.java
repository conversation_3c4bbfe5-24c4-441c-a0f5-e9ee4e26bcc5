package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.collaboration.OperationalTransformation;
import com.uiplatform.collaboration.OperationalTransformation.Operation;
import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Service for managing operation history and implementing undo/redo functionality.
 */
@Service
public class OperationHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(OperationHistoryService.class);
    
    private static final String OPERATION_HISTORY_KEY = "collaboration:operation_history:";
    private static final String OPERATION_SEQUENCE_KEY = "collaboration:operation_sequence:";
    private static final String UNDO_STACK_KEY = "collaboration:undo_stack:";
    private static final String REDO_STACK_KEY = "collaboration:redo_stack:";
    private static final int MAX_HISTORY_SIZE = 1000;
    private static final int HISTORY_TTL_HOURS = 24;

    private final RedisTemplate<String, Object> redisTemplate;
    private final OperationalTransformation operationalTransformation;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    public OperationHistoryService(RedisTemplate<String, Object> redisTemplate,
                                  OperationalTransformation operationalTransformation,
                                  SimpMessagingTemplate messagingTemplate,
                                  ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.operationalTransformation = operationalTransformation;
        this.messagingTemplate = messagingTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Apply an operation and add it to history.
     */
    public OperationResult applyOperation(UUID configId, Operation operation) {
        logger.debug("Applying operation {} for config {}", operation.getType(), configId);
        
        try {
            // Get current operation sequence number
            long sequenceNumber = getNextSequenceNumber(configId);
            operation.setId(UUID.randomUUID());
            
            // Get recent operations for transformation
            List<Operation> recentOperations = getRecentOperations(configId, 10);
            
            // Transform operation against recent operations
            Operation transformedOperation = operation;
            for (Operation recentOp : recentOperations) {
                if (recentOp.getTimestamp().isAfter(operation.getTimestamp().minusSeconds(5))) {
                    transformedOperation = operationalTransformation.transform(
                        transformedOperation, recentOp, operation.getPriority()
                    );
                    if (transformedOperation == null) {
                        // Operation was nullified by transformation
                        return new OperationResult(false, "Operation conflicts with recent changes", null);
                    }
                }
            }
            
            // Store operation in history
            storeOperation(configId, sequenceNumber, transformedOperation);
            
            // Add to user's undo stack
            addToUndoStack(configId, operation.getUserId(), transformedOperation);
            
            // Clear user's redo stack (new operation invalidates redo)
            clearRedoStack(configId, operation.getUserId());
            
            // Broadcast operation to other users
            broadcastOperation(configId, transformedOperation);
            
            return new OperationResult(true, "Operation applied successfully", transformedOperation);
            
        } catch (Exception e) {
            logger.error("Error applying operation", e);
            return new OperationResult(false, "Failed to apply operation: " + e.getMessage(), null);
        }
    }

    /**
     * Undo the last operation by a user.
     */
    public OperationResult undoOperation(UUID configId, UUID userId) {
        logger.debug("Undoing operation for user {} in config {}", userId, configId);
        
        try {
            // Get last operation from user's undo stack
            Operation lastOperation = popFromUndoStack(configId, userId);
            if (lastOperation == null) {
                return new OperationResult(false, "No operations to undo", null);
            }
            
            // Create inverse operation
            Operation inverseOperation = createInverseOperation(lastOperation);
            if (inverseOperation == null) {
                return new OperationResult(false, "Cannot create inverse operation", null);
            }
            
            // Apply inverse operation
            OperationResult result = applyOperation(configId, inverseOperation);
            if (result.isSuccess()) {
                // Add original operation to redo stack
                addToRedoStack(configId, userId, lastOperation);
                
                // Broadcast undo event
                broadcastUndoEvent(configId, userId, lastOperation);
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error undoing operation", e);
            return new OperationResult(false, "Failed to undo operation: " + e.getMessage(), null);
        }
    }

    /**
     * Redo the last undone operation by a user.
     */
    public OperationResult redoOperation(UUID configId, UUID userId) {
        logger.debug("Redoing operation for user {} in config {}", userId, configId);
        
        try {
            // Get last operation from user's redo stack
            Operation lastUndoneOperation = popFromRedoStack(configId, userId);
            if (lastUndoneOperation == null) {
                return new OperationResult(false, "No operations to redo", null);
            }
            
            // Apply the operation again
            OperationResult result = applyOperation(configId, lastUndoneOperation);
            if (result.isSuccess()) {
                // Broadcast redo event
                broadcastRedoEvent(configId, userId, lastUndoneOperation);
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error redoing operation", e);
            return new OperationResult(false, "Failed to redo operation: " + e.getMessage(), null);
        }
    }

    /**
     * Get operation history for a configuration.
     */
    public List<Operation> getOperationHistory(UUID configId, int limit) {
        try {
            String key = OPERATION_HISTORY_KEY + configId;
            List<Object> operations = redisTemplate.opsForList().range(key, -limit, -1);
            
            return operations.stream()
                    .map(this::deserializeOperation)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            logger.error("Error getting operation history", e);
            return Collections.emptyList();
        }
    }

    /**
     * Get recent operations for transformation.
     */
    public List<Operation> getRecentOperations(UUID configId, int limit) {
        return getOperationHistory(configId, limit);
    }

    /**
     * Clear operation history for a configuration.
     */
    public void clearHistory(UUID configId) {
        try {
            String historyKey = OPERATION_HISTORY_KEY + configId;
            String sequenceKey = OPERATION_SEQUENCE_KEY + configId;
            
            redisTemplate.delete(historyKey);
            redisTemplate.delete(sequenceKey);
            
            logger.info("Cleared operation history for config {}", configId);
            
        } catch (Exception e) {
            logger.error("Error clearing operation history", e);
        }
    }

    /**
     * Get undo stack size for a user.
     */
    public long getUndoStackSize(UUID configId, UUID userId) {
        try {
            String key = UNDO_STACK_KEY + configId + ":" + userId;
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            logger.error("Error getting undo stack size", e);
            return 0;
        }
    }

    /**
     * Get redo stack size for a user.
     */
    public long getRedoStackSize(UUID configId, UUID userId) {
        try {
            String key = REDO_STACK_KEY + configId + ":" + userId;
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            logger.error("Error getting redo stack size", e);
            return 0;
        }
    }

    // Private helper methods

    private long getNextSequenceNumber(UUID configId) {
        String key = OPERATION_SEQUENCE_KEY + configId;
        return redisTemplate.opsForValue().increment(key);
    }

    private void storeOperation(UUID configId, long sequenceNumber, Operation operation) {
        try {
            String key = OPERATION_HISTORY_KEY + configId;
            String serializedOperation = objectMapper.writeValueAsString(operation);
            
            // Add to list
            redisTemplate.opsForList().rightPush(key, serializedOperation);
            
            // Trim list to max size
            redisTemplate.opsForList().trim(key, -MAX_HISTORY_SIZE, -1);
            
            // Set TTL
            redisTemplate.expire(key, HISTORY_TTL_HOURS, TimeUnit.HOURS);
            
        } catch (Exception e) {
            logger.error("Error storing operation", e);
        }
    }

    private void addToUndoStack(UUID configId, UUID userId, Operation operation) {
        try {
            String key = UNDO_STACK_KEY + configId + ":" + userId;
            String serializedOperation = objectMapper.writeValueAsString(operation);
            
            redisTemplate.opsForList().rightPush(key, serializedOperation);
            redisTemplate.opsForList().trim(key, -50, -1); // Keep last 50 operations
            redisTemplate.expire(key, HISTORY_TTL_HOURS, TimeUnit.HOURS);
            
        } catch (Exception e) {
            logger.error("Error adding to undo stack", e);
        }
    }

    private void addToRedoStack(UUID configId, UUID userId, Operation operation) {
        try {
            String key = REDO_STACK_KEY + configId + ":" + userId;
            String serializedOperation = objectMapper.writeValueAsString(operation);
            
            redisTemplate.opsForList().rightPush(key, serializedOperation);
            redisTemplate.opsForList().trim(key, -50, -1); // Keep last 50 operations
            redisTemplate.expire(key, HISTORY_TTL_HOURS, TimeUnit.HOURS);
            
        } catch (Exception e) {
            logger.error("Error adding to redo stack", e);
        }
    }

    private Operation popFromUndoStack(UUID configId, UUID userId) {
        try {
            String key = UNDO_STACK_KEY + configId + ":" + userId;
            Object operation = redisTemplate.opsForList().rightPop(key);
            return deserializeOperation(operation);
        } catch (Exception e) {
            logger.error("Error popping from undo stack", e);
            return null;
        }
    }

    private Operation popFromRedoStack(UUID configId, UUID userId) {
        try {
            String key = REDO_STACK_KEY + configId + ":" + userId;
            Object operation = redisTemplate.opsForList().rightPop(key);
            return deserializeOperation(operation);
        } catch (Exception e) {
            logger.error("Error popping from redo stack", e);
            return null;
        }
    }

    private void clearRedoStack(UUID configId, UUID userId) {
        try {
            String key = REDO_STACK_KEY + configId + ":" + userId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error("Error clearing redo stack", e);
        }
    }

    private Operation deserializeOperation(Object serializedOperation) {
        try {
            if (serializedOperation instanceof String) {
                return objectMapper.readValue((String) serializedOperation, Operation.class);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error deserializing operation", e);
            return null;
        }
    }

    private Operation createInverseOperation(Operation operation) {
        // This is a simplified implementation
        // Real implementation would create proper inverse operations
        switch (operation.getType()) {
            case INSERT:
                // Inverse of insert is delete
                OperationalTransformation.DeleteOperation delete = new OperationalTransformation.DeleteOperation();
                delete.setElementId(operation.getElementId());
                delete.setUserId(operation.getUserId());
                delete.setTimestamp(LocalDateTime.now());
                return delete;
                
            case DELETE:
                // Inverse of delete is insert (would need original content)
                // This is complex and would require storing original content
                return null;
                
            case UPDATE:
                // Inverse of update is another update with previous values
                // Would need to store previous values
                return null;
                
            default:
                return null;
        }
    }

    private void broadcastOperation(UUID configId, Operation operation) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.OPERATION_APPLIED);
            event.setUserId(operation.getUserId());
            event.setConfigId(configId);
            event.setData(operation);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/operations", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting operation", e);
        }
    }

    private void broadcastUndoEvent(UUID configId, UUID userId, Operation operation) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.UNDO_PERFORMED);
            event.setUserId(userId);
            event.setConfigId(configId);
            event.setData(operation);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/operations", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting undo event", e);
        }
    }

    private void broadcastRedoEvent(UUID configId, UUID userId, Operation operation) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.REDO_PERFORMED);
            event.setUserId(userId);
            event.setConfigId(configId);
            event.setData(operation);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/operations", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting redo event", e);
        }
    }

    // Result class

    public static class OperationResult {
        private final boolean success;
        private final String message;
        private final Operation operation;
        
        public OperationResult(boolean success, String message, Operation operation) {
            this.success = success;
            this.message = message;
            this.operation = operation;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Operation getOperation() { return operation; }
    }
}
