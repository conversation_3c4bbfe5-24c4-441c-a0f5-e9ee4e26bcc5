package com.uiplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * Role entity for role-based access control (RBAC).
 */
@Entity
@Table(name = "roles", indexes = {
    @Index(name = "idx_role_name", columnList = "name", unique = true)
})
public class Role extends BaseEntity {

    @NotBlank
    @Size(max = 50)
    @Column(name = "name", nullable = false, unique = true, length = 50)
    private String name;

    @Size(max = 200)
    @Column(name = "description", length = 200)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private RoleType type = RoleType.CUSTOM;

    @Column(name = "system_role", nullable = false)
    private Boolean systemRole = false;

    // Relationships
    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();

    // Constructors
    public Role() {}

    public Role(String name, String description, RoleType type) {
        this.name = name;
        this.description = description;
        this.type = type;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public RoleType getType() {
        return type;
    }

    public void setType(RoleType type) {
        this.type = type;
    }

    public Boolean getSystemRole() {
        return systemRole;
    }

    public void setSystemRole(Boolean systemRole) {
        this.systemRole = systemRole;
    }

    public Set<User> getUsers() {
        return users;
    }

    public void setUsers(Set<User> users) {
        this.users = users;
    }

    public Set<Permission> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<Permission> permissions) {
        this.permissions = permissions;
    }

    // Utility methods
    public void addPermission(Permission permission) {
        this.permissions.add(permission);
        permission.getRoles().add(this);
    }

    public void removePermission(Permission permission) {
        this.permissions.remove(permission);
        permission.getRoles().remove(this);
    }

    public boolean hasPermission(String permissionName) {
        return permissions.stream()
                .anyMatch(permission -> permission.getName().equals(permissionName));
    }

    // Enums
    public enum RoleType {
        SYSTEM,     // System-defined roles (ADMIN, USER, etc.)
        CUSTOM,     // Organization-defined custom roles
        TEMPLATE    // Template-based roles
    }

    // Static factory methods for common roles
    public static Role createAdminRole() {
        Role role = new Role("ADMIN", "System Administrator", RoleType.SYSTEM);
        role.setSystemRole(true);
        return role;
    }

    public static Role createUserRole() {
        Role role = new Role("USER", "Regular User", RoleType.SYSTEM);
        role.setSystemRole(true);
        return role;
    }

    public static Role createDesignerRole() {
        Role role = new Role("DESIGNER", "UI Designer", RoleType.SYSTEM);
        role.setSystemRole(true);
        return role;
    }

    public static Role createViewerRole() {
        Role role = new Role("VIEWER", "Read-only Viewer", RoleType.SYSTEM);
        role.setSystemRole(true);
        return role;
    }
}
