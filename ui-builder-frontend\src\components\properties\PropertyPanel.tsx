import React, { useMemo, useCallback } from 'react';
import { Tabs, Card, Empty, Button } from 'antd';
import { SettingOutlined, BgColorsOutlined, LayoutOutlined, CodeOutlined } from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@store/index';
import { updateComponent } from '@store/slices/uiBuilderSlice';
import { UIComponent } from '@types/index';

// Property editors
import GeneralProperties from './GeneralProperties';
import StyleProperties from './StyleProperties';
import LayoutProperties from './LayoutProperties';
import AdvancedProperties from './AdvancedProperties';

// Utils
import { getComponentDefinition } from '@utils/componentLibrary';

const { TabPane } = Tabs;

interface PropertyPanelProps {
  className?: string;
}

const PropertyPanel: React.FC<PropertyPanelProps> = ({ className = '' }) => {
  const dispatch = useAppDispatch();
  const { 
    selection, 
    currentConfiguration 
  } = useAppSelector(state => state.uiBuilder);

  // Get selected component
  const selectedComponent = useMemo(() => {
    if (selection.selectedComponentIds.length !== 1 || !currentConfiguration) {
      return null;
    }

    const componentId = selection.selectedComponentIds[0];
    return findComponentById(currentConfiguration.components, componentId);
  }, [selection.selectedComponentIds, currentConfiguration]);

  // Get component definition for property schema
  const componentDefinition = useMemo(() => {
    if (!selectedComponent) return null;
    return getComponentDefinition(selectedComponent.type);
  }, [selectedComponent]);

  // Handle property update
  const handlePropertyUpdate = useCallback((
    property: string, 
    value: any, 
    category: 'properties' | 'styles' = 'properties'
  ) => {
    if (!selectedComponent) return;

    const updates: Partial<UIComponent> = {};
    
    if (category === 'properties') {
      updates.properties = {
        ...selectedComponent.properties,
        [property]: value,
      };
    } else if (category === 'styles') {
      updates.styles = {
        ...selectedComponent.styles,
        [property]: value,
      };
    }

    dispatch(updateComponent({
      id: selectedComponent.id,
      updates,
    }));
  }, [selectedComponent, dispatch]);

  // Handle multiple property updates
  const handleMultiplePropertyUpdates = useCallback((
    updates: Record<string, any>,
    category: 'properties' | 'styles' = 'properties'
  ) => {
    if (!selectedComponent) return;

    const componentUpdates: Partial<UIComponent> = {};
    
    if (category === 'properties') {
      componentUpdates.properties = {
        ...selectedComponent.properties,
        ...updates,
      };
    } else if (category === 'styles') {
      componentUpdates.styles = {
        ...selectedComponent.styles,
        ...updates,
      };
    }

    dispatch(updateComponent({
      id: selectedComponent.id,
      updates: componentUpdates,
    }));
  }, [selectedComponent, dispatch]);

  // Handle position/size updates
  const handleLayoutUpdate = useCallback((updates: {
    position?: Partial<typeof selectedComponent.position>;
    size?: Partial<typeof selectedComponent.size>;
    constraints?: Partial<typeof selectedComponent.constraints>;
  }) => {
    if (!selectedComponent) return;

    const componentUpdates: Partial<UIComponent> = {};

    if (updates.position) {
      componentUpdates.position = {
        ...selectedComponent.position,
        ...updates.position,
      };
    }

    if (updates.size) {
      componentUpdates.size = {
        ...selectedComponent.size,
        ...updates.size,
      };
    }

    if (updates.constraints) {
      componentUpdates.constraints = {
        ...selectedComponent.constraints,
        ...updates.constraints,
      };
    }

    dispatch(updateComponent({
      id: selectedComponent.id,
      updates: componentUpdates,
    }));
  }, [selectedComponent, dispatch]);

  // Render empty state
  if (!selectedComponent) {
    return (
      <div className={`flex flex-col h-full bg-white ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Properties</h3>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div className="text-center">
                <div className="text-gray-500 mb-2">No component selected</div>
                <div className="text-sm text-gray-400">
                  Select a component on the canvas to edit its properties
                </div>
              </div>
            }
          />
        </div>
      </div>
    );
  }

  // Render multi-selection state
  if (selection.selectedComponentIds.length > 1) {
    return (
      <div className={`flex flex-col h-full bg-white ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Properties</h3>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              {selection.selectedComponentIds.length} components selected
            </div>
            <div className="text-sm text-gray-400 mb-4">
              Multi-component editing coming soon
            </div>
            <Button 
              type="primary" 
              onClick={() => {
                // TODO: Implement bulk property editing
              }}
            >
              Edit Common Properties
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Properties</h3>
        <div className="text-sm text-gray-500 mt-1">
          {selectedComponent.displayName || selectedComponent.type}
        </div>
      </div>

      {/* Property tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs
          defaultActiveKey="general"
          className="h-full"
          tabBarStyle={{ margin: 0, padding: '0 16px' }}
        >
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                General
              </span>
            }
            key="general"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <GeneralProperties
                component={selectedComponent}
                definition={componentDefinition}
                onUpdate={handlePropertyUpdate}
                onMultipleUpdate={handleMultiplePropertyUpdates}
              />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BgColorsOutlined />
                Style
              </span>
            }
            key="style"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <StyleProperties
                component={selectedComponent}
                definition={componentDefinition}
                onUpdate={(property, value) => handlePropertyUpdate(property, value, 'styles')}
                onMultipleUpdate={(updates) => handleMultiplePropertyUpdates(updates, 'styles')}
              />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <LayoutOutlined />
                Layout
              </span>
            }
            key="layout"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <LayoutProperties
                component={selectedComponent}
                onUpdate={handleLayoutUpdate}
              />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <CodeOutlined />
                Advanced
              </span>
            }
            key="advanced"
            className="h-full overflow-y-auto"
          >
            <div className="p-4">
              <AdvancedProperties
                component={selectedComponent}
                definition={componentDefinition}
                onUpdate={handlePropertyUpdate}
                onMultipleUpdate={handleMultiplePropertyUpdates}
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

// Helper function to find component by ID
function findComponentById(components: UIComponent[], id: string): UIComponent | null {
  for (const component of components) {
    if (component.id === id) {
      return component;
    }
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }
  return null;
}

export default PropertyPanel;
