{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@ui-builder/design-tokens": ["packages/design-tokens/src/index.ts"], "@ui-builder/react-components": ["packages/react-components/src/index.ts"], "@ui-builder/icons": ["packages/icons/src/index.ts"], "@ui-builder/testing-utilities": ["packages/testing-utilities/src/index.ts"], "@ui-builder/core": ["packages/core/src/index.ts"], "@ui-builder/theming": ["packages/theming/src/index.ts"], "@ui-builder/accessibility": ["packages/accessibility/src/index.ts"], "@ui-builder/performance": ["packages/performance/src/index.ts"]}, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "jsx": "react-jsx", "allowJs": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "exclude": ["node_modules", "tmp"]}