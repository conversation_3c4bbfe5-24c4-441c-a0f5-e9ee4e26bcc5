import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from './utils';

const iconVariants = cva(
  'inline-flex items-center justify-center shrink-0',
  {
    variants: {
      size: {
        xs: 'h-3 w-3',
        sm: 'h-4 w-4',
        md: 'h-5 w-5',
        lg: 'h-6 w-6',
        xl: 'h-8 w-8',
        '2xl': 'h-10 w-10',
      },
      color: {
        current: 'text-current',
        primary: 'text-primary',
        secondary: 'text-secondary',
        success: 'text-success',
        warning: 'text-warning',
        error: 'text-error',
        muted: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      size: 'md',
      color: 'current',
    },
  }
);

export interface IconProps
  extends React.SVGProps<SVGSVGElement>,
    VariantProps<typeof iconVariants> {
  /** Icon name for accessibility */
  name?: string;
  /** Whether icon is decorative (hidden from screen readers) */
  decorative?: boolean;
}

const Icon = forwardRef<SVGSVGElement, IconProps>(
  (
    {
      className,
      size,
      color,
      name,
      decorative = false,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <svg
        ref={ref}
        className={cn(iconVariants({ size, color, className }))}
        fill="currentColor"
        viewBox="0 0 24 24"
        aria-hidden={decorative}
        aria-label={name}
        role={decorative ? 'presentation' : 'img'}
        {...props}
      >
        {children}
      </svg>
    );
  }
);

Icon.displayName = 'Icon';

export { Icon, iconVariants };

// Icon wrapper component for consistent sizing and styling
export interface IconWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Icon size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Background color */
  background?: 'none' | 'muted' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  /** Shape */
  shape?: 'square' | 'circle';
  /** Icon content */
  children: React.ReactNode;
}

const iconWrapperVariants = cva(
  'inline-flex items-center justify-center shrink-0',
  {
    variants: {
      size: {
        xs: 'h-6 w-6',
        sm: 'h-8 w-8',
        md: 'h-10 w-10',
        lg: 'h-12 w-12',
        xl: 'h-16 w-16',
        '2xl': 'h-20 w-20',
      },
      background: {
        none: '',
        muted: 'bg-muted',
        primary: 'bg-primary text-primary-foreground',
        secondary: 'bg-secondary text-secondary-foreground',
        success: 'bg-success text-success-foreground',
        warning: 'bg-warning text-warning-foreground',
        error: 'bg-error text-error-foreground',
      },
      shape: {
        square: 'rounded-md',
        circle: 'rounded-full',
      },
    },
    defaultVariants: {
      size: 'md',
      background: 'none',
      shape: 'square',
    },
  }
);

export const IconWrapper = forwardRef<HTMLDivElement, IconWrapperProps>(
  ({ className, size, background, shape, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(iconWrapperVariants({ size, background, shape, className }))}
        {...props}
      >
        {children}
      </div>
    );
  }
);

IconWrapper.displayName = 'IconWrapper';

// Icon button component
export interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Icon content */
  icon: React.ReactNode;
  /** Button size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Button variant */
  variant?: 'ghost' | 'outline' | 'solid';
  /** Shape */
  shape?: 'square' | 'circle';
  /** Loading state */
  loading?: boolean;
  /** Accessibility label */
  'aria-label': string;
}

const iconButtonVariants = cva(
  'inline-flex items-center justify-center shrink-0 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        solid: 'bg-primary text-primary-foreground hover:bg-primary/90',
      },
      size: {
        xs: 'h-6 w-6',
        sm: 'h-8 w-8',
        md: 'h-10 w-10',
        lg: 'h-12 w-12',
        xl: 'h-14 w-14',
      },
      shape: {
        square: 'rounded-md',
        circle: 'rounded-full',
      },
    },
    defaultVariants: {
      variant: 'ghost',
      size: 'md',
      shape: 'square',
    },
  }
);

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  (
    {
      className,
      variant,
      size,
      shape,
      icon,
      loading,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <button
        ref={ref}
        className={cn(iconButtonVariants({ variant, size, shape, className }))}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <Icon size={size === 'xs' ? 'xs' : size === 'sm' ? 'sm' : 'md'} name="Loading">
            <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83" />
          </Icon>
        ) : (
          icon
        )}
        {children}
      </button>
    );
  }
);

IconButton.displayName = 'IconButton';
