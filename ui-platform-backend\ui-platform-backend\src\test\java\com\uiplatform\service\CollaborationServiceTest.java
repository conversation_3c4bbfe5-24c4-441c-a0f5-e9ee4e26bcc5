package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.ActiveUser;
import com.uiplatform.dto.collaboration.CursorPosition;
import com.uiplatform.dto.collaboration.UserSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CollaborationService.
 */
@ExtendWith(MockitoExtension.class)
class CollaborationServiceTest {

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private PresenceService presenceService;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private CollaborationService collaborationService;

    private UUID testUserId;
    private String testUsername;
    private UUID testOrganizationId;
    private UUID testConfigId;
    private String testSessionId;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testUsername = "testuser";
        testOrganizationId = UUID.randomUUID();
        testConfigId = UUID.randomUUID();
        testSessionId = "test-session-123";

        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void handleUserConnect_Success() {
        // Act
        collaborationService.handleUserConnect(testUserId, testUsername, testOrganizationId, testSessionId);

        // Assert
        verify(presenceService).updateUserPresence(
            eq(testUserId), eq(testUsername), eq(testOrganizationId), 
            eq("active"), isNull(), isNull()
        );
    }

    @Test
    void handleUserDisconnect_Success() {
        // Act
        collaborationService.handleUserDisconnect(testUserId, testUsername, testOrganizationId, testSessionId);

        // Assert
        verify(presenceService).setUserOffline(testUserId, testUsername, testOrganizationId);
    }

    @Test
    void handleUserJoinConfig_Success() {
        // Arrange
        UserSession mockSession = new UserSession();
        mockSession.setUserId(testUserId);
        mockSession.setUsername(testUsername);
        mockSession.setOrganizationId(testOrganizationId);
        mockSession.setSessionId(testSessionId);

        // Act
        collaborationService.handleUserJoinConfig(testUserId, testConfigId, testSessionId);

        // Assert
        verify(messagingTemplate).convertAndSend(
            eq("/topic/ui-config/" + testConfigId + "/collaboration"),
            any()
        );
    }

    @Test
    void acquireEditingLock_Success() {
        // Arrange
        String elementId = "test-element";
        when(valueOperations.setIfAbsent(anyString(), anyString(), anyLong(), any()))
                .thenReturn(true);

        // Act
        boolean result = collaborationService.acquireEditingLock(testUserId, testConfigId, elementId);

        // Assert
        assertTrue(result);
        verify(messagingTemplate).convertAndSend(
            eq("/topic/ui-config/" + testConfigId + "/collaboration"),
            any()
        );
    }

    @Test
    void acquireEditingLock_AlreadyLocked() {
        // Arrange
        String elementId = "test-element";
        when(valueOperations.setIfAbsent(anyString(), anyString(), anyLong(), any()))
                .thenReturn(false);

        // Act
        boolean result = collaborationService.acquireEditingLock(testUserId, testConfigId, elementId);

        // Assert
        assertFalse(result);
    }

    @Test
    void releaseEditingLock_Success() {
        // Arrange
        String elementId = "test-element";
        when(valueOperations.get(anyString())).thenReturn(testUserId.toString());

        // Act
        collaborationService.releaseEditingLock(testUserId, testConfigId, elementId);

        // Assert
        verify(redisTemplate).delete(anyString());
        verify(messagingTemplate).convertAndSend(
            eq("/topic/ui-config/" + testConfigId + "/collaboration"),
            any()
        );
    }

    @Test
    void releaseEditingLock_NotOwner() {
        // Arrange
        String elementId = "test-element";
        UUID otherUserId = UUID.randomUUID();
        when(valueOperations.get(anyString())).thenReturn(otherUserId.toString());

        // Act
        collaborationService.releaseEditingLock(testUserId, testConfigId, elementId);

        // Assert
        verify(redisTemplate, never()).delete(anyString());
    }

    @Test
    void getActiveUsers_Success() {
        // Arrange
        when(presenceService.getActiveUsersInConfig(testConfigId))
                .thenReturn(List.of(createMockActiveUser()));

        // Act
        List<ActiveUser> result = collaborationService.getActiveUsers(testConfigId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(presenceService).getActiveUsersInConfig(testConfigId);
    }

    @Test
    void updateCursorPosition_Success() {
        // Arrange
        CursorPosition position = new CursorPosition();
        position.setX(100.0);
        position.setY(200.0);
        position.setElementId("test-element");

        // Act
        collaborationService.updateCursorPosition(testUserId, testConfigId, position);

        // Assert
        verify(redisTemplate.opsForValue()).set(anyString(), eq(position), anyLong(), any());
        verify(messagingTemplate).convertAndSend(
            eq("/topic/ui-config/" + testConfigId + "/collaboration"),
            any()
        );
    }

    @Test
    void handleUserJoinOrganization_Success() {
        // Act
        collaborationService.handleUserJoinOrganization(testUserId, testOrganizationId, testSessionId);

        // Assert
        verify(redisTemplate.opsForSet()).add(anyString(), eq(testSessionId));
    }

    @Test
    void handleUserUnsubscribe_Success() {
        // Arrange
        String subscriptionId = "test-subscription";

        // Act
        collaborationService.handleUserUnsubscribe(testUserId, subscriptionId, testSessionId);

        // Assert - This method currently doesn't do much, but we verify it doesn't throw
        assertDoesNotThrow(() -> 
            collaborationService.handleUserUnsubscribe(testUserId, subscriptionId, testSessionId)
        );
    }

    // Helper methods

    private ActiveUser createMockActiveUser() {
        ActiveUser user = new ActiveUser();
        user.setUserId(testUserId);
        user.setUsername(testUsername);
        user.setStatus("active");
        user.setConnectedAt(LocalDateTime.now());
        user.setLastActivity(LocalDateTime.now());
        return user;
    }
}
