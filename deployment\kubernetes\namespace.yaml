apiVersion: v1
kind: Namespace
metadata:
  name: ui-builder
  labels:
    name: ui-builder
    app: ui-builder-platform
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-builder-config
  namespace: ui-builder
data:
  # Database configuration
  POSTGRES_DB: "ui_builder"
  POSTGRES_USER: "ui_builder"
  
  # Redis configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # Service discovery
  EUREKA_URL: "http://eureka-service:8761/eureka"
  
  # API Gateway
  GATEWAY_PORT: "8080"
  
  # JWT configuration
  JWT_EXPIRATION: "86400000"
  
  # SMTP configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  
  # MinIO configuration
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_BUCKET: "ui-builder-assets"
  
  # Application URLs
  FRONTEND_URL: "http://ui-builder-frontend"
  API_URL: "http://api-gateway-service:8080"
  
  # Monitoring
  PROMETHEUS_URL: "http://prometheus-service:9090"
  GRAFANA_URL: "http://grafana-service:3000"
  
  # Logging
  ELASTICSEARCH_URL: "http://elasticsearch-service:9200"
  KIBANA_URL: "http://kibana-service:5601"
---
apiVersion: v1
kind: Secret
metadata:
  name: ui-builder-secrets
  namespace: ui-builder
type: Opaque
data:
  # Base64 encoded secrets
  POSTGRES_PASSWORD: dWlfYnVpbGRlcl9wYXNzd29yZA== # ui_builder_password
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ= # redis_password
  JWT_SECRET: eW91ci1zZWNyZXQta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9u # your-secret-key-change-in-production
  SMTP_USERNAME: ""
  SMTP_PASSWORD: ""
  MINIO_ROOT_USER: bWluaW9hZG1pbg== # minioadmin
  MINIO_ROOT_PASSWORD: bWluaW9hZG1pbjEyMw== # minioadmin123
  GRAFANA_ADMIN_USER: YWRtaW4= # admin
  GRAFANA_ADMIN_PASSWORD: YWRtaW4xMjM= # admin123
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elasticsearch-pvc
  namespace: ui-builder
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 15Gi
  storageClassName: standard
