import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type {
  UIConfiguration,
  CreateUIConfigurationRequest,
  UpdateUIConfigurationRequest,
  UIConfigurationQueryParams,
  PaginatedResponse,
  ApiResponse,
} from '@types/index';

export const uiConfigurationApi = createApi({
  reducerPath: 'uiConfigurationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/v1/ui-configurations',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['UIConfiguration'],
  endpoints: (builder) => ({
    // Get all UI configurations
    getUIConfigurations: builder.query<
      PaginatedResponse<UIConfiguration>,
      UIConfigurationQueryParams
    >({
      query: (params) => ({
        url: '',
        params,
      }),
      providesTags: ['UIConfiguration'],
    }),

    // Get UI configuration by ID
    getUIConfiguration: builder.query<ApiResponse<UIConfiguration>, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'UIConfiguration', id }],
    }),

    // Create new UI configuration
    createUIConfiguration: builder.mutation<
      ApiResponse<UIConfiguration>,
      CreateUIConfigurationRequest
    >({
      query: (data) => ({
        url: '',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['UIConfiguration'],
    }),

    // Update UI configuration
    updateUIConfiguration: builder.mutation<
      ApiResponse<UIConfiguration>,
      { id: string; data: UpdateUIConfigurationRequest }
    >({
      query: ({ id, data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'UIConfiguration', id },
        'UIConfiguration',
      ],
    }),

    // Delete UI configuration
    deleteUIConfiguration: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['UIConfiguration'],
    }),

    // Duplicate UI configuration
    duplicateUIConfiguration: builder.mutation<
      ApiResponse<UIConfiguration>,
      { id: string; name: string }
    >({
      query: ({ id, name }) => ({
        url: `/${id}/duplicate`,
        method: 'POST',
        body: { name },
      }),
      invalidatesTags: ['UIConfiguration'],
    }),

    // Publish UI configuration
    publishUIConfiguration: builder.mutation<ApiResponse<UIConfiguration>, string>({
      query: (id) => ({
        url: `/${id}/publish`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'UIConfiguration', id },
        'UIConfiguration',
      ],
    }),

    // Unpublish UI configuration
    unpublishUIConfiguration: builder.mutation<ApiResponse<UIConfiguration>, string>({
      query: (id) => ({
        url: `/${id}/unpublish`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'UIConfiguration', id },
        'UIConfiguration',
      ],
    }),

    // Get UI configuration versions
    getUIConfigurationVersions: builder.query<
      ApiResponse<UIConfiguration[]>,
      string
    >({
      query: (id) => `/${id}/versions`,
      providesTags: (result, error, id) => [{ type: 'UIConfiguration', id }],
    }),

    // Restore UI configuration version
    restoreUIConfigurationVersion: builder.mutation<
      ApiResponse<UIConfiguration>,
      { id: string; version: string }
    >({
      query: ({ id, version }) => ({
        url: `/${id}/versions/${version}/restore`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'UIConfiguration', id },
        'UIConfiguration',
      ],
    }),

    // Export UI configuration
    exportUIConfiguration: builder.mutation<
      ApiResponse<{ downloadUrl: string }>,
      { id: string; format: string; options?: any }
    >({
      query: ({ id, format, options }) => ({
        url: `/${id}/export`,
        method: 'POST',
        body: { format, options },
      }),
    }),

    // Import UI configuration
    importUIConfiguration: builder.mutation<
      ApiResponse<UIConfiguration>,
      { file: File; organizationId: string }
    >({
      query: ({ file, organizationId }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('organizationId', organizationId);
        
        return {
          url: '/import',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ['UIConfiguration'],
    }),

    // Get UI configuration analytics
    getUIConfigurationAnalytics: builder.query<
      ApiResponse<any>,
      { id: string; period: string }
    >({
      query: ({ id, period }) => ({
        url: `/${id}/analytics`,
        params: { period },
      }),
    }),

    // Search UI configurations
    searchUIConfigurations: builder.query<
      PaginatedResponse<UIConfiguration>,
      { query: string; filters?: any }
    >({
      query: ({ query, filters }) => ({
        url: '/search',
        params: { q: query, ...filters },
      }),
      providesTags: ['UIConfiguration'],
    }),
  }),
});

export const {
  useGetUIConfigurationsQuery,
  useGetUIConfigurationQuery,
  useCreateUIConfigurationMutation,
  useUpdateUIConfigurationMutation,
  useDeleteUIConfigurationMutation,
  useDuplicateUIConfigurationMutation,
  usePublishUIConfigurationMutation,
  useUnpublishUIConfigurationMutation,
  useGetUIConfigurationVersionsQuery,
  useRestoreUIConfigurationVersionMutation,
  useExportUIConfigurationMutation,
  useImportUIConfigurationMutation,
  useGetUIConfigurationAnalyticsQuery,
  useSearchUIConfigurationsQuery,
} = uiConfigurationApi;
