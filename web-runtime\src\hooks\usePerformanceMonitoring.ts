import { useEffect, useCallback, useRef } from 'react';
import { useRuntimeStore } from '@stores/runtimeStore';
import { PerformanceMetrics } from '@types/index';

interface PerformanceConfig {
  enableMetrics?: boolean;
  enableMemoryTracking?: boolean;
  enableRenderTracking?: boolean;
  reportInterval?: number;
  memoryThreshold?: number;
  renderTimeThreshold?: number;
}

export function usePerformanceMonitoring(config: PerformanceConfig = {}) {
  const {
    enableMetrics = true,
    enableMemoryTracking = true,
    enableRenderTracking = true,
    reportInterval = 30000, // 30 seconds
    memoryThreshold = 50 * 1024 * 1024, // 50MB
    renderTimeThreshold = 16, // 16ms (60fps)
  } = config;

  const { setPerformance } = useRuntimeStore();
  const metricsRef = useRef<PerformanceMetrics>({
    renderTime: 0,
    componentCount: 0,
    dataFetchTime: 0,
    bundleSize: 0,
    memoryUsage: 0,
  });
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<PerformanceObserver | null>(null);

  // Measure component render time
  const measureRenderTime = useCallback((componentName: string, startTime: number) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    metricsRef.current.renderTime = Math.max(metricsRef.current.renderTime, renderTime);

    if (renderTime > renderTimeThreshold) {
      console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }

    return renderTime;
  }, [renderTimeThreshold]);

  // Measure memory usage
  const measureMemoryUsage = useCallback(() => {
    if (!enableMemoryTracking || !(performance as any).memory) {
      return 0;
    }

    const memory = (performance as any).memory;
    const memoryUsage = memory.usedJSHeapSize;

    metricsRef.current.memoryUsage = memoryUsage;

    if (memoryUsage > memoryThreshold) {
      console.warn(`High memory usage detected: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }

    return memoryUsage;
  }, [enableMemoryTracking, memoryThreshold]);

  // Measure bundle size
  const measureBundleSize = useCallback(() => {
    if (!enableMetrics) return 0;

    // Estimate bundle size from loaded resources
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const jsResources = resources.filter(resource => 
      resource.name.includes('.js') || resource.name.includes('.mjs')
    );

    const bundleSize = jsResources.reduce((total, resource) => {
      return total + (resource.transferSize || 0);
    }, 0);

    metricsRef.current.bundleSize = bundleSize;
    return bundleSize;
  }, [enableMetrics]);

  // Count rendered components
  const incrementComponentCount = useCallback(() => {
    metricsRef.current.componentCount++;
  }, []);

  // Measure data fetch time
  const measureDataFetchTime = useCallback((startTime: number) => {
    const endTime = performance.now();
    const fetchTime = endTime - startTime;

    metricsRef.current.dataFetchTime = Math.max(metricsRef.current.dataFetchTime, fetchTime);
    return fetchTime;
  }, []);

  // Collect and report metrics
  const collectMetrics = useCallback(() => {
    if (!enableMetrics) return;

    const metrics: PerformanceMetrics = {
      ...metricsRef.current,
      memoryUsage: measureMemoryUsage(),
      bundleSize: measureBundleSize(),
    };

    setPerformance(metrics);

    // Log metrics in development
    if (import.meta.env.DEV) {
      console.group('Performance Metrics');
      console.log('Render Time:', `${metrics.renderTime.toFixed(2)}ms`);
      console.log('Component Count:', metrics.componentCount);
      console.log('Data Fetch Time:', `${metrics.dataFetchTime.toFixed(2)}ms`);
      console.log('Bundle Size:', `${(metrics.bundleSize / 1024).toFixed(2)}KB`);
      console.log('Memory Usage:', `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      console.groupEnd();
    }

    // Reset counters
    metricsRef.current.renderTime = 0;
    metricsRef.current.componentCount = 0;
    metricsRef.current.dataFetchTime = 0;
  }, [enableMetrics, measureMemoryUsage, measureBundleSize, setPerformance]);

  // Setup performance observer
  useEffect(() => {
    if (!enableRenderTracking || !window.PerformanceObserver) {
      return;
    }

    observerRef.current = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'measure') {
          const renderTime = entry.duration;
          metricsRef.current.renderTime = Math.max(metricsRef.current.renderTime, renderTime);
          
          if (renderTime > renderTimeThreshold) {
            console.warn(`Performance measure: ${entry.name} took ${renderTime.toFixed(2)}ms`);
          }
        }
      });
    });

    observerRef.current.observe({ entryTypes: ['measure', 'navigation', 'resource'] });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enableRenderTracking, renderTimeThreshold]);

  // Setup metrics collection interval
  useEffect(() => {
    if (!enableMetrics) return;

    intervalRef.current = setInterval(collectMetrics, reportInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enableMetrics, collectMetrics, reportInterval]);

  // Initial metrics collection
  useEffect(() => {
    if (enableMetrics) {
      // Collect initial metrics after a short delay
      setTimeout(collectMetrics, 1000);
    }
  }, [enableMetrics, collectMetrics]);

  return {
    measureRenderTime,
    measureMemoryUsage,
    measureDataFetchTime,
    incrementComponentCount,
    collectMetrics,
    getCurrentMetrics: () => ({ ...metricsRef.current }),
  };
}

// Hook for measuring component render performance
export function useRenderPerformance(componentName: string) {
  const { measureRenderTime, incrementComponentCount } = usePerformanceMonitoring();
  const startTimeRef = useRef<number>(0);

  const startMeasure = useCallback(() => {
    startTimeRef.current = performance.now();
    incrementComponentCount();
  }, [incrementComponentCount]);

  const endMeasure = useCallback(() => {
    if (startTimeRef.current > 0) {
      const renderTime = measureRenderTime(componentName, startTimeRef.current);
      startTimeRef.current = 0;
      return renderTime;
    }
    return 0;
  }, [componentName, measureRenderTime]);

  useEffect(() => {
    startMeasure();
    return endMeasure;
  }, [startMeasure, endMeasure]);

  return { startMeasure, endMeasure };
}

// Hook for measuring data fetch performance
export function useDataFetchPerformance() {
  const { measureDataFetchTime } = usePerformanceMonitoring();

  const measureFetch = useCallback(async <T>(
    fetchFn: () => Promise<T>,
    operationName?: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await fetchFn();
      const fetchTime = measureDataFetchTime(startTime);
      
      if (import.meta.env.DEV && operationName) {
        console.log(`Data fetch "${operationName}" took ${fetchTime.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const fetchTime = measureDataFetchTime(startTime);
      
      if (import.meta.env.DEV && operationName) {
        console.warn(`Data fetch "${operationName}" failed after ${fetchTime.toFixed(2)}ms`);
      }
      
      throw error;
    }
  }, [measureDataFetchTime]);

  return { measureFetch };
}

// Hook for monitoring Core Web Vitals
export function useCoreWebVitals() {
  const vitalsRef = useRef<{
    FCP?: number;
    LCP?: number;
    FID?: number;
    CLS?: number;
  }>({});

  useEffect(() => {
    if (!window.PerformanceObserver) return;

    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcp = entries[entries.length - 1];
      vitalsRef.current.FCP = fcp.startTime;
      console.log('FCP:', fcp.startTime);
    });
    fcpObserver.observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lcp = entries[entries.length - 1];
      vitalsRef.current.LCP = lcp.startTime;
      console.log('LCP:', lcp.startTime);
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        vitalsRef.current.FID = entry.processingStart - entry.startTime;
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          vitalsRef.current.CLS = clsValue;
          console.log('CLS:', clsValue);
        }
      });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });

    return () => {
      fcpObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
    };
  }, []);

  return vitalsRef.current;
}
