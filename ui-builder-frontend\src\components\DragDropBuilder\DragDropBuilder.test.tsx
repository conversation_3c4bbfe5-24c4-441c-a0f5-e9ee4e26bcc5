import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { DndContext } from '@dnd-kit/core';
import { configureStore } from '@reduxjs/toolkit';
import { DragDropBuilder } from './DragDropBuilder';
import { uiBuilderSlice } from '../../store/slices/uiBuilderSlice';
import { ComponentLibrary } from '../ComponentLibrary/ComponentLibrary';
import { Canvas } from '../Canvas/Canvas';
import { PropertiesPanel } from '../PropertiesPanel/PropertiesPanel';

// Mock the child components
jest.mock('../ComponentLibrary/ComponentLibrary', () => ({
  ComponentLibrary: jest.fn(() => <div data-testid="component-library">Component Library</div>)
}));

jest.mock('../Canvas/Canvas', () => ({
  Canvas: jest.fn(() => <div data-testid="canvas">Canvas</div>)
}));

jest.mock('../PropertiesPanel/PropertiesPanel', () => ({
  PropertiesPanel: jest.fn(() => <div data-testid="properties-panel">Properties Panel</div>)
}));

// Mock DnD Kit
jest.mock('@dnd-kit/core', () => ({
  DndContext: jest.fn(({ children }) => <div data-testid="dnd-context">{children}</div>),
  useDraggable: jest.fn(() => ({
    attributes: {},
    listeners: {},
    setNodeRef: jest.fn(),
    transform: null,
    isDragging: false
  })),
  useDroppable: jest.fn(() => ({
    setNodeRef: jest.fn(),
    isOver: false
  }))
}));

const mockStore = configureStore({
  reducer: {
    uiBuilder: uiBuilderSlice.reducer
  },
  preloadedState: {
    uiBuilder: {
      currentConfig: {
        id: 'test-config',
        name: 'Test Configuration',
        components: [],
        metadata: {}
      },
      selectedComponent: null,
      draggedComponent: null,
      isLoading: false,
      error: null,
      history: [],
      historyIndex: -1
    }
  }
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('DragDropBuilder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all main sections', () => {
    renderWithProviders(<DragDropBuilder />);

    expect(screen.getByTestId('component-library')).toBeInTheDocument();
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
    expect(screen.getByTestId('properties-panel')).toBeInTheDocument();
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument();
  });

  it('renders with correct layout structure', () => {
    renderWithProviders(<DragDropBuilder />);

    const builder = screen.getByTestId('drag-drop-builder');
    expect(builder).toHaveClass('drag-drop-builder');
    
    const sidebar = screen.getByTestId('builder-sidebar');
    expect(sidebar).toHaveClass('builder-sidebar');
    
    const mainArea = screen.getByTestId('builder-main');
    expect(mainArea).toHaveClass('builder-main');
    
    const propertiesArea = screen.getByTestId('builder-properties');
    expect(propertiesArea).toHaveClass('builder-properties');
  });

  it('handles drag start event', async () => {
    const mockDragStart = jest.fn();
    (DndContext as jest.Mock).mockImplementation(({ onDragStart, children }) => (
      <div data-testid="dnd-context" onClick={() => onDragStart?.({ active: { id: 'test-component' } })}>
        {children}
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const dndContext = screen.getByTestId('dnd-context');
    fireEvent.click(dndContext);

    // Verify that the drag start was handled
    expect(DndContext).toHaveBeenCalledWith(
      expect.objectContaining({
        onDragStart: expect.any(Function)
      }),
      {}
    );
  });

  it('handles drag end event with successful drop', async () => {
    const mockDragEnd = jest.fn();
    (DndContext as jest.Mock).mockImplementation(({ onDragEnd, children }) => (
      <div 
        data-testid="dnd-context" 
        onClick={() => onDragEnd?.({ 
          active: { id: 'button-component' }, 
          over: { id: 'canvas-drop-zone' } 
        })}
      >
        {children}
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const dndContext = screen.getByTestId('dnd-context');
    fireEvent.click(dndContext);

    await waitFor(() => {
      expect(DndContext).toHaveBeenCalledWith(
        expect.objectContaining({
          onDragEnd: expect.any(Function)
        }),
        {}
      );
    });
  });

  it('handles drag end event with no drop target', async () => {
    const mockDragEnd = jest.fn();
    (DndContext as jest.Mock).mockImplementation(({ onDragEnd, children }) => (
      <div 
        data-testid="dnd-context" 
        onClick={() => onDragEnd?.({ 
          active: { id: 'button-component' }, 
          over: null 
        })}
      >
        {children}
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const dndContext = screen.getByTestId('dnd-context');
    fireEvent.click(dndContext);

    // Should not throw error when no drop target
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument();
  });

  it('passes correct props to ComponentLibrary', () => {
    renderWithProviders(<DragDropBuilder />);

    expect(ComponentLibrary).toHaveBeenCalledWith(
      expect.objectContaining({
        onComponentSelect: expect.any(Function),
        searchQuery: '',
        selectedCategory: 'all'
      }),
      {}
    );
  });

  it('passes correct props to Canvas', () => {
    renderWithProviders(<DragDropBuilder />);

    expect(Canvas).toHaveBeenCalledWith(
      expect.objectContaining({
        components: [],
        onComponentSelect: expect.any(Function),
        onComponentUpdate: expect.any(Function),
        onComponentDelete: expect.any(Function)
      }),
      {}
    );
  });

  it('passes correct props to PropertiesPanel', () => {
    renderWithProviders(<DragDropBuilder />);

    expect(PropertiesPanel).toHaveBeenCalledWith(
      expect.objectContaining({
        selectedComponent: null,
        onPropertyChange: expect.any(Function),
        onStyleChange: expect.any(Function)
      }),
      {}
    );
  });

  it('handles component selection from library', () => {
    const mockOnComponentSelect = jest.fn();
    (ComponentLibrary as jest.Mock).mockImplementation(({ onComponentSelect }) => (
      <div 
        data-testid="component-library" 
        onClick={() => onComponentSelect?.({ type: 'button', name: 'Button' })}
      >
        Component Library
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const componentLibrary = screen.getByTestId('component-library');
    fireEvent.click(componentLibrary);

    expect(ComponentLibrary).toHaveBeenCalledWith(
      expect.objectContaining({
        onComponentSelect: expect.any(Function)
      }),
      {}
    );
  });

  it('handles component selection from canvas', () => {
    const mockOnComponentSelect = jest.fn();
    (Canvas as jest.Mock).mockImplementation(({ onComponentSelect }) => (
      <div 
        data-testid="canvas" 
        onClick={() => onComponentSelect?.('component-1')}
      >
        Canvas
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const canvas = screen.getByTestId('canvas');
    fireEvent.click(canvas);

    expect(Canvas).toHaveBeenCalledWith(
      expect.objectContaining({
        onComponentSelect: expect.any(Function)
      }),
      {}
    );
  });

  it('handles property changes from properties panel', () => {
    const mockOnPropertyChange = jest.fn();
    (PropertiesPanel as jest.Mock).mockImplementation(({ onPropertyChange }) => (
      <div 
        data-testid="properties-panel" 
        onClick={() => onPropertyChange?.('component-1', 'text', 'New Text')}
      >
        Properties Panel
      </div>
    ));

    renderWithProviders(<DragDropBuilder />);

    const propertiesPanel = screen.getByTestId('properties-panel');
    fireEvent.click(propertiesPanel);

    expect(PropertiesPanel).toHaveBeenCalledWith(
      expect.objectContaining({
        onPropertyChange: expect.any(Function)
      }),
      {}
    );
  });

  it('handles keyboard shortcuts', () => {
    renderWithProviders(<DragDropBuilder />);

    // Test undo shortcut
    fireEvent.keyDown(document, { key: 'z', ctrlKey: true });
    
    // Test redo shortcut
    fireEvent.keyDown(document, { key: 'y', ctrlKey: true });
    
    // Test delete shortcut
    fireEvent.keyDown(document, { key: 'Delete' });

    // Verify the component is still rendered (shortcuts handled gracefully)
    expect(screen.getByTestId('drag-drop-builder')).toBeInTheDocument();
  });

  it('handles window resize events', () => {
    renderWithProviders(<DragDropBuilder />);

    // Simulate window resize
    global.innerWidth = 1200;
    global.innerHeight = 800;
    fireEvent(window, new Event('resize'));

    expect(screen.getByTestId('drag-drop-builder')).toBeInTheDocument();
  });

  it('renders loading state correctly', () => {
    const loadingStore = configureStore({
      reducer: {
        uiBuilder: uiBuilderSlice.reducer
      },
      preloadedState: {
        uiBuilder: {
          currentConfig: null,
          selectedComponent: null,
          draggedComponent: null,
          isLoading: true,
          error: null,
          history: [],
          historyIndex: -1
        }
      }
    });

    render(
      <Provider store={loadingStore}>
        <DragDropBuilder />
      </Provider>
    );

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders error state correctly', () => {
    const errorStore = configureStore({
      reducer: {
        uiBuilder: uiBuilderSlice.reducer
      },
      preloadedState: {
        uiBuilder: {
          currentConfig: null,
          selectedComponent: null,
          draggedComponent: null,
          isLoading: false,
          error: 'Failed to load configuration',
          history: [],
          historyIndex: -1
        }
      }
    });

    render(
      <Provider store={errorStore}>
        <DragDropBuilder />
      </Provider>
    );

    expect(screen.getByTestId('error-message')).toBeInTheDocument();
    expect(screen.getByText('Failed to load configuration')).toBeInTheDocument();
  });

  it('maintains responsive layout on different screen sizes', () => {
    // Test mobile layout
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });

    renderWithProviders(<DragDropBuilder />);

    const builder = screen.getByTestId('drag-drop-builder');
    expect(builder).toHaveClass('drag-drop-builder');

    // Test desktop layout
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1920,
    });

    fireEvent(window, new Event('resize'));

    expect(builder).toHaveClass('drag-drop-builder');
  });
});
