package com.uibuilder.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uibuilder.config.TestConfig;
import com.uibuilder.dto.ConfigurationDto;
import com.uibuilder.dto.ComponentDto;
import com.uibuilder.entity.Configuration;
import com.uibuilder.repository.ConfigurationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RedisContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(
    classes = TestConfig.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
@Testcontainers
@AutoConfigureWebMvc
@Transactional
public class ConfigurationServiceIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("ui_builder_test")
            .withUsername("test")
            .withPassword("test");

    @Container
    static RedisContainer redis = new RedisContainer("redis:7")
            .withExposedPorts(6379);

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
    }

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ConfigurationRepository configurationRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private String baseUrl;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port + "/api/configurations";
        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("test-jwt-token"); // Mock JWT token
        
        // Clean up database
        configurationRepository.deleteAll();
    }

    @Test
    void shouldCreateConfiguration() {
        // Given
        ConfigurationDto configDto = createTestConfigurationDto();

        // When
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl,
            HttpMethod.POST,
            new HttpEntity<>(configDto, headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getName()).isEqualTo("Test Configuration");
        assertThat(response.getBody().getId()).isNotNull();
        assertThat(response.getBody().getComponents()).hasSize(2);
    }

    @Test
    void shouldGetConfigurationById() {
        // Given
        Configuration savedConfig = configurationRepository.save(createTestConfiguration());

        // When
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl + "/" + savedConfig.getId(),
            HttpMethod.GET,
            new HttpEntity<>(headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getId()).isEqualTo(savedConfig.getId());
        assertThat(response.getBody().getName()).isEqualTo("Test Configuration");
    }

    @Test
    void shouldReturnNotFoundForNonExistentConfiguration() {
        // When
        ResponseEntity<String> response = restTemplate.exchange(
            baseUrl + "/999",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            String.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    void shouldUpdateConfiguration() {
        // Given
        Configuration savedConfig = configurationRepository.save(createTestConfiguration());
        ConfigurationDto updateDto = new ConfigurationDto();
        updateDto.setName("Updated Configuration");
        updateDto.setDescription("Updated description");

        // When
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl + "/" + savedConfig.getId(),
            HttpMethod.PUT,
            new HttpEntity<>(updateDto, headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getName()).isEqualTo("Updated Configuration");
        assertThat(response.getBody().getDescription()).isEqualTo("Updated description");
    }

    @Test
    void shouldDeleteConfiguration() {
        // Given
        Configuration savedConfig = configurationRepository.save(createTestConfiguration());

        // When
        ResponseEntity<Void> response = restTemplate.exchange(
            baseUrl + "/" + savedConfig.getId(),
            HttpMethod.DELETE,
            new HttpEntity<>(headers),
            Void.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
        assertThat(configurationRepository.findById(savedConfig.getId())).isEmpty();
    }

    @Test
    void shouldGetAllConfigurations() {
        // Given
        configurationRepository.saveAll(Arrays.asList(
            createTestConfiguration("Config 1"),
            createTestConfiguration("Config 2"),
            createTestConfiguration("Config 3")
        ));

        // When
        ResponseEntity<ConfigurationDto[]> response = restTemplate.exchange(
            baseUrl,
            HttpMethod.GET,
            new HttpEntity<>(headers),
            ConfigurationDto[].class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody()).hasSize(3);
    }

    @Test
    void shouldSearchConfigurationsByName() {
        // Given
        configurationRepository.saveAll(Arrays.asList(
            createTestConfiguration("Dashboard Config"),
            createTestConfiguration("Form Config"),
            createTestConfiguration("Chart Dashboard")
        ));

        // When
        ResponseEntity<ConfigurationDto[]> response = restTemplate.exchange(
            baseUrl + "?search=Dashboard",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            ConfigurationDto[].class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody()).hasSize(2);
    }

    @Test
    void shouldPaginateConfigurations() {
        // Given
        for (int i = 1; i <= 15; i++) {
            configurationRepository.save(createTestConfiguration("Config " + i));
        }

        // When
        ResponseEntity<String> response = restTemplate.exchange(
            baseUrl + "?page=0&size=10",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            String.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        // Parse response to check pagination metadata
        // This would typically be done with a proper PagedResponse DTO
    }

    @Test
    void shouldValidateConfigurationInput() {
        // Given
        ConfigurationDto invalidConfig = new ConfigurationDto();
        // Missing required fields

        // When
        ResponseEntity<String> response = restTemplate.exchange(
            baseUrl,
            HttpMethod.POST,
            new HttpEntity<>(invalidConfig, headers),
            String.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
    }

    @Test
    void shouldHandleUnauthorizedAccess() {
        // Given
        HttpHeaders unauthorizedHeaders = new HttpHeaders();
        unauthorizedHeaders.setContentType(MediaType.APPLICATION_JSON);
        // No authorization header

        // When
        ResponseEntity<String> response = restTemplate.exchange(
            baseUrl,
            HttpMethod.GET,
            new HttpEntity<>(unauthorizedHeaders),
            String.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void shouldCloneConfiguration() {
        // Given
        Configuration originalConfig = configurationRepository.save(createTestConfiguration());

        // When
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl + "/" + originalConfig.getId() + "/clone",
            HttpMethod.POST,
            new HttpEntity<>(headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getId()).isNotEqualTo(originalConfig.getId());
        assertThat(response.getBody().getName()).startsWith("Copy of");
    }

    @Test
    void shouldExportConfiguration() {
        // Given
        Configuration savedConfig = configurationRepository.save(createTestConfiguration());

        // When
        ResponseEntity<String> response = restTemplate.exchange(
            baseUrl + "/" + savedConfig.getId() + "/export",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            String.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getHeaders().getContentType()).isEqualTo(MediaType.APPLICATION_JSON);
        assertThat(response.getBody()).contains("\"name\":\"Test Configuration\"");
    }

    @Test
    void shouldImportConfiguration() {
        // Given
        String configJson = """
            {
                "name": "Imported Configuration",
                "description": "Imported from JSON",
                "components": [
                    {
                        "id": "text-1",
                        "type": "text",
                        "properties": {
                            "text": "Imported text"
                        }
                    }
                ]
            }
            """;

        // When
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl + "/import",
            HttpMethod.POST,
            new HttpEntity<>(configJson, headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getName()).isEqualTo("Imported Configuration");
    }

    @Test
    void shouldVersionConfiguration() {
        // Given
        Configuration originalConfig = configurationRepository.save(createTestConfiguration());
        ConfigurationDto updateDto = new ConfigurationDto();
        updateDto.setName("Updated Configuration");

        // When - Update configuration (should create new version)
        ResponseEntity<ConfigurationDto> response = restTemplate.exchange(
            baseUrl + "/" + originalConfig.getId(),
            HttpMethod.PUT,
            new HttpEntity<>(updateDto, headers),
            ConfigurationDto.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getVersion()).isGreaterThan(originalConfig.getVersion());
    }

    @Test
    void shouldGetConfigurationHistory() {
        // Given
        Configuration config = configurationRepository.save(createTestConfiguration());
        
        // Update configuration multiple times to create history
        for (int i = 1; i <= 3; i++) {
            ConfigurationDto updateDto = new ConfigurationDto();
            updateDto.setName("Version " + i);
            restTemplate.exchange(
                baseUrl + "/" + config.getId(),
                HttpMethod.PUT,
                new HttpEntity<>(updateDto, headers),
                ConfigurationDto.class
            );
        }

        // When
        ResponseEntity<ConfigurationDto[]> response = restTemplate.exchange(
            baseUrl + "/" + config.getId() + "/history",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            ConfigurationDto[].class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().length).isGreaterThan(1);
    }

    private ConfigurationDto createTestConfigurationDto() {
        ConfigurationDto config = new ConfigurationDto();
        config.setName("Test Configuration");
        config.setDescription("Test description");
        config.setVersion(1L);

        ComponentDto textComponent = new ComponentDto();
        textComponent.setId("text-1");
        textComponent.setType("text");
        Map<String, Object> textProps = new HashMap<>();
        textProps.put("text", "Hello World");
        textComponent.setProperties(textProps);

        ComponentDto buttonComponent = new ComponentDto();
        buttonComponent.setId("button-1");
        buttonComponent.setType("button");
        Map<String, Object> buttonProps = new HashMap<>();
        buttonProps.put("text", "Click Me");
        buttonProps.put("variant", "primary");
        buttonComponent.setProperties(buttonProps);

        config.setComponents(Arrays.asList(textComponent, buttonComponent));

        return config;
    }

    private Configuration createTestConfiguration() {
        return createTestConfiguration("Test Configuration");
    }

    private Configuration createTestConfiguration(String name) {
        Configuration config = new Configuration();
        config.setName(name);
        config.setDescription("Test description");
        config.setVersion(1L);
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());
        config.setCreatedBy("test-user");
        config.setOrganizationId("test-org");

        // Set components as JSON string (simplified for test)
        config.setComponentsJson("""
            [
                {
                    "id": "text-1",
                    "type": "text",
                    "properties": {
                        "text": "Hello World"
                    }
                },
                {
                    "id": "button-1",
                    "type": "button",
                    "properties": {
                        "text": "Click Me",
                        "variant": "primary"
                    }
                }
            ]
            """);

        return config;
    }
}
