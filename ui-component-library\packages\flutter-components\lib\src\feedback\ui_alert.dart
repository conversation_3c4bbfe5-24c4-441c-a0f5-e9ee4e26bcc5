import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Alert component
class UIAlert extends StatelessWidget {
  const UIAlert({
    super.key,
    required this.message,
    this.title,
    this.variant = UIAlertVariant.info,
    this.showIcon = true,
    this.dismissible = false,
    this.onDismiss,
    this.actions,
  });

  final String message;
  final String? title;
  final UIAlertVariant variant;
  final bool showIcon;
  final bool dismissible;
  final VoidCallback? onDismiss;
  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final colors = _getVariantColors(colorScheme);

    return Container(
      padding: EdgeInsets.all(tokens.spacing.size4),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: tokens.borderRadius.lg,
        border: Border.all(color: colors.border),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showIcon) ...[
            Icon(
              _getVariantIcon(),
              color: colors.foreground,
              size: 20,
            ),
            SizedBox(width: tokens.spacing.size3),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null) ...[
                  Text(
                    title!,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: colors.foreground,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: tokens.spacing.size1),
                ],
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colors.foreground,
                  ),
                ),
                if (actions != null) ...[
                  SizedBox(height: tokens.spacing.size3),
                  Row(children: actions!),
                ],
              ],
            ),
          ),
          if (dismissible) ...[
            SizedBox(width: tokens.spacing.size2),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(Icons.close, size: 18),
              color: colors.foreground,
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
        ],
      ),
    );
  }

  _AlertColors _getVariantColors(ColorScheme colorScheme) {
    switch (variant) {
      case UIAlertVariant.info:
        return _AlertColors(
          background: const Color(0xFF3B82F6).withOpacity(0.1),
          foreground: const Color(0xFF1E40AF),
          border: const Color(0xFF3B82F6).withOpacity(0.3),
        );
      case UIAlertVariant.success:
        return _AlertColors(
          background: const Color(0xFF10B981).withOpacity(0.1),
          foreground: const Color(0xFF047857),
          border: const Color(0xFF10B981).withOpacity(0.3),
        );
      case UIAlertVariant.warning:
        return _AlertColors(
          background: const Color(0xFFF59E0B).withOpacity(0.1),
          foreground: const Color(0xFFB45309),
          border: const Color(0xFFF59E0B).withOpacity(0.3),
        );
      case UIAlertVariant.error:
        return _AlertColors(
          background: colorScheme.error.withOpacity(0.1),
          foreground: colorScheme.error,
          border: colorScheme.error.withOpacity(0.3),
        );
    }
  }

  IconData _getVariantIcon() {
    switch (variant) {
      case UIAlertVariant.info:
        return Icons.info;
      case UIAlertVariant.success:
        return Icons.check_circle;
      case UIAlertVariant.warning:
        return Icons.warning;
      case UIAlertVariant.error:
        return Icons.error;
    }
  }
}

/// Alert colors helper class
class _AlertColors {
  const _AlertColors({
    required this.background,
    required this.foreground,
    required this.border,
  });

  final Color background;
  final Color foreground;
  final Color border;
}
