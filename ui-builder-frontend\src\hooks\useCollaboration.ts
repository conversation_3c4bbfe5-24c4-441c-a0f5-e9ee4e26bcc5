import { useEffect, useCallback, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { RootState } from '../store';
import { 
  setCollaborators, 
  addCollaborator, 
  removeCollaborator, 
  updateCursor,
  setConnectionStatus,
  addComment,
  updateComment,
  resolveComment
} from '../store/slices/collaborationSlice';
import { updateUIConfig } from '../store/slices/uiBuilderSlice';

export interface CollaborationUser {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  color: string;
  cursor?: {
    x: number;
    y: number;
    elementId?: string;
  };
  selection?: {
    elementId: string;
    startOffset?: number;
    endOffset?: number;
  };
}

export interface CollaborationComment {
  id: string;
  content: string;
  position: {
    x: number;
    y: number;
  };
  elementId?: string;
  user: CollaborationUser;
  createdAt: string;
  updatedAt: string;
  status: 'open' | 'resolved' | 'closed';
  replies?: CollaborationComment[];
}

export interface UseCollaborationOptions {
  configId: string;
  enabled?: boolean;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  heartbeatInterval?: number;
}

export interface UseCollaborationReturn {
  collaborators: CollaborationUser[];
  comments: CollaborationComment[];
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  currentUser: CollaborationUser | null;
  
  // Connection methods
  connect: () => void;
  disconnect: () => void;
  
  // Cursor and selection methods
  updateCursorPosition: (x: number, y: number, elementId?: string) => void;
  updateSelection: (elementId: string, startOffset?: number, endOffset?: number) => void;
  clearSelection: () => void;
  
  // Comment methods
  addComment: (content: string, position: { x: number; y: number }, elementId?: string) => void;
  updateComment: (commentId: string, content: string) => void;
  deleteComment: (commentId: string) => void;
  resolveComment: (commentId: string) => void;
  replyToComment: (parentCommentId: string, content: string) => void;
  
  // Configuration sync methods
  broadcastConfigChange: (changes: any) => void;
  
  // Presence methods
  setUserStatus: (status: 'online' | 'away' | 'busy') => void;
}

export const useCollaboration = (options: UseCollaborationOptions): UseCollaborationReturn => {
  const {
    configId,
    enabled = true,
    autoConnect = true,
    reconnectAttempts = 5,
    heartbeatInterval = 30000
  } = options;

  const dispatch = useDispatch();
  const socketRef = useRef<Socket | null>(null);
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [reconnectCount, setReconnectCount] = useState(0);

  const {
    collaborators,
    comments,
    isConnected,
    isConnecting,
    connectionError,
    currentUser
  } = useSelector((state: RootState) => state.collaboration);

  const { currentConfig } = useSelector((state: RootState) => state.uiBuilder);

  // Initialize socket connection
  const connect = useCallback(() => {
    if (!enabled || !configId || socketRef.current?.connected) {
      return;
    }

    dispatch(setConnectionStatus({ isConnecting: true, connectionError: null }));

    const socket = io(process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      query: {
        configId,
        userId: currentUser?.id || 'anonymous'
      }
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on('connect', () => {
      console.log('Connected to collaboration server');
      dispatch(setConnectionStatus({ isConnected: true, isConnecting: false, connectionError: null }));
      setReconnectCount(0);
      
      // Join the configuration room
      socket.emit('join-config', { configId });
      
      // Start heartbeat
      startHeartbeat();
    });

    socket.on('disconnect', (reason) => {
      console.log('Disconnected from collaboration server:', reason);
      dispatch(setConnectionStatus({ isConnected: false, isConnecting: false }));
      stopHeartbeat();
      
      // Attempt reconnection if not manually disconnected
      if (reason !== 'io client disconnect' && reconnectCount < reconnectAttempts) {
        attemptReconnect();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      dispatch(setConnectionStatus({ 
        isConnected: false, 
        isConnecting: false, 
        connectionError: error.message 
      }));
      
      if (reconnectCount < reconnectAttempts) {
        attemptReconnect();
      }
    });

    // Collaboration event handlers
    socket.on('user-joined', (user: CollaborationUser) => {
      console.log('User joined:', user);
      dispatch(addCollaborator(user));
    });

    socket.on('user-left', (userId: string) => {
      console.log('User left:', userId);
      dispatch(removeCollaborator(userId));
    });

    socket.on('collaborators-list', (users: CollaborationUser[]) => {
      console.log('Received collaborators list:', users);
      dispatch(setCollaborators(users));
    });

    socket.on('cursor-update', (data: { userId: string; cursor: any }) => {
      dispatch(updateCursor({ userId: data.userId, cursor: data.cursor }));
    });

    socket.on('selection-update', (data: { userId: string; selection: any }) => {
      dispatch(updateCursor({ userId: data.userId, selection: data.selection }));
    });

    // Configuration sync handlers
    socket.on('config-updated', (changes: any) => {
      console.log('Received config update:', changes);
      dispatch(updateUIConfig(changes));
    });

    // Comment handlers
    socket.on('comment-added', (comment: CollaborationComment) => {
      dispatch(addComment(comment));
    });

    socket.on('comment-updated', (comment: CollaborationComment) => {
      dispatch(updateComment(comment));
    });

    socket.on('comment-resolved', (commentId: string) => {
      dispatch(resolveComment(commentId));
    });

    // Error handlers
    socket.on('error', (error: any) => {
      console.error('Collaboration error:', error);
      dispatch(setConnectionStatus({ connectionError: error.message }));
    });

  }, [enabled, configId, currentUser?.id, dispatch, reconnectCount, reconnectAttempts]);

  // Disconnect from socket
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    stopHeartbeat();
    clearReconnectTimeout();
    dispatch(setConnectionStatus({ isConnected: false, isConnecting: false }));
  }, [dispatch]);

  // Start heartbeat to maintain connection
  const startHeartbeat = useCallback(() => {
    if (heartbeatRef.current) {
      clearInterval(heartbeatRef.current);
    }
    
    heartbeatRef.current = setInterval(() => {
      if (socketRef.current?.connected) {
        socketRef.current.emit('heartbeat');
      }
    }, heartbeatInterval);
  }, [heartbeatInterval]);

  // Stop heartbeat
  const stopHeartbeat = useCallback(() => {
    if (heartbeatRef.current) {
      clearInterval(heartbeatRef.current);
      heartbeatRef.current = null;
    }
  }, []);

  // Attempt reconnection with exponential backoff
  const attemptReconnect = useCallback(() => {
    const delay = Math.min(1000 * Math.pow(2, reconnectCount), 30000);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      setReconnectCount(prev => prev + 1);
      connect();
    }, delay);
  }, [reconnectCount, connect]);

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Update cursor position
  const updateCursorPosition = useCallback((x: number, y: number, elementId?: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('cursor-update', { x, y, elementId });
    }
  }, []);

  // Update selection
  const updateSelection = useCallback((elementId: string, startOffset?: number, endOffset?: number) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('selection-update', { elementId, startOffset, endOffset });
    }
  }, []);

  // Clear selection
  const clearSelection = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('selection-clear');
    }
  }, []);

  // Add comment
  const addCommentHandler = useCallback((content: string, position: { x: number; y: number }, elementId?: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('comment-add', { content, position, elementId });
    }
  }, []);

  // Update comment
  const updateCommentHandler = useCallback((commentId: string, content: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('comment-update', { commentId, content });
    }
  }, []);

  // Delete comment
  const deleteComment = useCallback((commentId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('comment-delete', { commentId });
    }
  }, []);

  // Resolve comment
  const resolveCommentHandler = useCallback((commentId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('comment-resolve', { commentId });
    }
  }, []);

  // Reply to comment
  const replyToComment = useCallback((parentCommentId: string, content: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('comment-reply', { parentCommentId, content });
    }
  }, []);

  // Broadcast configuration changes
  const broadcastConfigChange = useCallback((changes: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('config-update', changes);
    }
  }, []);

  // Set user status
  const setUserStatus = useCallback((status: 'online' | 'away' | 'busy') => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('status-update', { status });
    }
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && enabled && configId) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, enabled, configId, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
      clearReconnectTimeout();
    };
  }, [disconnect, clearReconnectTimeout]);

  return {
    collaborators,
    comments,
    isConnected,
    isConnecting,
    connectionError,
    currentUser,
    connect,
    disconnect,
    updateCursorPosition,
    updateSelection,
    clearSelection,
    addComment: addCommentHandler,
    updateComment: updateCommentHandler,
    deleteComment,
    resolveComment: resolveCommentHandler,
    replyToComment,
    broadcastConfigChange,
    setUserStatus
  };
};
