package com.uiplatform.service;

import com.uiplatform.dto.TemplateDTO;
import com.uiplatform.entity.Organization;
import com.uiplatform.entity.Template;
import com.uiplatform.entity.User;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.exception.BusinessException;
import com.uiplatform.mapper.TemplateMapper;
import com.uiplatform.repository.OrganizationRepository;
import com.uiplatform.repository.TemplateRepository;
import com.uiplatform.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for Template management.
 * Handles business logic for template marketplace operations.
 */
@Service
@Transactional
public class TemplateService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateService.class);

    private final TemplateRepository templateRepository;
    private final OrganizationRepository organizationRepository;
    private final UserRepository userRepository;
    private final TemplateMapper templateMapper;

    @Autowired
    public TemplateService(TemplateRepository templateRepository,
                          OrganizationRepository organizationRepository,
                          UserRepository userRepository,
                          TemplateMapper templateMapper) {
        this.templateRepository = templateRepository;
        this.organizationRepository = organizationRepository;
        this.userRepository = userRepository;
        this.templateMapper = templateMapper;
    }

    /**
     * Create a new template.
     */
    public TemplateDTO createTemplate(TemplateDTO.CreateDTO createDTO, UUID authorId, UUID organizationId) {
        logger.info("Creating new template '{}' by author: {} for organization: {}", 
                   createDTO.getName(), authorId, organizationId);

        // Validate organization exists (optional for templates)
        Organization organization = null;
        if (organizationId != null) {
            organization = organizationRepository.findById(organizationId)
                    .filter(org -> !org.getDeleted())
                    .orElseThrow(() -> new ResourceNotFoundException("Organization not found with ID: " + organizationId));
        }

        // Validate author exists
        User author = userRepository.findById(authorId)
                .filter(user -> !user.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Author not found with ID: " + authorId));

        // If organization is provided, ensure author belongs to it
        if (organization != null && !author.getOrganization().getId().equals(organizationId)) {
            throw new BusinessException("Author does not belong to the specified organization");
        }

        Template template = templateMapper.toEntity(createDTO);
        template.setOrganization(organization);
        template.setAuthor(author);

        template = templateRepository.save(template);

        logger.info("Successfully created template with ID: {}", template.getId());
        return templateMapper.toDTO(template);
    }

    /**
     * Get template by ID.
     */
    @Transactional(readOnly = true)
    public TemplateDTO getTemplateById(UUID id) {
        logger.debug("Fetching template by ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        // Increment view count
        templateRepository.incrementViewCount(id);

        return templateMapper.toDTO(template);
    }

    /**
     * Update template.
     */
    public TemplateDTO updateTemplate(UUID id, TemplateDTO updateDTO) {
        logger.info("Updating template with ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        templateMapper.updateEntityFromDTO(updateDTO, template);
        template = templateRepository.save(template);

        logger.info("Successfully updated template with ID: {}", id);
        return templateMapper.toDTO(template);
    }

    /**
     * Delete template (soft delete).
     */
    public void deleteTemplate(UUID id) {
        logger.info("Deleting template with ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        template.markAsDeleted("SYSTEM"); // TODO: Get current user
        templateRepository.save(template);

        logger.info("Successfully deleted template with ID: {}", id);
    }

    /**
     * Get templates by organization.
     */
    @Transactional(readOnly = true)
    public Page<TemplateDTO> getTemplatesByOrganization(UUID organizationId, Pageable pageable) {
        logger.debug("Fetching templates for organization ID: {}", organizationId);

        Page<Template> templates = templateRepository.findByOrganizationIdAndDeletedFalse(organizationId, pageable);
        return templates.map(templateMapper::toDTO);
    }

    /**
     * Get templates by author.
     */
    @Transactional(readOnly = true)
    public Page<TemplateDTO> getTemplatesByAuthor(UUID authorId, Pageable pageable) {
        logger.debug("Fetching templates for author ID: {}", authorId);

        Page<Template> templates = templateRepository.findByAuthorIdAndDeletedFalse(authorId, pageable);
        return templates.map(templateMapper::toDTO);
    }

    /**
     * Get public templates (marketplace).
     */
    @Transactional(readOnly = true)
    public Page<TemplateDTO> getPublicTemplates(Pageable pageable) {
        logger.debug("Fetching public templates for marketplace");

        Page<Template> templates = templateRepository.findByIsPublicTrueAndDeletedFalse(pageable);
        return templates.map(templateMapper::toDTO);
    }

    /**
     * Search templates in marketplace.
     */
    @Transactional(readOnly = true)
    public Page<TemplateDTO> searchTemplates(String name,
                                            String category,
                                            String subcategory,
                                            Boolean isPremium,
                                            BigDecimal minPrice,
                                            BigDecimal maxPrice,
                                            BigDecimal minRating,
                                            Pageable pageable) {
        logger.debug("Searching templates with criteria - name: {}, category: {}, premium: {}", name, category, isPremium);

        Page<Template> templates = templateRepository.searchTemplates(
                name, category, subcategory, isPremium, minPrice, maxPrice, minRating, pageable);
        return templates.map(templateMapper::toDTO);
    }

    /**
     * Get templates by category.
     */
    @Transactional(readOnly = true)
    public Page<TemplateDTO> getTemplatesByCategory(String category, Pageable pageable) {
        logger.debug("Fetching templates for category: {}", category);

        Page<Template> templates = templateRepository.findByCategoryAndDeletedFalse(category, pageable);
        return templates.map(templateMapper::toDTO);
    }

    /**
     * Get featured templates.
     */
    @Transactional(readOnly = true)
    public List<TemplateDTO> getFeaturedTemplates() {
        logger.debug("Fetching featured templates");

        List<Template> templates = templateRepository.findByIsFeaturedTrueAndDeletedFalse();
        return templates.stream()
                .map(templateMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get most popular templates.
     */
    @Transactional(readOnly = true)
    public List<TemplateDTO> getMostPopularTemplates(int limit) {
        logger.debug("Fetching {} most popular templates", limit);

        List<Template> templates = templateRepository.findMostPopular(Pageable.ofSize(limit));
        return templates.stream()
                .map(templateMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get highest rated templates.
     */
    @Transactional(readOnly = true)
    public List<TemplateDTO> getHighestRatedTemplates(int limit) {
        logger.debug("Fetching {} highest rated templates", limit);

        List<Template> templates = templateRepository.findHighestRated(Pageable.ofSize(limit));
        return templates.stream()
                .map(templateMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get recently published templates.
     */
    @Transactional(readOnly = true)
    public List<TemplateDTO> getRecentlyPublishedTemplates(int limit) {
        logger.debug("Fetching {} recently published templates", limit);

        List<Template> templates = templateRepository.findRecentlyPublished(Pageable.ofSize(limit));
        return templates.stream()
                .map(templateMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Publish template to marketplace.
     */
    public TemplateDTO publishTemplate(UUID id) {
        logger.info("Publishing template with ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        if (template.getStatus() == Template.TemplateStatus.PUBLISHED) {
            throw new BusinessException("Template is already published");
        }

        template.publish();
        template = templateRepository.save(template);

        logger.info("Successfully published template with ID: {}", id);
        return templateMapper.toDTO(template);
    }

    /**
     * Unpublish template from marketplace.
     */
    public TemplateDTO unpublishTemplate(UUID id) {
        logger.info("Unpublishing template with ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        if (template.getStatus() != Template.TemplateStatus.PUBLISHED) {
            throw new BusinessException("Template is not published");
        }

        template.unpublish();
        template = templateRepository.save(template);

        logger.info("Successfully unpublished template with ID: {}", id);
        return templateMapper.toDTO(template);
    }

    /**
     * Update template status.
     */
    public TemplateDTO updateTemplateStatus(UUID id, Template.TemplateStatus newStatus) {
        logger.info("Updating status for template ID: {} to {}", id, newStatus);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        Template.TemplateStatus oldStatus = template.getStatus();
        template.setStatus(newStatus);
        
        // Update public status based on new status
        if (newStatus == Template.TemplateStatus.PUBLISHED) {
            template.setIsPublic(true);
        } else if (newStatus == Template.TemplateStatus.DRAFT || newStatus == Template.TemplateStatus.REJECTED) {
            template.setIsPublic(false);
        }

        template = templateRepository.save(template);

        logger.info("Successfully updated status for template ID: {} from {} to {}", id, oldStatus, newStatus);
        return templateMapper.toDTO(template);
    }

    /**
     * Update template featured status.
     */
    public TemplateDTO updateFeaturedStatus(UUID id, Boolean isFeatured) {
        logger.info("Updating featured status for template ID: {} to {}", id, isFeatured);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        template.setIsFeatured(isFeatured);
        template = templateRepository.save(template);

        logger.info("Successfully updated featured status for template ID: {}", id);
        return templateMapper.toDTO(template);
    }

    /**
     * Download template (increment download count).
     */
    public void downloadTemplate(UUID id) {
        logger.info("Recording download for template ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted() && t.getIsPublic() && t.getStatus() == Template.TemplateStatus.PUBLISHED)
                .orElseThrow(() -> new ResourceNotFoundException("Template not found or not available for download"));

        templateRepository.incrementDownloadCount(id);

        logger.info("Successfully recorded download for template ID: {}", id);
    }

    /**
     * Get all template categories.
     */
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        logger.debug("Fetching all template categories");
        return templateRepository.findAllCategories();
    }

    /**
     * Get subcategories by category.
     */
    @Transactional(readOnly = true)
    public List<String> getSubcategoriesByCategory(String category) {
        logger.debug("Fetching subcategories for category: {}", category);
        return templateRepository.findSubcategoriesByCategory(category);
    }

    /**
     * Get template statistics.
     */
    @Transactional(readOnly = true)
    public TemplateDTO getTemplateWithStats(UUID id) {
        logger.debug("Fetching template with statistics for ID: {}", id);

        Template template = templateRepository.findById(id)
                .filter(t -> !t.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("Template not found with ID: " + id));

        TemplateDTO dto = templateMapper.toDTO(template);
        
        // Add statistics
        dto.setReviewCount(templateRepository.countByTemplateId(id));
        dto.setAverageRating(templateRepository.calculateAverageRating(id));
        
        return dto;
    }

    /**
     * Clone template for user's organization.
     */
    public TemplateDTO cloneTemplate(UUID templateId, String newName, UUID userId) {
        logger.info("Cloning template {} with new name '{}' for user {}", templateId, newName, userId);

        Template original = templateRepository.findById(templateId)
                .filter(t -> !t.getDeleted() && t.getIsPublic() && t.getStatus() == Template.TemplateStatus.PUBLISHED)
                .orElseThrow(() -> new ResourceNotFoundException("Template not found or not available for cloning"));

        User user = userRepository.findById(userId)
                .filter(u -> !u.getDeleted())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        Template cloned = new Template();
        cloned.setName(newName);
        cloned.setDescription("Cloned from: " + original.getName());
        cloned.setCategory(original.getCategory());
        cloned.setSubcategory(original.getSubcategory());
        cloned.setTemplateData(original.getTemplateData());
        cloned.setCompatibility(original.getCompatibility());
        cloned.setRequirements(original.getRequirements());
        cloned.setInstallationGuide(original.getInstallationGuide());
        cloned.setOrganization(user.getOrganization());
        cloned.setAuthor(user);
        cloned.setIsPublic(false);
        cloned.setIsPremium(false);
        cloned.setPrice(BigDecimal.ZERO);
        cloned.setLicenseType(original.getLicenseType());

        cloned = templateRepository.save(cloned);

        // Record download for original template
        templateRepository.incrementDownloadCount(templateId);

        logger.info("Successfully cloned template with new ID: {}", cloned.getId());
        return templateMapper.toDTO(cloned);
    }
}
