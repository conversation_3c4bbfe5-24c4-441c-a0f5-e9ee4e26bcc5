import { queryHelpers, buildQueries, Matcher, MatcherOptions } from '@testing-library/react';

// Custom query for finding elements by data-testid with specific prefix
const queryAllByTestIdPrefix = (
  container: HTMLElement,
  prefix: string,
  options?: MatcherOptions
) => {
  return Array.from(container.querySelectorAll(`[data-testid^="${prefix}"]`));
};

const getMultipleError = (prefix: string) =>
  `Found multiple elements with data-testid prefix: ${prefix}`;

const getMissingError = (prefix: string) =>
  `Unable to find an element with data-testid prefix: ${prefix}`;

const [
  queryByTestIdPrefix,
  getAllByTestIdPrefix,
  getByTestIdPrefix,
  findAllByTestIdPrefix,
  findByTestIdPrefix,
] = buildQueries(queryAllByTestIdPrefix, getMultipleError, getMissingError);

// Custom query for finding elements by ARIA role and name
const queryAllByRoleAndName = (
  container: HTMLElement,
  role: string,
  name: Matcher,
  options?: MatcherOptions
) => {
  const elements = Array.from(container.querySelectorAll(`[role="${role}"]`));
  return elements.filter(element => {
    const accessibleName = element.getAttribute('aria-label') || 
                          element.getAttribute('aria-labelledby') ||
                          element.textContent;
    
    if (typeof name === 'string') {
      return accessibleName?.includes(name);
    } else if (name instanceof RegExp) {
      return name.test(accessibleName || '');
    } else if (typeof name === 'function') {
      return name(accessibleName || '', element);
    }
    
    return false;
  });
};

const getRoleAndNameMultipleError = (role: string, name: Matcher) =>
  `Found multiple elements with role "${role}" and name matching: ${name}`;

const getRoleAndNameMissingError = (role: string, name: Matcher) =>
  `Unable to find an element with role "${role}" and name matching: ${name}`;

const [
  queryByRoleAndName,
  getAllByRoleAndName,
  getByRoleAndName,
  findAllByRoleAndName,
  findByRoleAndName,
] = buildQueries(queryAllByRoleAndName, getRoleAndNameMultipleError, getRoleAndNameMissingError);

// Custom query for finding elements by CSS class pattern
const queryAllByClassPattern = (
  container: HTMLElement,
  pattern: RegExp,
  options?: MatcherOptions
) => {
  return Array.from(container.querySelectorAll('*')).filter(element => {
    return pattern.test(element.className);
  });
};

const getClassPatternMultipleError = (pattern: RegExp) =>
  `Found multiple elements with class pattern: ${pattern}`;

const getClassPatternMissingError = (pattern: RegExp) =>
  `Unable to find an element with class pattern: ${pattern}`;

const [
  queryByClassPattern,
  getAllByClassPattern,
  getByClassPattern,
  findAllByClassPattern,
  findByClassPattern,
] = buildQueries(queryAllByClassPattern, getClassPatternMultipleError, getClassPatternMissingError);

// Custom query for finding elements by CSS custom property
const queryAllByCSSProperty = (
  container: HTMLElement,
  property: string,
  value?: string,
  options?: MatcherOptions
) => {
  return Array.from(container.querySelectorAll('*')).filter(element => {
    const computedStyle = window.getComputedStyle(element);
    const propertyValue = computedStyle.getPropertyValue(property);
    
    if (value) {
      return propertyValue === value;
    }
    
    return propertyValue !== '';
  });
};

const getCSSPropertyMultipleError = (property: string, value?: string) =>
  `Found multiple elements with CSS property "${property}"${value ? ` = "${value}"` : ''}`;

const getCSSPropertyMissingError = (property: string, value?: string) =>
  `Unable to find an element with CSS property "${property}"${value ? ` = "${value}"` : ''}`;

const [
  queryByCSSProperty,
  getAllByCSSProperty,
  getByCSSProperty,
  findAllByCSSProperty,
  findByCSSProperty,
] = buildQueries(queryAllByCSSProperty, getCSSPropertyMultipleError, getCSSPropertyMissingError);

// Custom query for finding elements by theme token
const queryAllByThemeToken = (
  container: HTMLElement,
  token: string,
  options?: MatcherOptions
) => {
  return Array.from(container.querySelectorAll('*')).filter(element => {
    const computedStyle = window.getComputedStyle(element);
    const cssText = computedStyle.cssText;
    return cssText.includes(`var(--${token})`);
  });
};

const getThemeTokenMultipleError = (token: string) =>
  `Found multiple elements using theme token: ${token}`;

const getThemeTokenMissingError = (token: string) =>
  `Unable to find an element using theme token: ${token}`;

const [
  queryByThemeToken,
  getAllByThemeToken,
  getByThemeToken,
  findAllByThemeToken,
  findByThemeToken,
] = buildQueries(queryAllByThemeToken, getThemeTokenMultipleError, getThemeTokenMissingError);

// Custom query for finding focusable elements
const queryAllFocusable = (container: HTMLElement, options?: MatcherOptions) => {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ];
  
  return Array.from(container.querySelectorAll(focusableSelectors.join(', ')))
    .filter(element => {
      const style = window.getComputedStyle(element);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });
};

const getFocusableMultipleError = () => 'Found multiple focusable elements';
const getFocusableMissingError = () => 'Unable to find any focusable elements';

const [
  queryFocusable,
  getAllFocusable,
  getFocusable,
  findAllFocusable,
  findFocusable,
] = buildQueries(queryAllFocusable, getFocusableMultipleError, getFocusableMissingError);

// Custom query for finding elements with specific ARIA attributes
const queryAllByAriaAttribute = (
  container: HTMLElement,
  attribute: string,
  value?: string,
  options?: MatcherOptions
) => {
  const selector = value ? `[${attribute}="${value}"]` : `[${attribute}]`;
  return Array.from(container.querySelectorAll(selector));
};

const getAriaAttributeMultipleError = (attribute: string, value?: string) =>
  `Found multiple elements with ${attribute}${value ? `="${value}"` : ''}`;

const getAriaAttributeMissingError = (attribute: string, value?: string) =>
  `Unable to find an element with ${attribute}${value ? `="${value}"` : ''}`;

const [
  queryByAriaAttribute,
  getAllByAriaAttribute,
  getByAriaAttribute,
  findAllByAriaAttribute,
  findByAriaAttribute,
] = buildQueries(queryAllByAriaAttribute, getAriaAttributeMultipleError, getAriaAttributeMissingError);

// Custom query for finding elements by component name (data-component attribute)
const queryAllByComponent = (
  container: HTMLElement,
  componentName: string,
  options?: MatcherOptions
) => {
  return Array.from(container.querySelectorAll(`[data-component="${componentName}"]`));
};

const getComponentMultipleError = (componentName: string) =>
  `Found multiple elements with component name: ${componentName}`;

const getComponentMissingError = (componentName: string) =>
  `Unable to find an element with component name: ${componentName}`;

const [
  queryByComponent,
  getAllByComponent,
  getByComponent,
  findAllByComponent,
  findByComponent,
] = buildQueries(queryAllByComponent, getComponentMultipleError, getComponentMissingError);

// Export all custom queries
export {
  // Test ID prefix queries
  queryByTestIdPrefix,
  queryAllByTestIdPrefix,
  getByTestIdPrefix,
  getAllByTestIdPrefix,
  findByTestIdPrefix,
  findAllByTestIdPrefix,
  
  // Role and name queries
  queryByRoleAndName,
  queryAllByRoleAndName,
  getByRoleAndName,
  getAllByRoleAndName,
  findByRoleAndName,
  findAllByRoleAndName,
  
  // Class pattern queries
  queryByClassPattern,
  queryAllByClassPattern,
  getByClassPattern,
  getAllByClassPattern,
  findByClassPattern,
  findAllByClassPattern,
  
  // CSS property queries
  queryByCSSProperty,
  queryAllByCSSProperty,
  getByCSSProperty,
  getAllByCSSProperty,
  findByCSSProperty,
  findAllByCSSProperty,
  
  // Theme token queries
  queryByThemeToken,
  queryAllByThemeToken,
  getByThemeToken,
  getAllByThemeToken,
  findByThemeToken,
  findAllByThemeToken,
  
  // Focusable queries
  queryFocusable,
  queryAllFocusable,
  getFocusable,
  getAllFocusable,
  findFocusable,
  findAllFocusable,
  
  // ARIA attribute queries
  queryByAriaAttribute,
  queryAllByAriaAttribute,
  getByAriaAttribute,
  getAllByAriaAttribute,
  findByAriaAttribute,
  findAllByAriaAttribute,
  
  // Component queries
  queryByComponent,
  queryAllByComponent,
  getByComponent,
  getAllByComponent,
  findByComponent,
  findAllByComponent,
};
