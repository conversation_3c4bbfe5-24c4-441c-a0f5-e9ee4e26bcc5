package com.uibuilder.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * OpenAPI Configuration for UI Builder Platform
 * 
 * Configures Swagger/OpenAPI documentation with:
 * - API information and metadata
 * - Security schemes (JWT, OAuth2)
 * - Server configurations
 * - Global security requirements
 */
@Configuration
public class OpenApiConfig {

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${app.api.base-url:http://localhost:8080}")
    private String apiBaseUrl;

    @Value("${app.api.description:UI Builder Platform API}")
    private String apiDescription;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(serverList())
                .components(securityComponents())
                .security(securityRequirements());
    }

    private Info apiInfo() {
        return new Info()
                .title("UI Builder Platform API")
                .description(apiDescription)
                .version(appVersion)
                .contact(new Contact()
                        .name("UI Builder Team")
                        .email("<EMAIL>")
                        .url("https://ui-builder.com/support"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }

    private List<Server> serverList() {
        return Arrays.asList(
                new Server()
                        .url(apiBaseUrl)
                        .description("Development Server"),
                new Server()
                        .url("https://api-staging.ui-builder.com")
                        .description("Staging Server"),
                new Server()
                        .url("https://api.ui-builder.com")
                        .description("Production Server")
        );
    }

    private Components securityComponents() {
        return new Components()
                .addSecuritySchemes("bearerAuth", new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .description("JWT Bearer Token Authentication"))
                .addSecuritySchemes("oauth2", new SecurityScheme()
                        .type(SecurityScheme.Type.OAUTH2)
                        .description("OAuth2 Authentication")
                        .flows(new io.swagger.v3.oas.models.security.OAuthFlows()
                                .authorizationCode(new io.swagger.v3.oas.models.security.OAuthFlow()
                                        .authorizationUrl(apiBaseUrl + "/oauth2/authorize")
                                        .tokenUrl(apiBaseUrl + "/oauth2/token")
                                        .scopes(new io.swagger.v3.oas.models.security.Scopes()
                                                .addString("read", "Read access")
                                                .addString("write", "Write access")
                                                .addString("admin", "Admin access")))));
    }

    private List<SecurityRequirement> securityRequirements() {
        return Arrays.asList(
                new SecurityRequirement().addList("bearerAuth"),
                new SecurityRequirement().addList("oauth2", Arrays.asList("read", "write"))
        );
    }
}

/**
 * API Documentation Tags
 */
public class ApiTags {
    public static final String AUTHENTICATION = "Authentication";
    public static final String CONFIGURATIONS = "Configurations";
    public static final String TEMPLATES = "Templates";
    public static final String COMPONENTS = "Components";
    public static final String USERS = "Users";
    public static final String ORGANIZATIONS = "Organizations";
    public static final String ANALYTICS = "Analytics";
    public static final String ADMIN = "Admin";
    public static final String HEALTH = "Health";
}

/**
 * Common API Responses
 */
public class ApiResponses {
    public static final String SUCCESS_200 = "200";
    public static final String CREATED_201 = "201";
    public static final String NO_CONTENT_204 = "204";
    public static final String BAD_REQUEST_400 = "400";
    public static final String UNAUTHORIZED_401 = "401";
    public static final String FORBIDDEN_403 = "403";
    public static final String NOT_FOUND_404 = "404";
    public static final String CONFLICT_409 = "409";
    public static final String INTERNAL_ERROR_500 = "500";
}

/**
 * API Examples
 */
public class ApiExamples {
    
    public static final String CONFIGURATION_EXAMPLE = """
        {
          "id": "config-123",
          "name": "Dashboard Configuration",
          "description": "Main dashboard layout",
          "version": 1,
          "components": [
            {
              "id": "header-1",
              "type": "header",
              "properties": {
                "title": "Dashboard",
                "subtitle": "Welcome back!"
              },
              "style": {
                "backgroundColor": "#f8f9fa",
                "padding": "20px"
              }
            },
            {
              "id": "chart-1",
              "type": "chart",
              "properties": {
                "type": "line",
                "data": {
                  "labels": ["Jan", "Feb", "Mar"],
                  "datasets": [{
                    "label": "Sales",
                    "data": [100, 150, 200]
                  }]
                }
              }
            }
          ],
          "theme": {
            "colors": {
              "primary": "#007bff",
              "secondary": "#6c757d"
            },
            "typography": {
              "fontFamily": "Inter, sans-serif"
            }
          },
          "layout": {
            "type": "grid",
            "properties": {
              "columns": 12,
              "gap": "16px"
            }
          },
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z",
          "createdBy": "user-123",
          "organizationId": "org-456"
        }
        """;

    public static final String TEMPLATE_EXAMPLE = """
        {
          "id": "template-123",
          "name": "E-commerce Product Page",
          "description": "Template for product detail pages",
          "category": "E-commerce",
          "tags": ["product", "retail", "responsive"],
          "isPublic": true,
          "configuration": {
            "components": [
              {
                "id": "product-image",
                "type": "image",
                "properties": {
                  "src": "{{product.imageUrl}}",
                  "alt": "{{product.name}}"
                }
              },
              {
                "id": "product-title",
                "type": "text",
                "properties": {
                  "text": "{{product.name}}",
                  "variant": "h1"
                }
              },
              {
                "id": "product-price",
                "type": "text",
                "properties": {
                  "text": "${{product.price}}",
                  "variant": "h2",
                  "color": "primary"
                }
              }
            ]
          },
          "variables": [
            {
              "name": "product",
              "type": "object",
              "description": "Product data object",
              "schema": {
                "type": "object",
                "properties": {
                  "name": {"type": "string"},
                  "price": {"type": "number"},
                  "imageUrl": {"type": "string"}
                }
              }
            }
          ],
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z",
          "createdBy": "user-123",
          "organizationId": "org-456"
        }
        """;

    public static final String USER_EXAMPLE = """
        {
          "id": "user-123",
          "email": "<EMAIL>",
          "firstName": "John",
          "lastName": "Doe",
          "avatar": "https://example.com/avatars/john.jpg",
          "roles": ["USER"],
          "permissions": ["READ_CONFIGURATIONS", "WRITE_CONFIGURATIONS"],
          "organizationId": "org-456",
          "isActive": true,
          "lastLoginAt": "2024-01-01T12:00:00Z",
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z"
        }
        """;

    public static final String ORGANIZATION_EXAMPLE = """
        {
          "id": "org-456",
          "name": "Acme Corporation",
          "slug": "acme-corp",
          "description": "Leading provider of innovative solutions",
          "website": "https://acme-corp.com",
          "logo": "https://example.com/logos/acme.png",
          "settings": {
            "allowPublicTemplates": true,
            "maxUsers": 100,
            "features": ["ADVANCED_ANALYTICS", "CUSTOM_THEMES"]
          },
          "subscription": {
            "plan": "ENTERPRISE",
            "status": "ACTIVE",
            "expiresAt": "2024-12-31T23:59:59Z"
          },
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z"
        }
        """;

    public static final String ERROR_EXAMPLE = """
        {
          "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid request data",
            "details": [
              {
                "field": "name",
                "message": "Name is required"
              },
              {
                "field": "email",
                "message": "Invalid email format"
              }
            ],
            "timestamp": "2024-01-01T12:00:00Z",
            "path": "/api/users",
            "requestId": "req-123"
          }
        }
        """;

    public static final String PAGINATION_EXAMPLE = """
        {
          "data": [
            {
              "id": "config-1",
              "name": "Configuration 1"
            },
            {
              "id": "config-2",
              "name": "Configuration 2"
            }
          ],
          "pagination": {
            "page": 1,
            "size": 20,
            "total": 150,
            "totalPages": 8,
            "hasNext": true,
            "hasPrevious": false
          },
          "links": {
            "self": "/api/configurations?page=1&size=20",
            "next": "/api/configurations?page=2&size=20",
            "last": "/api/configurations?page=8&size=20"
          }
        }
        """;
}

/**
 * Common Schema Definitions
 */
public class ApiSchemas {
    
    public static final String ERROR_SCHEMA = """
        {
          "type": "object",
          "properties": {
            "error": {
              "type": "object",
              "properties": {
                "code": {
                  "type": "string",
                  "description": "Error code"
                },
                "message": {
                  "type": "string",
                  "description": "Error message"
                },
                "details": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "field": {
                        "type": "string"
                      },
                      "message": {
                        "type": "string"
                      }
                    }
                  }
                },
                "timestamp": {
                  "type": "string",
                  "format": "date-time"
                },
                "path": {
                  "type": "string"
                },
                "requestId": {
                  "type": "string"
                }
              }
            }
          }
        }
        """;

    public static final String PAGINATION_SCHEMA = """
        {
          "type": "object",
          "properties": {
            "page": {
              "type": "integer",
              "description": "Current page number (1-based)"
            },
            "size": {
              "type": "integer",
              "description": "Number of items per page"
            },
            "total": {
              "type": "integer",
              "description": "Total number of items"
            },
            "totalPages": {
              "type": "integer",
              "description": "Total number of pages"
            },
            "hasNext": {
              "type": "boolean",
              "description": "Whether there is a next page"
            },
            "hasPrevious": {
              "type": "boolean",
              "description": "Whether there is a previous page"
            }
          }
        }
        """;
}

/**
 * API Operation Descriptions
 */
public class ApiDescriptions {
    
    public static final String GET_CONFIGURATIONS = """
        Retrieve a paginated list of UI configurations.
        
        Supports filtering by:
        - Name (partial match)
        - Organization
        - Created date range
        - Tags
        
        Results are sorted by creation date (newest first) by default.
        """;

    public static final String CREATE_CONFIGURATION = """
        Create a new UI configuration.
        
        The configuration must include:
        - Name (unique within organization)
        - Component definitions
        - Layout specification
        
        Optional elements:
        - Theme customization
        - Metadata and tags
        - Sharing settings
        """;

    public static final String UPDATE_CONFIGURATION = """
        Update an existing UI configuration.
        
        This operation:
        - Creates a new version of the configuration
        - Preserves the original version for rollback
        - Updates the modification timestamp
        - Validates component definitions
        """;

    public static final String DELETE_CONFIGURATION = """
        Delete a UI configuration.
        
        This operation:
        - Soft deletes the configuration (can be restored)
        - Removes it from active listings
        - Preserves audit trail
        - Requires appropriate permissions
        """;

    public static final String CLONE_CONFIGURATION = """
        Create a copy of an existing configuration.
        
        The cloned configuration:
        - Gets a new unique ID
        - Inherits all components and settings
        - Is owned by the requesting user
        - Can be modified independently
        """;

    public static final String EXPORT_CONFIGURATION = """
        Export a configuration as JSON.
        
        The exported data includes:
        - Complete configuration definition
        - Component specifications
        - Theme and layout settings
        - Metadata (excluding sensitive information)
        """;

    public static final String IMPORT_CONFIGURATION = """
        Import a configuration from JSON data.
        
        The import process:
        - Validates the configuration structure
        - Assigns new IDs to avoid conflicts
        - Sets the current user as owner
        - Preserves component relationships
        """;
}
