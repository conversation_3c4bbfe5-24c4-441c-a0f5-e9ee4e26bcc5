package com.uiplatform.websocket;

import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * WebSocket controller for handling collaborative comments and notifications.
 */
@Controller
public class CommentController {

    private static final Logger logger = LoggerFactory.getLogger(CommentController.class);

    /**
     * Handle new comment creation.
     */
    @MessageMapping("/comment/add")
    @SendTo("/topic/ui-config/{configId}/comments")
    public CollaborationEvent addComment(@DestinationVariable UUID configId,
                                       @Payload CommentRequest request,
                                       Principal principal,
                                       SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("New comment from user {} on element {} in config {}", 
                    username, request.getElementId(), configId);
        
        try {
            // Create comment object
            Comment comment = new Comment();
            comment.setId(UUID.randomUUID());
            comment.setUserId(userId);
            comment.setUsername(username);
            comment.setConfigId(configId);
            comment.setElementId(request.getElementId());
            comment.setText(request.getText());
            comment.setPosition(request.getPosition());
            comment.setCreatedAt(LocalDateTime.now());
            comment.setStatus("active");
            
            // Create collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.COMMENT_ADDED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setElementId(request.getElementId());
            event.setData(comment);
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error adding comment", e);
            return createErrorEvent(userId, username, configId, "Failed to add comment");
        }
    }

    /**
     * Handle comment updates.
     */
    @MessageMapping("/comment/update")
    @SendTo("/topic/ui-config/{configId}/comments")
    public CollaborationEvent updateComment(@DestinationVariable UUID configId,
                                          @Payload CommentUpdateRequest request,
                                          Principal principal,
                                          SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Comment update from user {} for comment {} in config {}", 
                    username, request.getCommentId(), configId);
        
        try {
            // Create updated comment object
            Comment comment = new Comment();
            comment.setId(request.getCommentId());
            comment.setUserId(userId);
            comment.setUsername(username);
            comment.setConfigId(configId);
            comment.setText(request.getText());
            comment.setUpdatedAt(LocalDateTime.now());
            
            // Create collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.COMMENT_UPDATED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setData(comment);
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error updating comment", e);
            return createErrorEvent(userId, username, configId, "Failed to update comment");
        }
    }

    /**
     * Handle comment deletion.
     */
    @MessageMapping("/comment/delete")
    @SendTo("/topic/ui-config/{configId}/comments")
    public CollaborationEvent deleteComment(@DestinationVariable UUID configId,
                                          @Payload CommentDeleteRequest request,
                                          Principal principal,
                                          SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Comment deletion from user {} for comment {} in config {}", 
                    username, request.getCommentId(), configId);
        
        try {
            // Create collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.COMMENT_DELETED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setData(request.getCommentId());
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error deleting comment", e);
            return createErrorEvent(userId, username, configId, "Failed to delete comment");
        }
    }

    /**
     * Handle comment resolution.
     */
    @MessageMapping("/comment/resolve")
    @SendTo("/topic/ui-config/{configId}/comments")
    public CollaborationEvent resolveComment(@DestinationVariable UUID configId,
                                           @Payload CommentResolveRequest request,
                                           Principal principal,
                                           SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Comment resolution from user {} for comment {} in config {}", 
                    username, request.getCommentId(), configId);
        
        try {
            // Create comment resolution object
            CommentResolution resolution = new CommentResolution();
            resolution.setCommentId(request.getCommentId());
            resolution.setResolvedBy(userId);
            resolution.setResolvedByUsername(username);
            resolution.setResolvedAt(LocalDateTime.now());
            resolution.setResolution(request.getResolution());
            
            // Create collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.COMMENT_RESOLVED);
            event.setUserId(userId);
            event.setUsername(username);
            event.setConfigId(configId);
            event.setData(resolution);
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error resolving comment", e);
            return createErrorEvent(userId, username, configId, "Failed to resolve comment");
        }
    }

    /**
     * Handle notification broadcasts.
     */
    @MessageMapping("/notification/broadcast")
    @SendTo("/topic/organization/{orgId}/notifications")
    public CollaborationEvent broadcastNotification(@DestinationVariable UUID orgId,
                                                   @Payload NotificationRequest request,
                                                   Principal principal,
                                                   SimpMessageHeaderAccessor headerAccessor) {
        
        UUID userId = UUID.fromString(principal.getName());
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        logger.debug("Notification broadcast from user {} to organization {}", username, orgId);
        
        try {
            // Create notification object
            Notification notification = new Notification();
            notification.setId(UUID.randomUUID());
            notification.setFromUserId(userId);
            notification.setFromUsername(username);
            notification.setOrganizationId(orgId);
            notification.setType(request.getType());
            notification.setTitle(request.getTitle());
            notification.setMessage(request.getMessage());
            notification.setData(request.getData());
            notification.setCreatedAt(LocalDateTime.now());
            
            // Create collaboration event
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.NOTIFICATION_BROADCAST);
            event.setUserId(userId);
            event.setUsername(username);
            event.setData(notification);
            event.setTimestamp(LocalDateTime.now());
            
            return event;
            
        } catch (Exception e) {
            logger.error("Error broadcasting notification", e);
            return createErrorEvent(userId, username, null, "Failed to broadcast notification");
        }
    }

    // Helper methods

    private CollaborationEvent createErrorEvent(UUID userId, String username, UUID configId, String errorMessage) {
        CollaborationEvent event = new CollaborationEvent();
        event.setType(CollaborationEventType.ERROR_OCCURRED);
        event.setUserId(userId);
        event.setUsername(username);
        event.setConfigId(configId);
        event.setData(errorMessage);
        event.setTimestamp(LocalDateTime.now());
        return event;
    }

    // Inner classes for request and response DTOs

    public static class CommentRequest {
        private String elementId;
        private String text;
        private CommentPosition position;
        
        // Getters and setters
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public CommentPosition getPosition() { return position; }
        public void setPosition(CommentPosition position) { this.position = position; }
    }

    public static class CommentUpdateRequest {
        private UUID commentId;
        private String text;
        
        // Getters and setters
        public UUID getCommentId() { return commentId; }
        public void setCommentId(UUID commentId) { this.commentId = commentId; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
    }

    public static class CommentDeleteRequest {
        private UUID commentId;
        
        // Getters and setters
        public UUID getCommentId() { return commentId; }
        public void setCommentId(UUID commentId) { this.commentId = commentId; }
    }

    public static class CommentResolveRequest {
        private UUID commentId;
        private String resolution;
        
        // Getters and setters
        public UUID getCommentId() { return commentId; }
        public void setCommentId(UUID commentId) { this.commentId = commentId; }
        public String getResolution() { return resolution; }
        public void setResolution(String resolution) { this.resolution = resolution; }
    }

    public static class NotificationRequest {
        private String type;
        private String title;
        private String message;
        private Object data;
        
        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }

    public static class Comment {
        private UUID id;
        private UUID userId;
        private String username;
        private UUID configId;
        private String elementId;
        private String text;
        private CommentPosition position;
        private String status;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        
        // Getters and setters
        public UUID getId() { return id; }
        public void setId(UUID id) { this.id = id; }
        public UUID getUserId() { return userId; }
        public void setUserId(UUID userId) { this.userId = userId; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public UUID getConfigId() { return configId; }
        public void setConfigId(UUID configId) { this.configId = configId; }
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public CommentPosition getPosition() { return position; }
        public void setPosition(CommentPosition position) { this.position = position; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
        public LocalDateTime getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    }

    public static class CommentPosition {
        private double x;
        private double y;
        
        // Getters and setters
        public double getX() { return x; }
        public void setX(double x) { this.x = x; }
        public double getY() { return y; }
        public void setY(double y) { this.y = y; }
    }

    public static class CommentResolution {
        private UUID commentId;
        private UUID resolvedBy;
        private String resolvedByUsername;
        private LocalDateTime resolvedAt;
        private String resolution;
        
        // Getters and setters
        public UUID getCommentId() { return commentId; }
        public void setCommentId(UUID commentId) { this.commentId = commentId; }
        public UUID getResolvedBy() { return resolvedBy; }
        public void setResolvedBy(UUID resolvedBy) { this.resolvedBy = resolvedBy; }
        public String getResolvedByUsername() { return resolvedByUsername; }
        public void setResolvedByUsername(String resolvedByUsername) { this.resolvedByUsername = resolvedByUsername; }
        public LocalDateTime getResolvedAt() { return resolvedAt; }
        public void setResolvedAt(LocalDateTime resolvedAt) { this.resolvedAt = resolvedAt; }
        public String getResolution() { return resolution; }
        public void setResolution(String resolution) { this.resolution = resolution; }
    }

    public static class Notification {
        private UUID id;
        private UUID fromUserId;
        private String fromUsername;
        private UUID organizationId;
        private String type;
        private String title;
        private String message;
        private Object data;
        private LocalDateTime createdAt;
        
        // Getters and setters
        public UUID getId() { return id; }
        public void setId(UUID id) { this.id = id; }
        public UUID getFromUserId() { return fromUserId; }
        public void setFromUserId(UUID fromUserId) { this.fromUserId = fromUserId; }
        public String getFromUsername() { return fromUsername; }
        public void setFromUsername(String fromUsername) { this.fromUsername = fromUsername; }
        public UUID getOrganizationId() { return organizationId; }
        public void setOrganizationId(UUID organizationId) { this.organizationId = organizationId; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    }
}
