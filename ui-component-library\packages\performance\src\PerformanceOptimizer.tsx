import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';

export interface PerformanceMetrics {
  renderTime: number;
  rerenderCount: number;
  memoryUsage: number;
  componentCount: number;
  lastRenderTimestamp: number;
}

export interface OptimizationConfig {
  enableMemoization: boolean;
  enableVirtualization: boolean;
  enableLazyLoading: boolean;
  enableBundleSplitting: boolean;
  maxRenderTime: number;
  virtualScrollThreshold: number;
  lazyLoadThreshold: number;
}

export interface VirtualizationOptions {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
  scrollDirection: 'vertical' | 'horizontal';
}

/**
 * Performance Optimizer for Component Library
 * 
 * Provides comprehensive performance optimizations including:
 * - Component memoization and optimization
 * - Virtual scrolling for large lists
 * - Lazy loading and code splitting
 * - Performance monitoring and metrics
 * - Memory leak detection and prevention
 */
export class ComponentPerformanceOptimizer {
  private metrics: PerformanceMetrics;
  private config: OptimizationConfig;
  private renderObserver: PerformanceObserver | null = null;
  private memoryMonitor: NodeJS.Timeout | null = null;

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = {
      enableMemoization: true,
      enableVirtualization: true,
      enableLazyLoading: true,
      enableBundleSplitting: true,
      maxRenderTime: 16, // 60fps
      virtualScrollThreshold: 100,
      lazyLoadThreshold: 50,
      ...config,
    };

    this.metrics = {
      renderTime: 0,
      rerenderCount: 0,
      memoryUsage: 0,
      componentCount: 0,
      lastRenderTimestamp: 0,
    };

    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Monitor render performance
    if ('PerformanceObserver' in window) {
      this.renderObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'measure' && entry.name.startsWith('React')) {
            this.metrics.renderTime = entry.duration;
            this.metrics.lastRenderTimestamp = entry.startTime;
          }
        });
      });

      this.renderObserver.observe({ entryTypes: ['measure'] });
    }

    // Monitor memory usage
    this.memoryMonitor = setInterval(() => {
      if ('memory' in performance) {
        this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
      }
    }, 5000);
  }

  /**
   * Optimize component with memoization
   */
  optimizeComponent<P extends ComponentProps>(
    Component: React.ComponentType<P>,
    config: ComponentConfig
  ): React.ComponentType<P> {
    if (!this.config.enableMemoization) {
      return Component;
    }

    // Create memoized component with custom comparison
    const MemoizedComponent = memo(Component, (prevProps, nextProps) => {
      return this.shouldSkipRender(prevProps, nextProps, config);
    });

    // Add display name for debugging
    MemoizedComponent.displayName = `Optimized(${Component.displayName || Component.name})`;

    return MemoizedComponent;
  }

  /**
   * Determine if component should skip re-render
   */
  private shouldSkipRender<P extends ComponentProps>(
    prevProps: P,
    nextProps: P,
    config: ComponentConfig
  ): boolean {
    // Check if props have actually changed
    const propsChanged = !this.shallowEqual(prevProps, nextProps);
    
    if (!propsChanged) {
      return true; // Skip render if props haven't changed
    }

    // Check if only non-essential props changed
    const essentialProps = config.essentialProps || Object.keys(nextProps);
    const essentialPropsChanged = essentialProps.some(
      key => prevProps[key] !== nextProps[key]
    );

    return !essentialPropsChanged;
  }

  /**
   * Shallow equality check for props
   */
  private shallowEqual(obj1: any, obj2: any): boolean {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Create virtualized list component
   */
  createVirtualizedList<T>(
    items: T[],
    renderItem: (item: T, index: number) => React.ReactNode,
    options: VirtualizationOptions
  ): React.ComponentType {
    if (!this.config.enableVirtualization || items.length < this.config.virtualScrollThreshold) {
      // Return regular list for small datasets
      return () => (
        <div>
          {items.map((item, index) => (
            <div key={index}>{renderItem(item, index)}</div>
          ))}
        </div>
      );
    }

    return () => {
      const [scrollTop, setScrollTop] = useState(0);
      const containerRef = useRef<HTMLDivElement>(null);

      const visibleRange = useMemo(() => {
        const startIndex = Math.floor(scrollTop / options.itemHeight);
        const endIndex = Math.min(
          startIndex + Math.ceil(options.containerHeight / options.itemHeight) + options.overscan,
          items.length - 1
        );
        return { startIndex: Math.max(0, startIndex - options.overscan), endIndex };
      }, [scrollTop, items.length]);

      const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
        setScrollTop(e.currentTarget.scrollTop);
      }, []);

      const visibleItems = useMemo(() => {
        return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
      }, [items, visibleRange]);

      return (
        <div
          ref={containerRef}
          style={{
            height: options.containerHeight,
            overflow: 'auto',
          }}
          onScroll={handleScroll}
        >
          <div style={{ height: items.length * options.itemHeight, position: 'relative' }}>
            {visibleItems.map((item, index) => (
              <div
                key={visibleRange.startIndex + index}
                style={{
                  position: 'absolute',
                  top: (visibleRange.startIndex + index) * options.itemHeight,
                  height: options.itemHeight,
                  width: '100%',
                }}
              >
                {renderItem(item, visibleRange.startIndex + index)}
              </div>
            ))}
          </div>
        </div>
      );
    };
  }

  /**
   * Create lazy-loaded component
   */
  createLazyComponent<P extends ComponentProps>(
    importFn: () => Promise<{ default: React.ComponentType<P> }>,
    fallback?: React.ComponentType
  ): React.ComponentType<P> {
    if (!this.config.enableLazyLoading) {
      // Return a component that immediately loads
      return (props: P) => {
        const [Component, setComponent] = useState<React.ComponentType<P> | null>(null);

        useEffect(() => {
          importFn().then(module => setComponent(() => module.default));
        }, []);

        if (!Component) {
          return fallback ? React.createElement(fallback) : <div>Loading...</div>;
        }

        return React.createElement(Component, props);
      };
    }

    const LazyComponent = React.lazy(importFn);

    return (props: P) => (
      <React.Suspense fallback={fallback ? React.createElement(fallback) : <div>Loading...</div>}>
        <LazyComponent {...props} />
      </React.Suspense>
    );
  }

  /**
   * Optimize component tree with batching
   */
  optimizeComponentTree(components: React.ReactNode[]): React.ReactNode {
    // Batch similar components together
    const batches = this.batchSimilarComponents(components);
    
    return batches.map((batch, index) => (
      <React.Fragment key={index}>
        {batch}
      </React.Fragment>
    ));
  }

  /**
   * Batch similar components for better performance
   */
  private batchSimilarComponents(components: React.ReactNode[]): React.ReactNode[][] {
    const batches: React.ReactNode[][] = [];
    let currentBatch: React.ReactNode[] = [];
    let lastComponentType: string | null = null;

    components.forEach((component) => {
      const componentType = this.getComponentType(component);
      
      if (componentType === lastComponentType) {
        currentBatch.push(component);
      } else {
        if (currentBatch.length > 0) {
          batches.push(currentBatch);
        }
        currentBatch = [component];
        lastComponentType = componentType;
      }
    });

    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    return batches;
  }

  /**
   * Get component type for batching
   */
  private getComponentType(component: React.ReactNode): string {
    if (React.isValidElement(component)) {
      return typeof component.type === 'string' 
        ? component.type 
        : component.type.displayName || component.type.name || 'Unknown';
    }
    return 'Primitive';
  }

  /**
   * Create performance-optimized event handlers
   */
  optimizeEventHandlers<T extends (...args: any[]) => any>(
    handler: T,
    dependencies: React.DependencyList
  ): T {
    return useCallback(handler, dependencies) as T;
  }

  /**
   * Create optimized computed values
   */
  optimizeComputedValue<T>(
    computeFn: () => T,
    dependencies: React.DependencyList
  ): T {
    return useMemo(computeFn, dependencies);
  }

  /**
   * Monitor component performance
   */
  monitorComponentPerformance<P extends ComponentProps>(
    Component: React.ComponentType<P>,
    componentName: string
  ): React.ComponentType<P> {
    return (props: P) => {
      const renderStartTime = useRef<number>(0);
      const renderCount = useRef<number>(0);

      useEffect(() => {
        renderStartTime.current = performance.now();
        renderCount.current++;
      });

      useEffect(() => {
        const renderTime = performance.now() - renderStartTime.current;
        
        if (renderTime > this.config.maxRenderTime) {
          console.warn(
            `Component ${componentName} render time (${renderTime.toFixed(2)}ms) exceeds threshold (${this.config.maxRenderTime}ms)`
          );
        }

        this.metrics.rerenderCount = renderCount.current;
        this.metrics.componentCount++;
      });

      return <Component {...props} />;
    };
  }

  /**
   * Detect and prevent memory leaks
   */
  preventMemoryLeaks<P extends ComponentProps>(
    Component: React.ComponentType<P>
  ): React.ComponentType<P> {
    return (props: P) => {
      const cleanupFunctions = useRef<(() => void)[]>([]);

      const addCleanup = useCallback((cleanup: () => void) => {
        cleanupFunctions.current.push(cleanup);
      }, []);

      useEffect(() => {
        return () => {
          // Execute all cleanup functions
          cleanupFunctions.current.forEach(cleanup => {
            try {
              cleanup();
            } catch (error) {
              console.error('Error during cleanup:', error);
            }
          });
          cleanupFunctions.current = [];
        };
      }, []);

      return <Component {...props} addCleanup={addCleanup} />;
    };
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      renderTime: 0,
      rerenderCount: 0,
      memoryUsage: 0,
      componentCount: 0,
      lastRenderTimestamp: 0,
    };
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(): {
    metrics: PerformanceMetrics;
    recommendations: string[];
    score: number;
  } {
    const recommendations: string[] = [];
    let score = 100;

    // Check render time
    if (this.metrics.renderTime > this.config.maxRenderTime) {
      recommendations.push('Consider optimizing component render time');
      score -= 20;
    }

    // Check rerender count
    if (this.metrics.rerenderCount > 10) {
      recommendations.push('High rerender count detected - consider memoization');
      score -= 15;
    }

    // Check memory usage
    if (this.metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('High memory usage detected - check for memory leaks');
      score -= 25;
    }

    // Check component count
    if (this.metrics.componentCount > 1000) {
      recommendations.push('Large number of components - consider virtualization');
      score -= 10;
    }

    return {
      metrics: this.getMetrics(),
      recommendations,
      score: Math.max(0, score),
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.renderObserver) {
      this.renderObserver.disconnect();
      this.renderObserver = null;
    }

    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
      this.memoryMonitor = null;
    }
  }
}

// Global performance optimizer instance
export const performanceOptimizer = new ComponentPerformanceOptimizer();

/**
 * React hook for performance optimization
 */
export const usePerformanceOptimization = (componentName: string) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(0);

  useEffect(() => {
    renderCount.current++;
    lastRenderTime.current = performance.now();
  });

  const optimizeCallback = useCallback((fn: Function, deps: React.DependencyList) => {
    return performanceOptimizer.optimizeEventHandlers(fn as any, deps);
  }, []);

  const optimizeValue = useCallback((computeFn: () => any, deps: React.DependencyList) => {
    return performanceOptimizer.optimizeComputedValue(computeFn, deps);
  }, []);

  return {
    renderCount: renderCount.current,
    lastRenderTime: lastRenderTime.current,
    optimizeCallback,
    optimizeValue,
  };
};

/**
 * Higher-order component for performance optimization
 */
export const withPerformanceOptimization = <P extends ComponentProps>(
  Component: React.ComponentType<P>,
  config: ComponentConfig
) => {
  const OptimizedComponent = performanceOptimizer.optimizeComponent(Component, config);
  const MonitoredComponent = performanceOptimizer.monitorComponentPerformance(
    OptimizedComponent,
    Component.displayName || Component.name || 'Unknown'
  );
  
  return performanceOptimizer.preventMemoryLeaks(MonitoredComponent);
};

/**
 * Performance monitoring component
 */
export const PerformanceMonitor: React.FC<{
  children: React.ReactNode;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}> = ({ children, onMetricsUpdate }) => {
  useEffect(() => {
    const interval = setInterval(() => {
      const metrics = performanceOptimizer.getMetrics();
      onMetricsUpdate?.(metrics);
    }, 1000);

    return () => clearInterval(interval);
  }, [onMetricsUpdate]);

  return <>{children}</>;
};
