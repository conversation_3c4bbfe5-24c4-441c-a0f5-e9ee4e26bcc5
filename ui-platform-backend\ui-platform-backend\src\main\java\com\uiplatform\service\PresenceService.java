package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.ActiveUser;
import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import com.uiplatform.dto.collaboration.UserPresence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Service for managing user presence and activity tracking in real-time collaboration.
 */
@Service
public class PresenceService {

    private static final Logger logger = LoggerFactory.getLogger(PresenceService.class);
    
    private static final String USER_PRESENCE_KEY = "presence:user:";
    private static final String ORGANIZATION_USERS_KEY = "presence:org:";
    private static final String CONFIG_USERS_KEY = "presence:config:";
    private static final String USER_ACTIVITY_KEY = "presence:activity:";
    private static final String USER_STATUS_KEY = "presence:status:";
    
    private static final int PRESENCE_TTL_MINUTES = 5;
    private static final int ACTIVITY_TTL_HOURS = 24;
    private static final int INACTIVE_THRESHOLD_MINUTES = 2;

    private final RedisTemplate<String, Object> redisTemplate;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    // Color palette for user identification
    private static final String[] USER_COLORS = {
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
    };

    @Autowired
    public PresenceService(RedisTemplate<String, Object> redisTemplate,
                          SimpMessagingTemplate messagingTemplate,
                          ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.messagingTemplate = messagingTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Update user presence information.
     */
    public void updateUserPresence(UUID userId, String username, UUID organizationId, 
                                  String status, UUID currentConfigId, String currentElementId) {
        try {
            UserPresence presence = new UserPresence();
            presence.setUserId(userId);
            presence.setUsername(username);
            presence.setOrganizationId(organizationId);
            presence.setOnline(true);
            presence.setStatus(status);
            presence.setLastSeen(LocalDateTime.now());
            presence.setCurrentConfigId(currentConfigId);
            presence.setCurrentElementId(currentElementId);
            
            // Store user presence
            String presenceKey = USER_PRESENCE_KEY + userId;
            redisTemplate.opsForValue().set(presenceKey, presence, PRESENCE_TTL_MINUTES, TimeUnit.MINUTES);
            
            // Add to organization users set
            String orgKey = ORGANIZATION_USERS_KEY + organizationId;
            redisTemplate.opsForSet().add(orgKey, userId.toString());
            redisTemplate.expire(orgKey, PRESENCE_TTL_MINUTES, TimeUnit.MINUTES);
            
            // Add to config users set if editing a config
            if (currentConfigId != null) {
                String configKey = CONFIG_USERS_KEY + currentConfigId;
                redisTemplate.opsForSet().add(configKey, userId.toString());
                redisTemplate.expire(configKey, PRESENCE_TTL_MINUTES, TimeUnit.MINUTES);
            }
            
            // Update activity timestamp
            updateUserActivity(userId);
            
            // Broadcast presence update
            broadcastPresenceUpdate(organizationId, presence);
            
            logger.debug("Updated presence for user {} in organization {}", username, organizationId);
            
        } catch (Exception e) {
            logger.error("Error updating user presence", e);
        }
    }

    /**
     * Set user as offline.
     */
    public void setUserOffline(UUID userId, String username, UUID organizationId) {
        try {
            UserPresence presence = getUserPresence(userId);
            if (presence != null) {
                presence.setOnline(false);
                presence.setStatus("offline");
                presence.setLastSeen(LocalDateTime.now());
                presence.setCurrentConfigId(null);
                presence.setCurrentElementId(null);
                
                // Store updated presence with shorter TTL
                String presenceKey = USER_PRESENCE_KEY + userId;
                redisTemplate.opsForValue().set(presenceKey, presence, 1, TimeUnit.HOURS);
                
                // Remove from active sets
                removeUserFromActiveSets(userId, organizationId);
                
                // Broadcast offline status
                broadcastPresenceUpdate(organizationId, presence);
                
                logger.debug("Set user {} offline in organization {}", username, organizationId);
            }
            
        } catch (Exception e) {
            logger.error("Error setting user offline", e);
        }
    }

    /**
     * Get user presence information.
     */
    public UserPresence getUserPresence(UUID userId) {
        try {
            String presenceKey = USER_PRESENCE_KEY + userId;
            Object presence = redisTemplate.opsForValue().get(presenceKey);
            
            if (presence instanceof UserPresence) {
                return (UserPresence) presence;
            }
            
            return null;
            
        } catch (Exception e) {
            logger.error("Error getting user presence", e);
            return null;
        }
    }

    /**
     * Get all active users in an organization.
     */
    public List<ActiveUser> getActiveUsersInOrganization(UUID organizationId) {
        try {
            String orgKey = ORGANIZATION_USERS_KEY + organizationId;
            Set<Object> userIds = redisTemplate.opsForSet().members(orgKey);
            
            List<ActiveUser> activeUsers = new ArrayList<>();
            
            if (userIds != null) {
                for (Object userIdObj : userIds) {
                    UUID userId = UUID.fromString(userIdObj.toString());
                    UserPresence presence = getUserPresence(userId);
                    
                    if (presence != null && presence.isOnline()) {
                        ActiveUser activeUser = convertToActiveUser(presence);
                        activeUsers.add(activeUser);
                    }
                }
            }
            
            return activeUsers;
            
        } catch (Exception e) {
            logger.error("Error getting active users in organization", e);
            return Collections.emptyList();
        }
    }

    /**
     * Get active users editing a specific UI configuration.
     */
    public List<ActiveUser> getActiveUsersInConfig(UUID configId) {
        try {
            String configKey = CONFIG_USERS_KEY + configId;
            Set<Object> userIds = redisTemplate.opsForSet().members(configKey);
            
            List<ActiveUser> activeUsers = new ArrayList<>();
            
            if (userIds != null) {
                for (Object userIdObj : userIds) {
                    UUID userId = UUID.fromString(userIdObj.toString());
                    UserPresence presence = getUserPresence(userId);
                    
                    if (presence != null && presence.isOnline() && 
                        configId.equals(presence.getCurrentConfigId())) {
                        ActiveUser activeUser = convertToActiveUser(presence);
                        activeUsers.add(activeUser);
                    }
                }
            }
            
            return activeUsers;
            
        } catch (Exception e) {
            logger.error("Error getting active users in config", e);
            return Collections.emptyList();
        }
    }

    /**
     * Update user activity timestamp.
     */
    public void updateUserActivity(UUID userId) {
        try {
            String activityKey = USER_ACTIVITY_KEY + userId;
            redisTemplate.opsForValue().set(activityKey, LocalDateTime.now(), ACTIVITY_TTL_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("Error updating user activity", e);
        }
    }

    /**
     * Check if user is currently active (recently seen).
     */
    public boolean isUserActive(UUID userId) {
        try {
            UserPresence presence = getUserPresence(userId);
            if (presence == null || !presence.isOnline()) {
                return false;
            }
            
            LocalDateTime lastSeen = presence.getLastSeen();
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(INACTIVE_THRESHOLD_MINUTES);
            
            return lastSeen.isAfter(threshold);
            
        } catch (Exception e) {
            logger.error("Error checking user activity", e);
            return false;
        }
    }

    /**
     * Get user status (active, away, idle, offline).
     */
    public String getUserStatus(UUID userId) {
        try {
            UserPresence presence = getUserPresence(userId);
            if (presence == null || !presence.isOnline()) {
                return "offline";
            }
            
            if (isUserActive(userId)) {
                return "active";
            } else {
                return "away";
            }
            
        } catch (Exception e) {
            logger.error("Error getting user status", e);
            return "offline";
        }
    }

    /**
     * Assign a color to a user for visual identification.
     */
    public String assignUserColor(UUID userId) {
        try {
            String statusKey = USER_STATUS_KEY + userId;
            String existingColor = (String) redisTemplate.opsForValue().get(statusKey);
            
            if (existingColor != null) {
                return existingColor;
            }
            
            // Assign color based on user ID hash
            int colorIndex = Math.abs(userId.hashCode()) % USER_COLORS.length;
            String color = USER_COLORS[colorIndex];
            
            // Store color with long TTL
            redisTemplate.opsForValue().set(statusKey, color, 7, TimeUnit.DAYS);
            
            return color;
            
        } catch (Exception e) {
            logger.error("Error assigning user color", e);
            return USER_COLORS[0]; // Default color
        }
    }

    /**
     * Remove user from a specific UI configuration.
     */
    public void removeUserFromConfig(UUID userId, UUID configId) {
        try {
            String configKey = CONFIG_USERS_KEY + configId;
            redisTemplate.opsForSet().remove(configKey, userId.toString());
            
            // Update user presence to remove current config
            UserPresence presence = getUserPresence(userId);
            if (presence != null && configId.equals(presence.getCurrentConfigId())) {
                presence.setCurrentConfigId(null);
                presence.setCurrentElementId(null);
                
                String presenceKey = USER_PRESENCE_KEY + userId;
                redisTemplate.opsForValue().set(presenceKey, presence, PRESENCE_TTL_MINUTES, TimeUnit.MINUTES);
                
                // Broadcast presence update
                broadcastPresenceUpdate(presence.getOrganizationId(), presence);
            }
            
        } catch (Exception e) {
            logger.error("Error removing user from config", e);
        }
    }

    /**
     * Scheduled task to clean up inactive users.
     */
    @Scheduled(fixedRate = 60000) // Run every minute
    public void cleanupInactiveUsers() {
        try {
            // This would scan for expired presence entries and clean them up
            // Implementation would depend on Redis key patterns and cleanup strategy
            logger.debug("Running inactive user cleanup");
            
        } catch (Exception e) {
            logger.error("Error during inactive user cleanup", e);
        }
    }

    /**
     * Get presence statistics for an organization.
     */
    public PresenceStatistics getPresenceStatistics(UUID organizationId) {
        try {
            List<ActiveUser> activeUsers = getActiveUsersInOrganization(organizationId);
            
            long totalUsers = activeUsers.size();
            long activeCount = activeUsers.stream()
                    .mapToLong(user -> isUserActive(user.getUserId()) ? 1 : 0)
                    .sum();
            long awayCount = totalUsers - activeCount;
            
            return new PresenceStatistics(totalUsers, activeCount, awayCount);
            
        } catch (Exception e) {
            logger.error("Error getting presence statistics", e);
            return new PresenceStatistics(0, 0, 0);
        }
    }

    // Private helper methods

    private ActiveUser convertToActiveUser(UserPresence presence) {
        ActiveUser activeUser = new ActiveUser();
        activeUser.setUserId(presence.getUserId());
        activeUser.setUsername(presence.getUsername());
        activeUser.setStatus(presence.getStatus());
        activeUser.setLastActivity(presence.getLastSeen());
        activeUser.setCurrentConfigId(presence.getCurrentConfigId());
        activeUser.setCurrentElementId(presence.getCurrentElementId());
        activeUser.setUserColor(assignUserColor(presence.getUserId()));
        
        return activeUser;
    }

    private void removeUserFromActiveSets(UUID userId, UUID organizationId) {
        try {
            // Remove from organization set
            String orgKey = ORGANIZATION_USERS_KEY + organizationId;
            redisTemplate.opsForSet().remove(orgKey, userId.toString());
            
            // Remove from all config sets (this is simplified - real implementation might track this better)
            // For now, we'll let the TTL handle cleanup
            
        } catch (Exception e) {
            logger.error("Error removing user from active sets", e);
        }
    }

    private void broadcastPresenceUpdate(UUID organizationId, UserPresence presence) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(presence.isOnline() ? CollaborationEventType.USER_ONLINE : CollaborationEventType.USER_OFFLINE);
            event.setUserId(presence.getUserId());
            event.setUsername(presence.getUsername());
            event.setData(presence);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/organization/" + organizationId + "/presence", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting presence update", e);
        }
    }

    // Statistics class

    public static class PresenceStatistics {
        private final long totalUsers;
        private final long activeUsers;
        private final long awayUsers;
        
        public PresenceStatistics(long totalUsers, long activeUsers, long awayUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.awayUsers = awayUsers;
        }
        
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getAwayUsers() { return awayUsers; }
    }
}
