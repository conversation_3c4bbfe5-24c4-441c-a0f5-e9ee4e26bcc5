import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Menu component
class UIMenu extends StatelessWidget {
  const UIMenu({
    super.key,
    required this.items,
    this.onSelected,
  });

  final List<UIMenuItem> items;
  final Function(String value)? onSelected;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: onSelected,
      itemBuilder: (context) => items.map((item) {
        return PopupMenuItem<String>(
          value: item.value,
          enabled: item.enabled,
          child: Row(
            children: [
              if (item.icon != null) ...[
                Icon(item.icon),
                SizedBox(width: DesignTokens.instance.spacing.size2),
              ],
              Text(item.label),
            ],
          ),
        );
      }).toList(),
    );
  }
}

/// Menu item data class
class UIMenuItem {
  const UIMenuItem({
    required this.value,
    required this.label,
    this.icon,
    this.enabled = true,
  });

  final String value;
  final String label;
  final IconData? icon;
  final bool enabled;
}
