import React, { useEffect, useState, useRef } from 'react';
import { useCollaboration } from './CollaborationProvider';
import './LiveCursors.scss';

export interface LiveCursorsProps {
  containerRef: React.RefObject<HTMLElement>;
  enabled?: boolean;
}

interface CursorPosition {
  x: number;
  y: number;
  userId: string;
  userName: string;
  userColor: string;
  elementId?: string;
  timestamp: number;
}

export const LiveCursors: React.FC<LiveCursorsProps> = ({
  containerRef,
  enabled = true
}) => {
  const { users, currentUser, updateCursor } = useCollaboration();
  const [cursors, setCursors] = useState<CursorPosition[]>([]);
  const lastUpdateRef = useRef<number>(0);

  // Track mouse movement and update cursor position
  useEffect(() => {
    if (!enabled || !containerRef.current || !currentUser) return;

    const container = containerRef.current;

    const handleMouseMove = (event: MouseEvent) => {
      const now = Date.now();
      
      // Throttle updates to avoid overwhelming the server
      if (now - lastUpdateRef.current < 50) return;
      lastUpdateRef.current = now;

      const rect = container.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Get element under cursor
      const elementUnderCursor = document.elementFromPoint(event.clientX, event.clientY);
      const elementId = elementUnderCursor?.getAttribute('data-component-id') || undefined;

      updateCursor(x, y, elementId);
    };

    const handleMouseLeave = () => {
      // Hide cursor when leaving the container
      updateCursor(-1, -1);
    };

    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [enabled, containerRef, currentUser, updateCursor]);

  // Update cursor positions from collaboration users
  useEffect(() => {
    const newCursors: CursorPosition[] = users
      .filter(user => user.cursor && user.cursor.x >= 0 && user.cursor.y >= 0)
      .map(user => ({
        x: user.cursor!.x,
        y: user.cursor!.y,
        userId: user.id,
        userName: user.name,
        userColor: user.color,
        elementId: user.cursor!.elementId,
        timestamp: Date.now()
      }));

    setCursors(newCursors);
  }, [users]);

  // Clean up old cursors
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setCursors(prev => prev.filter(cursor => now - cursor.timestamp < 5000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!enabled || cursors.length === 0) {
    return null;
  }

  return (
    <div className="live-cursors">
      {cursors.map(cursor => (
        <LiveCursor
          key={cursor.userId}
          position={cursor}
          containerRef={containerRef}
        />
      ))}
    </div>
  );
};

interface LiveCursorProps {
  position: CursorPosition;
  containerRef: React.RefObject<HTMLElement>;
}

const LiveCursor: React.FC<LiveCursorProps> = ({ position, containerRef }) => {
  const [isVisible, setIsVisible] = useState(true);
  const cursorRef = useRef<HTMLDivElement>(null);

  // Check if cursor is within container bounds
  useEffect(() => {
    if (!containerRef.current || !cursorRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    
    const isInBounds = 
      position.x >= 0 && 
      position.x <= rect.width && 
      position.y >= 0 && 
      position.y <= rect.height;

    setIsVisible(isInBounds);
  }, [position, containerRef]);

  // Animate cursor movement
  useEffect(() => {
    if (!cursorRef.current) return;

    const cursor = cursorRef.current;
    cursor.style.transform = `translate(${position.x}px, ${position.y}px)`;
  }, [position.x, position.y]);

  if (!isVisible) return null;

  return (
    <div
      ref={cursorRef}
      className="live-cursor"
      style={{
        '--user-color': position.userColor,
      } as React.CSSProperties}
    >
      {/* Cursor pointer */}
      <svg
        className="cursor-pointer"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M2 2L18 8L8 12L2 18V2Z"
          fill="currentColor"
          stroke="white"
          strokeWidth="1"
        />
      </svg>

      {/* User label */}
      <div className="cursor-label">
        <span className="cursor-name">{position.userName}</span>
        {position.elementId && (
          <span className="cursor-element">
            on {position.elementId}
          </span>
        )}
      </div>
    </div>
  );
};
