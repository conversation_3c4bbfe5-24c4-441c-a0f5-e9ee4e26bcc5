.template-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;

  .template-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #d9d9d9;

    .header-left {
      h2 {
        margin: 0;
        color: #262626;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .template-filters {
    margin: 16px 24px 0;
    border-radius: 8px;
  }

  .template-content {
    flex: 1;
    padding: 16px 24px;
    overflow: auto;
  }

  .template-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #666;

    p {
      margin-top: 16px;
      margin-bottom: 0;
    }
  }
}

.template-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .ant-card-cover {
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .template-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-card-body {
    padding: 16px;
  }

  .template-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;

    span {
      font-weight: 600;
      font-size: 16px;
      line-height: 1.3;
      flex: 1;
      margin-right: 8px;
    }
  }

  .template-description {
    p {
      margin-bottom: 12px;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .template-meta {
      margin-bottom: 8px;
      font-size: 12px;
      color: #999;

      .ant-rate {
        font-size: 12px;
      }
    }

    .template-tags {
      margin-bottom: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .template-author {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #666;
    }
  }

  .ant-card-actions {
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    li {
      margin: 8px 0;

      .anticon {
        font-size: 16px;
        color: #666;
        transition: color 0.2s ease;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

// List view styles
.templates-list {
  .template-card {
    .ant-card {
      display: flex;
      flex-direction: row;
      height: auto;
    }

    .ant-card-cover {
      width: 200px;
      height: 120px;
      flex-shrink: 0;
    }

    .ant-card-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .ant-card-actions {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      display: flex;
      flex-direction: row;
      gap: 8px;

      li {
        margin: 0;
        padding: 0;
        border: none;
      }
    }
  }
}

// Template preview modal
.template-preview {
  .preview-placeholder {
    margin-top: 16px;
    padding: 20px;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    text-align: center;

    p {
      margin-bottom: 16px;
      color: #666;
    }

    pre {
      text-align: left;
      background: #fff;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 16px;
      font-size: 12px;
      max-height: 300px;
      overflow: auto;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .templates-grid {
    .ant-col-lg-6 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }
  }
}

@media (max-width: 992px) {
  .templates-grid {
    .ant-col-md-8 {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }
}

@media (max-width: 768px) {
  .template-manager {
    .template-manager-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-right {
        justify-content: center;
      }
    }

    .template-filters {
      margin: 16px;

      .ant-row {
        .ant-col {
          margin-bottom: 8px;
        }
      }
    }

    .template-content {
      padding: 16px;
    }
  }

  .templates-grid {
    .ant-col-sm-12 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .templates-list {
    .template-card {
      .ant-card {
        flex-direction: column;
      }

      .ant-card-cover {
        width: 100%;
        height: 200px;
      }

      .ant-card-actions {
        position: static;
        transform: none;
        background: #fafafa;
        border-top: 1px solid #f0f0f0;
        flex-direction: row;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 576px) {
  .template-manager {
    .template-manager-header {
      padding: 12px 16px;

      .header-right {
        flex-direction: column;
        gap: 8px;
      }
    }

    .template-filters {
      margin: 12px;
    }

    .template-content {
      padding: 12px;
    }
  }
}

// Dark theme support
.dark-theme {
  .template-manager {
    background: #1f1f1f;

    .template-manager-header {
      background: #2a2a2a;
      border-bottom-color: #444;

      h2 {
        color: #fff;
      }
    }
  }

  .template-card {
    .ant-card {
      background: #2a2a2a;
      border-color: #444;
    }

    .template-placeholder {
      background: #333;
      border-bottom-color: #444;
    }

    .template-description {
      p {
        color: #ccc;
      }

      .template-meta,
      .template-author {
        color: #999;
      }
    }

    .ant-card-actions {
      background: #333;
      border-top-color: #444;

      .anticon {
        color: #ccc;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .template-preview {
    .preview-placeholder {
      background: #2a2a2a;
      border-color: #444;

      p {
        color: #ccc;
      }

      pre {
        background: #1f1f1f;
        border-color: #444;
        color: #fff;
      }
    }
  }
}

// Animation keyframes
@keyframes templateCardHover {
  from {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
  to {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// Loading skeleton styles
.template-skeleton {
  .ant-skeleton-content {
    .ant-skeleton-title {
      width: 60%;
    }

    .ant-skeleton-paragraph {
      li {
        &:nth-child(1) {
          width: 100%;
        }
        &:nth-child(2) {
          width: 80%;
        }
        &:nth-child(3) {
          width: 60%;
        }
      }
    }
  }
}
