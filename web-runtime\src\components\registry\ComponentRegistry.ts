import React, { lazy } from 'react';
import { ComponentDefinition, ComponentProps } from '@types/index';

// Core component registry for dynamic UI rendering
class ComponentRegistry {
  private components = new Map<string, ComponentDefinition>();
  private lazyComponents = new Map<string, () => Promise<{ default: React.ComponentType<any> }>>();

  constructor() {
    this.registerCoreComponents();
  }

  /**
   * Register a component in the registry
   */
  register(definition: ComponentDefinition): void {
    this.components.set(definition.type, definition);
  }

  /**
   * Register a lazy-loaded component
   */
  registerLazy(
    type: string,
    loader: () => Promise<{ default: React.ComponentType<any> }>,
    definition?: Omit<ComponentDefinition, 'component'>
  ): void {
    this.lazyComponents.set(type, loader);
    
    if (definition) {
      this.components.set(type, {
        ...definition,
        type,
        component: lazy(loader),
      });
    }
  }

  /**
   * Get a component by type
   */
  get(type: string): React.ComponentType<any> | null {
    const definition = this.components.get(type);
    return definition?.component || null;
  }

  /**
   * Get component definition
   */
  getDefinition(type: string): ComponentDefinition | null {
    return this.components.get(type) || null;
  }

  /**
   * Check if component exists
   */
  has(type: string): boolean {
    return this.components.has(type) || this.lazyComponents.has(type);
  }

  /**
   * Get all registered component types
   */
  getTypes(): string[] {
    return Array.from(this.components.keys());
  }

  /**
   * Get components by category
   */
  getByCategory(category: string): ComponentDefinition[] {
    return Array.from(this.components.values()).filter(
      def => def.category === category
    );
  }

  /**
   * Load a lazy component
   */
  async loadComponent(type: string): Promise<React.ComponentType<any> | null> {
    const loader = this.lazyComponents.get(type);
    if (!loader) return null;

    try {
      const module = await loader();
      const component = module.default;
      
      // Update the registry with the loaded component
      const existingDef = this.components.get(type);
      if (existingDef) {
        this.components.set(type, {
          ...existingDef,
          component,
        });
      }
      
      return component;
    } catch (error) {
      console.error(`Failed to load component ${type}:`, error);
      return null;
    }
  }

  /**
   * Register core components that are always available
   */
  private registerCoreComponents(): void {
    // Layout Components
    this.registerLazy('Container', () => import('../layout/Container'), {
      type: 'Container',
      category: 'layout',
      description: 'A flexible container component',
      props: [
        { name: 'maxWidth', type: 'string', description: 'Maximum width of container' },
        { name: 'padding', type: 'string', description: 'Container padding' },
        { name: 'centered', type: 'boolean', default: true, description: 'Center the container' },
      ],
    });

    this.registerLazy('Grid', () => import('../layout/Grid'), {
      type: 'Grid',
      category: 'layout',
      description: 'CSS Grid layout component',
      props: [
        { name: 'columns', type: 'number', default: 12, description: 'Number of columns' },
        { name: 'gap', type: 'string', default: '1rem', description: 'Grid gap' },
        { name: 'rows', type: 'number', description: 'Number of rows' },
      ],
    });

    this.registerLazy('Flex', () => import('../layout/Flex'), {
      type: 'Flex',
      category: 'layout',
      description: 'Flexbox layout component',
      props: [
        { name: 'direction', type: 'string', default: 'row', options: [
          { label: 'Row', value: 'row' },
          { label: 'Column', value: 'column' },
        ]},
        { name: 'justify', type: 'string', default: 'start' },
        { name: 'align', type: 'string', default: 'start' },
        { name: 'wrap', type: 'boolean', default: false },
        { name: 'gap', type: 'string', default: '1rem' },
      ],
    });

    this.registerLazy('Stack', () => import('../layout/Stack'), {
      type: 'Stack',
      category: 'layout',
      description: 'Vertical or horizontal stack layout',
      props: [
        { name: 'direction', type: 'string', default: 'vertical' },
        { name: 'spacing', type: 'string', default: '1rem' },
        { name: 'align', type: 'string', default: 'start' },
      ],
    });

    // Text Components
    this.registerLazy('Text', () => import('../display/Text'), {
      type: 'Text',
      category: 'display',
      description: 'Text display component',
      props: [
        { name: 'content', type: 'string', required: true, description: 'Text content' },
        { name: 'variant', type: 'string', default: 'body', options: [
          { label: 'Heading 1', value: 'h1' },
          { label: 'Heading 2', value: 'h2' },
          { label: 'Heading 3', value: 'h3' },
          { label: 'Body', value: 'body' },
          { label: 'Caption', value: 'caption' },
        ]},
        { name: 'color', type: 'string', description: 'Text color' },
        { name: 'align', type: 'string', default: 'left' },
      ],
    });

    this.registerLazy('Heading', () => import('../display/Heading'), {
      type: 'Heading',
      category: 'display',
      description: 'Heading component',
      props: [
        { name: 'level', type: 'number', default: 1, description: 'Heading level (1-6)' },
        { name: 'content', type: 'string', required: true, description: 'Heading text' },
        { name: 'color', type: 'string', description: 'Text color' },
      ],
    });

    // Input Components
    this.registerLazy('Input', () => import('../forms/Input'), {
      type: 'Input',
      category: 'forms',
      description: 'Text input component',
      props: [
        { name: 'name', type: 'string', required: true, description: 'Input name' },
        { name: 'label', type: 'string', description: 'Input label' },
        { name: 'placeholder', type: 'string', description: 'Placeholder text' },
        { name: 'type', type: 'string', default: 'text', options: [
          { label: 'Text', value: 'text' },
          { label: 'Email', value: 'email' },
          { label: 'Password', value: 'password' },
          { label: 'Number', value: 'number' },
        ]},
        { name: 'required', type: 'boolean', default: false },
        { name: 'disabled', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('Select', () => import('../forms/Select'), {
      type: 'Select',
      category: 'forms',
      description: 'Select dropdown component',
      props: [
        { name: 'name', type: 'string', required: true, description: 'Select name' },
        { name: 'label', type: 'string', description: 'Select label' },
        { name: 'options', type: 'array', required: true, description: 'Select options' },
        { name: 'placeholder', type: 'string', description: 'Placeholder text' },
        { name: 'multiple', type: 'boolean', default: false },
        { name: 'required', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('Checkbox', () => import('../forms/Checkbox'), {
      type: 'Checkbox',
      category: 'forms',
      description: 'Checkbox input component',
      props: [
        { name: 'name', type: 'string', required: true, description: 'Checkbox name' },
        { name: 'label', type: 'string', description: 'Checkbox label' },
        { name: 'checked', type: 'boolean', default: false },
        { name: 'disabled', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('Radio', () => import('../forms/Radio'), {
      type: 'Radio',
      category: 'forms',
      description: 'Radio button group component',
      props: [
        { name: 'name', type: 'string', required: true, description: 'Radio group name' },
        { name: 'label', type: 'string', description: 'Radio group label' },
        { name: 'options', type: 'array', required: true, description: 'Radio options' },
        { name: 'value', type: 'string', description: 'Selected value' },
        { name: 'disabled', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('Textarea', () => import('../forms/Textarea'), {
      type: 'Textarea',
      category: 'forms',
      description: 'Textarea input component',
      props: [
        { name: 'name', type: 'string', required: true, description: 'Textarea name' },
        { name: 'label', type: 'string', description: 'Textarea label' },
        { name: 'placeholder', type: 'string', description: 'Placeholder text' },
        { name: 'rows', type: 'number', default: 4, description: 'Number of rows' },
        { name: 'required', type: 'boolean', default: false },
      ],
    });

    // Action Components
    this.registerLazy('Button', () => import('../actions/Button'), {
      type: 'Button',
      category: 'actions',
      description: 'Button component',
      props: [
        { name: 'text', type: 'string', required: true, description: 'Button text' },
        { name: 'variant', type: 'string', default: 'primary', options: [
          { label: 'Primary', value: 'primary' },
          { label: 'Secondary', value: 'secondary' },
          { label: 'Outline', value: 'outline' },
          { label: 'Ghost', value: 'ghost' },
        ]},
        { name: 'size', type: 'string', default: 'md', options: [
          { label: 'Small', value: 'sm' },
          { label: 'Medium', value: 'md' },
          { label: 'Large', value: 'lg' },
        ]},
        { name: 'disabled', type: 'boolean', default: false },
        { name: 'loading', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('Link', () => import('../actions/Link'), {
      type: 'Link',
      category: 'actions',
      description: 'Link component',
      props: [
        { name: 'text', type: 'string', required: true, description: 'Link text' },
        { name: 'href', type: 'string', required: true, description: 'Link URL' },
        { name: 'external', type: 'boolean', default: false, description: 'External link' },
        { name: 'variant', type: 'string', default: 'default' },
      ],
    });

    // Display Components
    this.registerLazy('Image', () => import('../display/Image'), {
      type: 'Image',
      category: 'display',
      description: 'Image component',
      props: [
        { name: 'src', type: 'string', required: true, description: 'Image source URL' },
        { name: 'alt', type: 'string', required: true, description: 'Alt text' },
        { name: 'width', type: 'string', description: 'Image width' },
        { name: 'height', type: 'string', description: 'Image height' },
        { name: 'objectFit', type: 'string', default: 'cover' },
      ],
    });

    this.registerLazy('Card', () => import('../display/Card'), {
      type: 'Card',
      category: 'display',
      description: 'Card container component',
      props: [
        { name: 'title', type: 'string', description: 'Card title' },
        { name: 'subtitle', type: 'string', description: 'Card subtitle' },
        { name: 'padding', type: 'string', default: 'md' },
        { name: 'shadow', type: 'string', default: 'md' },
        { name: 'border', type: 'boolean', default: true },
      ],
    });

    // Data Components
    this.registerLazy('Table', () => import('../data/Table'), {
      type: 'Table',
      category: 'data',
      description: 'Data table component',
      props: [
        { name: 'columns', type: 'array', required: true, description: 'Table columns' },
        { name: 'data', type: 'array', required: true, description: 'Table data' },
        { name: 'sortable', type: 'boolean', default: false },
        { name: 'filterable', type: 'boolean', default: false },
        { name: 'pagination', type: 'boolean', default: false },
      ],
    });

    this.registerLazy('List', () => import('../data/List'), {
      type: 'List',
      category: 'data',
      description: 'List component',
      props: [
        { name: 'items', type: 'array', required: true, description: 'List items' },
        { name: 'variant', type: 'string', default: 'unordered' },
        { name: 'spacing', type: 'string', default: 'md' },
      ],
    });

    // Chart Components
    this.registerLazy('LineChart', () => import('../charts/LineChart'), {
      type: 'LineChart',
      category: 'charts',
      description: 'Line chart component',
      props: [
        { name: 'data', type: 'array', required: true, description: 'Chart data' },
        { name: 'xKey', type: 'string', required: true, description: 'X-axis data key' },
        { name: 'yKey', type: 'string', required: true, description: 'Y-axis data key' },
        { name: 'width', type: 'number', default: 400 },
        { name: 'height', type: 'number', default: 300 },
      ],
    });

    this.registerLazy('BarChart', () => import('../charts/BarChart'), {
      type: 'BarChart',
      category: 'charts',
      description: 'Bar chart component',
      props: [
        { name: 'data', type: 'array', required: true, description: 'Chart data' },
        { name: 'xKey', type: 'string', required: true, description: 'X-axis data key' },
        { name: 'yKey', type: 'string', required: true, description: 'Y-axis data key' },
        { name: 'width', type: 'number', default: 400 },
        { name: 'height', type: 'number', default: 300 },
      ],
    });

    // Navigation Components
    this.registerLazy('Menu', () => import('../navigation/Menu'), {
      type: 'Menu',
      category: 'navigation',
      description: 'Navigation menu component',
      props: [
        { name: 'items', type: 'array', required: true, description: 'Menu items' },
        { name: 'orientation', type: 'string', default: 'horizontal' },
        { name: 'variant', type: 'string', default: 'default' },
      ],
    });

    this.registerLazy('Breadcrumb', () => import('../navigation/Breadcrumb'), {
      type: 'Breadcrumb',
      category: 'navigation',
      description: 'Breadcrumb navigation component',
      props: [
        { name: 'items', type: 'array', required: true, description: 'Breadcrumb items' },
        { name: 'separator', type: 'string', default: '/' },
      ],
    });
  }
}

// Create singleton instance
export const componentRegistry = new ComponentRegistry();

// Export for external registration
export default ComponentRegistry;
