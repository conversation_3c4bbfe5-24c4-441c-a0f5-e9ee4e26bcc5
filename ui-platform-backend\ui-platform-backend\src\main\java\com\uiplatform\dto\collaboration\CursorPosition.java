package com.uiplatform.dto.collaboration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO representing cursor position for real-time tracking.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CursorPosition {
    
    private UUID userId;
    private String username;
    private UUID configId;
    private String elementId;
    
    // Cursor coordinates
    private double x;
    private double y;
    
    // Selection information
    private String selectionStart;
    private String selectionEnd;
    private String selectedText;
    
    // Element context
    private String elementType;
    private String elementPath; // XPath or CSS selector
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;
    
    // Visual indicator properties
    private String cursorColor;
    private String userInitials;
    
    // Constructors
    public CursorPosition() {
        this.timestamp = LocalDateTime.now();
    }
    
    public CursorPosition(UUID userId, String username, UUID configId, String elementId, double x, double y) {
        this.userId = userId;
        this.username = username;
        this.configId = configId;
        this.elementId = elementId;
        this.x = x;
        this.y = y;
        this.timestamp = LocalDateTime.now();
    }
    
    // Getters and Setters
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public UUID getConfigId() {
        return configId;
    }
    
    public void setConfigId(UUID configId) {
        this.configId = configId;
    }
    
    public String getElementId() {
        return elementId;
    }
    
    public void setElementId(String elementId) {
        this.elementId = elementId;
    }
    
    public double getX() {
        return x;
    }
    
    public void setX(double x) {
        this.x = x;
    }
    
    public double getY() {
        return y;
    }
    
    public void setY(double y) {
        this.y = y;
    }
    
    public String getSelectionStart() {
        return selectionStart;
    }
    
    public void setSelectionStart(String selectionStart) {
        this.selectionStart = selectionStart;
    }
    
    public String getSelectionEnd() {
        return selectionEnd;
    }
    
    public void setSelectionEnd(String selectionEnd) {
        this.selectionEnd = selectionEnd;
    }
    
    public String getSelectedText() {
        return selectedText;
    }
    
    public void setSelectedText(String selectedText) {
        this.selectedText = selectedText;
    }
    
    public String getElementType() {
        return elementType;
    }
    
    public void setElementType(String elementType) {
        this.elementType = elementType;
    }
    
    public String getElementPath() {
        return elementPath;
    }
    
    public void setElementPath(String elementPath) {
        this.elementPath = elementPath;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getCursorColor() {
        return cursorColor;
    }
    
    public void setCursorColor(String cursorColor) {
        this.cursorColor = cursorColor;
    }
    
    public String getUserInitials() {
        return userInitials;
    }
    
    public void setUserInitials(String userInitials) {
        this.userInitials = userInitials;
    }
    
    @Override
    public String toString() {
        return "CursorPosition{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", configId=" + configId +
                ", elementId='" + elementId + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", timestamp=" + timestamp +
                '}';
    }
}
