// Theme types and interfaces for the UI Builder Component Library

export type ThemeMode = 'light' | 'dark' | 'system';
export type BrandTheme = 'default' | 'corporate' | 'creative' | 'minimal' | 'vibrant';

export interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  focusVisible: boolean;
  screenReaderOptimized: boolean;
}

export interface ColorPalette {
  // Primary colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // Secondary colors
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  
  // Semantic colors
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Neutral colors
  background: string;
  surface: string;
  surfaceVariant: string;
  outline: string;
  outlineVariant: string;
  
  // Text colors
  onPrimary: string;
  onSecondary: string;
  onSurface: string;
  onSurfaceVariant: string;
  onBackground: string;
  onError: string;
  onWarning: string;
  onSuccess: string;
  onInfo: string;
  
  // Disabled states
  onSurfaceDisabled: string;
  surfaceDisabled: string;
  
  // Interactive states
  hover: string;
  pressed: string;
  focus: string;
  selected: string;
  
  // Shadows and overlays
  shadow: string;
  scrim: string;
  overlay: string;
}

export interface Typography {
  fontFamily: string;
  fontFamilyMono: string;
  
  // Font sizes
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
  };
  
  // Font weights
  fontWeight: {
    thin: number;
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
    extrabold: number;
    black: number;
  };
  
  // Line heights
  lineHeight: {
    none: number;
    tight: number;
    snug: number;
    normal: number;
    relaxed: number;
    loose: number;
  };
  
  // Letter spacing
  letterSpacing: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
  };
}

export interface Spacing {
  px: string;
  0: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  3.5: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
  9: string;
  10: string;
  11: string;
  12: string;
  14: string;
  16: string;
  20: string;
  24: string;
  28: string;
  32: string;
  36: string;
  40: string;
  44: string;
  48: string;
  52: string;
  56: string;
  60: string;
  64: string;
  72: string;
  80: string;
  96: string;
}

export interface BorderRadius {
  none: string;
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
}

export interface Shadows {
  none: string;
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
}

export interface Transitions {
  none: string;
  all: string;
  default: string;
  colors: string;
  opacity: string;
  shadow: string;
  transform: string;
  
  // Durations
  duration: {
    75: string;
    100: string;
    150: string;
    200: string;
    300: string;
    500: string;
    700: string;
    1000: string;
  };
  
  // Timing functions
  timing: {
    linear: string;
    in: string;
    out: string;
    inOut: string;
  };
}

export interface Breakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

export interface ZIndex {
  auto: string;
  0: number;
  10: number;
  20: number;
  30: number;
  40: number;
  50: number;
  
  // Semantic z-index values
  hide: number;
  base: number;
  docked: number;
  dropdown: number;
  sticky: number;
  banner: number;
  overlay: number;
  modal: number;
  popover: number;
  skipLink: number;
  toast: number;
  tooltip: number;
}

export interface Theme {
  name: string;
  mode: ThemeMode;
  brand: BrandTheme;
  
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
  transitions: Transitions;
  breakpoints: Breakpoints;
  zIndex: ZIndex;
  
  // Component-specific tokens
  components: {
    button: ComponentTokens;
    input: ComponentTokens;
    card: ComponentTokens;
    modal: ComponentTokens;
    tooltip: ComponentTokens;
    dropdown: ComponentTokens;
    table: ComponentTokens;
    form: ComponentTokens;
    navigation: ComponentTokens;
    [key: string]: ComponentTokens;
  };
  
  // Accessibility settings
  accessibility: AccessibilitySettings;
}

export interface ComponentTokens {
  [key: string]: any;
}

export interface ThemeConfig {
  mode?: ThemeMode;
  brand?: BrandTheme;
  accessibility?: Partial<AccessibilitySettings>;
  customColors?: Partial<ColorPalette>;
  customTypography?: Partial<Typography>;
  customSpacing?: Partial<Spacing>;
  customComponents?: Record<string, ComponentTokens>;
}

// Brand theme configurations
export interface BrandThemeConfig {
  name: string;
  colors: Partial<ColorPalette>;
  typography?: Partial<Typography>;
  components?: Record<string, ComponentTokens>;
}

// Theme generation utilities
export interface ThemeGeneratorOptions {
  primaryColor: string;
  secondaryColor?: string;
  mode: ThemeMode;
  brand: BrandTheme;
  accessibility?: Partial<AccessibilitySettings>;
}

// CSS variable mapping
export interface CSSVariables {
  [key: string]: string;
}

// Theme validation
export interface ThemeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Theme export/import
export interface ThemeExport {
  version: string;
  theme: Theme;
  metadata: {
    name: string;
    description?: string;
    author?: string;
    createdAt: string;
    tags?: string[];
  };
}

// Animation presets
export interface AnimationPresets {
  fadeIn: string;
  fadeOut: string;
  slideIn: string;
  slideOut: string;
  scaleIn: string;
  scaleOut: string;
  bounce: string;
  pulse: string;
  spin: string;
  ping: string;
}

// Responsive design tokens
export interface ResponsiveTokens {
  mobile: Partial<Theme>;
  tablet: Partial<Theme>;
  desktop: Partial<Theme>;
  wide: Partial<Theme>;
}

// Theme context types
export interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  updateTheme: (updates: Partial<Theme>) => void;
  resetTheme: () => void;
  
  // Mode management
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  
  // Brand management
  brandTheme: BrandTheme;
  setBrandTheme: (brand: BrandTheme) => void;
  
  // Accessibility
  accessibility: AccessibilitySettings;
  setAccessibility: (settings: Partial<AccessibilitySettings>) => void;
  
  // Available themes
  availableThemes: Theme[];
  customThemes: Theme[];
  
  // Theme utilities
  generateTheme: (options: ThemeGeneratorOptions) => Theme;
  validateTheme: (theme: Theme) => ThemeValidationResult;
  exportTheme: (theme: Theme) => ThemeExport;
  importTheme: (themeExport: ThemeExport) => Theme;
}

// Component prop types for theming
export interface ThemeableProps {
  theme?: Partial<Theme>;
  themeOverrides?: Record<string, any>;
  variant?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  colorScheme?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

// Style function types
export type StyleFunction<T = any> = (theme: Theme) => T;
export type ResponsiveStyleFunction<T = any> = (theme: Theme, breakpoint: string) => T;

// Theme hook return types
export interface UseThemeReturn extends ThemeContextType {}

export interface UseThemeValuesReturn {
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
  transitions: Transitions;
  breakpoints: Breakpoints;
  zIndex: ZIndex;
}

export interface UseThemeModeReturn {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  toggle: () => void;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
}

export interface UseAccessibilityReturn {
  settings: AccessibilitySettings;
  update: (settings: Partial<AccessibilitySettings>) => void;
  toggle: (setting: keyof AccessibilitySettings) => void;
  isHighContrast: boolean;
  isReducedMotion: boolean;
  isLargeText: boolean;
  isFocusVisible: boolean;
  isScreenReaderOptimized: boolean;
}
