import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { updateComponent, addComponent, removeComponent } from '../../store/slices/uiBuilderSlice';
import { useAnalytics } from '../../hooks/useAnalytics';

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen: string;
  cursor?: {
    x: number;
    y: number;
    elementId?: string;
  };
  selection?: {
    componentId: string;
    type: 'component' | 'text' | 'property';
  };
}

export interface CollaborationComment {
  id: string;
  userId: string;
  componentId: string;
  content: string;
  position: { x: number; y: number };
  resolved: boolean;
  createdAt: string;
  updatedAt: string;
  replies: CollaborationComment[];
}

export interface CollaborationChange {
  id: string;
  type: 'component_added' | 'component_updated' | 'component_removed' | 'property_changed';
  userId: string;
  timestamp: string;
  data: any;
  componentId?: string;
  propertyPath?: string;
  oldValue?: any;
  newValue?: any;
}

export interface CollaborationContextValue {
  // Connection state
  isConnected: boolean;
  connectionError: string | null;
  
  // Users and presence
  users: CollaborationUser[];
  currentUser: CollaborationUser | null;
  
  // Comments
  comments: CollaborationComment[];
  
  // Changes and history
  changes: CollaborationChange[];
  
  // Actions
  connect: (configId: string) => void;
  disconnect: () => void;
  updateCursor: (x: number, y: number, elementId?: string) => void;
  updateSelection: (componentId: string, type: 'component' | 'text' | 'property') => void;
  addComment: (componentId: string, content: string, position: { x: number; y: number }) => void;
  resolveComment: (commentId: string) => void;
  replyToComment: (commentId: string, content: string) => void;
  broadcastChange: (change: Omit<CollaborationChange, 'id' | 'userId' | 'timestamp'>) => void;
}

const CollaborationContext = createContext<CollaborationContextValue | undefined>(undefined);

export interface CollaborationProviderProps {
  children: React.ReactNode;
  serverUrl?: string;
  enabled?: boolean;
}

export const CollaborationProvider: React.FC<CollaborationProviderProps> = ({
  children,
  serverUrl = process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080',
  enabled = true
}) => {
  const dispatch = useDispatch();
  const { trackUserAction } = useAnalytics();
  const { currentUser: authUser, currentConfig } = useSelector((state: RootState) => state.auth);
  
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [users, setUsers] = useState<CollaborationUser[]>([]);
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null);
  const [comments, setComments] = useState<CollaborationComment[]>([]);
  const [changes, setChanges] = useState<CollaborationChange[]>([]);
  
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const heartbeatIntervalRef = useRef<NodeJS.Interval>();
  const cursorThrottleRef = useRef<NodeJS.Timeout>();

  // Generate user color
  const generateUserColor = useCallback((userId: string): string => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return colors[Math.abs(hash) % colors.length];
  }, []);

  // Connect to collaboration server
  const connect = useCallback((configId: string) => {
    if (!enabled || !authUser || socket?.connected) return;

    try {
      const newSocket = io(serverUrl, {
        auth: {
          token: localStorage.getItem('authToken'),
          userId: authUser.id,
          configId: configId
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      // Connection events
      newSocket.on('connect', () => {
        setIsConnected(true);
        setConnectionError(null);
        
        const user: CollaborationUser = {
          id: authUser.id,
          name: `${authUser.firstName} ${authUser.lastName}`,
          email: authUser.email,
          avatar: authUser.avatarUrl,
          color: generateUserColor(authUser.id),
          isOnline: true,
          lastSeen: new Date().toISOString()
        };
        
        setCurrentUser(user);
        trackUserAction('collaboration_connected', { configId });
      });

      newSocket.on('disconnect', (reason) => {
        setIsConnected(false);
        if (reason === 'io server disconnect') {
          setConnectionError('Server disconnected');
        }
        trackUserAction('collaboration_disconnected', { reason });
      });

      newSocket.on('connect_error', (error) => {
        setConnectionError(error.message);
        console.error('Collaboration connection error:', error);
      });

      // User presence events
      newSocket.on('user_joined', (user: CollaborationUser) => {
        setUsers(prev => [...prev.filter(u => u.id !== user.id), user]);
        trackUserAction('collaboration_user_joined', { userId: user.id });
      });

      newSocket.on('user_left', (userId: string) => {
        setUsers(prev => prev.filter(u => u.id !== userId));
        trackUserAction('collaboration_user_left', { userId });
      });

      newSocket.on('users_list', (usersList: CollaborationUser[]) => {
        setUsers(usersList.filter(u => u.id !== authUser.id));
      });

      // Cursor and selection events
      newSocket.on('cursor_moved', ({ userId, x, y, elementId }: {
        userId: string;
        x: number;
        y: number;
        elementId?: string;
      }) => {
        setUsers(prev => prev.map(user => 
          user.id === userId 
            ? { ...user, cursor: { x, y, elementId } }
            : user
        ));
      });

      newSocket.on('selection_changed', ({ userId, componentId, type }: {
        userId: string;
        componentId: string;
        type: 'component' | 'text' | 'property';
      }) => {
        setUsers(prev => prev.map(user => 
          user.id === userId 
            ? { ...user, selection: { componentId, type } }
            : user
        ));
      });

      // Component change events
      newSocket.on('component_changed', (change: CollaborationChange) => {
        if (change.userId === authUser.id) return; // Ignore own changes

        setChanges(prev => [change, ...prev.slice(0, 99)]); // Keep last 100 changes

        // Apply change to local state
        switch (change.type) {
          case 'component_added':
            dispatch(addComponent(change.data));
            break;
          case 'component_updated':
            dispatch(updateComponent({ id: change.componentId!, updates: change.data }));
            break;
          case 'component_removed':
            dispatch(removeComponent(change.componentId!));
            break;
        }
      });

      // Comment events
      newSocket.on('comment_added', (comment: CollaborationComment) => {
        setComments(prev => [...prev, comment]);
      });

      newSocket.on('comment_resolved', (commentId: string) => {
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, resolved: true }
            : comment
        ));
      });

      newSocket.on('comment_reply', ({ commentId, reply }: {
        commentId: string;
        reply: CollaborationComment;
      }) => {
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, replies: [...comment.replies, reply] }
            : comment
        ));
      });

      // Setup heartbeat
      heartbeatIntervalRef.current = setInterval(() => {
        if (newSocket.connected) {
          newSocket.emit('heartbeat');
        }
      }, 30000);

      setSocket(newSocket);
    } catch (error) {
      console.error('Failed to connect to collaboration server:', error);
      setConnectionError('Failed to connect');
    }
  }, [enabled, authUser, serverUrl, generateUserColor, dispatch, trackUserAction]);

  // Disconnect from collaboration server
  const disconnect = useCallback(() => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    
    setIsConnected(false);
    setUsers([]);
    setCurrentUser(null);
    setComments([]);
    setChanges([]);
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, [socket]);

  // Update cursor position (throttled)
  const updateCursor = useCallback((x: number, y: number, elementId?: string) => {
    if (!socket?.connected) return;

    if (cursorThrottleRef.current) {
      clearTimeout(cursorThrottleRef.current);
    }

    cursorThrottleRef.current = setTimeout(() => {
      socket.emit('cursor_move', { x, y, elementId });
    }, 50); // Throttle to 20fps
  }, [socket]);

  // Update selection
  const updateSelection = useCallback((componentId: string, type: 'component' | 'text' | 'property') => {
    if (!socket?.connected) return;

    socket.emit('selection_change', { componentId, type });
    
    if (currentUser) {
      setCurrentUser(prev => prev ? { ...prev, selection: { componentId, type } } : null);
    }
  }, [socket, currentUser]);

  // Add comment
  const addComment = useCallback((
    componentId: string, 
    content: string, 
    position: { x: number; y: number }
  ) => {
    if (!socket?.connected || !currentUser) return;

    const comment: Omit<CollaborationComment, 'id' | 'createdAt' | 'updatedAt'> = {
      userId: currentUser.id,
      componentId,
      content,
      position,
      resolved: false,
      replies: []
    };

    socket.emit('add_comment', comment);
    trackUserAction('collaboration_comment_added', { componentId });
  }, [socket, currentUser, trackUserAction]);

  // Resolve comment
  const resolveComment = useCallback((commentId: string) => {
    if (!socket?.connected) return;

    socket.emit('resolve_comment', commentId);
    trackUserAction('collaboration_comment_resolved', { commentId });
  }, [socket, trackUserAction]);

  // Reply to comment
  const replyToComment = useCallback((commentId: string, content: string) => {
    if (!socket?.connected || !currentUser) return;

    const reply: Omit<CollaborationComment, 'id' | 'createdAt' | 'updatedAt'> = {
      userId: currentUser.id,
      componentId: '', // Will be set by server
      content,
      position: { x: 0, y: 0 }, // Will be set by server
      resolved: false,
      replies: []
    };

    socket.emit('reply_comment', { commentId, reply });
    trackUserAction('collaboration_comment_replied', { commentId });
  }, [socket, currentUser, trackUserAction]);

  // Broadcast change
  const broadcastChange = useCallback((
    change: Omit<CollaborationChange, 'id' | 'userId' | 'timestamp'>
  ) => {
    if (!socket?.connected || !currentUser) return;

    const fullChange: CollaborationChange = {
      ...change,
      id: `${Date.now()}-${Math.random()}`,
      userId: currentUser.id,
      timestamp: new Date().toISOString()
    };

    socket.emit('component_change', fullChange);
    setChanges(prev => [fullChange, ...prev.slice(0, 99)]);
  }, [socket, currentUser]);

  // Auto-connect when config changes
  useEffect(() => {
    if (currentConfig?.id && enabled) {
      connect(currentConfig.id);
    }
    
    return () => {
      disconnect();
    };
  }, [currentConfig?.id, enabled, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const contextValue: CollaborationContextValue = {
    isConnected,
    connectionError,
    users,
    currentUser,
    comments,
    changes,
    connect,
    disconnect,
    updateCursor,
    updateSelection,
    addComment,
    resolveComment,
    replyToComment,
    broadcastChange,
  };

  return (
    <CollaborationContext.Provider value={contextValue}>
      {children}
    </CollaborationContext.Provider>
  );
};

export const useCollaboration = (): CollaborationContextValue => {
  const context = useContext(CollaborationContext);
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};
