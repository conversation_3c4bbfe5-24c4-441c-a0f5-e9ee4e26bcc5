package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.TemplateDTO;
import com.uiplatform.entity.Template;
import com.uiplatform.service.TemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for Template marketplace operations.
 */
@RestController
@RequestMapping("/api/v1/templates")
@Tag(name = "Templates", description = "Template marketplace endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TemplateController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateController.class);

    private final TemplateService templateService;

    @Autowired
    public TemplateController(TemplateService templateService) {
        this.templateService = templateService;
    }

    /**
     * Create new template.
     */
    @PostMapping
    @Operation(summary = "Create template", description = "Create a new template")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Template created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid template data")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'CREATE')")
    public ResponseEntity<ApiResponse<TemplateDTO>> createTemplate(
            @Valid @RequestBody TemplateDTO.CreateDTO createDTO,
            @Parameter(description = "Organization ID") @RequestParam(required = false) UUID organizationId,
            Authentication authentication) {
        
        UUID authorId = UUID.fromString(authentication.getName());
        logger.info("Creating template '{}' by author: {}", createDTO.getName(), authorId);
        
        try {
            TemplateDTO template = templateService.createTemplate(createDTO, authorId, organizationId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Template created successfully", template));
        } catch (Exception e) {
            logger.error("Failed to create template", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to create template: " + e.getMessage()));
        }
    }

    /**
     * Get template by ID.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get template", description = "Get template by ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    public ResponseEntity<ApiResponse<TemplateDTO>> getTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id) {
        
        logger.debug("Fetching template with ID: {}", id);
        
        try {
            TemplateDTO template = templateService.getTemplateById(id);
            return ResponseEntity.ok(ApiResponse.success("Template found", template));
        } catch (Exception e) {
            logger.error("Failed to fetch template with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Template not found: " + e.getMessage()));
        }
    }

    /**
     * Update template.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update template", description = "Update template")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid template data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'UPDATE')")
    public ResponseEntity<ApiResponse<TemplateDTO>> updateTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id,
            @Valid @RequestBody TemplateDTO updateDTO) {
        
        logger.info("Updating template with ID: {}", id);
        
        try {
            TemplateDTO template = templateService.updateTemplate(id, updateDTO);
            return ResponseEntity.ok(ApiResponse.success("Template updated successfully", template));
        } catch (Exception e) {
            logger.error("Failed to update template with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to update template: " + e.getMessage()));
        }
    }

    /**
     * Delete template.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete template", description = "Delete template (soft delete)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id) {
        
        logger.info("Deleting template with ID: {}", id);
        
        try {
            templateService.deleteTemplate(id);
            return ResponseEntity.ok(ApiResponse.success("Template deleted successfully"));
        } catch (Exception e) {
            logger.error("Failed to delete template with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to delete template: " + e.getMessage()));
        }
    }

    /**
     * Get public templates (marketplace).
     */
    @GetMapping("/marketplace")
    @Operation(summary = "Get marketplace templates", description = "Get all public templates in marketplace")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Marketplace templates retrieved successfully")
    })
    public ResponseEntity<ApiResponse<Page<TemplateDTO>>> getMarketplaceTemplates(
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Fetching marketplace templates");
        
        try {
            Page<TemplateDTO> templates = templateService.getPublicTemplates(pageable);
            
            ApiResponse<Page<TemplateDTO>> response = ApiResponse.success("Marketplace templates retrieved successfully", templates);
            response.setPagination(new ApiResponse.PaginationInfo(
                    templates.getNumber(),
                    templates.getSize(),
                    templates.getTotalElements(),
                    templates.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch marketplace templates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch marketplace templates: " + e.getMessage()));
        }
    }

    /**
     * Search templates in marketplace.
     */
    @GetMapping("/search")
    @Operation(summary = "Search templates", description = "Search templates in marketplace by criteria")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    public ResponseEntity<ApiResponse<Page<TemplateDTO>>> searchTemplates(
            @Parameter(description = "Template name") @RequestParam(required = false) String name,
            @Parameter(description = "Category") @RequestParam(required = false) String category,
            @Parameter(description = "Subcategory") @RequestParam(required = false) String subcategory,
            @Parameter(description = "Is premium") @RequestParam(required = false) Boolean isPremium,
            @Parameter(description = "Minimum price") @RequestParam(required = false) BigDecimal minPrice,
            @Parameter(description = "Maximum price") @RequestParam(required = false) BigDecimal maxPrice,
            @Parameter(description = "Minimum rating") @RequestParam(required = false) BigDecimal minRating,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Searching templates with criteria");
        
        try {
            Page<TemplateDTO> templates = templateService.searchTemplates(
                    name, category, subcategory, isPremium, minPrice, maxPrice, minRating, pageable);
            
            ApiResponse<Page<TemplateDTO>> response = ApiResponse.success("Search completed successfully", templates);
            response.setPagination(new ApiResponse.PaginationInfo(
                    templates.getNumber(),
                    templates.getSize(),
                    templates.getTotalElements(),
                    templates.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search templates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to search templates: " + e.getMessage()));
        }
    }

    /**
     * Get templates by category.
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "Get templates by category", description = "Get templates by category")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Templates retrieved successfully")
    })
    public ResponseEntity<ApiResponse<Page<TemplateDTO>>> getTemplatesByCategory(
            @Parameter(description = "Category") @PathVariable String category,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Fetching templates for category: {}", category);
        
        try {
            Page<TemplateDTO> templates = templateService.getTemplatesByCategory(category, pageable);
            
            ApiResponse<Page<TemplateDTO>> response = ApiResponse.success("Templates retrieved successfully", templates);
            response.setPagination(new ApiResponse.PaginationInfo(
                    templates.getNumber(),
                    templates.getSize(),
                    templates.getTotalElements(),
                    templates.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch templates for category: {}", category, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch templates: " + e.getMessage()));
        }
    }

    /**
     * Get featured templates.
     */
    @GetMapping("/featured")
    @Operation(summary = "Get featured templates", description = "Get featured templates")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Featured templates retrieved successfully")
    })
    public ResponseEntity<ApiResponse<List<TemplateDTO>>> getFeaturedTemplates() {
        
        logger.debug("Fetching featured templates");
        
        try {
            List<TemplateDTO> templates = templateService.getFeaturedTemplates();
            return ResponseEntity.ok(ApiResponse.success("Featured templates retrieved successfully", templates));
        } catch (Exception e) {
            logger.error("Failed to fetch featured templates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch featured templates: " + e.getMessage()));
        }
    }

    /**
     * Get most popular templates.
     */
    @GetMapping("/popular")
    @Operation(summary = "Get popular templates", description = "Get most popular templates")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Popular templates retrieved successfully")
    })
    public ResponseEntity<ApiResponse<List<TemplateDTO>>> getPopularTemplates(
            @Parameter(description = "Limit") @RequestParam(defaultValue = "10") int limit) {
        
        logger.debug("Fetching {} most popular templates", limit);
        
        try {
            List<TemplateDTO> templates = templateService.getMostPopularTemplates(limit);
            return ResponseEntity.ok(ApiResponse.success("Popular templates retrieved successfully", templates));
        } catch (Exception e) {
            logger.error("Failed to fetch popular templates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch popular templates: " + e.getMessage()));
        }
    }

    /**
     * Get highest rated templates.
     */
    @GetMapping("/top-rated")
    @Operation(summary = "Get top rated templates", description = "Get highest rated templates")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Top rated templates retrieved successfully")
    })
    public ResponseEntity<ApiResponse<List<TemplateDTO>>> getTopRatedTemplates(
            @Parameter(description = "Limit") @RequestParam(defaultValue = "10") int limit) {
        
        logger.debug("Fetching {} highest rated templates", limit);
        
        try {
            List<TemplateDTO> templates = templateService.getHighestRatedTemplates(limit);
            return ResponseEntity.ok(ApiResponse.success("Top rated templates retrieved successfully", templates));
        } catch (Exception e) {
            logger.error("Failed to fetch top rated templates", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch top rated templates: " + e.getMessage()));
        }
    }

    /**
     * Publish template.
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "Publish template", description = "Publish template to marketplace")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template published successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'UPDATE')")
    public ResponseEntity<ApiResponse<TemplateDTO>> publishTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id) {
        
        logger.info("Publishing template with ID: {}", id);
        
        try {
            TemplateDTO template = templateService.publishTemplate(id);
            return ResponseEntity.ok(ApiResponse.success("Template published successfully", template));
        } catch (Exception e) {
            logger.error("Failed to publish template with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to publish template: " + e.getMessage()));
        }
    }

    /**
     * Update template status.
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "Update template status", description = "Update template status")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template status updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'UPDATE')")
    public ResponseEntity<ApiResponse<TemplateDTO>> updateTemplateStatus(
            @Parameter(description = "Template ID") @PathVariable UUID id,
            @Parameter(description = "New status") @RequestParam Template.TemplateStatus status) {
        
        logger.info("Updating status for template ID: {} to {}", id, status);
        
        try {
            TemplateDTO template = templateService.updateTemplateStatus(id, status);
            return ResponseEntity.ok(ApiResponse.success("Template status updated successfully", template));
        } catch (Exception e) {
            logger.error("Failed to update status for template ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to update template status: " + e.getMessage()));
        }
    }

    /**
     * Download template.
     */
    @PostMapping("/{id}/download")
    @Operation(summary = "Download template", description = "Download template (increment download count)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Template download recorded"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    public ResponseEntity<ApiResponse<Void>> downloadTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id) {
        
        logger.info("Recording download for template ID: {}", id);
        
        try {
            templateService.downloadTemplate(id);
            return ResponseEntity.ok(ApiResponse.success("Template download recorded"));
        } catch (Exception e) {
            logger.error("Failed to record download for template ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to download template: " + e.getMessage()));
        }
    }

    /**
     * Clone template.
     */
    @PostMapping("/{id}/clone")
    @Operation(summary = "Clone template", description = "Clone template for user's organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Template cloned successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Template not found")
    })
    @PreAuthorize("hasPermission('TEMPLATE', 'CREATE')")
    public ResponseEntity<ApiResponse<TemplateDTO>> cloneTemplate(
            @Parameter(description = "Template ID") @PathVariable UUID id,
            @Parameter(description = "New name") @RequestParam String newName,
            Authentication authentication) {
        
        UUID userId = UUID.fromString(authentication.getName());
        logger.info("Cloning template {} with new name '{}' for user {}", id, newName, userId);
        
        try {
            TemplateDTO template = templateService.cloneTemplate(id, newName, userId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Template cloned successfully", template));
        } catch (Exception e) {
            logger.error("Failed to clone template with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to clone template: " + e.getMessage()));
        }
    }

    /**
     * Get all template categories.
     */
    @GetMapping("/categories")
    @Operation(summary = "Get template categories", description = "Get all template categories")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Categories retrieved successfully")
    })
    public ResponseEntity<ApiResponse<List<String>>> getTemplateCategories() {
        
        logger.debug("Fetching template categories");
        
        try {
            List<String> categories = templateService.getAllCategories();
            return ResponseEntity.ok(ApiResponse.success("Categories retrieved successfully", categories));
        } catch (Exception e) {
            logger.error("Failed to fetch template categories", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch categories: " + e.getMessage()));
        }
    }

    /**
     * Get subcategories by category.
     */
    @GetMapping("/categories/{category}/subcategories")
    @Operation(summary = "Get subcategories", description = "Get subcategories for a category")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Subcategories retrieved successfully")
    })
    public ResponseEntity<ApiResponse<List<String>>> getSubcategories(
            @Parameter(description = "Category") @PathVariable String category) {
        
        logger.debug("Fetching subcategories for category: {}", category);
        
        try {
            List<String> subcategories = templateService.getSubcategoriesByCategory(category);
            return ResponseEntity.ok(ApiResponse.success("Subcategories retrieved successfully", subcategories));
        } catch (Exception e) {
            logger.error("Failed to fetch subcategories for category: {}", category, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch subcategories: " + e.getMessage()));
        }
    }
}
