import React, { useState, useEffect, useMemo } from 'react';
import { Card, Row, Col, Select, DatePicker, Spin, Alert, Statistic, Progress } from 'antd';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  EyeOutlined, 
  EditOutlined, 
  UserOutlined, 
  ClockCircleOutlined,
  TrophyOutlined,
  RiseOutlined
} from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import { useAnalytics } from '../../hooks/useAnalytics';
import { formatNumber, formatDuration, formatPercentage } from '../../utils/formatters';
import './AnalyticsDashboard.scss';

const { RangePicker } = DatePicker;
const { Option } = Select;

export interface AnalyticsDashboardProps {
  configId?: string;
  organizationId?: string;
  workspaceId?: string;
  className?: string;
}

interface MetricCard {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  suffix?: string;
  formatter?: (value: number) => string;
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff'];

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  configId,
  organizationId,
  workspaceId,
  className
}) => {
  const [timeRange, setTimeRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'days'),
    dayjs()
  ]);
  const [granularity, setGranularity] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'views', 'edits', 'collaborators', 'timeSpent'
  ]);

  const {
    data: analyticsData,
    loading,
    error,
    fetchAnalytics,
    fetchComponentUsage,
    fetchUserActivity,
    fetchPerformanceMetrics
  } = useAnalytics();

  // Fetch analytics data when parameters change
  useEffect(() => {
    const params = {
      configId,
      organizationId,
      workspaceId,
      startDate: timeRange[0].toISOString(),
      endDate: timeRange[1].toISOString(),
      granularity
    };

    fetchAnalytics(params);
    if (configId) {
      fetchComponentUsage(configId);
      fetchUserActivity(configId);
      fetchPerformanceMetrics(configId);
    }
  }, [configId, organizationId, workspaceId, timeRange, granularity, fetchAnalytics, fetchComponentUsage, fetchUserActivity, fetchPerformanceMetrics]);

  // Calculate summary metrics
  const summaryMetrics = useMemo((): MetricCard[] => {
    if (!analyticsData?.summary) return [];

    const { summary, trends } = analyticsData;

    return [
      {
        title: 'Total Views',
        value: summary.totalViews || 0,
        icon: <EyeOutlined />,
        color: '#1890ff',
        trend: trends?.views ? {
          value: trends.views.percentage,
          isPositive: trends.views.isPositive
        } : undefined,
        formatter: formatNumber
      },
      {
        title: 'Total Edits',
        value: summary.totalEdits || 0,
        icon: <EditOutlined />,
        color: '#52c41a',
        trend: trends?.edits ? {
          value: trends.edits.percentage,
          isPositive: trends.edits.isPositive
        } : undefined,
        formatter: formatNumber
      },
      {
        title: 'Active Users',
        value: summary.activeUsers || 0,
        icon: <UserOutlined />,
        color: '#722ed1',
        trend: trends?.users ? {
          value: trends.users.percentage,
          isPositive: trends.users.isPositive
        } : undefined,
        formatter: formatNumber
      },
      {
        title: 'Avg. Session Time',
        value: summary.averageSessionTime || 0,
        icon: <ClockCircleOutlined />,
        color: '#fa8c16',
        trend: trends?.sessionTime ? {
          value: trends.sessionTime.percentage,
          isPositive: trends.sessionTime.isPositive
        } : undefined,
        formatter: formatDuration
      }
    ];
  }, [analyticsData]);

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!analyticsData?.timeSeries) return [];

    return analyticsData.timeSeries.map(item => ({
      date: dayjs(item.timestamp).format(granularity === 'hour' ? 'HH:mm' : 'MMM DD'),
      views: item.views || 0,
      edits: item.edits || 0,
      collaborators: item.collaborators || 0,
      timeSpent: item.timeSpent || 0
    }));
  }, [analyticsData?.timeSeries, granularity]);

  // Prepare component usage data
  const componentUsageData = useMemo(() => {
    if (!analyticsData?.componentUsage) return [];

    return analyticsData.componentUsage
      .slice(0, 10) // Top 10 components
      .map(item => ({
        name: item.componentName,
        usage: item.usageCount,
        percentage: item.percentage
      }));
  }, [analyticsData?.componentUsage]);

  // Prepare user activity data
  const userActivityData = useMemo(() => {
    if (!analyticsData?.userActivity) return [];

    return analyticsData.userActivity
      .slice(0, 8) // Top 8 users
      .map(item => ({
        name: item.userName,
        edits: item.editCount,
        timeSpent: item.timeSpent,
        lastActive: item.lastActiveAt
      }));
  }, [analyticsData?.userActivity]);

  const handleTimeRangeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      setTimeRange(dates);
    }
  };

  const handleGranularityChange = (value: 'hour' | 'day' | 'week' | 'month') => {
    setGranularity(value);
  };

  const handleMetricsChange = (values: string[]) => {
    setSelectedMetrics(values);
  };

  if (loading) {
    return (
      <div className="analytics-dashboard-loading">
        <Spin size="large" />
        <p>Loading analytics data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error Loading Analytics"
        description={error}
        type="error"
        showIcon
        className="analytics-dashboard-error"
      />
    );
  }

  return (
    <div className={`analytics-dashboard ${className || ''}`}>
      {/* Controls */}
      <Card className="analytics-controls" size="small">
        <Row gutter={16} align="middle">
          <Col>
            <label>Time Range:</label>
            <RangePicker
              value={timeRange}
              onChange={handleTimeRangeChange}
              format="YYYY-MM-DD"
              allowClear={false}
            />
          </Col>
          <Col>
            <label>Granularity:</label>
            <Select value={granularity} onChange={handleGranularityChange} style={{ width: 100 }}>
              <Option value="hour">Hour</Option>
              <Option value="day">Day</Option>
              <Option value="week">Week</Option>
              <Option value="month">Month</Option>
            </Select>
          </Col>
          <Col>
            <label>Metrics:</label>
            <Select
              mode="multiple"
              value={selectedMetrics}
              onChange={handleMetricsChange}
              style={{ minWidth: 200 }}
              placeholder="Select metrics"
            >
              <Option value="views">Views</Option>
              <Option value="edits">Edits</Option>
              <Option value="collaborators">Collaborators</Option>
              <Option value="timeSpent">Time Spent</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Summary Metrics */}
      <Row gutter={[16, 16]} className="analytics-summary">
        {summaryMetrics.map((metric, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card className="metric-card">
              <Statistic
                title={metric.title}
                value={metric.value}
                prefix={metric.icon}
                valueStyle={{ color: metric.color }}
                formatter={metric.formatter}
              />
              {metric.trend && (
                <div className={`trend ${metric.trend.isPositive ? 'positive' : 'negative'}`}>
                  <RiseOutlined 
                    style={{ 
                      transform: metric.trend.isPositive ? 'none' : 'rotate(180deg)',
                      color: metric.trend.isPositive ? '#52c41a' : '#ff4d4f'
                    }} 
                  />
                  {formatPercentage(metric.trend.value)}
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]} className="analytics-charts">
        {/* Time Series Chart */}
        <Col xs={24} lg={16}>
          <Card title="Activity Over Time" className="chart-card">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                {selectedMetrics.includes('views') && (
                  <Line type="monotone" dataKey="views" stroke="#1890ff" strokeWidth={2} />
                )}
                {selectedMetrics.includes('edits') && (
                  <Line type="monotone" dataKey="edits" stroke="#52c41a" strokeWidth={2} />
                )}
                {selectedMetrics.includes('collaborators') && (
                  <Line type="monotone" dataKey="collaborators" stroke="#722ed1" strokeWidth={2} />
                )}
                {selectedMetrics.includes('timeSpent') && (
                  <Line type="monotone" dataKey="timeSpent" stroke="#fa8c16" strokeWidth={2} />
                )}
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Component Usage Pie Chart */}
        <Col xs={24} lg={8}>
          <Card title="Component Usage" className="chart-card">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={componentUsageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} (${formatPercentage(percentage)})`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="usage"
                >
                  {componentUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* User Activity Bar Chart */}
        <Col xs={24} lg={12}>
          <Card title="Top Contributors" className="chart-card">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={userActivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="edits" fill="#1890ff" name="Edits" />
                <Bar dataKey="timeSpent" fill="#52c41a" name="Time Spent (min)" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Performance Metrics */}
        <Col xs={24} lg={12}>
          <Card title="Performance Metrics" className="chart-card">
            <div className="performance-metrics">
              {analyticsData?.performance && (
                <>
                  <div className="metric-item">
                    <span>Load Time</span>
                    <Progress 
                      percent={Math.min((analyticsData.performance.loadTime / 5000) * 100, 100)} 
                      format={() => `${analyticsData.performance.loadTime}ms`}
                      strokeColor="#1890ff"
                    />
                  </div>
                  <div className="metric-item">
                    <span>Render Time</span>
                    <Progress 
                      percent={Math.min((analyticsData.performance.renderTime / 1000) * 100, 100)} 
                      format={() => `${analyticsData.performance.renderTime}ms`}
                      strokeColor="#52c41a"
                    />
                  </div>
                  <div className="metric-item">
                    <span>Memory Usage</span>
                    <Progress 
                      percent={analyticsData.performance.memoryUsage} 
                      format={() => `${analyticsData.performance.memoryUsage}%`}
                      strokeColor="#fa8c16"
                    />
                  </div>
                  <div className="metric-item">
                    <span>Error Rate</span>
                    <Progress 
                      percent={analyticsData.performance.errorRate} 
                      format={() => `${analyticsData.performance.errorRate}%`}
                      strokeColor="#ff4d4f"
                    />
                  </div>
                </>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Detailed Tables */}
      {configId && (
        <Row gutter={[16, 16]} className="analytics-tables">
          <Col xs={24}>
            <Card title="Detailed Analytics" className="table-card">
              {/* Add detailed tables for component usage, user activity, etc. */}
              <p>Detailed analytics tables would go here...</p>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};
