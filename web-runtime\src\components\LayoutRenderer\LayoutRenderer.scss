.layout-renderer {
  width: 100%;
  height: 100%;
  position: relative;
  
  &.layout-debug {
    outline: 2px dashed rgba(59, 130, 246, 0.5);
    outline-offset: -2px;
    
    .layout-debug-info {
      position: absolute;
      top: 4px;
      left: 4px;
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 10px;
      font-family: monospace;
      z-index: 1000;
      pointer-events: none;
      
      .debug-label {
        font-weight: bold;
        margin-bottom: 2px;
      }
      
      .debug-details {
        display: flex;
        flex-direction: column;
        gap: 1px;
        
        span {
          font-size: 9px;
          opacity: 0.9;
        }
      }
    }
  }
}

// Flex layout styles
.layout-flex {
  &.layout-direction-row {
    flex-direction: row;
  }
  
  &.layout-direction-column {
    flex-direction: column;
  }
  
  &.layout-wrap {
    flex-wrap: wrap;
  }
}

// Grid layout styles
.layout-grid {
  display: grid;
  
  // Auto-sizing utilities
  &.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  &.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

// Absolute layout styles
.layout-absolute {
  position: relative;
  
  .layout-item {
    position: absolute;
  }
}

// Flow layout styles
.layout-flow {
  display: block;
  
  .layout-item {
    display: block;
    margin-bottom: var(--spacing-md, 1rem);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Layout item styles
.layout-item {
  position: relative;
  
  &.layout-item-flex {
    // Flex item specific styles
    min-width: 0; // Prevent flex items from overflowing
    min-height: 0;
  }
  
  &.layout-item-grid {
    // Grid item specific styles
    min-width: 0; // Prevent grid items from overflowing
    min-height: 0;
  }
  
  &.layout-item-absolute {
    // Absolute positioned item styles
    position: absolute;
  }
  
  &.layout-item-flow {
    // Flow item specific styles
    display: block;
  }
}

// Responsive utilities
@media (max-width: 767px) {
  .layout-renderer {
    &.responsive-mobile {
      // Mobile-specific layout adjustments
      .layout-grid {
        grid-template-columns: 1fr !important;
      }
      
      .layout-flex {
        &.responsive-stack {
          flex-direction: column !important;
        }
      }
    }
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .layout-renderer {
    &.responsive-tablet {
      // Tablet-specific layout adjustments
    }
  }
}

@media (min-width: 1024px) {
  .layout-renderer {
    &.responsive-desktop {
      // Desktop-specific layout adjustments
    }
  }
}

// Common layout patterns
.layout-pattern {
  &.header-content-footer {
    display: grid;
    grid-template-rows: auto 1fr auto;
    grid-template-areas: 
      "header"
      "content"
      "footer";
    min-height: 100vh;
    
    .layout-item {
      &[data-area="header"] {
        grid-area: header;
      }
      
      &[data-area="content"] {
        grid-area: content;
      }
      
      &[data-area="footer"] {
        grid-area: footer;
      }
    }
  }
  
  &.sidebar-content {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-areas: "sidebar content";
    min-height: 100vh;
    
    .layout-item {
      &[data-area="sidebar"] {
        grid-area: sidebar;
      }
      
      &[data-area="content"] {
        grid-area: content;
      }
    }
    
    @media (max-width: 767px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
      grid-template-areas: 
        "content"
        "sidebar";
    }
  }
  
  &.dashboard {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    grid-template-areas: 
      "sidebar header"
      "sidebar content";
    min-height: 100vh;
    
    .layout-item {
      &[data-area="sidebar"] {
        grid-area: sidebar;
      }
      
      &[data-area="header"] {
        grid-area: header;
      }
      
      &[data-area="content"] {
        grid-area: content;
      }
    }
    
    @media (max-width: 767px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto 1fr;
      grid-template-areas: 
        "header"
        "sidebar"
        "content";
    }
  }
}

// Gap utilities
.gap-xs { gap: var(--spacing-xs, 0.25rem); }
.gap-sm { gap: var(--spacing-sm, 0.5rem); }
.gap-md { gap: var(--spacing-md, 1rem); }
.gap-lg { gap: var(--spacing-lg, 1.5rem); }
.gap-xl { gap: var(--spacing-xl, 2rem); }
.gap-2xl { gap: var(--spacing-2xl, 3rem); }

// Alignment utilities
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }
.align-baseline { align-items: baseline; }

// Flex utilities
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.flex-grow { flex-grow: 1; }
.flex-shrink { flex-shrink: 1; }
.flex-no-shrink { flex-shrink: 0; }

// Grid utilities
.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-full { grid-column: 1 / -1; }

.row-span-1 { grid-row: span 1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-full { grid-row: 1 / -1; }

// Position utilities
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// Z-index utilities
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

// Overflow utilities
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-visible { overflow: visible; }

.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-scroll { overflow-y: scroll; }

// Dark theme support
.dark-theme {
  .layout-renderer {
    &.layout-debug {
      outline-color: rgba(96, 165, 250, 0.5);
      
      .layout-debug-info {
        background: rgba(96, 165, 250, 0.9);
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .layout-renderer {
    &.layout-debug {
      outline-width: 3px;
      outline-color: #000;
      
      .layout-debug-info {
        background: #000;
        border: 1px solid #fff;
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .layout-renderer,
  .layout-item {
    transition: none !important;
    animation: none !important;
  }
}

// Print styles
@media print {
  .layout-renderer {
    &.layout-debug {
      outline: none;
      
      .layout-debug-info {
        display: none;
      }
    }
  }
  
  .layout-absolute {
    position: static;
    
    .layout-item {
      position: static;
      page-break-inside: avoid;
    }
  }
}
