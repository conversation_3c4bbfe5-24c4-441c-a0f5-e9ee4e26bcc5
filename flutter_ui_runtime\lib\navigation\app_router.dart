import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../screens/home_screen.dart';
import '../screens/configuration_screen.dart';
import '../screens/preview_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/templates_screen.dart';
import '../screens/collaboration_screen.dart';
import '../services/auth_service.dart';

/// Navigation System for Flutter UI Runtime
/// 
/// Provides declarative navigation using GoRouter with:
/// - Deep linking support
/// - Platform-specific transitions
/// - Authentication guards
/// - Nested navigation
/// - Custom route matching

class AppRouter {
  static final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter createRouter(WidgetRef ref) {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/home',
      debugLogDiagnostics: true,
      redirect: (context, state) => _handleRedirect(context, state, ref),
      routes: [
        // Authentication routes
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) => const RegisterScreen(),
        ),
        
        // Main app shell with bottom navigation
        ShellRoute(
          navigatorKey: _shellNavigatorKey,
          builder: (context, state, child) => AppShell(child: child),
          routes: [
            // Home tab
            GoRoute(
              path: '/home',
              name: 'home',
              builder: (context, state) => const HomeScreen(),
              routes: [
                GoRoute(
                  path: 'configuration/:id',
                  name: 'configuration-detail',
                  builder: (context, state) {
                    final configId = state.pathParameters['id']!;
                    return ConfigurationScreen(configurationId: configId);
                  },
                ),
              ],
            ),
            
            // Templates tab
            GoRoute(
              path: '/templates',
              name: 'templates',
              builder: (context, state) => const TemplatesScreen(),
              routes: [
                GoRoute(
                  path: ':id',
                  name: 'template-detail',
                  builder: (context, state) {
                    final templateId = state.pathParameters['id']!;
                    return TemplateDetailScreen(templateId: templateId);
                  },
                ),
                GoRoute(
                  path: ':id/preview',
                  name: 'template-preview',
                  builder: (context, state) {
                    final templateId = state.pathParameters['id']!;
                    return PreviewScreen(templateId: templateId);
                  },
                ),
              ],
            ),
            
            // Collaboration tab
            GoRoute(
              path: '/collaboration',
              name: 'collaboration',
              builder: (context, state) => const CollaborationScreen(),
              routes: [
                GoRoute(
                  path: 'session/:id',
                  name: 'collaboration-session',
                  builder: (context, state) {
                    final sessionId = state.pathParameters['id']!;
                    return CollaborationSessionScreen(sessionId: sessionId);
                  },
                ),
              ],
            ),
            
            // Profile tab
            GoRoute(
              path: '/profile',
              name: 'profile',
              builder: (context, state) => const ProfileScreen(),
              routes: [
                GoRoute(
                  path: 'settings',
                  name: 'settings',
                  builder: (context, state) => const SettingsScreen(),
                ),
              ],
            ),
          ],
        ),
        
        // Full-screen routes
        GoRoute(
          path: '/preview/:id',
          name: 'preview',
          builder: (context, state) {
            final configId = state.pathParameters['id']!;
            final mode = state.uri.queryParameters['mode'] ?? 'mobile';
            return PreviewScreen(
              configurationId: configId,
              previewMode: mode,
            );
          },
        ),
        
        // Configuration editor (full-screen)
        GoRoute(
          path: '/editor/:id',
          name: 'editor',
          builder: (context, state) {
            final configId = state.pathParameters['id']!;
            return ConfigurationEditorScreen(configurationId: configId);
          },
        ),
        
        // Deep link handlers
        GoRoute(
          path: '/share/:shareId',
          name: 'shared-configuration',
          builder: (context, state) {
            final shareId = state.pathParameters['shareId']!;
            return SharedConfigurationScreen(shareId: shareId);
          },
        ),
        
        // Error route
        GoRoute(
          path: '/error',
          name: 'error',
          builder: (context, state) {
            final error = state.extra as String? ?? 'Unknown error';
            return ErrorScreen(error: error);
          },
        ),
      ],
      errorBuilder: (context, state) => ErrorScreen(
        error: 'Page not found: ${state.location}',
      ),
    );
  }

  static String? _handleRedirect(BuildContext context, GoRouterState state, WidgetRef ref) {
    final authService = ref.read(authServiceProvider);
    final isAuthenticated = authService.isAuthenticated;
    final isAuthRoute = state.location.startsWith('/login') || state.location.startsWith('/register');
    
    // Redirect to login if not authenticated and not on auth route
    if (!isAuthenticated && !isAuthRoute) {
      return '/login';
    }
    
    // Redirect to home if authenticated and on auth route
    if (isAuthenticated && isAuthRoute) {
      return '/home';
    }
    
    return null;
  }
}

/// App Shell with bottom navigation
class AppShell extends ConsumerWidget {
  final Widget child;

  const AppShell({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: child,
      bottomNavigationBar: const AppBottomNavigation(),
    );
  }
}

/// Bottom navigation bar
class AppBottomNavigation extends ConsumerWidget {
  const AppBottomNavigation({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).location;
    
    int selectedIndex = 0;
    if (currentLocation.startsWith('/templates')) {
      selectedIndex = 1;
    } else if (currentLocation.startsWith('/collaboration')) {
      selectedIndex = 2;
    } else if (currentLocation.startsWith('/profile')) {
      selectedIndex = 3;
    }

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: selectedIndex,
      onTap: (index) => _onTabTapped(context, index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.template_outlined),
          label: 'Templates',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.people),
          label: 'Collaborate',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }

  void _onTabTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/templates');
        break;
      case 2:
        context.go('/collaboration');
        break;
      case 3:
        context.go('/profile');
        break;
    }
  }
}

/// Navigation extensions for easy routing
extension AppNavigation on BuildContext {
  void goToConfiguration(String configId) {
    go('/home/<USER>/$configId');
  }
  
  void goToTemplate(String templateId) {
    go('/templates/$templateId');
  }
  
  void goToPreview(String configId, {String mode = 'mobile'}) {
    go('/preview/$configId?mode=$mode');
  }
  
  void goToEditor(String configId) {
    go('/editor/$configId');
  }
  
  void goToCollaborationSession(String sessionId) {
    go('/collaboration/session/$sessionId');
  }
  
  void goToSharedConfiguration(String shareId) {
    go('/share/$shareId');
  }
  
  void goToSettings() {
    go('/profile/settings');
  }
}

/// Route information provider
final currentRouteProvider = Provider<GoRouterState>((ref) {
  throw UnimplementedError('currentRouteProvider must be overridden');
});

/// Navigation service for programmatic navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = AppRouter._rootNavigatorKey;
  
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  static void goBack() {
    if (navigatorKey.currentState?.canPop() == true) {
      navigatorKey.currentState?.pop();
    }
  }
  
  static void goToLogin() {
    navigatorKey.currentContext?.go('/login');
  }
  
  static void goToHome() {
    navigatorKey.currentContext?.go('/home');
  }
  
  static void showError(String error) {
    navigatorKey.currentContext?.go('/error', extra: error);
  }
  
  static void showSnackBar(String message) {
    final context = currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }
}

/// Deep link handler
class DeepLinkHandler {
  static void handleDeepLink(String link) {
    final uri = Uri.parse(link);
    final context = NavigationService.currentContext;
    
    if (context == null) return;
    
    // Handle different deep link patterns
    if (uri.pathSegments.isNotEmpty) {
      switch (uri.pathSegments.first) {
        case 'config':
          if (uri.pathSegments.length > 1) {
            context.goToConfiguration(uri.pathSegments[1]);
          }
          break;
        case 'template':
          if (uri.pathSegments.length > 1) {
            context.goToTemplate(uri.pathSegments[1]);
          }
          break;
        case 'share':
          if (uri.pathSegments.length > 1) {
            context.goToSharedConfiguration(uri.pathSegments[1]);
          }
          break;
        case 'collaborate':
          if (uri.pathSegments.length > 1) {
            context.goToCollaborationSession(uri.pathSegments[1]);
          }
          break;
        default:
          context.go('/home');
      }
    }
  }
}

/// Route guards for authentication and permissions
class RouteGuards {
  static bool requiresAuth(String route) {
    const publicRoutes = ['/login', '/register', '/error'];
    return !publicRoutes.contains(route) && !route.startsWith('/share/');
  }
  
  static bool requiresPermission(String route, List<String> userPermissions) {
    // Define route permissions
    const routePermissions = {
      '/editor': ['edit_configurations'],
      '/collaboration': ['collaborate'],
      '/profile/settings': ['manage_profile'],
    };
    
    final requiredPermissions = routePermissions[route];
    if (requiredPermissions == null) return true;
    
    return requiredPermissions.every(userPermissions.contains);
  }
}

/// Platform-specific transitions
class PlatformTransitions {
  static CustomTransitionPage<void> buildTransition({
    required Widget child,
    required GoRouterState state,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return CustomTransitionPage<void>(
      key: state.pageKey,
      child: child,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Use platform-appropriate transitions
        switch (Theme.of(context).platform) {
          case TargetPlatform.iOS:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero),
              ),
              child: child,
            );
          case TargetPlatform.android:
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position: animation.drive(
                  Tween(begin: const Offset(0.0, 0.1), end: Offset.zero),
                ),
                child: child,
              ),
            );
          default:
            return FadeTransition(opacity: animation, child: child);
        }
      },
    );
  }
}

/// Router provider for dependency injection
final routerProvider = Provider<GoRouter>((ref) {
  return AppRouter.createRouter(ref);
});

/// Current location provider
final currentLocationProvider = Provider<String>((ref) {
  // This would be updated by the router
  return '/home';
});

/// Navigation history provider
final navigationHistoryProvider = StateNotifierProvider<NavigationHistoryNotifier, List<String>>((ref) {
  return NavigationHistoryNotifier();
});

class NavigationHistoryNotifier extends StateNotifier<List<String>> {
  NavigationHistoryNotifier() : super([]);
  
  void addRoute(String route) {
    state = [...state, route];
    
    // Keep only last 10 routes
    if (state.length > 10) {
      state = state.sublist(state.length - 10);
    }
  }
  
  void clearHistory() {
    state = [];
  }
  
  String? get previousRoute => state.length > 1 ? state[state.length - 2] : null;
}
