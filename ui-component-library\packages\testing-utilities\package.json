{"name": "@ui-builder/testing-utilities", "version": "1.0.0", "description": "Testing utilities for UI Builder component library", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}"}, "dependencies": {"@ui-builder/design-tokens": "workspace:*", "@ui-builder/react-components": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "jest-axe": "^8.0.0", "msw": "^2.0.11"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "vitest": "^0.34.0"}, "devDependencies": {"@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "typescript": "^5.2.2", "vite": "^5.0.5", "vite-plugin-dts": "^3.6.4", "vitest": "^0.34.6"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/ui-builder/component-library.git", "directory": "packages/testing-utilities"}, "keywords": ["testing", "utilities", "react", "ui-builder", "jest", "vitest"], "author": "UI Builder Team", "license": "MIT"}