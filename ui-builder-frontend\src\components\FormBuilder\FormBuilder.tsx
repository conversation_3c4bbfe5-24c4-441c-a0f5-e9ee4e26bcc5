import React, { useState, useCallback, useMemo } from 'react';
import { Card, Button, Space, Tabs, Form, Input, Select, Switch, InputNumber, Collapse } from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  CopyOutlined, 
  SettingOutlined,
  EyeOutlined,
  CodeOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { addComponent, updateComponent, removeComponent, reorderComponents } from '../../store/slices/uiBuilderSlice';
// Note: These components would be implemented separately
// import { FormFieldRenderer } from './FormFieldRenderer';
// import { FormValidationBuilder } from './FormValidationBuilder';
// import { FormPreview } from './FormPreview';
import './FormBuilder.scss';

const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

export interface FormField {
  id: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'file';
  label: string;
  name: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ label: string; value: string }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
  layout?: {
    span?: number;
    offset?: number;
  };
  style?: React.CSSProperties;
  conditional?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
  };
}

export interface FormBuilderProps {
  className?: string;
  onSave?: (formConfig: FormConfiguration) => void;
  onPreview?: (formConfig: FormConfiguration) => void;
  initialConfig?: FormConfiguration;
}

export interface FormConfiguration {
  id: string;
  name: string;
  description?: string;
  fields: FormField[];
  layout: 'vertical' | 'horizontal' | 'inline';
  submitButton: {
    text: string;
    loading?: boolean;
    disabled?: boolean;
  };
  validation: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    showErrorSummary?: boolean;
  };
  styling: {
    theme?: 'default' | 'compact' | 'borderless';
    size?: 'small' | 'middle' | 'large';
  };
}

const FIELD_TYPES = [
  { type: 'text', label: 'Text Input', icon: '📝' },
  { type: 'email', label: 'Email', icon: '📧' },
  { type: 'password', label: 'Password', icon: '🔒' },
  { type: 'number', label: 'Number', icon: '🔢' },
  { type: 'textarea', label: 'Text Area', icon: '📄' },
  { type: 'select', label: 'Select Dropdown', icon: '📋' },
  { type: 'checkbox', label: 'Checkbox', icon: '☑️' },
  { type: 'radio', label: 'Radio Group', icon: '🔘' },
  { type: 'date', label: 'Date Picker', icon: '📅' },
  { type: 'file', label: 'File Upload', icon: '📎' }
];

export const FormBuilder: React.FC<FormBuilderProps> = ({
  className,
  onSave,
  onPreview,
  initialConfig
}) => {
  const dispatch = useDispatch();
  const { currentConfig } = useSelector((state: RootState) => state.uiBuilder);
  
  const [formConfig, setFormConfig] = useState<FormConfiguration>(
    initialConfig || {
      id: 'form-' + Date.now(),
      name: 'New Form',
      fields: [],
      layout: 'vertical',
      submitButton: { text: 'Submit' },
      validation: {
        validateOnChange: true,
        validateOnBlur: true,
        showErrorSummary: false
      },
      styling: {
        theme: 'default',
        size: 'middle'
      }
    }
  );
  
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('fields');
  const [previewMode, setPreviewMode] = useState(false);

  // Add new field
  const handleAddField = useCallback((fieldType: string) => {
    const newField: FormField = {
      id: 'field-' + Date.now(),
      type: fieldType as FormField['type'],
      label: `${fieldType.charAt(0).toUpperCase() + fieldType.slice(1)} Field`,
      name: `field_${formConfig.fields.length + 1}`,
      required: false,
      layout: { span: 24 }
    };

    if (fieldType === 'select' || fieldType === 'radio') {
      newField.options = [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' }
      ];
    }

    setFormConfig(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }));
    
    setSelectedField(newField.id);
  }, [formConfig.fields.length]);

  // Update field
  const handleUpdateField = useCallback((fieldId: string, updates: Partial<FormField>) => {
    setFormConfig(prev => ({
      ...prev,
      fields: prev.fields.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      )
    }));
  }, []);

  // Remove field
  const handleRemoveField = useCallback((fieldId: string) => {
    setFormConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId)
    }));
    
    if (selectedField === fieldId) {
      setSelectedField(null);
    }
  }, [selectedField]);

  // Duplicate field
  const handleDuplicateField = useCallback((fieldId: string) => {
    const fieldToDuplicate = formConfig.fields.find(f => f.id === fieldId);
    if (!fieldToDuplicate) return;

    const duplicatedField: FormField = {
      ...fieldToDuplicate,
      id: 'field-' + Date.now(),
      name: fieldToDuplicate.name + '_copy',
      label: fieldToDuplicate.label + ' (Copy)'
    };

    setFormConfig(prev => ({
      ...prev,
      fields: [...prev.fields, duplicatedField]
    }));
  }, [formConfig.fields]);

  // Handle drag end
  const handleDragEnd = useCallback((result: any) => {
    if (!result.destination) return;

    const newFields = Array.from(formConfig.fields);
    const [reorderedField] = newFields.splice(result.source.index, 1);
    newFields.splice(result.destination.index, 0, reorderedField);

    setFormConfig(prev => ({
      ...prev,
      fields: newFields
    }));
  }, [formConfig.fields]);

  // Update form settings
  const handleFormSettingsChange = useCallback((key: string, value: any) => {
    setFormConfig(prev => ({
      ...prev,
      [key]: typeof prev[key] === 'object' ? { ...prev[key], ...value } : value
    }));
  }, []);

  // Save form
  const handleSave = useCallback(() => {
    onSave?.(formConfig);
    
    // Convert form to UI component and add to current config
    const formComponent = {
      id: formConfig.id,
      type: 'form',
      props: {
        name: formConfig.name,
        layout: formConfig.layout,
        fields: formConfig.fields,
        submitButton: formConfig.submitButton,
        validation: formConfig.validation,
        styling: formConfig.styling
      }
    };
    
    dispatch(addComponent(formComponent));
  }, [formConfig, onSave, dispatch]);

  // Preview form
  const handlePreview = useCallback(() => {
    setPreviewMode(!previewMode);
    onPreview?.(formConfig);
  }, [previewMode, formConfig, onPreview]);

  // Get selected field data
  const selectedFieldData = useMemo(() => {
    return formConfig.fields.find(field => field.id === selectedField);
  }, [formConfig.fields, selectedField]);

  return (
    <div className={`form-builder ${className || ''}`}>
      <div className="form-builder-header">
        <Space>
          <Input
            value={formConfig.name}
            onChange={(e) => handleFormSettingsChange('name', e.target.value)}
            placeholder="Form Name"
            style={{ width: 200 }}
          />
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            Save Form
          </Button>
          <Button
            icon={<EyeOutlined />}
            onClick={handlePreview}
          >
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
        </Space>
      </div>

      {previewMode ? (
        <div className="form-preview-placeholder">
          <h3>Form Preview</h3>
          <p>Preview functionality would be implemented here</p>
          <pre>{JSON.stringify(formConfig, null, 2)}</pre>
        </div>
      ) : (
        <div className="form-builder-content">
          <div className="form-builder-main">
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="Fields" key="fields">
                <div className="form-fields-panel">
                  {/* Field Types Palette */}
                  <Card title="Field Types" size="small" className="field-types-card">
                    <div className="field-types-grid">
                      {FIELD_TYPES.map(fieldType => (
                        <Button
                          key={fieldType.type}
                          className="field-type-button"
                          onClick={() => handleAddField(fieldType.type)}
                          title={fieldType.label}
                        >
                          <div className="field-type-icon">{fieldType.icon}</div>
                          <div className="field-type-label">{fieldType.label}</div>
                        </Button>
                      ))}
                    </div>
                  </Card>

                  {/* Form Fields List */}
                  <Card title="Form Fields" size="small" className="form-fields-card">
                    <DragDropContext onDragEnd={handleDragEnd}>
                      <Droppable droppableId="form-fields">
                        {(provided) => (
                          <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className="form-fields-list"
                          >
                            {formConfig.fields.map((field, index) => (
                              <Draggable
                                key={field.id}
                                draggableId={field.id}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <div
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    className={`form-field-item ${
                                      selectedField === field.id ? 'selected' : ''
                                    } ${snapshot.isDragging ? 'dragging' : ''}`}
                                    onClick={() => setSelectedField(field.id)}
                                  >
                                    <div
                                      {...provided.dragHandleProps}
                                      className="field-drag-handle"
                                    >
                                      ⋮⋮
                                    </div>
                                    
                                    <div className="field-info">
                                      <div className="field-label">{field.label}</div>
                                      <div className="field-type">{field.type}</div>
                                    </div>
                                    
                                    <div className="field-actions">
                                      <Button
                                        size="small"
                                        icon={<CopyOutlined />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDuplicateField(field.id);
                                        }}
                                      />
                                      <Button
                                        size="small"
                                        danger
                                        icon={<DeleteOutlined />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleRemoveField(field.id);
                                        }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                            
                            {formConfig.fields.length === 0 && (
                              <div className="empty-fields-message">
                                No fields added yet. Click on a field type above to add one.
                              </div>
                            )}
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </Card>
                </div>
              </TabPane>

              <TabPane tab="Settings" key="settings">
                <Card title="Form Settings" size="small">
                  <Collapse defaultActiveKey={['layout', 'validation', 'styling']}>
                    <Panel header="Layout" key="layout">
                      <Form layout="vertical" size="small">
                        <Form.Item label="Form Layout">
                          <Select
                            value={formConfig.layout}
                            onChange={(value) => handleFormSettingsChange('layout', value)}
                          >
                            <Option value="vertical">Vertical</Option>
                            <Option value="horizontal">Horizontal</Option>
                            <Option value="inline">Inline</Option>
                          </Select>
                        </Form.Item>
                        
                        <Form.Item label="Submit Button Text">
                          <Input
                            value={formConfig.submitButton.text}
                            onChange={(e) => handleFormSettingsChange('submitButton', { text: e.target.value })}
                          />
                        </Form.Item>
                      </Form>
                    </Panel>

                    <Panel header="Validation" key="validation">
                      <Form layout="vertical" size="small">
                        <Form.Item>
                          <Space direction="vertical">
                            <Switch
                              checked={formConfig.validation.validateOnChange}
                              onChange={(checked) => 
                                handleFormSettingsChange('validation', { validateOnChange: checked })
                              }
                            />
                            <span>Validate on change</span>
                          </Space>
                        </Form.Item>
                        
                        <Form.Item>
                          <Space direction="vertical">
                            <Switch
                              checked={formConfig.validation.validateOnBlur}
                              onChange={(checked) => 
                                handleFormSettingsChange('validation', { validateOnBlur: checked })
                              }
                            />
                            <span>Validate on blur</span>
                          </Space>
                        </Form.Item>
                        
                        <Form.Item>
                          <Space direction="vertical">
                            <Switch
                              checked={formConfig.validation.showErrorSummary}
                              onChange={(checked) => 
                                handleFormSettingsChange('validation', { showErrorSummary: checked })
                              }
                            />
                            <span>Show error summary</span>
                          </Space>
                        </Form.Item>
                      </Form>
                    </Panel>

                    <Panel header="Styling" key="styling">
                      <Form layout="vertical" size="small">
                        <Form.Item label="Theme">
                          <Select
                            value={formConfig.styling.theme}
                            onChange={(value) => handleFormSettingsChange('styling', { theme: value })}
                          >
                            <Option value="default">Default</Option>
                            <Option value="compact">Compact</Option>
                            <Option value="borderless">Borderless</Option>
                          </Select>
                        </Form.Item>
                        
                        <Form.Item label="Size">
                          <Select
                            value={formConfig.styling.size}
                            onChange={(value) => handleFormSettingsChange('styling', { size: value })}
                          >
                            <Option value="small">Small</Option>
                            <Option value="middle">Middle</Option>
                            <Option value="large">Large</Option>
                          </Select>
                        </Form.Item>
                      </Form>
                    </Panel>
                  </Collapse>
                </Card>
              </TabPane>

              <TabPane tab="Code" key="code">
                <Card title="Generated Code" size="small">
                  <pre className="form-code-preview">
                    {JSON.stringify(formConfig, null, 2)}
                  </pre>
                </Card>
              </TabPane>
            </Tabs>
          </div>

          {/* Field Properties Panel */}
          {selectedFieldData && (
            <div className="form-builder-sidebar">
              <Card title={`${selectedFieldData.label} Properties`} size="small">
                <div className="field-properties-placeholder">
                  <p>Field properties editor would be implemented here</p>
                  <pre>{JSON.stringify(selectedFieldData, null, 2)}</pre>
                </div>
              </Card>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
