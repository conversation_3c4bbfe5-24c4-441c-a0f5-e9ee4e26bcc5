package com.uiplatform.repository;

import com.uiplatform.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for User entity.
 * Provides CRUD operations and custom queries for user management.
 */
@Repository
public interface UserRepository extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User> {

    /**
     * Find user by username.
     */
    Optional<User> findByUsernameAndDeletedFalse(String username);

    /**
     * Find user by email.
     */
    Optional<User> findByEmailAndDeletedFalse(String email);

    /**
     * Find user by email verification token.
     */
    Optional<User> findByEmailVerificationTokenAndDeletedFalse(String token);

    /**
     * Find user by password reset token.
     */
    Optional<User> findByPasswordResetTokenAndDeletedFalse(String token);

    /**
     * Find users by organization.
     */
    List<User> findByOrganizationIdAndDeletedFalse(UUID organizationId);

    /**
     * Find users by organization with pagination.
     */
    Page<User> findByOrganizationIdAndDeletedFalse(UUID organizationId, Pageable pageable);

    /**
     * Find users by status.
     */
    List<User> findByStatusAndDeletedFalse(User.UserStatus status);

    /**
     * Find users by organization and status.
     */
    List<User> findByOrganizationIdAndStatusAndDeletedFalse(UUID organizationId, User.UserStatus status);

    /**
     * Find users with specific role.
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName AND u.deleted = false")
    List<User> findByRoleName(@Param("roleName") String roleName);

    /**
     * Find users by organization and role.
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE u.organization.id = :organizationId AND r.name = :roleName AND u.deleted = false")
    List<User> findByOrganizationAndRole(@Param("organizationId") UUID organizationId, @Param("roleName") String roleName);

    /**
     * Search users by name or email.
     */
    @Query("SELECT u FROM User u WHERE u.organization.id = :organizationId AND " +
           "(LOWER(CONCAT(u.firstName, ' ', u.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND u.deleted = false")
    Page<User> searchUsers(@Param("organizationId") UUID organizationId, @Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find users with expired password reset tokens.
     */
    @Query("SELECT u FROM User u WHERE u.passwordResetExpiresAt < :now AND u.passwordResetToken IS NOT NULL AND u.deleted = false")
    List<User> findUsersWithExpiredPasswordResetTokens(@Param("now") LocalDateTime now);

    /**
     * Find locked users.
     */
    @Query("SELECT u FROM User u WHERE u.lockedUntil > :now AND u.deleted = false")
    List<User> findLockedUsers(@Param("now") LocalDateTime now);

    /**
     * Find users who haven't logged in recently.
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt < :cutoffDate AND u.deleted = false")
    List<User> findInactiveUsers(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Count users by organization.
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.organization.id = :organizationId AND u.deleted = false")
    Long countByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Count active users by organization.
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.organization.id = :organizationId AND u.status = 'ACTIVE' AND u.deleted = false")
    Long countActiveUsersByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Check if username exists in organization (excluding current user).
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.username = :username AND u.organization.id = :organizationId AND u.id != :excludeId AND u.deleted = false")
    boolean existsByUsernameAndOrganizationIdAndIdNot(@Param("username") String username, 
                                                     @Param("organizationId") UUID organizationId, 
                                                     @Param("excludeId") UUID excludeId);

    /**
     * Check if email exists (excluding current user).
     */
    @Query("SELECT COUNT(u) > 0 FROM User u WHERE u.email = :email AND u.id != :excludeId AND u.deleted = false")
    boolean existsByEmailAndIdNot(@Param("email") String email, @Param("excludeId") UUID excludeId);

    /**
     * Update last login time.
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :loginTime, u.loginAttempts = 0, u.lockedUntil = null WHERE u.id = :userId")
    void updateLastLoginTime(@Param("userId") UUID userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * Increment login attempts.
     */
    @Modifying
    @Query("UPDATE User u SET u.loginAttempts = u.loginAttempts + 1 WHERE u.id = :userId")
    void incrementLoginAttempts(@Param("userId") UUID userId);

    /**
     * Lock user account.
     */
    @Modifying
    @Query("UPDATE User u SET u.lockedUntil = :lockUntil WHERE u.id = :userId")
    void lockUserAccount(@Param("userId") UUID userId, @Param("lockUntil") LocalDateTime lockUntil);

    /**
     * Clear password reset token.
     */
    @Modifying
    @Query("UPDATE User u SET u.passwordResetToken = null, u.passwordResetExpiresAt = null WHERE u.id = :userId")
    void clearPasswordResetToken(@Param("userId") UUID userId);

    /**
     * Verify email.
     */
    @Modifying
    @Query("UPDATE User u SET u.emailVerified = true, u.emailVerificationToken = null WHERE u.id = :userId")
    void verifyEmail(@Param("userId") UUID userId);

    /**
     * Find users by multiple criteria.
     */
    @Query("SELECT u FROM User u WHERE u.organization.id = :organizationId AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:emailVerified IS NULL OR u.emailVerified = :emailVerified) AND " +
           "u.deleted = false")
    Page<User> findUsersByCriteria(@Param("organizationId") UUID organizationId,
                                  @Param("status") User.UserStatus status,
                                  @Param("emailVerified") Boolean emailVerified,
                                  Pageable pageable);
}
