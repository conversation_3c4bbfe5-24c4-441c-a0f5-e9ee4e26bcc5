package com.uibuilder.security;

import com.uibuilder.security.jwt.JwtAuthenticationEntryPoint;
import com.uibuilder.security.jwt.JwtAuthenticationFilter;
import com.uibuilder.security.oauth.OAuth2AuthenticationSuccessHandler;
import com.uibuilder.security.oauth.OAuth2AuthenticationFailureHandler;
import com.uibuilder.security.oauth.OAuth2UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Security Configuration for UI Builder Platform
 * 
 * Provides comprehensive security configuration including:
 * - JWT-based authentication
 * - OAuth2 social login
 * - CORS configuration
 * - Security headers
 * - Method-level security
 * - Rate limiting
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private OAuth2UserService oAuth2UserService;

    @Autowired
    private OAuth2AuthenticationSuccessHandler oAuth2AuthenticationSuccessHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler oAuth2AuthenticationFailureHandler;

    /**
     * Password encoder bean
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }

    /**
     * JWT authentication filter bean
     */
    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter();
    }

    /**
     * Authentication manager bean
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * DAO authentication provider
     */
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    /**
     * Security filter chain configuration
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // Disable CSRF for stateless API
            .csrf(csrf -> csrf.disable())
            
            // Configure CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            
            // Configure session management
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // Configure exception handling
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            )
            
            // Configure authorization rules
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers(
                    "/api/auth/**",
                    "/api/public/**",
                    "/actuator/health",
                    "/actuator/info",
                    "/swagger-ui/**",
                    "/v3/api-docs/**",
                    "/favicon.ico",
                    "/error"
                ).permitAll()
                
                // Admin endpoints
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                
                // Organization admin endpoints
                .requestMatchers("/api/organizations/*/admin/**").hasRole("ORG_ADMIN")
                
                // User management endpoints
                .requestMatchers("/api/users/**").hasAnyRole("ADMIN", "ORG_ADMIN")
                
                // Configuration endpoints
                .requestMatchers("/api/configurations/**").hasAnyRole("USER", "ADMIN", "ORG_ADMIN")
                
                // Template endpoints
                .requestMatchers("/api/templates/**").hasAnyRole("USER", "ADMIN", "ORG_ADMIN")
                
                // Analytics endpoints (read-only for users)
                .requestMatchers("GET", "/api/analytics/**").hasAnyRole("USER", "ADMIN", "ORG_ADMIN")
                .requestMatchers("/api/analytics/**").hasAnyRole("ADMIN", "ORG_ADMIN")
                
                // All other requests require authentication
                .anyRequest().authenticated()
            )
            
            // Configure OAuth2 login
            .oauth2Login(oauth2 -> oauth2
                .authorizationEndpoint(authorization -> authorization
                    .baseUri("/oauth2/authorize")
                )
                .redirectionEndpoint(redirection -> redirection
                    .baseUri("/oauth2/callback/*")
                )
                .userInfoEndpoint(userInfo -> userInfo
                    .userService(oAuth2UserService)
                )
                .successHandler(oAuth2AuthenticationSuccessHandler)
                .failureHandler(oAuth2AuthenticationFailureHandler)
            )
            
            // Configure security headers
            .headers(headers -> headers
                .frameOptions().deny()
                .contentTypeOptions().and()
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)
                    .includeSubdomains(true)
                )
                .referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
                .and()
            );

        // Add JWT filter
        http.addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * CORS configuration
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Allow specific origins in production
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:3000",
            "http://localhost:3001",
            "https://*.ui-builder.com",
            "https://ui-builder.com"
        ));
        
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"
        ));
        
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "X-Organization-Id",
            "X-Request-ID"
        ));
        
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials",
            "X-Total-Count",
            "X-Request-ID"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}

/**
 * Method Security Configuration
 */
@Configuration
@EnableMethodSecurity(prePostEnabled = true)
public class MethodSecurityConfig {
    
    /**
     * Custom permission evaluator for complex authorization logic
     */
    @Bean
    public CustomPermissionEvaluator permissionEvaluator() {
        return new CustomPermissionEvaluator();
    }
}

/**
 * Custom Permission Evaluator
 */
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private OrganizationService organizationService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String permissionString = permission.toString();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        // Check system-level permissions
        if (hasSystemPermission(userPrincipal, permissionString)) {
            return true;
        }

        // Check resource-specific permissions
        if (targetDomainObject instanceof Configuration) {
            return hasConfigurationPermission(userPrincipal, (Configuration) targetDomainObject, permissionString);
        }

        if (targetDomainObject instanceof Template) {
            return hasTemplatePermission(userPrincipal, (Template) targetDomainObject, permissionString);
        }

        if (targetDomainObject instanceof Organization) {
            return hasOrganizationPermission(userPrincipal, (Organization) targetDomainObject, permissionString);
        }

        return false;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String permissionString = permission.toString();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        // Load target object by ID and type
        Object targetObject = loadTargetObject(targetId, targetType);
        if (targetObject == null) {
            return false;
        }

        return hasPermission(authentication, targetObject, permission);
    }

    private boolean hasSystemPermission(UserPrincipal user, String permission) {
        return user.getAuthorities().stream()
            .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN"));
    }

    private boolean hasConfigurationPermission(UserPrincipal user, Configuration configuration, String permission) {
        // Owner has full permissions
        if (configuration.getCreatedBy().equals(user.getId())) {
            return true;
        }

        // Organization admin has full permissions for org resources
        if (isOrganizationAdmin(user, configuration.getOrganizationId())) {
            return true;
        }

        // Check specific permissions
        switch (permission) {
            case "READ":
                return hasReadAccess(user, configuration);
            case "WRITE":
                return hasWriteAccess(user, configuration);
            case "DELETE":
                return hasDeleteAccess(user, configuration);
            default:
                return false;
        }
    }

    private boolean hasTemplatePermission(UserPrincipal user, Template template, String permission) {
        // Public templates are readable by all authenticated users
        if (template.isPublic() && "READ".equals(permission)) {
            return true;
        }

        // Owner has full permissions
        if (template.getCreatedBy().equals(user.getId())) {
            return true;
        }

        // Organization admin has full permissions for org templates
        if (isOrganizationAdmin(user, template.getOrganizationId())) {
            return true;
        }

        return false;
    }

    private boolean hasOrganizationPermission(UserPrincipal user, Organization organization, String permission) {
        // Organization admin has full permissions
        if (isOrganizationAdmin(user, organization.getId())) {
            return true;
        }

        // Organization member has read permissions
        if (isOrganizationMember(user, organization.getId()) && "READ".equals(permission)) {
            return true;
        }

        return false;
    }

    private boolean isOrganizationAdmin(UserPrincipal user, String organizationId) {
        return user.getAuthorities().stream()
            .anyMatch(authority -> 
                authority.getAuthority().equals("ROLE_ORG_ADMIN") &&
                user.getOrganizationId().equals(organizationId)
            );
    }

    private boolean isOrganizationMember(UserPrincipal user, String organizationId) {
        return user.getOrganizationId().equals(organizationId);
    }

    private boolean hasReadAccess(UserPrincipal user, Configuration configuration) {
        // Check if user is in the same organization
        if (user.getOrganizationId().equals(configuration.getOrganizationId())) {
            return true;
        }

        // Check if configuration is shared
        return configuration.isShared();
    }

    private boolean hasWriteAccess(UserPrincipal user, Configuration configuration) {
        // Only owner and org admin can write
        return configuration.getCreatedBy().equals(user.getId()) ||
               isOrganizationAdmin(user, configuration.getOrganizationId());
    }

    private boolean hasDeleteAccess(UserPrincipal user, Configuration configuration) {
        // Only owner and org admin can delete
        return configuration.getCreatedBy().equals(user.getId()) ||
               isOrganizationAdmin(user, configuration.getOrganizationId());
    }

    private Object loadTargetObject(Serializable targetId, String targetType) {
        switch (targetType.toLowerCase()) {
            case "configuration":
                return configurationService.findById(targetId.toString());
            case "template":
                return templateService.findById(targetId.toString());
            case "organization":
                return organizationService.findById(targetId.toString());
            default:
                return null;
        }
    }
}

/**
 * Security Event Listener
 */
@Component
@EventListener
public class SecurityEventListener {

    private static final Logger logger = LoggerFactory.getLogger(SecurityEventListener.class);

    @Autowired
    private AuditService auditService;

    @EventListener
    public void onAuthenticationSuccess(AuthenticationSuccessEvent event) {
        UserPrincipal user = (UserPrincipal) event.getAuthentication().getPrincipal();
        logger.info("User {} authenticated successfully", user.getEmail());
        
        auditService.logSecurityEvent(
            AuditEventType.AUTHENTICATION_SUCCESS,
            user.getId(),
            "User authenticated successfully",
            getClientInfo()
        );
    }

    @EventListener
    public void onAuthenticationFailure(AbstractAuthenticationFailureEvent event) {
        String username = event.getAuthentication().getName();
        logger.warn("Authentication failed for user: {}", username);
        
        auditService.logSecurityEvent(
            AuditEventType.AUTHENTICATION_FAILURE,
            null,
            "Authentication failed for user: " + username,
            getClientInfo()
        );
    }

    @EventListener
    public void onAuthorizationFailure(AuthorizationDeniedEvent event) {
        Authentication auth = event.getAuthentication();
        String username = auth != null ? auth.getName() : "anonymous";
        
        logger.warn("Authorization denied for user: {}", username);
        
        auditService.logSecurityEvent(
            AuditEventType.AUTHORIZATION_FAILURE,
            auth instanceof UserPrincipal ? ((UserPrincipal) auth.getPrincipal()).getId() : null,
            "Authorization denied for user: " + username,
            getClientInfo()
        );
    }

    private Map<String, Object> getClientInfo() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            Map<String, Object> clientInfo = new HashMap<>();
            clientInfo.put("ipAddress", getClientIpAddress(request));
            clientInfo.put("userAgent", request.getHeader("User-Agent"));
            clientInfo.put("requestUri", request.getRequestURI());
            return clientInfo;
        }
        return Collections.emptyMap();
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
