import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/layout_config.dart';
import '../models/widget_config.dart';
import '../models/device_info.dart';
import '../providers/app_state_provider.dart';
import 'dynamic_widget_renderer.dart';

/// Layout renderer that handles different layout types and responsive behavior
class LayoutRenderer extends ConsumerWidget {
  const LayoutRenderer({
    super.key,
    required this.layout,
    required this.children,
    this.debugMode = false,
    this.onWidgetMount,
    this.onWidgetUnmount,
    this.onWidgetError,
  });

  final LayoutConfig layout;
  final List<WidgetConfig> children;
  final bool debugMode;
  final Function(String widgetId, String widgetType)? onWidgetMount;
  final Function(String widgetId, String widgetType)? onWidgetUnmount;
  final Function(Object error, String widgetType)? onWidgetError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appStateProvider);
    final deviceInfo = appState.deviceInfo;

    // Get responsive layout configuration
    final responsiveLayout = _getResponsiveLayout(layout, deviceInfo);

    // Build layout based on type
    Widget layoutWidget;
    switch (responsiveLayout.type) {
      case LayoutType.column:
        layoutWidget = _buildColumnLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.row:
        layoutWidget = _buildRowLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.stack:
        layoutWidget = _buildStackLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.wrap:
        layoutWidget = _buildWrapLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.grid:
        layoutWidget = _buildGridLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.flex:
        layoutWidget = _buildFlexLayout(responsiveLayout, deviceInfo);
        break;
      case LayoutType.custom:
        layoutWidget = _buildCustomLayout(responsiveLayout, deviceInfo);
        break;
    }

    // Wrap with debug information if needed
    if (debugMode) {
      return _wrapWithDebugInfo(layoutWidget, responsiveLayout);
    }

    return layoutWidget;
  }

  LayoutConfig _getResponsiveLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    if (layout.responsive == null || layout.responsive!.isEmpty) {
      return layout;
    }

    final breakpoint = _getBreakpoint(deviceInfo);
    final responsiveConfig = layout.responsive![breakpoint];

    if (responsiveConfig != null) {
      return layout.copyWith(
        type: responsiveConfig.type ?? layout.type,
        direction: responsiveConfig.direction ?? layout.direction,
        mainAxisAlignment: responsiveConfig.mainAxisAlignment ?? layout.mainAxisAlignment,
        crossAxisAlignment: responsiveConfig.crossAxisAlignment ?? layout.crossAxisAlignment,
        spacing: responsiveConfig.spacing ?? layout.spacing,
        padding: responsiveConfig.padding ?? layout.padding,
        margin: responsiveConfig.margin ?? layout.margin,
        properties: {...layout.properties, ...responsiveConfig.properties},
      );
    }

    return layout;
  }

  String _getBreakpoint(DeviceInfoModel deviceInfo) {
    switch (deviceInfo.deviceType) {
      case DeviceType.mobile:
        return 'mobile';
      case DeviceType.tablet:
        return 'tablet';
      case DeviceType.desktop:
        return 'desktop';
    }
  }

  Widget _buildColumnLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Column(
        mainAxisAlignment: _getMainAxisAlignment(layout.mainAxisAlignment),
        crossAxisAlignment: _getCrossAxisAlignment(layout.crossAxisAlignment),
        children: _buildChildren(layout, deviceInfo, addSpacing: true),
      ),
    );
  }

  Widget _buildRowLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Row(
        mainAxisAlignment: _getMainAxisAlignment(layout.mainAxisAlignment),
        crossAxisAlignment: _getCrossAxisAlignment(layout.crossAxisAlignment),
        children: _buildChildren(layout, deviceInfo, addSpacing: true),
      ),
    );
  }

  Widget _buildStackLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Stack(
        alignment: _getStackAlignment(layout.mainAxisAlignment, layout.crossAxisAlignment),
        children: _buildChildren(layout, deviceInfo),
      ),
    );
  }

  Widget _buildWrapLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Wrap(
        direction: layout.direction == 'horizontal' ? Axis.horizontal : Axis.vertical,
        alignment: _getWrapAlignment(layout.mainAxisAlignment),
        crossAxisAlignment: _getWrapCrossAlignment(layout.crossAxisAlignment),
        spacing: layout.spacing ?? 8.0,
        runSpacing: layout.spacing ?? 8.0,
        children: _buildChildren(layout, deviceInfo),
      ),
    );
  }

  Widget _buildGridLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    final crossAxisCount = layout.properties['crossAxisCount'] as int? ?? 2;
    final childAspectRatio = layout.properties['childAspectRatio'] as double? ?? 1.0;
    final mainAxisSpacing = layout.spacing ?? 8.0;
    final crossAxisSpacing = layout.spacing ?? 8.0;

    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: GridView.count(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: _buildChildren(layout, deviceInfo),
      ),
    );
  }

  Widget _buildFlexLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Flex(
        direction: layout.direction == 'horizontal' ? Axis.horizontal : Axis.vertical,
        mainAxisAlignment: _getMainAxisAlignment(layout.mainAxisAlignment),
        crossAxisAlignment: _getCrossAxisAlignment(layout.crossAxisAlignment),
        children: _buildChildren(layout, deviceInfo, addSpacing: true),
      ),
    );
  }

  Widget _buildCustomLayout(LayoutConfig layout, DeviceInfoModel deviceInfo) {
    // For custom layouts, we'll use a basic container
    // In a real implementation, this could be extended to support custom layout builders
    return Container(
      padding: _getEdgeInsets(layout.padding),
      margin: _getEdgeInsets(layout.margin),
      child: Column(
        children: _buildChildren(layout, deviceInfo),
      ),
    );
  }

  List<Widget> _buildChildren(
    LayoutConfig layout,
    DeviceInfoModel deviceInfo, {
    bool addSpacing = false,
  }) {
    final widgets = <Widget>[];

    for (int i = 0; i < children.length; i++) {
      final child = children[i];
      
      // Build the child widget
      final childWidget = DynamicWidgetRenderer(
        key: ValueKey(child.id ?? 'child_$i'),
        config: child.toJson(),
        mode: RenderMode.preview,
        debugMode: debugMode,
        onWidgetMount: onWidgetMount,
        onWidgetUnmount: onWidgetUnmount,
        onWidgetError: onWidgetError,
      );

      // Apply layout-specific properties to child
      final wrappedChild = _wrapChildWithLayoutProperties(childWidget, child, layout);
      widgets.add(wrappedChild);

      // Add spacing between children if needed
      if (addSpacing && i < children.length - 1 && layout.spacing != null) {
        if (layout.direction == 'horizontal' || layout.type == LayoutType.row) {
          widgets.add(SizedBox(width: layout.spacing));
        } else {
          widgets.add(SizedBox(height: layout.spacing));
        }
      }
    }

    return widgets;
  }

  Widget _wrapChildWithLayoutProperties(
    Widget child,
    WidgetConfig childConfig,
    LayoutConfig layout,
  ) {
    Widget wrappedChild = child;

    // Apply flex properties for flex layouts
    if (layout.type == LayoutType.flex || layout.type == LayoutType.row || layout.type == LayoutType.column) {
      final flex = childConfig.properties['flex'] as int?;
      if (flex != null) {
        wrappedChild = Flexible(
          flex: flex,
          child: wrappedChild,
        );
      }

      final expanded = childConfig.properties['expanded'] as bool? ?? false;
      if (expanded) {
        wrappedChild = Expanded(child: wrappedChild);
      }
    }

    // Apply positioned properties for stack layouts
    if (layout.type == LayoutType.stack) {
      final positioned = childConfig.properties['positioned'] as Map<String, dynamic>?;
      if (positioned != null) {
        wrappedChild = Positioned(
          top: positioned['top']?.toDouble(),
          right: positioned['right']?.toDouble(),
          bottom: positioned['bottom']?.toDouble(),
          left: positioned['left']?.toDouble(),
          width: positioned['width']?.toDouble(),
          height: positioned['height']?.toDouble(),
          child: wrappedChild,
        );
      }
    }

    // Apply alignment
    final alignment = childConfig.properties['alignment'] as String?;
    if (alignment != null) {
      wrappedChild = Align(
        alignment: _parseAlignment(alignment),
        child: wrappedChild,
      );
    }

    return wrappedChild;
  }

  Widget _wrapWithDebugInfo(Widget child, LayoutConfig layout) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.green.withOpacity(0.5),
              style: BorderStyle.solid,
            ),
          ),
          child: child,
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.8),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(4),
              ),
            ),
            child: Text(
              'Layout: ${layout.type.name}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods for converting layout properties
  EdgeInsets? _getEdgeInsets(Map<String, double>? insets) {
    if (insets == null) return null;
    
    return EdgeInsets.only(
      top: insets['top'] ?? 0,
      right: insets['right'] ?? 0,
      bottom: insets['bottom'] ?? 0,
      left: insets['left'] ?? 0,
    );
  }

  MainAxisAlignment _getMainAxisAlignment(String? alignment) {
    switch (alignment) {
      case 'start':
        return MainAxisAlignment.start;
      case 'center':
        return MainAxisAlignment.center;
      case 'end':
        return MainAxisAlignment.end;
      case 'spaceBetween':
        return MainAxisAlignment.spaceBetween;
      case 'spaceAround':
        return MainAxisAlignment.spaceAround;
      case 'spaceEvenly':
        return MainAxisAlignment.spaceEvenly;
      default:
        return MainAxisAlignment.start;
    }
  }

  CrossAxisAlignment _getCrossAxisAlignment(String? alignment) {
    switch (alignment) {
      case 'start':
        return CrossAxisAlignment.start;
      case 'center':
        return CrossAxisAlignment.center;
      case 'end':
        return CrossAxisAlignment.end;
      case 'stretch':
        return CrossAxisAlignment.stretch;
      case 'baseline':
        return CrossAxisAlignment.baseline;
      default:
        return CrossAxisAlignment.center;
    }
  }

  AlignmentGeometry _getStackAlignment(String? mainAxis, String? crossAxis) {
    // Combine main and cross axis alignment for stack
    if (mainAxis == 'center' && crossAxis == 'center') {
      return Alignment.center;
    } else if (mainAxis == 'start' && crossAxis == 'start') {
      return Alignment.topLeft;
    } else if (mainAxis == 'end' && crossAxis == 'end') {
      return Alignment.bottomRight;
    }
    // Add more combinations as needed
    return Alignment.center;
  }

  WrapAlignment _getWrapAlignment(String? alignment) {
    switch (alignment) {
      case 'start':
        return WrapAlignment.start;
      case 'center':
        return WrapAlignment.center;
      case 'end':
        return WrapAlignment.end;
      case 'spaceBetween':
        return WrapAlignment.spaceBetween;
      case 'spaceAround':
        return WrapAlignment.spaceAround;
      case 'spaceEvenly':
        return WrapAlignment.spaceEvenly;
      default:
        return WrapAlignment.start;
    }
  }

  WrapCrossAlignment _getWrapCrossAlignment(String? alignment) {
    switch (alignment) {
      case 'start':
        return WrapCrossAlignment.start;
      case 'center':
        return WrapCrossAlignment.center;
      case 'end':
        return WrapCrossAlignment.end;
      default:
        return WrapCrossAlignment.start;
    }
  }

  Alignment _parseAlignment(String alignment) {
    switch (alignment) {
      case 'topLeft':
        return Alignment.topLeft;
      case 'topCenter':
        return Alignment.topCenter;
      case 'topRight':
        return Alignment.topRight;
      case 'centerLeft':
        return Alignment.centerLeft;
      case 'center':
        return Alignment.center;
      case 'centerRight':
        return Alignment.centerRight;
      case 'bottomLeft':
        return Alignment.bottomLeft;
      case 'bottomCenter':
        return Alignment.bottomCenter;
      case 'bottomRight':
        return Alignment.bottomRight;
      default:
        return Alignment.center;
    }
  }
}
