import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Eye, EyeOff, Search, X } from 'lucide-react';

import { cn } from '../utils/cn';
import type { ExtendedInputProps, FormFieldState, Size } from '../types/component';

const inputVariants = cva(
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        filled: 'border-0 bg-muted',
        outline: 'border-2',
        underline: 'border-0 border-b-2 rounded-none bg-transparent px-0',
      },
      size: {
        xs: 'h-7 px-2 text-xs',
        sm: 'h-8 px-2 text-xs',
        md: 'h-9 px-3 text-sm',
        lg: 'h-10 px-3 text-sm',
        xl: 'h-11 px-4 text-base',
      },
      state: {
        default: '',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-success focus-visible:ring-success',
        warning: 'border-warning focus-visible:ring-warning',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      state: 'default',
    },
  }
);

export interface InputProps
  extends Omit<ExtendedInputProps, 'size'>,
    VariantProps<typeof inputVariants>,
    FormFieldState {
  /** Input size */
  size?: Size;
  /** Icon to display before input */
  leftIcon?: React.ReactNode;
  /** Icon to display after input */
  rightIcon?: React.ReactNode;
  /** Whether input is clearable */
  clearable?: boolean;
  /** Callback when input is cleared */
  onClear?: () => void;
  /** Input label */
  label?: string;
  /** Whether label is floating */
  floatingLabel?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = 'text',
      variant,
      size,
      state,
      error,
      errorMessage,
      helperText,
      required,
      leftIcon,
      rightIcon,
      clearable,
      onClear,
      label,
      floatingLabel,
      disabled,
      value,
      placeholder,
      testId,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(Boolean(value));

    const inputState = error ? 'error' : state;
    const isPassword = type === 'password';
    const isSearch = type === 'search';
    const showClearButton = clearable && hasValue && !disabled;
    const showPasswordToggle = isPassword && !disabled;

    const handlePasswordToggle = () => {
      setShowPassword(!showPassword);
    };

    const handleClear = () => {
      onClear?.();
      setHasValue(false);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(Boolean(e.target.value));
      props.onChange?.(e);
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    const inputType = isPassword ? (showPassword ? 'text' : 'password') : type;

    const renderInput = () => (
      <input
        type={inputType}
        className={cn(
          inputVariants({ variant, size, state: inputState }),
          leftIcon && 'pl-10',
          (rightIcon || showClearButton || showPasswordToggle) && 'pr-10',
          className
        )}
        ref={ref}
        disabled={disabled}
        value={value}
        placeholder={floatingLabel ? '' : placeholder}
        required={required}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        data-testid={testId}
        {...props}
      />
    );

    const renderLabel = () => {
      if (!label) return null;

      if (floatingLabel) {
        return (
          <label
            className={cn(
              'absolute left-3 transition-all duration-200 pointer-events-none',
              'text-muted-foreground',
              (isFocused || hasValue) && 'top-0 -translate-y-1/2 text-xs bg-background px-1',
              !(isFocused || hasValue) && 'top-1/2 -translate-y-1/2 text-sm',
              leftIcon && 'left-10'
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </label>
        );
      }

      return (
        <label className="block text-sm font-medium text-foreground mb-1">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      );
    };

    const renderHelperText = () => {
      if (errorMessage) {
        return <p className="mt-1 text-xs text-destructive">{errorMessage}</p>;
      }
      if (helperText) {
        return <p className="mt-1 text-xs text-muted-foreground">{helperText}</p>;
      }
      return null;
    };

    const inputWrapper = (
      <div className={cn('relative', floatingLabel && 'pt-2')}>
        {/* Left icon */}
        {leftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {leftIcon}
          </div>
        )}

        {/* Search icon for search inputs */}
        {isSearch && !leftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            <Search className="h-4 w-4" />
          </div>
        )}

        {/* Floating label */}
        {floatingLabel && renderLabel()}

        {/* Input */}
        {renderInput()}

        {/* Right side icons */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {/* Clear button */}
          {showClearButton && (
            <button
              type="button"
              onClick={handleClear}
              className="text-muted-foreground hover:text-foreground transition-colors"
              tabIndex={-1}
            >
              <X className="h-4 w-4" />
            </button>
          )}

          {/* Password toggle */}
          {showPasswordToggle && (
            <button
              type="button"
              onClick={handlePasswordToggle}
              className="text-muted-foreground hover:text-foreground transition-colors"
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          )}

          {/* Right icon */}
          {rightIcon && <div className="text-muted-foreground">{rightIcon}</div>}
        </div>
      </div>
    );

    if (floatingLabel) {
      return (
        <div className="space-y-1">
          {inputWrapper}
          {renderHelperText()}
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {renderLabel()}
        {inputWrapper}
        {renderHelperText()}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input, inputVariants };

// Input group component for combining inputs with addons
export interface InputGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Input group size */
  size?: Size;
  /** Left addon */
  leftAddon?: React.ReactNode;
  /** Right addon */
  rightAddon?: React.ReactNode;
}

export const InputGroup = forwardRef<HTMLDivElement, InputGroupProps>(
  ({ className, size = 'md', leftAddon, rightAddon, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-stretch', className)}
        {...props}
      >
        {leftAddon && (
          <div className="flex items-center px-3 bg-muted border border-r-0 border-input rounded-l-md text-sm text-muted-foreground">
            {leftAddon}
          </div>
        )}
        
        <div className="flex-1">
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child) && child.type === Input) {
              return React.cloneElement(child, {
                size,
                className: cn(
                  leftAddon && 'rounded-l-none border-l-0',
                  rightAddon && 'rounded-r-none border-r-0',
                  child.props.className
                ),
              });
            }
            return child;
          })}
        </div>

        {rightAddon && (
          <div className="flex items-center px-3 bg-muted border border-l-0 border-input rounded-r-md text-sm text-muted-foreground">
            {rightAddon}
          </div>
        )}
      </div>
    );
  }
);

InputGroup.displayName = 'InputGroup';
