import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../models/app_state.dart';
import '../models/device_info.dart';
import '../models/ui_config.dart';
import '../services/config_service.dart';
import '../services/analytics_service.dart';
import '../services/cache_service.dart';

part 'app_state_provider.g.dart';

/// Main application state provider
@riverpod
class AppState extends _$AppState {
  @override
  AppStateModel build() {
    _initializeApp();
    return const AppStateModel();
  }

  /// Initialize the application
  Future<void> _initializeApp() async {
    try {
      state = state.copyWith(isLoading: true);

      // Initialize device info
      final deviceInfo = await _getDeviceInfo();
      
      // Initialize package info
      final packageInfo = await PackageInfo.fromPlatform();
      
      // Initialize connectivity
      final connectivity = await Connectivity().checkConnectivity();
      
      // Initialize services
      await _initializeServices();

      state = state.copyWith(
        isInitialized: true,
        isLoading: false,
        deviceInfo: deviceInfo,
        appVersion: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
        isOnline: connectivity != ConnectivityResult.none,
      );

      // Track app initialization
      ref.read(analyticsServiceProvider).trackEvent(
        'app_initialized',
        properties: {
          'version': packageInfo.version,
          'platform': deviceInfo.platform,
          'device_type': deviceInfo.deviceType.name,
        },
      );
    } catch (error, stackTrace) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      
      // Track initialization error
      ref.read(analyticsServiceProvider).trackError(
        error,
        stackTrace: stackTrace,
        context: {'phase': 'app_initialization'},
      );
    }
  }

  /// Initialize core services
  Future<void> _initializeServices() async {
    // Initialize cache service
    await ref.read(cacheServiceProvider).initialize();
    
    // Initialize analytics service
    await ref.read(analyticsServiceProvider).initialize();
    
    // Initialize config service
    await ref.read(configServiceProvider).initialize();
  }

  /// Get device information
  Future<DeviceInfoModel> _getDeviceInfo() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    
    try {
      if (Theme.of(context).platform == TargetPlatform.android) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return DeviceInfoModel(
          platform: 'android',
          deviceType: _getDeviceType(androidInfo.physicalDevice),
          model: androidInfo.model,
          version: androidInfo.version.release,
          manufacturer: androidInfo.manufacturer,
          isPhysicalDevice: androidInfo.isPhysicalDevice,
          screenSize: _getScreenSize(),
        );
      } else if (Theme.of(context).platform == TargetPlatform.iOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return DeviceInfoModel(
          platform: 'ios',
          deviceType: _getDeviceType(iosInfo.isPhysicalDevice),
          model: iosInfo.model,
          version: iosInfo.systemVersion,
          manufacturer: 'Apple',
          isPhysicalDevice: iosInfo.isPhysicalDevice,
          screenSize: _getScreenSize(),
        );
      } else {
        return const DeviceInfoModel(
          platform: 'unknown',
          deviceType: DeviceType.desktop,
          model: 'Unknown',
          version: 'Unknown',
          manufacturer: 'Unknown',
          isPhysicalDevice: true,
          screenSize: ScreenSize(width: 1920, height: 1080),
        );
      }
    } catch (error) {
      return const DeviceInfoModel(
        platform: 'unknown',
        deviceType: DeviceType.mobile,
        model: 'Unknown',
        version: 'Unknown',
        manufacturer: 'Unknown',
        isPhysicalDevice: true,
        screenSize: ScreenSize(width: 375, height: 812),
      );
    }
  }

  DeviceType _getDeviceType(dynamic deviceInfo) {
    // This is a simplified implementation
    // In a real app, you'd use more sophisticated device detection
    return DeviceType.mobile;
  }

  ScreenSize _getScreenSize() {
    // This would typically use MediaQuery or similar
    // For now, return default values
    return const ScreenSize(width: 375, height: 812);
  }

  /// Update app state
  void updateState(AppStateModel Function(AppStateModel) updater) {
    state = updater(state);
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set error state
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Update connectivity status
  void updateConnectivity(bool isOnline) {
    state = state.copyWith(isOnline: isOnline);
  }

  /// Set current UI configuration
  void setCurrentConfig(UIConfigModel config) {
    state = state.copyWith(currentConfig: config);
    
    // Track config load
    ref.read(analyticsServiceProvider).trackEvent(
      'config_loaded',
      properties: {
        'config_id': config.id,
        'config_name': config.name,
        'component_count': config.components.length,
      },
    );
  }

  /// Clear current configuration
  void clearCurrentConfig() {
    state = state.copyWith(currentConfig: null);
  }

  /// Update performance metrics
  void updatePerformanceMetrics(PerformanceMetrics metrics) {
    state = state.copyWith(performanceMetrics: metrics);
  }

  /// Set debug mode
  void setDebugMode(bool debugMode) {
    state = state.copyWith(debugMode: debugMode);
  }

  /// Set theme mode
  void setThemeMode(ThemeMode themeMode) {
    state = state.copyWith(themeMode: themeMode);
  }

  /// Update user preferences
  void updateUserPreferences(Map<String, dynamic> preferences) {
    state = state.copyWith(userPreferences: preferences);
  }
}

/// Connectivity provider
@riverpod
class Connectivity extends _$Connectivity {
  @override
  Stream<ConnectivityResult> build() {
    return Connectivity().onConnectivityChanged;
  }
}

/// Device orientation provider
@riverpod
class DeviceOrientation extends _$DeviceOrientation {
  @override
  Orientation build() {
    // This would typically be updated by a MediaQuery listener
    return Orientation.portrait;
  }

  void updateOrientation(Orientation orientation) {
    state = orientation;
  }
}

/// Performance metrics provider
@riverpod
class PerformanceMetrics extends _$PerformanceMetrics {
  @override
  PerformanceMetricsModel build() {
    return const PerformanceMetricsModel();
  }

  void updateMetrics({
    double? frameRate,
    int? memoryUsage,
    double? cpuUsage,
    int? renderTime,
    int? componentCount,
  }) {
    state = state.copyWith(
      frameRate: frameRate ?? state.frameRate,
      memoryUsage: memoryUsage ?? state.memoryUsage,
      cpuUsage: cpuUsage ?? state.cpuUsage,
      renderTime: renderTime ?? state.renderTime,
      componentCount: componentCount ?? state.componentCount,
      lastUpdated: DateTime.now(),
    );
  }

  void incrementComponentCount() {
    state = state.copyWith(
      componentCount: state.componentCount + 1,
      lastUpdated: DateTime.now(),
    );
  }

  void decrementComponentCount() {
    state = state.copyWith(
      componentCount: math.max(0, state.componentCount - 1),
      lastUpdated: DateTime.now(),
    );
  }
}

/// Feature flags provider
@riverpod
class FeatureFlags extends _$FeatureFlags {
  @override
  Map<String, bool> build() {
    return {
      'analytics': true,
      'crash_reporting': true,
      'performance_monitoring': true,
      'debug_mode': false,
      'hot_reload': true,
      'offline_mode': true,
      'experimental_features': false,
    };
  }

  void setFlag(String flag, bool value) {
    state = {...state, flag: value};
  }

  void updateFlags(Map<String, bool> flags) {
    state = {...state, ...flags};
  }

  bool isEnabled(String flag) {
    return state[flag] ?? false;
  }
}

/// App lifecycle provider
@riverpod
class AppLifecycle extends _$AppLifecycle {
  @override
  AppLifecycleState build() {
    return AppLifecycleState.resumed;
  }

  void updateLifecycleState(AppLifecycleState lifecycleState) {
    state = lifecycleState;
    
    // Track lifecycle changes
    ref.read(analyticsServiceProvider).trackEvent(
      'app_lifecycle_changed',
      properties: {'state': lifecycleState.name},
    );
  }
}

/// Global error handler provider
@riverpod
class ErrorHandler extends _$ErrorHandler {
  @override
  List<AppError> build() {
    return [];
  }

  void addError(AppError error) {
    state = [...state, error];
    
    // Track error
    ref.read(analyticsServiceProvider).trackError(
      Exception(error.message),
      context: {
        'error_type': error.type,
        'timestamp': error.timestamp.toIso8601String(),
      },
    );
  }

  void clearErrors() {
    state = [];
  }

  void removeError(String errorId) {
    state = state.where((error) => error.id != errorId).toList();
  }
}

/// Cache status provider
@riverpod
class CacheStatus extends _$CacheStatus {
  @override
  CacheStatusModel build() {
    return const CacheStatusModel();
  }

  void updateCacheStatus({
    int? totalSize,
    int? itemCount,
    double? hitRate,
    DateTime? lastCleared,
  }) {
    state = state.copyWith(
      totalSize: totalSize ?? state.totalSize,
      itemCount: itemCount ?? state.itemCount,
      hitRate: hitRate ?? state.hitRate,
      lastCleared: lastCleared ?? state.lastCleared,
      lastUpdated: DateTime.now(),
    );
  }
}
