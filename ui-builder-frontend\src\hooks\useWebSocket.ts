import { useEffect, useRef, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@store/index';
import { io, Socket } from 'socket.io-client';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export const useWebSocket = (
  enabled: boolean = true,
  options: UseWebSocketOptions = {}
) => {
  const dispatch = useAppDispatch();
  const { user, token } = useAppSelector(state => state.auth);
  const socketRef = useRef<Socket | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 1000,
  } = options;

  const connect = useCallback(() => {
    if (!enabled || !token || socketRef.current?.connected) {
      return;
    }

    console.log('Connecting to WebSocket...');

    socketRef.current = io('/ws', {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('WebSocket connected');
      reconnectAttemptsRef.current = 0;
      
      // Join user-specific room
      if (user) {
        socket.emit('join-user-room', { userId: user.id });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      
      // Auto-reconnect on certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'io client disconnect') {
        // Don't auto-reconnect for intentional disconnects
        return;
      }

      if (reconnectAttemptsRef.current < reconnectAttempts) {
        setTimeout(() => {
          reconnectAttemptsRef.current++;
          console.log(`Reconnecting... (attempt ${reconnectAttemptsRef.current})`);
          connect();
        }, reconnectDelay * Math.pow(2, reconnectAttemptsRef.current));
      }
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
    });

    // Collaboration events
    socket.on('user-joined', (data) => {
      dispatch({
        type: 'collaboration/userJoined',
        payload: data,
      });
    });

    socket.on('user-left', (data) => {
      dispatch({
        type: 'collaboration/userLeft',
        payload: data,
      });
    });

    socket.on('cursor-moved', (data) => {
      dispatch({
        type: 'collaboration/cursorMoved',
        payload: data,
      });
    });

    socket.on('component-updated', (data) => {
      dispatch({
        type: 'collaboration/componentUpdated',
        payload: data,
      });
    });

    socket.on('component-selected', (data) => {
      dispatch({
        type: 'collaboration/componentSelected',
        payload: data,
      });
    });

    socket.on('lock-acquired', (data) => {
      dispatch({
        type: 'collaboration/lockAcquired',
        payload: data,
      });
    });

    socket.on('lock-released', (data) => {
      dispatch({
        type: 'collaboration/lockReleased',
        payload: data,
      });
    });

    socket.on('comment-added', (data) => {
      dispatch({
        type: 'collaboration/commentAdded',
        payload: data,
      });
    });

    socket.on('comment-updated', (data) => {
      dispatch({
        type: 'collaboration/commentUpdated',
        payload: data,
      });
    });

    socket.on('comment-deleted', (data) => {
      dispatch({
        type: 'collaboration/commentDeleted',
        payload: data,
      });
    });

    // Notification events
    socket.on('notification', (data) => {
      dispatch({
        type: 'notifications/addNotification',
        payload: data,
      });
    });

    // Error events
    socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      dispatch({
        type: 'notifications/addNotification',
        payload: {
          type: 'error',
          title: 'Connection Error',
          message: error.message || 'An error occurred with the real-time connection',
        },
      });
    });

  }, [enabled, token, user, dispatch, reconnectAttempts, reconnectDelay]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      console.log('Disconnecting WebSocket...');
      socketRef.current.disconnect();
      socketRef.current = null;
    }
  }, []);

  const emit = useCallback((event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot emit event:', event);
    }
  }, []);

  const joinRoom = useCallback((room: string) => {
    emit('join-room', { room });
  }, [emit]);

  const leaveRoom = useCallback((room: string) => {
    emit('leave-room', { room });
  }, [emit]);

  // Connect/disconnect based on enabled state and authentication
  useEffect(() => {
    if (enabled && token && autoConnect) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, token, autoConnect, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    socket: socketRef.current,
    connected: socketRef.current?.connected || false,
    connect,
    disconnect,
    emit,
    joinRoom,
    leaveRoom,
  };
};
