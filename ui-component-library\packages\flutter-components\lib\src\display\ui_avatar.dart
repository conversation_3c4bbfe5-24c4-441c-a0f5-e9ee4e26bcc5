import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Avatar component
class UIAvatar extends StatelessWidget {
  const UIAvatar({
    super.key,
    this.child,
    this.backgroundImage,
    this.backgroundColor,
    this.foregroundColor,
    this.radius,
    this.size = UISize.md,
    this.shape = UIAvatarShape.circle,
    this.border,
    this.onTap,
  });

  /// Named constructor for text avatar
  const UIAvatar.text({
    super.key,
    required String text,
    this.backgroundImage,
    this.backgroundColor,
    this.foregroundColor,
    this.radius,
    this.size = UISize.md,
    this.shape = UIAvatarShape.circle,
    this.border,
    this.onTap,
  }) : child = Text(text);

  /// Named constructor for icon avatar
  const UIAvatar.icon({
    super.key,
    required IconData icon,
    this.backgroundImage,
    this.backgroundColor,
    this.foregroundColor,
    this.radius,
    this.size = UISize.md,
    this.shape = UIAvatarShape.circle,
    this.border,
    this.onTap,
  }) : child = Icon(icon);

  final Widget? child;
  final ImageProvider? backgroundImage;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? radius;
  final UISize size;
  final UIAvatarShape shape;
  final Border? border;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    final effectiveRadius = radius ?? _getSizeRadius();
    final effectiveBackgroundColor = backgroundColor ?? colorScheme.primaryContainer;
    final effectiveForegroundColor = foregroundColor ?? colorScheme.onPrimaryContainer;

    Widget avatar = CircleAvatar(
      radius: effectiveRadius,
      backgroundImage: backgroundImage,
      backgroundColor: backgroundImage == null ? effectiveBackgroundColor : null,
      foregroundColor: effectiveForegroundColor,
      child: backgroundImage == null ? child : null,
    );

    // Apply shape if not circle
    if (shape != UIAvatarShape.circle) {
      avatar = ClipRRect(
        borderRadius: _getShapeBorderRadius(effectiveRadius),
        child: Container(
          width: effectiveRadius * 2,
          height: effectiveRadius * 2,
          decoration: BoxDecoration(
            color: backgroundImage == null ? effectiveBackgroundColor : null,
            image: backgroundImage != null 
              ? DecorationImage(
                  image: backgroundImage!,
                  fit: BoxFit.cover,
                )
              : null,
          ),
          child: backgroundImage == null 
            ? Center(
                child: DefaultTextStyle(
                  style: TextStyle(color: effectiveForegroundColor),
                  child: IconTheme(
                    data: IconThemeData(color: effectiveForegroundColor),
                    child: child ?? const SizedBox(),
                  ),
                ),
              )
            : null,
        ),
      );
    }

    // Apply border if provided
    if (border != null) {
      avatar = Container(
        decoration: BoxDecoration(
          shape: shape == UIAvatarShape.circle ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: shape != UIAvatarShape.circle ? _getShapeBorderRadius(effectiveRadius) : null,
          border: border,
        ),
        child: avatar,
      );
    }

    // Apply tap handler if provided
    if (onTap != null) {
      avatar = GestureDetector(
        onTap: onTap,
        child: avatar,
      );
    }

    return avatar;
  }

  double _getSizeRadius() {
    switch (size) {
      case UISize.xs:
        return 12;
      case UISize.sm:
        return 16;
      case UISize.md:
        return 20;
      case UISize.lg:
        return 24;
      case UISize.xl:
        return 32;
      case UISize.xxl:
        return 40;
    }
  }

  BorderRadius _getShapeBorderRadius(double radius) {
    switch (shape) {
      case UIAvatarShape.circle:
        return BorderRadius.circular(radius);
      case UIAvatarShape.square:
        return BorderRadius.zero;
      case UIAvatarShape.rounded:
        return BorderRadius.circular(radius * 0.2);
    }
  }
}

/// Avatar shape enum
enum UIAvatarShape {
  circle,
  square,
  rounded,
}

/// UI Builder Avatar Group component
class UIAvatarGroup extends StatelessWidget {
  const UIAvatarGroup({
    super.key,
    required this.children,
    this.spacing = -8,
    this.maxVisible = 3,
    this.showMore = true,
    this.moreBuilder,
    this.onMoreTap,
  });

  final List<UIAvatar> children;
  final double spacing;
  final int maxVisible;
  final bool showMore;
  final Widget Function(int count)? moreBuilder;
  final VoidCallback? onMoreTap;

  @override
  Widget build(BuildContext context) {
    final visibleAvatars = children.take(maxVisible).toList();
    final remainingCount = children.length - maxVisible;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...visibleAvatars.asMap().entries.map((entry) {
          final index = entry.key;
          final avatar = entry.value;
          
          return Container(
            margin: EdgeInsets.only(
              left: index > 0 ? spacing : 0,
            ),
            child: avatar,
          );
        }),
        if (showMore && remainingCount > 0) ...[
          Container(
            margin: EdgeInsets.only(left: spacing),
            child: moreBuilder?.call(remainingCount) ?? 
              UIAvatar.text(
                text: '+$remainingCount',
                size: visibleAvatars.isNotEmpty ? visibleAvatars.first.size : UISize.md,
                onTap: onMoreTap,
              ),
          ),
        ],
      ],
    );
  }
}
