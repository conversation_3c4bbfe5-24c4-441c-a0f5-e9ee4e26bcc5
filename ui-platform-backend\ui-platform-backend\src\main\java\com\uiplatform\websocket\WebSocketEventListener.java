package com.uiplatform.websocket;

import com.uiplatform.service.CollaborationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;
import org.springframework.web.socket.messaging.SessionUnsubscribeEvent;

import java.util.UUID;

/**
 * WebSocket event listener for handling connection lifecycle events.
 */
@Component
public class WebSocketEventListener {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);

    private final CollaborationService collaborationService;

    @Autowired
    public WebSocketEventListener(CollaborationService collaborationService) {
        this.collaborationService = collaborationService;
    }

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        UUID userId = (UUID) headerAccessor.getSessionAttributes().get("userId");
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        UUID organizationId = (UUID) headerAccessor.getSessionAttributes().get("organizationId");
        
        if (userId != null && username != null && organizationId != null) {
            logger.info("WebSocket connection established - User: {} ({}), Session: {}, Organization: {}", 
                       username, userId, sessionId, organizationId);
            
            // Register user session in collaboration service
            collaborationService.handleUserConnect(userId, username, organizationId, sessionId);
        } else {
            logger.warn("WebSocket connection without proper user information - Session: {}", sessionId);
        }
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        UUID userId = (UUID) headerAccessor.getSessionAttributes().get("userId");
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        UUID organizationId = (UUID) headerAccessor.getSessionAttributes().get("organizationId");
        
        if (userId != null && username != null && organizationId != null) {
            logger.info("WebSocket connection closed - User: {} ({}), Session: {}, Organization: {}", 
                       username, userId, sessionId, organizationId);
            
            // Handle user disconnect in collaboration service
            collaborationService.handleUserDisconnect(userId, username, organizationId, sessionId);
        } else {
            logger.warn("WebSocket disconnection without proper user information - Session: {}", sessionId);
        }
    }

    @EventListener
    public void handleWebSocketSubscribeListener(SessionSubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        String destination = headerAccessor.getDestination();
        
        UUID userId = (UUID) headerAccessor.getSessionAttributes().get("userId");
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        if (userId != null && destination != null) {
            logger.debug("User {} subscribed to {} - Session: {}", username, destination, sessionId);
            
            // Handle subscription for specific collaboration rooms
            if (destination.startsWith("/topic/ui-config/")) {
                String configId = extractConfigIdFromDestination(destination);
                if (configId != null) {
                    collaborationService.handleUserJoinConfig(userId, UUID.fromString(configId), sessionId);
                }
            } else if (destination.startsWith("/topic/organization/")) {
                String orgId = extractOrganizationIdFromDestination(destination);
                if (orgId != null) {
                    collaborationService.handleUserJoinOrganization(userId, UUID.fromString(orgId), sessionId);
                }
            }
        }
    }

    @EventListener
    public void handleWebSocketUnsubscribeListener(SessionUnsubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        String subscriptionId = headerAccessor.getSubscriptionId();
        
        UUID userId = (UUID) headerAccessor.getSessionAttributes().get("userId");
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        
        if (userId != null) {
            logger.debug("User {} unsubscribed from subscription {} - Session: {}", 
                        username, subscriptionId, sessionId);
            
            // Handle unsubscription
            collaborationService.handleUserUnsubscribe(userId, subscriptionId, sessionId);
        }
    }

    /**
     * Extract UI configuration ID from WebSocket destination.
     */
    private String extractConfigIdFromDestination(String destination) {
        // Expected format: /topic/ui-config/{configId}
        if (destination.startsWith("/topic/ui-config/")) {
            String[] parts = destination.split("/");
            if (parts.length >= 4) {
                return parts[3];
            }
        }
        return null;
    }

    /**
     * Extract organization ID from WebSocket destination.
     */
    private String extractOrganizationIdFromDestination(String destination) {
        // Expected format: /topic/organization/{orgId}
        if (destination.startsWith("/topic/organization/")) {
            String[] parts = destination.split("/");
            if (parts.length >= 4) {
                return parts[3];
            }
        }
        return null;
    }
}
