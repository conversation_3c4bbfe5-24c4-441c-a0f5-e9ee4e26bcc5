.ui-button {
  // Base styles
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs, 0.25rem);
  
  // Typography
  font-family: var(--font-family-primary, inherit);
  font-weight: var(--font-weight-medium, 500);
  text-decoration: none;
  white-space: nowrap;
  
  // Layout
  border: 1px solid transparent;
  border-radius: var(--border-radius-md, 0.375rem);
  cursor: pointer;
  user-select: none;
  
  // Transitions
  transition: all 0.2s ease-in-out;
  
  // Focus styles
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary, #3b82f6), 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
  
  // Disabled state
  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;
    
    &:hover,
    &:focus,
    &:active {
      transform: none;
      box-shadow: none;
    }
  }
  
  // Loading state
  &--loading {
    cursor: wait;
    
    .ui-button__text {
      opacity: 0.7;
    }
  }
  
  // Active state
  &--active {
    transform: scale(0.98);
  }
  
  // Block layout
  &--block {
    width: 100%;
  }
  
  // Icon only
  &--icon-only {
    .ui-button__icon {
      margin: 0;
    }
  }
}

// Size variants
.ui-button--xs {
  padding: var(--spacing-xs, 0.25rem) var(--spacing-sm, 0.5rem);
  font-size: var(--font-size-xs, 0.75rem);
  line-height: 1.2;
  
  &.ui-button--icon-only {
    padding: var(--spacing-xs, 0.25rem);
    width: 1.5rem;
    height: 1.5rem;
  }
}

.ui-button--sm {
  padding: var(--spacing-sm, 0.5rem) var(--spacing-md, 1rem);
  font-size: var(--font-size-sm, 0.875rem);
  line-height: 1.25;
  
  &.ui-button--icon-only {
    padding: var(--spacing-sm, 0.5rem);
    width: 2rem;
    height: 2rem;
  }
}

.ui-button--md {
  padding: var(--spacing-sm, 0.5rem) var(--spacing-lg, 1.5rem);
  font-size: var(--font-size-base, 1rem);
  line-height: 1.5;
  
  &.ui-button--icon-only {
    padding: var(--spacing-sm, 0.5rem);
    width: 2.5rem;
    height: 2.5rem;
  }
}

.ui-button--lg {
  padding: var(--spacing-md, 1rem) var(--spacing-xl, 2rem);
  font-size: var(--font-size-lg, 1.125rem);
  line-height: 1.5;
  
  &.ui-button--icon-only {
    padding: var(--spacing-md, 1rem);
    width: 3rem;
    height: 3rem;
  }
}

.ui-button--xl {
  padding: var(--spacing-lg, 1.5rem) var(--spacing-2xl, 3rem);
  font-size: var(--font-size-xl, 1.25rem);
  line-height: 1.5;
  
  &.ui-button--icon-only {
    padding: var(--spacing-lg, 1.5rem);
    width: 3.5rem;
    height: 3.5rem;
  }
}

// Shape variants
.ui-button--round {
  border-radius: var(--border-radius-full, 9999px);
}

.ui-button--circle {
  border-radius: var(--border-radius-full, 9999px);
  aspect-ratio: 1;
}

// Color variants
.ui-button--primary {
  background-color: var(--color-primary, #3b82f6);
  color: var(--color-text-inverse, #ffffff);
  border-color: var(--color-primary, #3b82f6);
  
  &:hover:not(.ui-button--disabled) {
    background-color: var(--color-primary-dark, #2563eb);
    border-color: var(--color-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }
  
  &:active:not(.ui-button--disabled) {
    background-color: var(--color-primary-darker, #1d4ed8);
    transform: translateY(0);
  }
}

.ui-button--secondary {
  background-color: var(--color-secondary, #8b5cf6);
  color: var(--color-text-inverse, #ffffff);
  border-color: var(--color-secondary, #8b5cf6);
  
  &:hover:not(.ui-button--disabled) {
    background-color: var(--color-secondary-dark, #7c3aed);
    border-color: var(--color-secondary-dark, #7c3aed);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }
  
  &:active:not(.ui-button--disabled) {
    background-color: var(--color-secondary-darker, #6d28d9);
    transform: translateY(0);
  }
}

.ui-button--outline {
  background-color: transparent;
  color: var(--color-primary, #3b82f6);
  border-color: var(--color-primary, #3b82f6);
  
  &:hover:not(.ui-button--disabled) {
    background-color: var(--color-primary, #3b82f6);
    color: var(--color-text-inverse, #ffffff);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }
  
  &:active:not(.ui-button--disabled) {
    background-color: var(--color-primary-dark, #2563eb);
    transform: translateY(0);
  }
}

.ui-button--ghost {
  background-color: transparent;
  color: var(--color-text-primary, #1e293b);
  border-color: transparent;
  
  &:hover:not(.ui-button--disabled) {
    background-color: var(--color-surface, #f8fafc);
    transform: translateY(-1px);
  }
  
  &:active:not(.ui-button--disabled) {
    background-color: var(--color-border-light, #f1f5f9);
    transform: translateY(0);
  }
}

.ui-button--link {
  background-color: transparent;
  color: var(--color-primary, #3b82f6);
  border-color: transparent;
  padding: 0;
  height: auto;
  
  &:hover:not(.ui-button--disabled) {
    color: var(--color-primary-dark, #2563eb);
    text-decoration: underline;
  }
  
  &:active:not(.ui-button--disabled) {
    color: var(--color-primary-darker, #1d4ed8);
  }
}

.ui-button--danger {
  background-color: var(--color-error, #ef4444);
  color: var(--color-text-inverse, #ffffff);
  border-color: var(--color-error, #ef4444);
  
  &:hover:not(.ui-button--disabled) {
    background-color: var(--color-error-dark, #dc2626);
    border-color: var(--color-error-dark, #dc2626);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }
  
  &:active:not(.ui-button--disabled) {
    background-color: var(--color-error-darker, #b91c1c);
    transform: translateY(0);
  }
}

// Button content elements
.ui-button__text {
  display: inline-block;
}

.ui-button__icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &--left {
    margin-right: var(--spacing-xs, 0.25rem);
  }
  
  &--right {
    margin-left: var(--spacing-xs, 0.25rem);
  }
  
  svg {
    width: 1em;
    height: 1em;
    fill: currentColor;
  }
}

.ui-button__spinner {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: ui-button-spin 1s linear infinite;
}

// Animations
@keyframes ui-button-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Dark theme support
.dark-theme {
  .ui-button--ghost {
    color: var(--color-text-primary, #f8fafc);
    
    &:hover:not(.ui-button--disabled) {
      background-color: var(--color-surface, #1e293b);
    }
    
    &:active:not(.ui-button--disabled) {
      background-color: var(--color-border-light, #334155);
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .ui-button {
    border-width: 2px;
    
    &:focus {
      box-shadow: 0 0 0 3px var(--color-primary, #3b82f6);
    }
  }
  
  .ui-button--outline {
    border-width: 2px;
  }
  
  .ui-button--ghost {
    border: 2px solid var(--color-border-medium, #e2e8f0);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .ui-button {
    transition: none;
    
    &:hover:not(.ui-button--disabled),
    &:active:not(.ui-button--disabled) {
      transform: none;
    }
  }
  
  .ui-button__spinner {
    animation: none;
  }
}

// Print styles
@media print {
  .ui-button {
    background: transparent !important;
    color: black !important;
    border: 1px solid black !important;
    box-shadow: none !important;
    
    &::after {
      content: " [" attr(href) "]";
      font-size: 0.8em;
    }
  }
}
