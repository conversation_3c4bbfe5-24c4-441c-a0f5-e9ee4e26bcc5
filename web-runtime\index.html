<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Dynamic UI Runtime Application - Renders UI configurations from JSON metadata" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- PWA Meta Tags -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="mask-icon" href="/masked-icon.svg" color="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="UI Runtime" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Default theme CSS variables -->
    <style>
      :root {
        /* Default color palette */
        --color-primary-50: 239 246 255;
        --color-primary-100: 219 234 254;
        --color-primary-200: 191 219 254;
        --color-primary-300: 147 197 253;
        --color-primary-400: 96 165 250;
        --color-primary-500: 59 130 246;
        --color-primary-600: 37 99 235;
        --color-primary-700: 29 78 216;
        --color-primary-800: 30 64 175;
        --color-primary-900: 30 58 138;
        --color-primary-950: 23 37 84;

        --color-secondary-50: 248 250 252;
        --color-secondary-100: 241 245 249;
        --color-secondary-200: 226 232 240;
        --color-secondary-300: 203 213 225;
        --color-secondary-400: 148 163 184;
        --color-secondary-500: 100 116 139;
        --color-secondary-600: 71 85 105;
        --color-secondary-700: 51 65 85;
        --color-secondary-800: 30 41 59;
        --color-secondary-900: 15 23 42;
        --color-secondary-950: 2 6 23;

        --color-accent-50: 240 253 244;
        --color-accent-100: 220 252 231;
        --color-accent-200: 187 247 208;
        --color-accent-300: 134 239 172;
        --color-accent-400: 74 222 128;
        --color-accent-500: 34 197 94;
        --color-accent-600: 22 163 74;
        --color-accent-700: 21 128 61;
        --color-accent-800: 22 101 52;
        --color-accent-900: 20 83 45;
        --color-accent-950: 5 46 22;

        --color-surface-50: 249 250 251;
        --color-surface-100: 243 244 246;
        --color-surface-200: 229 231 235;
        --color-surface-300: 209 213 219;
        --color-surface-400: 156 163 175;
        --color-surface-500: 107 114 128;
        --color-surface-600: 75 85 99;
        --color-surface-700: 55 65 81;
        --color-surface-800: 31 41 55;
        --color-surface-900: 17 24 39;
        --color-surface-950: 3 7 18;

        /* Typography */
        --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
        --font-family-serif: 'Georgia', serif;
        --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

        --font-size-xs: 0.75rem;
        --font-size-sm: 0.875rem;
        --font-size-base: 1rem;
        --font-size-lg: 1.125rem;
        --font-size-xl: 1.25rem;
        --font-size-2xl: 1.5rem;
        --font-size-3xl: 1.875rem;
        --font-size-4xl: 2.25rem;
        --font-size-5xl: 3rem;
        --font-size-6xl: 3.75rem;

        --line-height-xs: 1rem;
        --line-height-sm: 1.25rem;
        --line-height-base: 1.5rem;
        --line-height-lg: 1.75rem;
        --line-height-xl: 1.75rem;
        --line-height-2xl: 2rem;
        --line-height-3xl: 2.25rem;
        --line-height-4xl: 2.5rem;
        --line-height-5xl: 1;
        --line-height-6xl: 1;

        /* Spacing */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 2.5rem;
        --spacing-3xl: 3rem;
        --spacing-4xl: 4rem;
        --spacing-5xl: 5rem;
        --spacing-6xl: 6rem;

        /* Border radius */
        --border-radius-xs: 0.125rem;
        --border-radius-sm: 0.25rem;
        --border-radius-md: 0.375rem;
        --border-radius-lg: 0.5rem;
        --border-radius-xl: 0.75rem;
        --border-radius-2xl: 1rem;
        --border-radius-3xl: 1.5rem;

        /* Shadows */
        --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

        /* Animations */
        --animation-duration-fast: 150ms;
        --animation-duration-normal: 300ms;
        --animation-duration-slow: 500ms;

        --animation-easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
        --animation-easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
        --animation-easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* Dark mode overrides */
      @media (prefers-color-scheme: dark) {
        :root {
          --color-surface-50: 3 7 18;
          --color-surface-100: 17 24 39;
          --color-surface-200: 31 41 55;
          --color-surface-300: 55 65 81;
          --color-surface-400: 75 85 99;
          --color-surface-500: 107 114 128;
          --color-surface-600: 156 163 175;
          --color-surface-700: 209 213 219;
          --color-surface-800: 229 231 235;
          --color-surface-900: 243 244 246;
          --color-surface-950: 249 250 251;
        }
      }

      /* Loading styles */
      .loading-spinner {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      /* Prevent flash of unstyled content */
      body {
        visibility: hidden;
      }

      body.loaded {
        visibility: visible;
      }
    </style>

    <title>UI Builder Runtime</title>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div class="flex items-center justify-center min-h-screen bg-surface-50">
        <div class="text-center">
          <div class="loading-spinner w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full mx-auto mb-4"></div>
          <p class="text-surface-600">Loading UI Runtime...</p>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
