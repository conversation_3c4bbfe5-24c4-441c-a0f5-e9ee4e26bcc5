import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';

/// Layout Manager for Flutter UI Runtime
///
/// Provides responsive layout system supporting:
/// - Column, Row, Stack layouts
/// - Grid and Wrap layouts
/// - Custom responsive layouts
/// - Breakpoint-based adaptations
/// - Nested layout compositions

enum LayoutType {
  column,
  row,
  stack,
  grid,
  wrap,
  flex,
  positioned,
  sliverList,
  sliverGrid,
  custom,
}

enum ResponsiveBreakpoint {
  xs, // < 576px
  sm, // >= 576px
  md, // >= 768px
  lg, // >= 992px
  xl, // >= 1200px
  xxl, // >= 1400px
}

class LayoutConfiguration {
  final LayoutType type;
  final Map<String, dynamic> properties;
  final Map<ResponsiveBreakpoint, Map<String, dynamic>>? responsiveProperties;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BoxConstraints? constraints;
  final AlignmentGeometry? alignment;

  LayoutConfiguration({
    required this.type,
    required this.properties,
    this.responsiveProperties,
    this.padding,
    this.margin,
    this.constraints,
    this.alignment,
  });

  factory LayoutConfiguration.fromJson(Map<String, dynamic> json) {
    return LayoutConfiguration(
      type: LayoutType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => LayoutType.column,
      ),
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
      responsiveProperties: json['responsiveProperties'] != null
          ? Map<ResponsiveBreakpoint, Map<String, dynamic>>.fromEntries(
              (json['responsiveProperties'] as Map<String, dynamic>).entries.map(
                    (e) => MapEntry(
                      ResponsiveBreakpoint.values.firstWhere(
                        (bp) => bp.name == e.key,
                        orElse: () => ResponsiveBreakpoint.md,
                      ),
                      Map<String, dynamic>.from(e.value),
                    ),
                  ),
            )
          : null,
      padding: json['padding'] != null ? _parseEdgeInsets(json['padding']) : null,
      margin: json['margin'] != null ? _parseEdgeInsets(json['margin']) : null,
      constraints: json['constraints'] != null ? _parseBoxConstraints(json['constraints']) : null,
      alignment: json['alignment'] != null ? _parseAlignment(json['alignment']) : null,
    );
  }

  static EdgeInsets? _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map<String, dynamic>) {
      return EdgeInsets.only(
        top: (value['top'] as num?)?.toDouble() ?? 0,
        right: (value['right'] as num?)?.toDouble() ?? 0,
        bottom: (value['bottom'] as num?)?.toDouble() ?? 0,
        left: (value['left'] as num?)?.toDouble() ?? 0,
      );
    }
    return null;
  }

  static BoxConstraints? _parseBoxConstraints(Map<String, dynamic> value) {
    return BoxConstraints(
      minWidth: (value['minWidth'] as num?)?.toDouble() ?? 0,
      maxWidth: (value['maxWidth'] as num?)?.toDouble() ?? double.infinity,
      minHeight: (value['minHeight'] as num?)?.toDouble() ?? 0,
      maxHeight: (value['maxHeight'] as num?)?.toDouble() ?? double.infinity,
    );
  }

  static AlignmentGeometry? _parseAlignment(String value) {
    switch (value) {
      case 'topLeft':
        return Alignment.topLeft;
      case 'topCenter':
        return Alignment.topCenter;
      case 'topRight':
        return Alignment.topRight;
      case 'centerLeft':
        return Alignment.centerLeft;
      case 'center':
        return Alignment.center;
      case 'centerRight':
        return Alignment.centerRight;
      case 'bottomLeft':
        return Alignment.bottomLeft;
      case 'bottomCenter':
        return Alignment.bottomCenter;
      case 'bottomRight':
        return Alignment.bottomRight;
      default:
        return null;
    }
  }
}

class LayoutManager {
  static final LayoutManager _instance = LayoutManager._internal();
  factory LayoutManager() => _instance;
  LayoutManager._internal();

  final Map<ResponsiveBreakpoint, double> _breakpoints = {
    ResponsiveBreakpoint.xs: 0,
    ResponsiveBreakpoint.sm: 576,
    ResponsiveBreakpoint.md: 768,
    ResponsiveBreakpoint.lg: 992,
    ResponsiveBreakpoint.xl: 1200,
    ResponsiveBreakpoint.xxl: 1400,
  };

  /// Get current breakpoint based on screen width
  ResponsiveBreakpoint getCurrentBreakpoint(double width) {
    if (width >= _breakpoints[ResponsiveBreakpoint.xxl]!) {
      return ResponsiveBreakpoint.xxl;
    } else if (width >= _breakpoints[ResponsiveBreakpoint.xl]!) {
      return ResponsiveBreakpoint.xl;
    } else if (width >= _breakpoints[ResponsiveBreakpoint.lg]!) {
      return ResponsiveBreakpoint.lg;
    } else if (width >= _breakpoints[ResponsiveBreakpoint.md]!) {
      return ResponsiveBreakpoint.md;
    } else if (width >= _breakpoints[ResponsiveBreakpoint.sm]!) {
      return ResponsiveBreakpoint.sm;
    } else {
      return ResponsiveBreakpoint.xs;
    }
  }

  /// Build layout widget from configuration
  Widget buildLayout(
    LayoutConfiguration config,
    List<Widget> children,
    BuildContext context,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    final currentBreakpoint = getCurrentBreakpoint(screenWidth);

    // Merge base properties with responsive properties
    final effectiveProperties = Map<String, dynamic>.from(config.properties);
    if (config.responsiveProperties != null) {
      final responsiveProps = config.responsiveProperties![currentBreakpoint];
      if (responsiveProps != null) {
        effectiveProperties.addAll(responsiveProps);
      }
    }

    Widget layout = _buildLayoutByType(config.type, effectiveProperties, children);

    // Apply container properties
    if (config.padding != null || config.margin != null || config.constraints != null) {
      layout = Container(
        padding: config.padding,
        margin: config.margin,
        constraints: config.constraints,
        alignment: config.alignment,
        child: layout,
      );
    }

    return layout;
  }

  Widget _buildLayoutByType(
    LayoutType type,
    Map<String, dynamic> properties,
    List<Widget> children,
  ) {
    switch (type) {
      case LayoutType.column:
        return _buildColumn(properties, children);
      case LayoutType.row:
        return _buildRow(properties, children);
      case LayoutType.stack:
        return _buildStack(properties, children);
      case LayoutType.grid:
        return _buildGrid(properties, children);
      case LayoutType.wrap:
        return _buildWrap(properties, children);
      case LayoutType.flex:
        return _buildFlex(properties, children);
      case LayoutType.positioned:
        return _buildPositioned(properties, children);
      case LayoutType.sliverList:
        return _buildSliverList(properties, children);
      case LayoutType.sliverGrid:
        return _buildSliverGrid(properties, children);
      case LayoutType.custom:
        return _buildCustomLayout(properties, children);
    }
  }

  Widget _buildColumn(Map<String, dynamic> properties, List<Widget> children) {
    return Column(
      mainAxisAlignment: _parseMainAxisAlignment(properties['mainAxisAlignment']),
      crossAxisAlignment: _parseCrossAxisAlignment(properties['crossAxisAlignment']),
      mainAxisSize: _parseMainAxisSize(properties['mainAxisSize']),
      children: children,
    );
  }

  Widget _buildRow(Map<String, dynamic> properties, List<Widget> children) {
    return Row(
      mainAxisAlignment: _parseMainAxisAlignment(properties['mainAxisAlignment']),
      crossAxisAlignment: _parseCrossAxisAlignment(properties['crossAxisAlignment']),
      mainAxisSize: _parseMainAxisSize(properties['mainAxisSize']),
      children: children,
    );
  }

  Widget _buildStack(Map<String, dynamic> properties, List<Widget> children) {
    return Stack(
      alignment: _parseAlignment(properties['alignment']) ?? Alignment.topLeft,
      fit: _parseStackFit(properties['fit']),
      children: children,
    );
  }

  Widget _buildGrid(Map<String, dynamic> properties, List<Widget> children) {
    final crossAxisCount = (properties['crossAxisCount'] as num?)?.toInt() ?? 2;
    final childAspectRatio = (properties['childAspectRatio'] as num?)?.toDouble() ?? 1.0;
    final mainAxisSpacing = (properties['mainAxisSpacing'] as num?)?.toDouble() ?? 0.0;
    final crossAxisSpacing = (properties['crossAxisSpacing'] as num?)?.toDouble() ?? 0.0;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: children,
    );
  }

  Widget _buildWrap(Map<String, dynamic> properties, List<Widget> children) {
    return Wrap(
      direction: _parseAxis(properties['direction']),
      alignment: _parseWrapAlignment(properties['alignment']),
      spacing: (properties['spacing'] as num?)?.toDouble() ?? 0.0,
      runSpacing: (properties['runSpacing'] as num?)?.toDouble() ?? 0.0,
      children: children,
    );
  }

  Widget _buildFlex(Map<String, dynamic> properties, List<Widget> children) {
    final direction = _parseAxis(properties['direction']);
    return Flex(
      direction: direction,
      mainAxisAlignment: _parseMainAxisAlignment(properties['mainAxisAlignment']),
      crossAxisAlignment: _parseCrossAxisAlignment(properties['crossAxisAlignment']),
      children: children,
    );
  }

  Widget _buildPositioned(Map<String, dynamic> properties, List<Widget> children) {
    if (children.isEmpty) return const SizedBox.shrink();

    return Stack(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        final positionKey = 'position_$index';
        final position = properties[positionKey] as Map<String, dynamic>?;

        if (position != null) {
          return Positioned(
            top: (position['top'] as num?)?.toDouble(),
            right: (position['right'] as num?)?.toDouble(),
            bottom: (position['bottom'] as num?)?.toDouble(),
            left: (position['left'] as num?)?.toDouble(),
            width: (position['width'] as num?)?.toDouble(),
            height: (position['height'] as num?)?.toDouble(),
            child: child,
          );
        }
        return child;
      }).toList(),
    );
  }

  Widget _buildSliverList(Map<String, dynamic> properties, List<Widget> children) {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildListDelegate(children),
        ),
      ],
    );
  }

  Widget _buildSliverGrid(Map<String, dynamic> properties, List<Widget> children) {
    final crossAxisCount = (properties['crossAxisCount'] as num?)?.toInt() ?? 2;
    final childAspectRatio = (properties['childAspectRatio'] as num?)?.toDouble() ?? 1.0;
    final mainAxisSpacing = (properties['mainAxisSpacing'] as num?)?.toDouble() ?? 0.0;
    final crossAxisSpacing = (properties['crossAxisSpacing'] as num?)?.toDouble() ?? 0.0;

    return CustomScrollView(
      slivers: [
        SliverGrid(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            mainAxisSpacing: mainAxisSpacing,
            crossAxisSpacing: crossAxisSpacing,
          ),
          delegate: SliverChildListDelegate(children),
        ),
      ],
    );
  }

  Widget _buildCustomLayout(Map<String, dynamic> properties, List<Widget> children) {
    // Custom layout implementation based on properties
    final layoutName = properties['layoutName'] as String?;

    switch (layoutName) {
      case 'masonry':
        return _buildMasonryLayout(properties, children);
      case 'carousel':
        return _buildCarouselLayout(properties, children);
      case 'tabs':
        return _buildTabLayout(properties, children);
      default:
        return Column(children: children);
    }
  }

  Widget _buildMasonryLayout(Map<String, dynamic> properties, List<Widget> children) {
    final columns = (properties['columns'] as num?)?.toInt() ?? 2;
    final spacing = (properties['spacing'] as num?)?.toDouble() ?? 8.0;

    // Simple masonry implementation
    final columnChildren = List.generate(columns, (index) => <Widget>[]);

    for (int i = 0; i < children.length; i++) {
      columnChildren[i % columns].add(children[i]);
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnChildren
          .map(
            (columnWidgets) => Expanded(
              child: Column(
                children: columnWidgets
                    .map(
                      (widget) => Padding(
                        padding: EdgeInsets.all(spacing / 2),
                        child: widget,
                      ),
                    )
                    .toList(),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildCarouselLayout(Map<String, dynamic> properties, List<Widget> children) {
    final height = (properties['height'] as num?)?.toDouble() ?? 200.0;
    final autoPlay = properties['autoPlay'] as bool? ?? false;

    return SizedBox(
      height: height,
      child: PageView(
        children: children,
      ),
    );
  }

  Widget _buildTabLayout(Map<String, dynamic> properties, List<Widget> children) {
    final tabLabels = properties['tabLabels'] as List<String>? ?? List.generate(children.length, (index) => 'Tab ${index + 1}');

    return DefaultTabController(
      length: children.length,
      child: Column(
        children: [
          TabBar(
            tabs: tabLabels.map((label) => Tab(text: label)).toList(),
          ),
          Expanded(
            child: TabBarView(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  // Parsing utility methods
  MainAxisAlignment _parseMainAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start':
          return MainAxisAlignment.start;
        case 'end':
          return MainAxisAlignment.end;
        case 'center':
          return MainAxisAlignment.center;
        case 'spaceBetween':
          return MainAxisAlignment.spaceBetween;
        case 'spaceAround':
          return MainAxisAlignment.spaceAround;
        case 'spaceEvenly':
          return MainAxisAlignment.spaceEvenly;
      }
    }
    return MainAxisAlignment.start;
  }

  CrossAxisAlignment _parseCrossAxisAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start':
          return CrossAxisAlignment.start;
        case 'end':
          return CrossAxisAlignment.end;
        case 'center':
          return CrossAxisAlignment.center;
        case 'stretch':
          return CrossAxisAlignment.stretch;
        case 'baseline':
          return CrossAxisAlignment.baseline;
      }
    }
    return CrossAxisAlignment.center;
  }

  MainAxisSize _parseMainAxisSize(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'min':
          return MainAxisSize.min;
        case 'max':
          return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  StackFit _parseStackFit(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'loose':
          return StackFit.loose;
        case 'expand':
          return StackFit.expand;
        case 'passthrough':
          return StackFit.passthrough;
      }
    }
    return StackFit.loose;
  }

  Axis _parseAxis(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'horizontal':
          return Axis.horizontal;
        case 'vertical':
          return Axis.vertical;
      }
    }
    return Axis.vertical;
  }

  WrapAlignment _parseWrapAlignment(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'start':
          return WrapAlignment.start;
        case 'end':
          return WrapAlignment.end;
        case 'center':
          return WrapAlignment.center;
        case 'spaceBetween':
          return WrapAlignment.spaceBetween;
        case 'spaceAround':
          return WrapAlignment.spaceAround;
        case 'spaceEvenly':
          return WrapAlignment.spaceEvenly;
      }
    }
    return WrapAlignment.start;
  }
}

/// Responsive layout builder widget
class ResponsiveLayoutBuilder extends ConsumerWidget {
  final Map<ResponsiveBreakpoint, Widget Function(BuildContext)> builders;
  final Widget Function(BuildContext)? fallback;

  const ResponsiveLayoutBuilder({
    Key? key,
    required this.builders,
    this.fallback,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final layoutManager = LayoutManager();
    final currentBreakpoint = layoutManager.getCurrentBreakpoint(screenWidth);

    // Find the best matching builder
    Widget Function(BuildContext)? builder;

    // Try current breakpoint first
    builder = builders[currentBreakpoint];

    // Fall back to smaller breakpoints
    if (builder == null) {
      final breakpointOrder = [
        ResponsiveBreakpoint.xxl,
        ResponsiveBreakpoint.xl,
        ResponsiveBreakpoint.lg,
        ResponsiveBreakpoint.md,
        ResponsiveBreakpoint.sm,
        ResponsiveBreakpoint.xs,
      ];

      final currentIndex = breakpointOrder.indexOf(currentBreakpoint);
      for (int i = currentIndex; i < breakpointOrder.length; i++) {
        builder = builders[breakpointOrder[i]];
        if (builder != null) break;
      }
    }

    return builder?.call(context) ?? fallback?.call(context) ?? const SizedBox.shrink();
  }
}

/// Layout configuration provider
final layoutConfigurationProvider = StateProvider<LayoutConfiguration?>((ref) => null);

/// Current breakpoint provider
final currentBreakpointProvider = Provider<ResponsiveBreakpoint>((ref) {
  // This would be updated by a MediaQuery listener in practice
  return ResponsiveBreakpoint.md;
});

/// Layout manager provider
final layoutManagerProvider = Provider<LayoutManager>((ref) {
  return LayoutManager();
});

/// Helper widget for building layouts from configuration
class DynamicLayoutWidget extends ConsumerWidget {
  final LayoutConfiguration configuration;
  final List<Widget> children;

  const DynamicLayoutWidget({
    Key? key,
    required this.configuration,
    required this.children,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final layoutManager = ref.read(layoutManagerProvider);
    return layoutManager.buildLayout(configuration, children, context);
  }
}
