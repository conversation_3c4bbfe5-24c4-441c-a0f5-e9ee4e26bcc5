import React, { useState, useMemo, useCallback } from 'react';
import { Tree, Input, Button, Dropdown, Tooltip, Space } from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@store/index';
import {
  selectComponent,
  updateComponent,
  deleteComponent,
  duplicateComponent,
  moveComponent,
} from '@store/slices/uiBuilderSlice';
import { UIComponent } from '@types/index';

// Utils
import { getComponentDisplayName } from '@utils/componentUtils';

interface LayerPanelProps {
  className?: string;
}

interface LayerTreeNode {
  key: string;
  title: React.ReactNode;
  children?: LayerTreeNode[];
  component: UIComponent;
  isLeaf?: boolean;
}

const LayerPanel: React.FC<LayerPanelProps> = ({ className = '' }) => {
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const {
    currentConfiguration,
    selection,
  } = useAppSelector(state => state.uiBuilder);

  // Convert components to tree structure
  const treeData = useMemo(() => {
    if (!currentConfiguration?.components) return [];

    const buildTreeNode = (component: UIComponent): LayerTreeNode => {
      const isSelected = selection.selectedComponentIds.includes(component.id);
      const isVisible = component.styles?.display !== 'none';
      const isLocked = component.constraints?.draggable === false;

      return {
        key: component.id,
        component,
        isLeaf: !component.children || component.children.length === 0,
        title: (
          <LayerTreeItem
            component={component}
            isSelected={isSelected}
            isVisible={isVisible}
            isLocked={isLocked}
            onToggleVisibility={() => handleToggleVisibility(component.id)}
            onToggleLock={() => handleToggleLock(component.id)}
            onDuplicate={() => handleDuplicate(component.id)}
            onDelete={() => handleDelete(component.id)}
            onRename={() => handleRename(component.id)}
          />
        ),
        children: component.children?.map(buildTreeNode),
      };
    };

    return currentConfiguration.components.map(buildTreeNode);
  }, [currentConfiguration?.components, selection.selectedComponentIds]);

  // Filter tree data based on search
  const filteredTreeData = useMemo(() => {
    if (!searchTerm) return treeData;

    const filterTree = (nodes: LayerTreeNode[]): LayerTreeNode[] => {
      return nodes.reduce((acc: LayerTreeNode[], node) => {
        const matchesSearch = getComponentDisplayName(node.component)
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

        const filteredChildren = node.children ? filterTree(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren,
          });
        }

        return acc;
      }, []);
    };

    return filterTree(treeData);
  }, [treeData, searchTerm]);

  // Handle component selection
  const handleSelect = useCallback((selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      dispatch(selectComponent(selectedKeys[0] as string));
    }
  }, [dispatch]);

  // Handle tree expansion
  const handleExpand = useCallback((expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[]);
    setAutoExpandParent(false);
  }, []);

  // Handle drag and drop
  const handleDrop = useCallback((info: any) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    // Don't allow dropping on itself
    if (dragKey === dropKey) return;

    // Calculate new parent and index
    let newParentId: string | undefined;
    let newIndex = 0;

    if (info.dropToGap) {
      // Dropping between nodes
      const parentNode = info.node.parent;
      newParentId = parentNode?.key;
      newIndex = dropPosition;
    } else {
      // Dropping into a node
      newParentId = dropKey;
      newIndex = 0;
    }

    dispatch(moveComponent({
      componentId: dragKey,
      newParentId,
      newIndex,
    }));
  }, [dispatch]);

  // Component actions
  const handleToggleVisibility = useCallback((componentId: string) => {
    const component = findComponentById(currentConfiguration?.components || [], componentId);
    if (!component) return;

    const isVisible = component.styles?.display !== 'none';
    dispatch(updateComponent({
      id: componentId,
      updates: {
        styles: {
          ...component.styles,
          display: isVisible ? 'none' : undefined,
        },
      },
    }));
  }, [currentConfiguration?.components, dispatch]);

  const handleToggleLock = useCallback((componentId: string) => {
    const component = findComponentById(currentConfiguration?.components || [], componentId);
    if (!component) return;

    const isLocked = component.constraints?.draggable === false;
    dispatch(updateComponent({
      id: componentId,
      updates: {
        constraints: {
          ...component.constraints,
          draggable: !isLocked,
        },
      },
    }));
  }, [currentConfiguration?.components, dispatch]);

  const handleDuplicate = useCallback((componentId: string) => {
    dispatch(duplicateComponent(componentId));
  }, [dispatch]);

  const handleDelete = useCallback((componentId: string) => {
    dispatch(deleteComponent(componentId));
  }, [dispatch]);

  const handleRename = useCallback((componentId: string) => {
    // TODO: Implement inline renaming
    console.log('Rename component:', componentId);
  }, []);

  // Auto-expand search results
  React.useEffect(() => {
    if (searchTerm) {
      const getAllKeys = (nodes: LayerTreeNode[]): string[] => {
        const keys: string[] = [];
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            keys.push(...getAllKeys(node.children));
          }
        });
        return keys;
      };

      setExpandedKeys(getAllKeys(filteredTreeData));
      setAutoExpandParent(true);
    }
  }, [searchTerm, filteredTreeData]);

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Layers</h3>
        
        {/* Search */}
        <Input
          placeholder="Search layers..."
          prefix={<SearchOutlined className="text-gray-400" />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          allowClear
        />
      </div>

      {/* Tree */}
      <div className="flex-1 overflow-y-auto p-2">
        {filteredTreeData.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? (
              <div>
                <div className="text-sm">No layers found</div>
                <div className="text-xs mt-1">Try adjusting your search</div>
              </div>
            ) : (
              <div>
                <div className="text-sm">No components yet</div>
                <div className="text-xs mt-1">Drag components from the palette</div>
              </div>
            )}
          </div>
        ) : (
          <Tree
            treeData={filteredTreeData}
            selectedKeys={selection.selectedComponentIds}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onSelect={handleSelect}
            onExpand={handleExpand}
            onDrop={handleDrop}
            draggable
            blockNode
            showLine={{ showLeafIcon: false }}
            className="layer-tree"
          />
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 space-y-1">
          <div>💡 Drag layers to reorder or nest them</div>
          <div>👁️ Click eye icon to show/hide layers</div>
          <div>🔒 Click lock icon to prevent editing</div>
        </div>
      </div>
    </div>
  );
};

// Layer tree item component
interface LayerTreeItemProps {
  component: UIComponent;
  isSelected: boolean;
  isVisible: boolean;
  isLocked: boolean;
  onToggleVisibility: () => void;
  onToggleLock: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onRename: () => void;
}

const LayerTreeItem: React.FC<LayerTreeItemProps> = ({
  component,
  isSelected,
  isVisible,
  isLocked,
  onToggleVisibility,
  onToggleLock,
  onDuplicate,
  onDelete,
  onRename,
}) => {
  const moreActionsMenu = {
    items: [
      {
        key: 'rename',
        label: 'Rename',
        icon: <EditOutlined />,
        onClick: onRename,
      },
      {
        key: 'duplicate',
        label: 'Duplicate',
        icon: <CopyOutlined />,
        onClick: onDuplicate,
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        label: 'Delete',
        icon: <DeleteOutlined />,
        onClick: onDelete,
        danger: true,
      },
    ],
  };

  return (
    <div className={`
      flex items-center justify-between w-full px-2 py-1 rounded
      ${isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'}
      ${!isVisible ? 'opacity-50' : ''}
    `}>
      {/* Component info */}
      <div className="flex items-center flex-1 min-w-0">
        {/* Component icon */}
        <div className="flex-shrink-0 w-4 h-4 bg-blue-100 rounded mr-2 flex items-center justify-center">
          <div className="w-2 h-2 bg-blue-500 rounded"></div>
        </div>

        {/* Component name */}
        <span className="text-sm font-medium text-gray-900 truncate">
          {getComponentDisplayName(component)}
        </span>

        {/* Component type badge */}
        <span className="ml-2 text-xs text-gray-500 bg-gray-100 px-1 rounded">
          {component.type}
        </span>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-1 ml-2">
        {/* Visibility toggle */}
        <Tooltip title={isVisible ? 'Hide layer' : 'Show layer'}>
          <Button
            type="text"
            size="small"
            icon={isVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onToggleVisibility();
            }}
            className="w-6 h-6 flex items-center justify-center"
          />
        </Tooltip>

        {/* Lock toggle */}
        <Tooltip title={isLocked ? 'Unlock layer' : 'Lock layer'}>
          <Button
            type="text"
            size="small"
            icon={isLocked ? <LockOutlined /> : <UnlockOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onToggleLock();
            }}
            className="w-6 h-6 flex items-center justify-center"
          />
        </Tooltip>

        {/* More actions */}
        <Dropdown menu={moreActionsMenu} trigger={['click']}>
          <Button
            type="text"
            size="small"
            icon={<MoreOutlined />}
            onClick={(e) => e.stopPropagation()}
            className="w-6 h-6 flex items-center justify-center"
          />
        </Dropdown>
      </div>
    </div>
  );
};

// Helper function to find component by ID
function findComponentById(components: UIComponent[], id: string): UIComponent | null {
  for (const component of components) {
    if (component.id === id) {
      return component;
    }
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }
  return null;
}

export default LayerPanel;
