import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/ui_metadata.dart';
import 'dynamic_renderer.dart';

/// Route definition for dynamic UI
class DynamicRoute {
  final String path;
  final String name;
  final String? configurationId;
  final Map<String, dynamic>? parameters;
  final List<String>? requiredPermissions;
  final bool requiresAuth;
  final Widget Function(BuildContext, GoRouterState)? builder;

  DynamicRoute({
    required this.path,
    required this.name,
    this.configurationId,
    this.parameters,
    this.requiredPermissions,
    this.requiresAuth = false,
    this.builder,
  });
}

/// Navigation state
class NavigationState {
  final String currentRoute;
  final Map<String, dynamic> routeParameters;
  final List<String> navigationHistory;
  final bool canGoBack;

  NavigationState({
    required this.currentRoute,
    required this.routeParameters,
    required this.navigationHistory,
    required this.canGoBack,
  });

  NavigationState copyWith({
    String? currentRoute,
    Map<String, dynamic>? routeParameters,
    List<String>? navigationHistory,
    bool? canGoBack,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      routeParameters: routeParameters ?? this.routeParameters,
      navigationHistory: navigationHistory ?? this.navigationHistory,
      canGoBack: canGoBack ?? this.canGoBack,
    );
  }
}

/// Navigation System for Flutter UI Runtime
/// 
/// Manages dynamic routing, deep linking, and navigation state
/// with support for configuration-based routes and authentication.
class NavigationSystem extends StateNotifier<NavigationState> {
  NavigationSystem() : super(NavigationState(
    currentRoute: '/',
    routeParameters: {},
    navigationHistory: ['/'],
    canGoBack: false,
  ));

  final Map<String, DynamicRoute> _routes = {};
  final Map<String, UIConfiguration> _routeConfigurations = {};
  late final GoRouter _router;
  final DynamicRenderer _renderer = DynamicRenderer();

  /// Initialize navigation system
  void initialize() {
    _setupDefaultRoutes();
    _createRouter();
  }

  /// Setup default routes
  void _setupDefaultRoutes() {
    registerRoute(DynamicRoute(
      path: '/',
      name: 'home',
      builder: (context, state) => const HomePage(),
    ));

    registerRoute(DynamicRoute(
      path: '/login',
      name: 'login',
      builder: (context, state) => const LoginPage(),
    ));

    registerRoute(DynamicRoute(
      path: '/error',
      name: 'error',
      builder: (context, state) => ErrorPage(
        error: state.queryParameters['message'] ?? 'Unknown error',
      ),
    ));

    registerRoute(DynamicRoute(
      path: '/ui/:configId',
      name: 'dynamic-ui',
      requiresAuth: true,
      builder: (context, state) {
        final configId = state.pathParameters['configId']!;
        return DynamicUIPage(configurationId: configId);
      },
    ));
  }

  /// Create GoRouter instance
  void _createRouter() {
    _router = GoRouter(
      initialLocation: '/',
      routes: _buildRoutes(),
      redirect: _handleRedirect,
      errorBuilder: (context, state) => ErrorPage(
        error: 'Page not found: ${state.location}',
      ),
    );
  }

  /// Build route list for GoRouter
  List<RouteBase> _buildRoutes() {
    return _routes.values.map((route) {
      return GoRoute(
        path: route.path,
        name: route.name,
        builder: route.builder ?? _buildDynamicRoute,
      );
    }).toList();
  }

  /// Handle route redirects
  String? _handleRedirect(BuildContext context, GoRouterState state) {
    final route = _routes.values.firstWhere(
      (r) => r.path == state.location,
      orElse: () => DynamicRoute(path: '', name: ''),
    );

    // Check authentication
    if (route.requiresAuth && !_isAuthenticated()) {
      return '/login?redirect=${Uri.encodeComponent(state.location)}';
    }

    // Check permissions
    if (route.requiredPermissions != null && !_hasPermissions(route.requiredPermissions!)) {
      return '/error?message=Insufficient permissions';
    }

    return null;
  }

  /// Build dynamic route from configuration
  Widget _buildDynamicRoute(BuildContext context, GoRouterState state) {
    final routeName = state.name ?? '';
    final route = _routes[routeName];
    
    if (route?.configurationId != null) {
      final config = _routeConfigurations[route!.configurationId];
      if (config != null) {
        return DynamicUIPage(
          configuration: config,
          parameters: state.queryParameters,
        );
      }
    }

    return ErrorPage(error: 'Configuration not found for route: $routeName');
  }

  /// Register a new route
  void registerRoute(DynamicRoute route) {
    _routes[route.name] = route;
    _recreateRouter();
  }

  /// Register route with configuration
  void registerRouteWithConfiguration(DynamicRoute route, UIConfiguration configuration) {
    _routes[route.name] = route;
    _routeConfigurations[route.configurationId ?? route.name] = configuration;
    _recreateRouter();
  }

  /// Unregister route
  void unregisterRoute(String routeName) {
    final route = _routes.remove(routeName);
    if (route?.configurationId != null) {
      _routeConfigurations.remove(route!.configurationId);
    }
    _recreateRouter();
  }

  /// Navigate to route
  void navigateTo(String routeName, {Map<String, dynamic>? parameters}) {
    final route = _routes[routeName];
    if (route == null) {
      navigateToError('Route not found: $routeName');
      return;
    }

    String path = route.path;
    
    // Replace path parameters
    if (parameters != null) {
      parameters.forEach((key, value) {
        path = path.replaceAll(':$key', value.toString());
      });
    }

    _updateNavigationState(path, parameters ?? {});
    _router.go(path);
  }

  /// Navigate with path
  void navigateToPath(String path, {Map<String, dynamic>? queryParameters}) {
    String fullPath = path;
    
    if (queryParameters != null && queryParameters.isNotEmpty) {
      final query = queryParameters.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      fullPath += '?$query';
    }

    _updateNavigationState(path, queryParameters ?? {});
    _router.go(fullPath);
  }

  /// Push route onto navigation stack
  void pushRoute(String routeName, {Map<String, dynamic>? parameters}) {
    final route = _routes[routeName];
    if (route == null) {
      navigateToError('Route not found: $routeName');
      return;
    }

    String path = route.path;
    
    if (parameters != null) {
      parameters.forEach((key, value) {
        path = path.replaceAll(':$key', value.toString());
      });
    }

    _updateNavigationState(path, parameters ?? {}, isPush: true);
    _router.push(path);
  }

  /// Go back in navigation
  void goBack() {
    if (state.canGoBack) {
      _router.pop();
      _updateNavigationStateOnBack();
    }
  }

  /// Navigate to error page
  void navigateToError(String error) {
    _router.go('/error?message=${Uri.encodeComponent(error)}');
  }

  /// Replace current route
  void replaceRoute(String routeName, {Map<String, dynamic>? parameters}) {
    final route = _routes[routeName];
    if (route == null) {
      navigateToError('Route not found: $routeName');
      return;
    }

    String path = route.path;
    
    if (parameters != null) {
      parameters.forEach((key, value) {
        path = path.replaceAll(':$key', value.toString());
      });
    }

    _updateNavigationState(path, parameters ?? {}, isReplace: true);
    _router.pushReplacement(path);
  }

  /// Clear navigation history
  void clearHistory() {
    state = state.copyWith(
      navigationHistory: [state.currentRoute],
      canGoBack: false,
    );
  }

  /// Get current route
  String getCurrentRoute() => state.currentRoute;

  /// Get route parameters
  Map<String, dynamic> getRouteParameters() => state.routeParameters;

  /// Get navigation history
  List<String> getNavigationHistory() => state.navigationHistory;

  /// Check if can go back
  bool canGoBack() => state.canGoBack;

  /// Get router instance
  GoRouter get router => _router;

  /// Update navigation state
  void _updateNavigationState(
    String route, 
    Map<String, dynamic> parameters, {
    bool isPush = false,
    bool isReplace = false,
  }) {
    List<String> newHistory = List.from(state.navigationHistory);
    
    if (isPush) {
      newHistory.add(route);
    } else if (isReplace) {
      if (newHistory.isNotEmpty) {
        newHistory[newHistory.length - 1] = route;
      } else {
        newHistory.add(route);
      }
    } else {
      newHistory = [route];
    }

    state = state.copyWith(
      currentRoute: route,
      routeParameters: parameters,
      navigationHistory: newHistory,
      canGoBack: newHistory.length > 1,
    );
  }

  /// Update navigation state on back navigation
  void _updateNavigationStateOnBack() {
    List<String> newHistory = List.from(state.navigationHistory);
    if (newHistory.length > 1) {
      newHistory.removeLast();
      final previousRoute = newHistory.last;
      
      state = state.copyWith(
        currentRoute: previousRoute,
        routeParameters: {}, // Reset parameters
        navigationHistory: newHistory,
        canGoBack: newHistory.length > 1,
      );
    }
  }

  /// Recreate router with updated routes
  void _recreateRouter() {
    _createRouter();
  }

  /// Check if user is authenticated
  bool _isAuthenticated() {
    // This would integrate with your authentication system
    return true; // Placeholder
  }

  /// Check if user has required permissions
  bool _hasPermissions(List<String> requiredPermissions) {
    // This would integrate with your permission system
    return true; // Placeholder
  }

  /// Get all registered routes
  Map<String, DynamicRoute> getAllRoutes() => Map.unmodifiable(_routes);

  /// Get route by name
  DynamicRoute? getRoute(String name) => _routes[name];

  /// Check if route exists
  bool hasRoute(String name) => _routes.containsKey(name);
}

/// Home page widget
class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('UI Builder Runtime')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Welcome to UI Builder Runtime',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text('Select a configuration to render dynamic UI'),
          ],
        ),
      ),
    );
  }
}

/// Login page widget
class LoginPage extends StatelessWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Login Page'),
            // Add login form here
          ],
        ),
      ),
    );
  }
}

/// Error page widget
class ErrorPage extends StatelessWidget {
  final String error;

  const ErrorPage({Key? key, required this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Dynamic UI page widget
class DynamicUIPage extends ConsumerWidget {
  final String? configurationId;
  final UIConfiguration? configuration;
  final Map<String, dynamic>? parameters;

  const DynamicUIPage({
    Key? key,
    this.configurationId,
    this.configuration,
    this.parameters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (configuration != null) {
      return Scaffold(
        body: ref.read(dynamicRendererProvider).renderConfiguration(
          configuration!,
          context,
        ),
      );
    }

    if (configurationId != null) {
      // Load configuration by ID
      return FutureBuilder<UIConfiguration?>(
        future: _loadConfiguration(configurationId!),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          if (snapshot.hasError) {
            return ErrorPage(error: 'Failed to load configuration: ${snapshot.error}');
          }

          if (snapshot.data == null) {
            return const ErrorPage(error: 'Configuration not found');
          }

          return Scaffold(
            body: ref.read(dynamicRendererProvider).renderConfiguration(
              snapshot.data!,
              context,
            ),
          );
        },
      );
    }

    return const ErrorPage(error: 'No configuration provided');
  }

  Future<UIConfiguration?> _loadConfiguration(String configId) async {
    // This would load the configuration from your backend
    // For now, return null as placeholder
    return null;
  }
}

/// Riverpod providers
final navigationSystemProvider = StateNotifierProvider<NavigationSystem, NavigationState>((ref) {
  final navigationSystem = NavigationSystem();
  navigationSystem.initialize();
  return navigationSystem;
});

final routerProvider = Provider<GoRouter>((ref) {
  return ref.watch(navigationSystemProvider.notifier).router;
});

/// Navigation helper functions
extension NavigationHelpers on WidgetRef {
  NavigationSystem get navigation => read(navigationSystemProvider.notifier);
  
  void navigateTo(String routeName, {Map<String, dynamic>? parameters}) {
    navigation.navigateTo(routeName, parameters: parameters);
  }
  
  void navigateToPath(String path, {Map<String, dynamic>? queryParameters}) {
    navigation.navigateToPath(path, queryParameters: queryParameters);
  }
  
  void goBack() {
    navigation.goBack();
  }
}
