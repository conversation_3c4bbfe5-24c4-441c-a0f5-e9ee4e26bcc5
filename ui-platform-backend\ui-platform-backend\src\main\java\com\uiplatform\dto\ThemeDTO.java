package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.*;

import java.util.Map;
import java.util.UUID;

/**
 * DTO for Theme entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThemeDTO extends BaseDTO {

    @NotBlank(message = "Theme name is required")
    @Size(max = 100, message = "Theme name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    private Map<String, Object> colorPalette;
    private Map<String, Object> typography;
    private Map<String, Object> spacing;
    private Map<String, Object> borders;
    private Map<String, Object> shadows;
    private Map<String, Object> animations;
    private Map<String, Object> componentStyles;
    private Map<String, Object> responsiveBreakpoints;

    private String customCss;
    private String cssVariables;

    private Boolean isDefault;
    private Boolean isPublic;
    private Boolean isSystem;

    @Pattern(regexp = "^https?://.*", message = "Preview image URL must be a valid HTTP/HTTPS URL")
    private String previewImageUrl;

    @NotBlank(message = "Version is required")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Version must be in format: x.y.z")
    private String version;

    private String tags;

    // Related entities
    private UUID organizationId;
    private String organizationName;

    @NotNull(message = "Author ID is required")
    private UUID authorId;
    private String authorName;

    // Statistics
    private Long usageCount;

    // Constructors
    public ThemeDTO() {}

    public ThemeDTO(String name, String version) {
        this.name = name;
        this.version = version;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getColorPalette() {
        return colorPalette;
    }

    public void setColorPalette(Map<String, Object> colorPalette) {
        this.colorPalette = colorPalette;
    }

    public Map<String, Object> getTypography() {
        return typography;
    }

    public void setTypography(Map<String, Object> typography) {
        this.typography = typography;
    }

    public Map<String, Object> getSpacing() {
        return spacing;
    }

    public void setSpacing(Map<String, Object> spacing) {
        this.spacing = spacing;
    }

    public Map<String, Object> getBorders() {
        return borders;
    }

    public void setBorders(Map<String, Object> borders) {
        this.borders = borders;
    }

    public Map<String, Object> getShadows() {
        return shadows;
    }

    public void setShadows(Map<String, Object> shadows) {
        this.shadows = shadows;
    }

    public Map<String, Object> getAnimations() {
        return animations;
    }

    public void setAnimations(Map<String, Object> animations) {
        this.animations = animations;
    }

    public Map<String, Object> getComponentStyles() {
        return componentStyles;
    }

    public void setComponentStyles(Map<String, Object> componentStyles) {
        this.componentStyles = componentStyles;
    }

    public Map<String, Object> getResponsiveBreakpoints() {
        return responsiveBreakpoints;
    }

    public void setResponsiveBreakpoints(Map<String, Object> responsiveBreakpoints) {
        this.responsiveBreakpoints = responsiveBreakpoints;
    }

    public String getCustomCss() {
        return customCss;
    }

    public void setCustomCss(String customCss) {
        this.customCss = customCss;
    }

    public String getCssVariables() {
        return cssVariables;
    }

    public void setCssVariables(String cssVariables) {
        this.cssVariables = cssVariables;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public UUID getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(UUID organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public UUID getAuthorId() {
        return authorId;
    }

    public void setAuthorId(UUID authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Long getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    /**
     * Create DTO for theme creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Theme name is required")
        @Size(max = 100, message = "Theme name must not exceed 100 characters")
        private String name;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        private Map<String, Object> colorPalette;
        private Map<String, Object> typography;
        private Map<String, Object> spacing;
        private Map<String, Object> borders;
        private Map<String, Object> shadows;
        private Map<String, Object> animations;
        private Map<String, Object> componentStyles;
        private Map<String, Object> responsiveBreakpoints;

        private String customCss;
        private String cssVariables;

        @Pattern(regexp = "^https?://.*", message = "Preview image URL must be a valid HTTP/HTTPS URL")
        private String previewImageUrl;

        @NotBlank(message = "Version is required")
        @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Version must be in format: x.y.z")
        private String version = "1.0.0";

        private String tags;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Map<String, Object> getColorPalette() {
            return colorPalette;
        }

        public void setColorPalette(Map<String, Object> colorPalette) {
            this.colorPalette = colorPalette;
        }

        public Map<String, Object> getTypography() {
            return typography;
        }

        public void setTypography(Map<String, Object> typography) {
            this.typography = typography;
        }

        public Map<String, Object> getSpacing() {
            return spacing;
        }

        public void setSpacing(Map<String, Object> spacing) {
            this.spacing = spacing;
        }

        public Map<String, Object> getBorders() {
            return borders;
        }

        public void setBorders(Map<String, Object> borders) {
            this.borders = borders;
        }

        public Map<String, Object> getShadows() {
            return shadows;
        }

        public void setShadows(Map<String, Object> shadows) {
            this.shadows = shadows;
        }

        public Map<String, Object> getAnimations() {
            return animations;
        }

        public void setAnimations(Map<String, Object> animations) {
            this.animations = animations;
        }

        public Map<String, Object> getComponentStyles() {
            return componentStyles;
        }

        public void setComponentStyles(Map<String, Object> componentStyles) {
            this.componentStyles = componentStyles;
        }

        public Map<String, Object> getResponsiveBreakpoints() {
            return responsiveBreakpoints;
        }

        public void setResponsiveBreakpoints(Map<String, Object> responsiveBreakpoints) {
            this.responsiveBreakpoints = responsiveBreakpoints;
        }

        public String getCustomCss() {
            return customCss;
        }

        public void setCustomCss(String customCss) {
            this.customCss = customCss;
        }

        public String getCssVariables() {
            return cssVariables;
        }

        public void setCssVariables(String cssVariables) {
            this.cssVariables = cssVariables;
        }

        public String getPreviewImageUrl() {
            return previewImageUrl;
        }

        public void setPreviewImageUrl(String previewImageUrl) {
            this.previewImageUrl = previewImageUrl;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }
    }
}
