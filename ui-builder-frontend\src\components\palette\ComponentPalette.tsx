import React, { useState, useMemo } from 'react';
import { Input, Collapse, Tooltip, Badge } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useDraggable } from '@dnd-kit/core';
import { ComponentType, ComponentCategory } from '@types/index';

// Component definitions
import { componentLibrary } from '@utils/componentLibrary';

const { Panel } = Collapse;

interface ComponentPaletteProps {
  className?: string;
}

interface DraggableComponentProps {
  type: ComponentType;
  name: string;
  displayName: string;
  description: string;
  icon?: string;
  category: string;
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({
  type,
  name,
  displayName,
  description,
  icon,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `palette-${type}`,
    data: {
      type: 'component-palette',
      componentType: type,
    },
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    zIndex: isDragging ? 1000 : 1,
    opacity: isDragging ? 0.8 : 1,
  } : undefined;

  return (
    <Tooltip title={description} placement="right">
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        className={`
          flex items-center p-3 bg-white border border-gray-200 rounded-lg cursor-grab
          hover:border-blue-300 hover:shadow-sm transition-all duration-200
          ${isDragging ? 'shadow-lg border-blue-400' : ''}
        `}
      >
        {/* Component icon */}
        <div className="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-md flex items-center justify-center mr-3">
          {icon ? (
            <span className="text-blue-600 text-lg">{icon}</span>
          ) : (
            <div className="w-4 h-4 bg-blue-400 rounded"></div>
          )}
        </div>

        {/* Component info */}
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {displayName}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {description}
          </div>
        </div>
      </div>
    </Tooltip>
  );
};

const ComponentPalette: React.FC<ComponentPaletteProps> = ({ className = '' }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategories, setActiveCategories] = useState<string[]>([
    'layout',
    'text',
    'form',
    'interactive',
  ]);

  // Filter components based on search term
  const filteredComponents = useMemo(() => {
    if (!searchTerm) {
      return componentLibrary.components;
    }

    return componentLibrary.components.filter(component =>
      component.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      component.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [searchTerm]);

  // Group components by category
  const componentsByCategory = useMemo(() => {
    const grouped: Record<string, typeof componentLibrary.components> = {};

    componentLibrary.categories.forEach(category => {
      grouped[category.id] = filteredComponents.filter(
        component => component.category === category.id
      );
    });

    return grouped;
  }, [filteredComponents]);

  // Get category info
  const getCategoryInfo = (categoryId: string) => {
    return componentLibrary.categories.find(cat => cat.id === categoryId);
  };

  return (
    <div className={`flex flex-col h-full bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Components</h3>
        
        {/* Search */}
        <Input
          placeholder="Search components..."
          prefix={<SearchOutlined className="text-gray-400" />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="mb-3"
        />

        {/* Component count */}
        <div className="text-sm text-gray-500">
          {filteredComponents.length} components available
        </div>
      </div>

      {/* Component categories */}
      <div className="flex-1 overflow-y-auto">
        <Collapse
          activeKey={activeCategories}
          onChange={(keys) => setActiveCategories(keys as string[])}
          ghost
          className="bg-transparent"
        >
          {componentLibrary.categories.map(category => {
            const categoryComponents = componentsByCategory[category.id] || [];
            
            if (categoryComponents.length === 0 && searchTerm) {
              return null;
            }

            return (
              <Panel
                key={category.id}
                header={
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {category.icon && (
                        <span className="mr-2 text-lg">{category.icon}</span>
                      )}
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <Badge 
                      count={categoryComponents.length} 
                      size="small"
                      style={{ backgroundColor: '#3b82f6' }}
                    />
                  </div>
                }
                className="border-0"
              >
                <div className="space-y-2 px-2">
                  {categoryComponents.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <div className="text-sm">No components found</div>
                      {searchTerm && (
                        <div className="text-xs mt-1">
                          Try adjusting your search terms
                        </div>
                      )}
                    </div>
                  ) : (
                    categoryComponents.map(component => (
                      <DraggableComponent
                        key={component.id}
                        type={component.type}
                        name={component.name}
                        displayName={component.displayName}
                        description={component.description}
                        icon={component.icon}
                        category={component.category}
                      />
                    ))
                  )}
                </div>
              </Panel>
            );
          })}
        </Collapse>
      </div>

      {/* Footer with tips */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="text-xs text-gray-500 space-y-1">
          <div>💡 Drag components to the canvas to add them</div>
          <div>🔍 Use search to quickly find components</div>
          <div>⌨️ Press Ctrl+D to duplicate selected components</div>
        </div>
      </div>
    </div>
  );
};

export default ComponentPalette;
