import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';
import { ThemeProvider } from '../../core/src/theme/ThemeProvider';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

export interface TestCase {
  name: string;
  props: ComponentProps;
  expectedBehavior: string;
  assertions: (element: HTMLElement) => void | Promise<void>;
}

export interface AccessibilityTestOptions {
  skipAxe?: boolean;
  customRules?: any[];
  tags?: string[];
}

export interface VisualTestOptions {
  viewports?: Array<{ width: number; height: number; name: string }>;
  themes?: string[];
  states?: string[];
}

export interface PerformanceTestOptions {
  renderCount?: number;
  maxRenderTime?: number;
  memoryThreshold?: number;
}

/**
 * Component Testing Framework
 * 
 * Provides comprehensive testing utilities for UI components including:
 * - Unit testing with React Testing Library
 * - Accessibility testing with jest-axe
 * - Visual regression testing
 * - Performance testing
 * - Cross-browser testing
 */
export class ComponentTestingFramework {
  private defaultTheme = {
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
  };

  /**
   * Render component with theme provider
   */
  renderWithTheme(component: React.ReactElement, theme = this.defaultTheme) {
    return render(
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    );
  }

  /**
   * Run comprehensive component tests
   */
  async runComponentTests(
    Component: React.ComponentType<any>,
    config: ComponentConfig,
    testCases: TestCase[],
    options: {
      accessibility?: AccessibilityTestOptions;
      visual?: VisualTestOptions;
      performance?: PerformanceTestOptions;
    } = {}
  ) {
    describe(`${config.displayName} Component`, () => {
      // Basic rendering tests
      describe('Rendering', () => {
        testCases.forEach((testCase) => {
          it(`should render correctly: ${testCase.name}`, async () => {
            const { container } = this.renderWithTheme(
              <Component {...testCase.props} />
            );

            await testCase.assertions(container);
          });
        });
      });

      // Props validation tests
      describe('Props Validation', () => {
        this.generatePropsTests(Component, config);
      });

      // Accessibility tests
      if (!options.accessibility?.skipAxe) {
        describe('Accessibility', () => {
          this.generateAccessibilityTests(Component, testCases, options.accessibility);
        });
      }

      // Interaction tests
      describe('Interactions', () => {
        this.generateInteractionTests(Component, config, testCases);
      });

      // Performance tests
      if (options.performance) {
        describe('Performance', () => {
          this.generatePerformanceTests(Component, testCases, options.performance);
        });
      }

      // Visual regression tests
      if (options.visual) {
        describe('Visual Regression', () => {
          this.generateVisualTests(Component, testCases, options.visual);
        });
      }
    });
  }

  /**
   * Generate props validation tests
   */
  private generatePropsTests(Component: React.ComponentType<any>, config: ComponentConfig) {
    // Test required props
    if (config.requiredProperties && config.requiredProperties.length > 0) {
      it('should require all required props', () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        
        render(<Component />);
        
        expect(consoleSpy).toHaveBeenCalled();
        consoleSpy.mockRestore();
      });
    }

    // Test default props
    if (config.defaultProps) {
      it('should apply default props correctly', () => {
        const { container } = render(<Component />);
        
        // Verify default props are applied
        Object.entries(config.defaultProps).forEach(([key, value]) => {
          // Add specific assertions based on prop type
          if (typeof value === 'string' && container.textContent) {
            expect(container.textContent).toContain(value);
          }
        });
      });
    }

    // Test prop types validation
    Object.entries(config.properties || {}).forEach(([propName, propDef]) => {
      if (propDef.type === 'boolean') {
        it(`should handle boolean prop: ${propName}`, () => {
          const { rerender } = render(<Component {...{ [propName]: true }} />);
          expect(() => rerender(<Component {...{ [propName]: false }} />)).not.toThrow();
        });
      }

      if (propDef.type === 'number') {
        it(`should handle number prop: ${propName}`, () => {
          expect(() => render(<Component {...{ [propName]: 42 }} />)).not.toThrow();
        });
      }

      if (propDef.type === 'string') {
        it(`should handle string prop: ${propName}`, () => {
          expect(() => render(<Component {...{ [propName]: 'test' }} />)).not.toThrow();
        });
      }
    });
  }

  /**
   * Generate accessibility tests
   */
  private generateAccessibilityTests(
    Component: React.ComponentType<any>,
    testCases: TestCase[],
    options?: AccessibilityTestOptions
  ) {
    testCases.forEach((testCase) => {
      it(`should be accessible: ${testCase.name}`, async () => {
        const { container } = this.renderWithTheme(
          <Component {...testCase.props} />
        );

        const results = await axe(container, {
          rules: options?.customRules,
          tags: options?.tags,
        });

        expect(results).toHaveNoViolations();
      });
    });

    // Keyboard navigation tests
    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      this.renderWithTheme(<Component />);

      // Test Tab navigation
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();

      // Test Enter/Space activation if clickable
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement && (activeElement.onclick || activeElement.getAttribute('role') === 'button')) {
        await user.keyboard('{Enter}');
        // Add specific assertions based on component behavior
      }
    });

    // Screen reader tests
    it('should provide proper screen reader support', () => {
      this.renderWithTheme(<Component aria-label="Test component" />);
      
      const element = screen.getByLabelText('Test component');
      expect(element).toBeInTheDocument();
    });
  }

  /**
   * Generate interaction tests
   */
  private generateInteractionTests(
    Component: React.ComponentType<any>,
    config: ComponentConfig,
    testCases: TestCase[]
  ) {
    // Test event handlers
    config.events?.forEach((event) => {
      it(`should handle ${event.name} event`, async () => {
        const handler = jest.fn();
        const props = { [event.name]: handler };

        this.renderWithTheme(<Component {...props} />);

        // Simulate the event based on its type
        if (event.name === 'onClick') {
          const element = screen.getByRole('button') || screen.getByTestId('clickable');
          fireEvent.click(element);
          expect(handler).toHaveBeenCalled();
        }

        if (event.name === 'onChange') {
          const input = screen.getByRole('textbox') || screen.getByDisplayValue('');
          fireEvent.change(input, { target: { value: 'test' } });
          expect(handler).toHaveBeenCalledWith('test', expect.any(Object));
        }

        if (event.name === 'onFocus') {
          const element = screen.getByRole('textbox') || screen.getByTestId('focusable');
          fireEvent.focus(element);
          expect(handler).toHaveBeenCalled();
        }
      });
    });

    // Test state changes
    it('should handle state changes correctly', async () => {
      const { rerender } = this.renderWithTheme(<Component />);

      // Test different states if component supports them
      const states = ['default', 'hover', 'focus', 'active', 'disabled'];
      
      states.forEach((state) => {
        rerender(<Component state={state} />);
        // Add state-specific assertions
      });
    });
  }

  /**
   * Generate performance tests
   */
  private generatePerformanceTests(
    Component: React.ComponentType<any>,
    testCases: TestCase[],
    options: PerformanceTestOptions
  ) {
    it('should render within performance budget', async () => {
      const renderCount = options.renderCount || 100;
      const maxRenderTime = options.maxRenderTime || 16; // 60fps = 16ms per frame

      const startTime = performance.now();

      for (let i = 0; i < renderCount; i++) {
        const { unmount } = render(<Component />);
        unmount();
      }

      const endTime = performance.now();
      const averageRenderTime = (endTime - startTime) / renderCount;

      expect(averageRenderTime).toBeLessThan(maxRenderTime);
    });

    it('should not cause memory leaks', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Render and unmount multiple times
      for (let i = 0; i < 50; i++) {
        const { unmount } = render(<Component />);
        unmount();
      }

      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;
      const threshold = options.memoryThreshold || 1024 * 1024; // 1MB

      expect(memoryIncrease).toBeLessThan(threshold);
    });
  }

  /**
   * Generate visual regression tests
   */
  private generateVisualTests(
    Component: React.ComponentType<any>,
    testCases: TestCase[],
    options: VisualTestOptions
  ) {
    const viewports = options.viewports || [
      { width: 320, height: 568, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' },
    ];

    const themes = options.themes || ['light', 'dark'];

    testCases.forEach((testCase) => {
      viewports.forEach((viewport) => {
        themes.forEach((theme) => {
          it(`should match visual snapshot: ${testCase.name} - ${viewport.name} - ${theme}`, () => {
            // Set viewport size
            Object.defineProperty(window, 'innerWidth', {
              writable: true,
              configurable: true,
              value: viewport.width,
            });
            Object.defineProperty(window, 'innerHeight', {
              writable: true,
              configurable: true,
              value: viewport.height,
            });

            const { container } = this.renderWithTheme(
              <Component {...testCase.props} />,
              theme === 'dark' ? this.getDarkTheme() : this.defaultTheme
            );

            expect(container.firstChild).toMatchSnapshot(
              `${testCase.name}-${viewport.name}-${theme}.snap`
            );
          });
        });
      });
    });
  }

  /**
   * Test component with different data scenarios
   */
  testWithDataScenarios(
    Component: React.ComponentType<any>,
    scenarios: Array<{
      name: string;
      data: any;
      expectedOutcome: string;
    }>
  ) {
    describe('Data Scenarios', () => {
      scenarios.forEach((scenario) => {
        it(`should handle ${scenario.name}`, () => {
          const { container } = this.renderWithTheme(
            <Component data={scenario.data} />
          );

          // Add scenario-specific assertions
          expect(container).toBeInTheDocument();
        });
      });
    });
  }

  /**
   * Test component error boundaries
   */
  testErrorHandling(Component: React.ComponentType<any>) {
    describe('Error Handling', () => {
      it('should handle errors gracefully', () => {
        const ThrowError = () => {
          throw new Error('Test error');
        };

        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        expect(() => {
          render(
            <Component>
              <ThrowError />
            </Component>
          );
        }).not.toThrow();

        consoleSpy.mockRestore();
      });
    });
  }

  /**
   * Get dark theme configuration
   */
  private getDarkTheme() {
    return {
      ...this.defaultTheme,
      colors: {
        ...this.defaultTheme.colors,
        primary: '#60a5fa',
        background: '#1f2937',
        text: '#f9fafb',
      },
    };
  }

  /**
   * Create mock functions for testing
   */
  createMockProps(config: ComponentConfig): any {
    const mockProps: any = {};

    Object.entries(config.properties || {}).forEach(([key, prop]) => {
      if (prop.type === 'function') {
        mockProps[key] = jest.fn();
      }
    });

    return mockProps;
  }

  /**
   * Wait for async operations
   */
  async waitForAsyncOperations() {
    await waitFor(() => {
      // Wait for any pending promises
    });
  }
}

// Export testing utilities
export const testingFramework = new ComponentTestingFramework();

// Helper functions
export const createTestCase = (
  name: string,
  props: ComponentProps,
  expectedBehavior: string,
  assertions: (element: HTMLElement) => void | Promise<void>
): TestCase => ({
  name,
  props,
  expectedBehavior,
  assertions,
});

export const expectElementToBeVisible = (element: HTMLElement) => {
  expect(element).toBeVisible();
};

export const expectElementToHaveText = (element: HTMLElement, text: string) => {
  expect(element).toHaveTextContent(text);
};

export const expectElementToHaveClass = (element: HTMLElement, className: string) => {
  expect(element).toHaveClass(className);
};

export const expectElementToBeAccessible = async (element: HTMLElement) => {
  const results = await axe(element);
  expect(results).toHaveNoViolations();
};
