{"name": "@ui-builder/react-components", "version": "1.0.0", "description": "React component library for UI Builder platform", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "type-check": "tsc --noEmit"}, "dependencies": {"@ui-builder/design-tokens": "workspace:*", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react-aria": "^3.30.0", "react-stately": "^3.28.0", "tailwind-merge": "^2.1.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "jsdom": "^23.0.1", "postcss": "^8.4.32", "storybook": "^7.6.6", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.5", "vite-plugin-dts": "^3.6.4", "vitest": "^0.34.6"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/ui-builder/component-library.git", "directory": "packages/react-components"}, "keywords": ["react", "components", "design-system", "ui-builder", "typescript"], "author": "UI Builder Team", "license": "MIT"}