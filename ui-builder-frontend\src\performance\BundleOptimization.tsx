// Bundle optimization and code splitting configuration
import { lazy, Suspense, ComponentType } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// Lazy loading with error boundaries and loading states
export const createLazyComponent = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <ErrorBoundary
      fallback={<div>Something went wrong loading this component.</div>}
      onError={(error, errorInfo) => {
        console.error('Lazy component error:', error, errorInfo);
        // Send to error tracking service
        window.Sentry?.captureException(error, { extra: errorInfo });
      }}
    >
      <Suspense fallback={fallback ? <fallback /> : <div>Loading...</div>}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Route-based code splitting
export const UIBuilderCanvas = createLazyComponent(
  () => import('../components/Canvas/UIBuilderCanvas'),
  () => <div className="animate-pulse bg-gray-200 h-96 rounded-lg" />
);

export const ComponentLibrary = createLazyComponent(
  () => import('../components/ComponentLibrary/ComponentLibrary'),
  () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg" />
);

export const PropertyPanel = createLazyComponent(
  () => import('../components/PropertyPanel/PropertyPanel'),
  () => <div className="animate-pulse bg-gray-200 h-full w-80 rounded-lg" />
);

export const TemplateGallery = createLazyComponent(
  () => import('../components/Templates/TemplateGallery'),
  () => <div className="animate-pulse bg-gray-200 h-96 rounded-lg" />
);

export const CollaborationPanel = createLazyComponent(
  () => import('../components/Collaboration/CollaborationPanel'),
  () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg" />
);

// Feature-based code splitting
export const AdvancedEditor = createLazyComponent(
  () => import('../features/AdvancedEditor/AdvancedEditor')
);

export const Analytics = createLazyComponent(
  () => import('../features/Analytics/Analytics')
);

export const AdminPanel = createLazyComponent(
  () => import('../features/Admin/AdminPanel')
);

// Dynamic imports for heavy libraries
export const loadMonacoEditor = () => 
  import('monaco-editor').then(monaco => {
    // Configure Monaco for better performance
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });
    
    return monaco;
  });

export const loadChartLibrary = () =>
  import('recharts').then(recharts => recharts);

export const loadDatePicker = () =>
  import('react-datepicker').then(datePicker => datePicker);

// Resource preloading
export const preloadCriticalResources = () => {
  // Preload critical CSS
  const criticalCSS = document.createElement('link');
  criticalCSS.rel = 'preload';
  criticalCSS.as = 'style';
  criticalCSS.href = '/css/critical.css';
  document.head.appendChild(criticalCSS);

  // Preload critical fonts
  const font = document.createElement('link');
  font.rel = 'preload';
  font.as = 'font';
  font.type = 'font/woff2';
  font.href = '/fonts/inter-var.woff2';
  font.crossOrigin = 'anonymous';
  document.head.appendChild(font);

  // Preload critical images
  const heroImage = new Image();
  heroImage.src = '/images/hero-bg.webp';
};

// Service Worker for caching
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, prompt user to refresh
              if (confirm('New version available! Refresh to update?')) {
                window.location.reload();
              }
            }
          });
        }
      });
      
      console.log('Service Worker registered successfully');
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }
};

// Image optimization
export const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}> = ({ src, alt, width, height, className, priority = false }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (!imgRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting || priority) {
          const img = imgRef.current;
          if (img && !img.src) {
            img.src = src;
          }
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, [src, priority]);

  const handleLoad = () => setIsLoaded(true);
  const handleError = () => setError(true);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {!isLoaded && !error && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{ width, height }}
        />
      )}
      
      <img
        ref={imgRef}
        alt={alt}
        width={width}
        height={height}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
      />
      
      {error && (
        <div 
          className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500"
          style={{ width, height }}
        >
          Failed to load image
        </div>
      )}
    </div>
  );
};

// Virtual scrolling for large lists
export const VirtualizedList: React.FC<{
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
}> = ({ items, itemHeight, containerHeight, renderItem, overscan = 5 }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  return (
    <div
      ref={containerRef}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Performance monitoring
export const PerformanceMonitor: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
          // Send to analytics
          window.gtag?.('event', 'web_vitals', {
            name: 'LCP',
            value: entry.startTime,
          });
        }
        
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
          window.gtag?.('event', 'web_vitals', {
            name: 'FID',
            value: entry.processingStart - entry.startTime,
          });
        }
        
        if (entry.entryType === 'layout-shift') {
          console.log('CLS:', entry.value);
          window.gtag?.('event', 'web_vitals', {
            name: 'CLS',
            value: entry.value,
          });
        }
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });

    return () => observer.disconnect();
  }, []);

  return <>{children}</>;
};

// Memory leak prevention
export const useMemoryLeakPrevention = () => {
  const timeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const intervalsRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const listenersRef = useRef<Map<EventTarget, Map<string, EventListener>>>(new Map());

  const setTimeout = (callback: () => void, delay: number) => {
    const timeoutId = window.setTimeout(() => {
      timeoutsRef.current.delete(timeoutId);
      callback();
    }, delay);
    timeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  const setInterval = (callback: () => void, delay: number) => {
    const intervalId = window.setInterval(callback, delay);
    intervalsRef.current.add(intervalId);
    return intervalId;
  };

  const addEventListener = (target: EventTarget, event: string, listener: EventListener) => {
    target.addEventListener(event, listener);
    
    if (!listenersRef.current.has(target)) {
      listenersRef.current.set(target, new Map());
    }
    listenersRef.current.get(target)!.set(event, listener);
  };

  useEffect(() => {
    return () => {
      // Clear all timeouts
      timeoutsRef.current.forEach(clearTimeout);
      timeoutsRef.current.clear();

      // Clear all intervals
      intervalsRef.current.forEach(clearInterval);
      intervalsRef.current.clear();

      // Remove all event listeners
      listenersRef.current.forEach((events, target) => {
        events.forEach((listener, event) => {
          target.removeEventListener(event, listener);
        });
      });
      listenersRef.current.clear();
    };
  }, []);

  return { setTimeout, setInterval, addEventListener };
};

// Bundle analyzer integration
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    import('webpack-bundle-analyzer').then(({ BundleAnalyzerPlugin }) => {
      console.log('Bundle analyzer available at http://localhost:8888');
    });
  }
};

// Critical CSS extraction
export const extractCriticalCSS = () => {
  const criticalStyles = `
    /* Critical styles for above-the-fold content */
    .header { /* styles */ }
    .navigation { /* styles */ }
    .hero { /* styles */ }
    .loading-spinner { /* styles */ }
  `;
  
  const style = document.createElement('style');
  style.textContent = criticalStyles;
  document.head.appendChild(style);
};

// Initialize performance optimizations
export const initializePerformanceOptimizations = () => {
  preloadCriticalResources();
  registerServiceWorker();
  extractCriticalCSS();
  
  // Enable React DevTools profiler in development
  if (process.env.NODE_ENV === 'development') {
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.onCommitFiberRoot = (id, root, priorityLevel) => {
      console.log('React commit:', { id, priorityLevel });
    };
  }
};
