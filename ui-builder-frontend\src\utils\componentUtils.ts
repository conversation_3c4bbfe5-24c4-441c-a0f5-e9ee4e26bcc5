import { v4 as uuidv4 } from 'uuid';
import { UIComponent, ComponentType, Position, Size } from '@types/index';
import { getComponentDefinition } from './componentLibrary';

/**
 * Generate a unique component ID
 */
export function generateComponentId(): string {
  return `comp_${uuidv4().replace(/-/g, '').substring(0, 12)}`;
}

/**
 * Create a default component of the specified type
 */
export function createDefaultComponent(type: ComponentType): UIComponent {
  const definition = getComponentDefinition(type);
  
  if (!definition) {
    throw new Error(`Unknown component type: ${type}`);
  }

  const now = new Date().toISOString();

  return {
    id: generateComponentId(),
    type,
    name: definition.name,
    displayName: definition.displayName,
    properties: { ...definition.defaultProperties },
    position: { x: 0, y: 0, z: 1 },
    size: { ...definition.defaultSize },
    styles: { ...definition.defaultStyles },
    constraints: { ...definition.constraints },
    metadata: {
      createdAt: now,
      updatedAt: now,
      createdBy: 'current-user', // This would be replaced with actual user ID
      version: 1,
      tags: definition.tags,
      description: definition.description,
    },
  };
}

/**
 * Create a component with custom properties
 */
export function createComponent(
  type: ComponentType,
  overrides: Partial<UIComponent> = {}
): UIComponent {
  const defaultComponent = createDefaultComponent(type);
  
  return {
    ...defaultComponent,
    ...overrides,
    properties: {
      ...defaultComponent.properties,
      ...overrides.properties,
    },
    styles: {
      ...defaultComponent.styles,
      ...overrides.styles,
    },
    constraints: {
      ...defaultComponent.constraints,
      ...overrides.constraints,
    },
    metadata: {
      ...defaultComponent.metadata,
      ...overrides.metadata,
    },
  };
}

/**
 * Clone a component with a new ID
 */
export function cloneComponent(component: UIComponent): UIComponent {
  const cloned: UIComponent = {
    ...component,
    id: generateComponentId(),
    children: component.children?.map(cloneComponent),
    metadata: {
      ...component.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: 1,
    },
  };

  // Update parent references in children
  if (cloned.children) {
    cloned.children.forEach(child => {
      child.parentId = cloned.id;
    });
  }

  return cloned;
}

/**
 * Get all component IDs in a tree (including nested children)
 */
export function getAllComponentIds(components: UIComponent[]): string[] {
  const ids: string[] = [];
  
  function traverse(comps: UIComponent[]) {
    comps.forEach(comp => {
      ids.push(comp.id);
      if (comp.children) {
        traverse(comp.children);
      }
    });
  }
  
  traverse(components);
  return ids;
}

/**
 * Find a component by ID in a component tree
 */
export function findComponentById(
  components: UIComponent[], 
  id: string
): UIComponent | null {
  for (const component of components) {
    if (component.id === id) {
      return component;
    }
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }
  return null;
}

/**
 * Find the parent component of a given component
 */
export function findParentComponent(
  components: UIComponent[], 
  childId: string
): UIComponent | null {
  for (const component of components) {
    if (component.children?.some(child => child.id === childId)) {
      return component;
    }
    if (component.children) {
      const found = findParentComponent(component.children, childId);
      if (found) return found;
    }
  }
  return null;
}

/**
 * Get the path to a component (array of parent IDs)
 */
export function getComponentPath(
  components: UIComponent[], 
  targetId: string
): string[] {
  function findPath(comps: UIComponent[], path: string[] = []): string[] | null {
    for (const comp of comps) {
      const currentPath = [...path, comp.id];
      
      if (comp.id === targetId) {
        return currentPath;
      }
      
      if (comp.children) {
        const found = findPath(comp.children, currentPath);
        if (found) return found;
      }
    }
    return null;
  }
  
  return findPath(components) || [];
}

/**
 * Check if a component can be a child of another component
 */
export function canBeChild(parentType: ComponentType, childType: ComponentType): boolean {
  // Define parent-child relationships
  const relationships: Record<ComponentType, ComponentType[]> = {
    [ComponentType.CONTAINER]: [
      ComponentType.TEXT,
      ComponentType.HEADING,
      ComponentType.PARAGRAPH,
      ComponentType.BUTTON,
      ComponentType.INPUT,
      ComponentType.IMAGE,
      ComponentType.CONTAINER,
      ComponentType.GRID,
      ComponentType.FLEX,
    ],
    [ComponentType.GRID]: [
      ComponentType.TEXT,
      ComponentType.HEADING,
      ComponentType.PARAGRAPH,
      ComponentType.BUTTON,
      ComponentType.INPUT,
      ComponentType.IMAGE,
      ComponentType.CONTAINER,
    ],
    [ComponentType.FLEX]: [
      ComponentType.TEXT,
      ComponentType.HEADING,
      ComponentType.PARAGRAPH,
      ComponentType.BUTTON,
      ComponentType.INPUT,
      ComponentType.IMAGE,
      ComponentType.CONTAINER,
    ],
    [ComponentType.TABS]: [
      ComponentType.TEXT,
      ComponentType.HEADING,
      ComponentType.PARAGRAPH,
      ComponentType.BUTTON,
      ComponentType.INPUT,
      ComponentType.IMAGE,
      ComponentType.CONTAINER,
    ],
    [ComponentType.MODAL]: [
      ComponentType.TEXT,
      ComponentType.HEADING,
      ComponentType.PARAGRAPH,
      ComponentType.BUTTON,
      ComponentType.INPUT,
      ComponentType.IMAGE,
      ComponentType.CONTAINER,
    ],
  };

  const allowedChildren = relationships[parentType];
  return allowedChildren ? allowedChildren.includes(childType) : false;
}

/**
 * Calculate the bounding box of multiple components
 */
export function calculateBoundingBox(components: UIComponent[]): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  if (components.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  components.forEach(comp => {
    const x = comp.position.x;
    const y = comp.position.y;
    const width = typeof comp.size.width === 'number' ? comp.size.width : 100;
    const height = typeof comp.size.height === 'number' ? comp.size.height : 100;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  };
}

/**
 * Check if two components overlap
 */
export function componentsOverlap(comp1: UIComponent, comp2: UIComponent): boolean {
  const x1 = comp1.position.x;
  const y1 = comp1.position.y;
  const w1 = typeof comp1.size.width === 'number' ? comp1.size.width : 100;
  const h1 = typeof comp1.size.height === 'number' ? comp1.size.height : 100;

  const x2 = comp2.position.x;
  const y2 = comp2.position.y;
  const w2 = typeof comp2.size.width === 'number' ? comp2.size.width : 100;
  const h2 = typeof comp2.size.height === 'number' ? comp2.size.height : 100;

  return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1);
}

/**
 * Align components to a grid
 */
export function alignToGrid(position: Position, gridSize: number): Position {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
    z: position.z,
  };
}

/**
 * Validate component properties against schema
 */
export function validateComponentProperties(
  component: UIComponent,
  schema: any
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // This is a simplified validation - in a real implementation,
  // you'd use a proper schema validation library like Yup or Joi
  
  Object.entries(schema).forEach(([key, definition]: [string, any]) => {
    const value = component.properties[key];
    
    if (definition.required && (value === undefined || value === null || value === '')) {
      errors.push(`${definition.label || key} is required`);
    }
    
    if (value !== undefined && definition.type) {
      switch (definition.type) {
        case 'string':
          if (typeof value !== 'string') {
            errors.push(`${definition.label || key} must be a string`);
          }
          break;
        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push(`${definition.label || key} must be a number`);
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            errors.push(`${definition.label || key} must be a boolean`);
          }
          break;
        case 'enum':
          if (definition.options && !definition.options.some((opt: any) => opt.value === value)) {
            errors.push(`${definition.label || key} must be one of the allowed values`);
          }
          break;
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Get component display name with fallback
 */
export function getComponentDisplayName(component: UIComponent): string {
  return component.displayName || component.name || component.type;
}

/**
 * Check if component is a container type
 */
export function isContainerComponent(type: ComponentType): boolean {
  const containerTypes = [
    ComponentType.CONTAINER,
    ComponentType.GRID,
    ComponentType.FLEX,
    ComponentType.STACK,
    ComponentType.TABS,
    ComponentType.ACCORDION,
    ComponentType.MODAL,
    ComponentType.DRAWER,
    ComponentType.CARD,
  ];
  
  return containerTypes.includes(type);
}

/**
 * Get default size for component type
 */
export function getDefaultSize(type: ComponentType): Size {
  const definition = getComponentDefinition(type);
  return definition?.defaultSize || { width: 100, height: 100 };
}

/**
 * Get default position for new component
 */
export function getDefaultPosition(existingComponents: UIComponent[]): Position {
  // Simple algorithm to place new components without overlap
  const gridSize = 20;
  let x = gridSize;
  let y = gridSize;
  
  // Find a position that doesn't overlap with existing components
  while (existingComponents.some(comp => 
    Math.abs(comp.position.x - x) < 100 && Math.abs(comp.position.y - y) < 100
  )) {
    x += gridSize * 5;
    if (x > 800) {
      x = gridSize;
      y += gridSize * 5;
    }
  }
  
  return { x, y, z: 1 };
}
