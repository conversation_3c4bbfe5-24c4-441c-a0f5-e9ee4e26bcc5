import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:flutter_ui_runtime/core/widgets/widget_registry.dart';
import 'package:flutter_ui_runtime/core/widgets/dynamic_widget_renderer.dart';
import 'package:flutter_ui_runtime/core/models/ui_metadata.dart';
import 'package:flutter_ui_runtime/widgets/display/text_widget.dart';
import 'package:flutter_ui_runtime/widgets/input/button_widget.dart';
import 'package:flutter_ui_runtime/widgets/layout/column_widget.dart';
import 'package:flutter_ui_runtime/widgets/layout/row_widget.dart';

import 'widget_test.mocks.dart';
import 'test_utils.dart';

@GenerateMocks([])
void main() {
  group('Widget Registry Tests', () {
    late WidgetRegistry registry;

    setUp(() {
      registry = WidgetRegistry.instance;
      registry.initialize();
    });

    test('should register widgets correctly', () {
      expect(registry.isRegistered('Text'), isTrue);
      expect(registry.isRegistered('Button'), isTrue);
      expect(registry.isRegistered('Column'), isTrue);
      expect(registry.isRegistered('Row'), isTrue);
      expect(registry.isRegistered('UnknownWidget'), isFalse);
    });

    test('should get registered widget types', () {
      final types = registry.getRegisteredTypes();
      expect(types, contains('Text'));
      expect(types, contains('Button'));
      expect(types, contains('Column'));
      expect(types, contains('Row'));
    });
  });

  group('Text Widget Tests', () {
    testWidgets('should render text widget correctly', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-text',
        type: 'Text',
        props: {
          'text': 'Hello, World!',
          'style': {
            'fontSize': 16.0,
            'color': '#000000',
          },
        },
      );

      final textWidget = TextWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget.build(),
          ),
        ),
      );

      expect(find.text('Hello, World!'), findsOneWidget);
    });

    testWidgets('should handle text alignment', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-text-align',
        type: 'Text',
        props: {
          'text': 'Centered Text',
          'textAlign': 'center',
        },
      );

      final textWidget = TextWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget.build(),
          ),
        ),
      );

      final textFinder = find.text('Centered Text');
      expect(textFinder, findsOneWidget);

      final Text textWidgetInstance = tester.widget(textFinder);
      expect(textWidgetInstance.textAlign, equals(TextAlign.center));
    });

    testWidgets('should handle selectable text', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-selectable-text',
        type: 'Text',
        props: {
          'text': 'Selectable Text',
          'selectable': true,
        },
      );

      final textWidget = TextWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget.build(),
          ),
        ),
      );

      expect(find.byType(SelectableText), findsOneWidget);
      expect(find.text('Selectable Text'), findsOneWidget);
    });
  });

  group('Button Widget Tests', () {
    testWidgets('should render button widget correctly', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-button',
        type: 'Button',
        props: {
          'text': 'Click Me',
          'variant': 'elevated',
        },
      );

      final buttonWidget = ButtonWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: buttonWidget.build(),
          ),
        ),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Click Me'), findsOneWidget);
    });

    testWidgets('should handle button variants', (WidgetTester tester) async {
      final configs = [
        WidgetConfiguration(
          id: 'elevated-button',
          type: 'Button',
          props: {'text': 'Elevated', 'variant': 'elevated'},
        ),
        WidgetConfiguration(
          id: 'outlined-button',
          type: 'Button',
          props: {'text': 'Outlined', 'variant': 'outlined'},
        ),
        WidgetConfiguration(
          id: 'text-button',
          type: 'Button',
          props: {'text': 'Text', 'variant': 'text'},
        ),
      ];

      for (final config in configs) {
        final buttonWidget = ButtonWidget(config);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: buttonWidget.build(),
            ),
          ),
        );

        switch (config.props['variant']) {
          case 'elevated':
            expect(find.byType(ElevatedButton), findsOneWidget);
            break;
          case 'outlined':
            expect(find.byType(OutlinedButton), findsOneWidget);
            break;
          case 'text':
            expect(find.byType(TextButton), findsOneWidget);
            break;
        }

        expect(find.text(config.props['text']), findsOneWidget);
      }
    });

    testWidgets('should handle disabled state', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'disabled-button',
        type: 'Button',
        props: {
          'text': 'Disabled',
          'disabled': true,
        },
      );

      final buttonWidget = ButtonWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: buttonWidget.build(),
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should handle loading state', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'loading-button',
        type: 'Button',
        props: {
          'text': 'Loading',
          'loading': true,
        },
      );

      final buttonWidget = ButtonWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: buttonWidget.build(),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading'), findsOneWidget);
    });

    testWidgets('should handle full width', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'full-width-button',
        type: 'Button',
        props: {
          'text': 'Full Width',
          'fullWidth': true,
        },
      );

      final buttonWidget = ButtonWidget(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: buttonWidget.build(),
          ),
        ),
      );

      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.width, equals(double.infinity));
    });
  });

  group('Layout Widget Tests', () {
    testWidgets('should render column widget correctly', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-column',
        type: 'Column',
        props: {
          'mainAxisAlignment': 'center',
          'crossAxisAlignment': 'center',
        },
        children: [
          WidgetConfiguration(
            id: 'child-1',
            type: 'Text',
            props: {'text': 'Child 1'},
          ),
          WidgetConfiguration(
            id: 'child-2',
            type: 'Text',
            props: {'text': 'Child 2'},
          ),
        ],
      );

      final columnWidget = ColumnWidget(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: columnWidget.build(),
            ),
          ),
        ),
      );

      expect(find.byType(Column), findsOneWidget);
      expect(find.text('Child 1'), findsOneWidget);
      expect(find.text('Child 2'), findsOneWidget);

      final column = tester.widget<Column>(find.byType(Column));
      expect(column.mainAxisAlignment, equals(MainAxisAlignment.center));
      expect(column.crossAxisAlignment, equals(CrossAxisAlignment.center));
    });

    testWidgets('should render row widget correctly', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-row',
        type: 'Row',
        props: {
          'mainAxisAlignment': 'spaceEvenly',
          'crossAxisAlignment': 'center',
        },
        children: [
          WidgetConfiguration(
            id: 'child-1',
            type: 'Text',
            props: {'text': 'Child 1'},
          ),
          WidgetConfiguration(
            id: 'child-2',
            type: 'Text',
            props: {'text': 'Child 2'},
          ),
        ],
      );

      final rowWidget = RowWidget(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: rowWidget.build(),
            ),
          ),
        ),
      );

      expect(find.byType(Row), findsOneWidget);
      expect(find.text('Child 1'), findsOneWidget);
      expect(find.text('Child 2'), findsOneWidget);

      final row = tester.widget<Row>(find.byType(Row));
      expect(row.mainAxisAlignment, equals(MainAxisAlignment.spaceEvenly));
      expect(row.crossAxisAlignment, equals(CrossAxisAlignment.center));
    });

    testWidgets('should handle spacing in layouts', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'test-column-spacing',
        type: 'Column',
        props: {
          'spacing': 16.0,
        },
        children: [
          WidgetConfiguration(
            id: 'child-1',
            type: 'Text',
            props: {'text': 'Child 1'},
          ),
          WidgetConfiguration(
            id: 'child-2',
            type: 'Text',
            props: {'text': 'Child 2'},
          ),
        ],
      );

      final columnWidget = ColumnWidget(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: columnWidget.build(),
            ),
          ),
        ),
      );

      expect(find.byType(Column), findsOneWidget);
      expect(find.byType(SizedBox), findsOneWidget);

      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.height, equals(16.0));
    });
  });

  group('Dynamic Widget Renderer Tests', () {
    testWidgets('should render dynamic widgets correctly', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'dynamic-text',
        type: 'Text',
        props: {
          'text': 'Dynamic Text',
        },
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      expect(find.text('Dynamic Text'), findsOneWidget);
    });

    testWidgets('should handle unknown widget types', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'unknown-widget',
        type: 'UnknownWidget',
        props: {},
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      expect(find.text('Unknown Widget'), findsOneWidget);
      expect(find.text('Type: UnknownWidget'), findsOneWidget);
    });
  });

  group('Widget Registry Integration Tests', () {
    testWidgets('should build widgets from registry', (WidgetTester tester) async {
      final registry = WidgetRegistry.instance;
      registry.initialize();

      final config = WidgetConfiguration(
        id: 'registry-text',
        type: 'Text',
        props: {
          'text': 'Registry Text',
        },
      );

      final widget = registry.build(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget,
          ),
        ),
      );

      expect(find.text('Registry Text'), findsOneWidget);
    });

    testWidgets('should cache widgets when appropriate', (WidgetTester tester) async {
      final registry = WidgetRegistry.instance;
      registry.initialize();

      final config = WidgetConfiguration(
        id: 'cached-widget',
        type: 'Text',
        props: {
          'text': 'Cached Text',
        },
      );

      // Build widget twice
      final widget1 = registry.build(config);
      final widget2 = registry.build(config);

      // Both should work
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget1,
          ),
        ),
      );

      expect(find.text('Cached Text'), findsOneWidget);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget2,
          ),
        ),
      );

      expect(find.text('Cached Text'), findsOneWidget);
    });
  });

  group('Performance Tests', () {
    testWidgets('should render efficiently', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'performance-test',
        type: 'Text',
        props: {'text': 'Performance Test'},
      );

      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      stopwatch.stop();

      // Should render within reasonable time (16ms for 60fps)
      expect(stopwatch.elapsedMilliseconds, lessThan(16));
      expect(find.text('Performance Test'), findsOneWidget);
    });

    testWidgets('should handle multiple widgets efficiently', (WidgetTester tester) async {
      final configs = List.generate(
        100,
        (index) => WidgetConfiguration(
          id: 'widget-$index',
          type: 'Text',
          props: {'text': 'Widget $index'},
        ),
      );

      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: configs.map((config) => DynamicWidgetRenderer(config: config)).toList(),
              ),
            ),
          ),
        ),
      );

      stopwatch.stop();

      // Should handle multiple widgets efficiently
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
      expect(find.text('Widget 0'), findsOneWidget);
      expect(find.text('Widget 99'), findsOneWidget);
    });
  });

  group('Error Handling Tests', () {
    testWidgets('should handle null properties gracefully', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'null-props',
        type: 'Text',
        props: null,
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      // Should render without crashing
      expect(find.byType(DynamicWidgetRenderer), findsOneWidget);
    });

    testWidgets('should handle invalid property values', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'invalid-props',
        type: 'Text',
        props: {
          'text': 123, // Invalid type for text
          'fontSize': 'invalid', // Invalid type for fontSize
        },
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      // Should render with fallback values
      expect(find.byType(DynamicWidgetRenderer), findsOneWidget);
    });
  });

  group('Accessibility Tests', () {
    testWidgets('should provide proper semantics', (WidgetTester tester) async {
      final config = WidgetConfiguration(
        id: 'accessible-button',
        type: 'Button',
        props: {
          'text': 'Accessible Button',
          'semanticLabel': 'Button for accessibility testing',
        },
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: DynamicWidgetRenderer(config: config),
            ),
          ),
        ),
      );

      expect(find.text('Accessible Button'), findsOneWidget);

      // Check for semantic properties
      final SemanticsHandle handle = tester.ensureSemantics();
      expect(tester.getSemantics(find.text('Accessible Button')), isNotNull);
      handle.dispose();
    });
  });
}
