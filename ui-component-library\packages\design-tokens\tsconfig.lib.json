{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "../../dist/packages/design-tokens", "declaration": true, "declarationMap": true, "sourceMap": true, "rootDir": "src", "module": "CommonJS", "target": "ES2020", "lib": ["ES2020"], "skipLibCheck": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true}, "include": ["src/**/*", "tokens/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}