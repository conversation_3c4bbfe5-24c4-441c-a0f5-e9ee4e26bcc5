package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.uiplatform.entity.FormField;
import jakarta.validation.constraints.*;

import java.util.Map;
import java.util.UUID;

/**
 * DTO for FormField entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormFieldDTO extends BaseDTO {

    @NotBlank(message = "Field name is required")
    @Size(max = 100, message = "Field name must not exceed 100 characters")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "Field name must start with a letter and contain only letters, numbers, and underscores")
    private String fieldName;

    @Size(max = 100, message = "Field label must not exceed 100 characters")
    private String fieldLabel;

    @NotNull(message = "Field type is required")
    private FormField.FieldType fieldType;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @Size(max = 200, message = "Placeholder must not exceed 200 characters")
    private String placeholder;

    private String defaultValue;

    private Boolean isRequired;
    private Boolean isReadonly;
    private Boolean isDisabled;
    private Boolean isVisible;

    @Min(value = 0, message = "Sort order must be non-negative")
    private Integer sortOrder;

    private Map<String, Object> validationRules;
    private Map<String, Object> fieldOptions;
    private Map<String, Object> styleProperties;
    private Map<String, Object> conditionalLogic;

    private String cssClasses;
    private String dataSource;
    private Map<String, Object> dataBinding;

    // Related entities
    @NotNull(message = "UI Configuration ID is required")
    private UUID uiConfigurationId;
    private String uiConfigurationName;

    // Constructors
    public FormFieldDTO() {}

    public FormFieldDTO(String fieldName, FormField.FieldType fieldType) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.fieldLabel = fieldName; // Default label to field name
    }

    // Getters and Setters
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldLabel() {
        return fieldLabel;
    }

    public void setFieldLabel(String fieldLabel) {
        this.fieldLabel = fieldLabel;
    }

    public FormField.FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FormField.FieldType fieldType) {
        this.fieldType = fieldType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Boolean getIsReadonly() {
        return isReadonly;
    }

    public void setIsReadonly(Boolean isReadonly) {
        this.isReadonly = isReadonly;
    }

    public Boolean getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Map<String, Object> getFieldOptions() {
        return fieldOptions;
    }

    public void setFieldOptions(Map<String, Object> fieldOptions) {
        this.fieldOptions = fieldOptions;
    }

    public Map<String, Object> getStyleProperties() {
        return styleProperties;
    }

    public void setStyleProperties(Map<String, Object> styleProperties) {
        this.styleProperties = styleProperties;
    }

    public Map<String, Object> getConditionalLogic() {
        return conditionalLogic;
    }

    public void setConditionalLogic(Map<String, Object> conditionalLogic) {
        this.conditionalLogic = conditionalLogic;
    }

    public String getCssClasses() {
        return cssClasses;
    }

    public void setCssClasses(String cssClasses) {
        this.cssClasses = cssClasses;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Map<String, Object> getDataBinding() {
        return dataBinding;
    }

    public void setDataBinding(Map<String, Object> dataBinding) {
        this.dataBinding = dataBinding;
    }

    public UUID getUiConfigurationId() {
        return uiConfigurationId;
    }

    public void setUiConfigurationId(UUID uiConfigurationId) {
        this.uiConfigurationId = uiConfigurationId;
    }

    public String getUiConfigurationName() {
        return uiConfigurationName;
    }

    public void setUiConfigurationName(String uiConfigurationName) {
        this.uiConfigurationName = uiConfigurationName;
    }

    /**
     * Create DTO for form field creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Field name is required")
        @Size(max = 100, message = "Field name must not exceed 100 characters")
        @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "Field name must start with a letter and contain only letters, numbers, and underscores")
        private String fieldName;

        @Size(max = 100, message = "Field label must not exceed 100 characters")
        private String fieldLabel;

        @NotNull(message = "Field type is required")
        private FormField.FieldType fieldType;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @Size(max = 200, message = "Placeholder must not exceed 200 characters")
        private String placeholder;

        private String defaultValue;

        private Boolean isRequired = false;
        private Boolean isReadonly = false;
        private Boolean isDisabled = false;
        private Boolean isVisible = true;

        @Min(value = 0, message = "Sort order must be non-negative")
        private Integer sortOrder = 0;

        private Map<String, Object> validationRules;
        private Map<String, Object> fieldOptions;
        private Map<String, Object> styleProperties;

        // Getters and Setters
        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldLabel() {
            return fieldLabel;
        }

        public void setFieldLabel(String fieldLabel) {
            this.fieldLabel = fieldLabel;
        }

        public FormField.FieldType getFieldType() {
            return fieldType;
        }

        public void setFieldType(FormField.FieldType fieldType) {
            this.fieldType = fieldType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getPlaceholder() {
            return placeholder;
        }

        public void setPlaceholder(String placeholder) {
            this.placeholder = placeholder;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public Boolean getIsRequired() {
            return isRequired;
        }

        public void setIsRequired(Boolean isRequired) {
            this.isRequired = isRequired;
        }

        public Boolean getIsReadonly() {
            return isReadonly;
        }

        public void setIsReadonly(Boolean isReadonly) {
            this.isReadonly = isReadonly;
        }

        public Boolean getIsDisabled() {
            return isDisabled;
        }

        public void setIsDisabled(Boolean isDisabled) {
            this.isDisabled = isDisabled;
        }

        public Boolean getIsVisible() {
            return isVisible;
        }

        public void setIsVisible(Boolean isVisible) {
            this.isVisible = isVisible;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        public Map<String, Object> getValidationRules() {
            return validationRules;
        }

        public void setValidationRules(Map<String, Object> validationRules) {
            this.validationRules = validationRules;
        }

        public Map<String, Object> getFieldOptions() {
            return fieldOptions;
        }

        public void setFieldOptions(Map<String, Object> fieldOptions) {
            this.fieldOptions = fieldOptions;
        }

        public Map<String, Object> getStyleProperties() {
            return styleProperties;
        }

        public void setStyleProperties(Map<String, Object> styleProperties) {
            this.styleProperties = styleProperties;
        }
    }
}
