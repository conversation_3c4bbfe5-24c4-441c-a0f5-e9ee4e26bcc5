import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, DatePicker, Select, Spin, Alert } from 'antd';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { 
  UserOutlined, EyeOutlined, EditOutlined, CommentOutlined,
  TrophyOutlined, ClockCircleOutlined, TeamOutlined 
} from '@ant-design/icons';
import { useAnalytics } from '../../hooks/useAnalytics';
import { useWorkspace } from '../../hooks/useWorkspace';
import { formatDuration, formatNumber } from '../../utils/formatters';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface AnalyticsDashboardProps {
  workspaceId: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ workspaceId }) => {
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date()
  ]);
  const [selectedMetric, setSelectedMetric] = useState<string>('overview');
  
  const { currentWorkspace } = useWorkspace();
  const {
    userEngagement,
    componentAnalytics,
    uiConfigAnalytics,
    workspaceAnalytics,
    realTimeAnalytics,
    isLoading,
    error,
    fetchUserEngagement,
    fetchComponentAnalytics,
    fetchWorkspaceAnalytics,
    fetchRealTimeAnalytics
  } = useAnalytics();

  useEffect(() => {
    if (workspaceId) {
      fetchWorkspaceAnalytics(workspaceId, dateRange[0], dateRange[1]);
      fetchUserEngagement(workspaceId, dateRange[0], dateRange[1]);
      fetchComponentAnalytics(workspaceId, dateRange[0], dateRange[1]);
      fetchRealTimeAnalytics(workspaceId);
    }
  }, [workspaceId, dateRange]);

  const handleDateRangeChange = (dates: [Date, Date] | null) => {
    if (dates) {
      setDateRange(dates);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error loading analytics"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="analytics-dashboard p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <div className="flex gap-4">
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              format="YYYY-MM-DD"
            />
            <Select
              value={selectedMetric}
              onChange={setSelectedMetric}
              style={{ width: 200 }}
            >
              <Option value="overview">Overview</Option>
              <Option value="engagement">User Engagement</Option>
              <Option value="components">Components</Option>
              <Option value="collaboration">Collaboration</Option>
            </Select>
          </div>
        </div>
      </div>

      {/* Real-time Metrics */}
      <Card title="Real-time Activity" className="mb-6">
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Active Users"
              value={realTimeAnalytics?.currentActiveUsers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Recent Events"
              value={realTimeAnalytics?.recentEvents || 0}
              prefix={<EyeOutlined />}
              suffix="/ hour"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Active Configs"
              value={realTimeAnalytics?.activeConfigs || 0}
              prefix={<EditOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Workspace Health"
              value={95}
              suffix="%"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
        </Row>
      </Card>

      {/* Overview Metrics */}
      {selectedMetric === 'overview' && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total UI Configs"
                  value={workspaceAnalytics?.totalConfigs || 0}
                  prefix={<EditOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Active Users"
                  value={workspaceAnalytics?.activeUsers || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Templates Created"
                  value={workspaceAnalytics?.templatesCreated || 0}
                  prefix={<TrophyOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Collaboration Events"
                  value={workspaceAnalytics?.collaborationEvents || 0}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16} className="mb-6">
            <Col span={12}>
              <Card title="User Activity Trend">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={userEngagement?.activityTrend || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="activeUsers" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      name="Active Users"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="sessions" 
                      stroke="#82ca9d" 
                      strokeWidth={2}
                      name="Sessions"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Top Components">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={componentAnalytics?.topComponents?.slice(0, 8) || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="componentType" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="usageCount" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {/* User Engagement */}
      {selectedMetric === 'engagement' && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={6}>
              <Card>
                <Statistic
                  title="Active Users"
                  value={userEngagement?.activeUsers || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Sessions"
                  value={userEngagement?.totalSessions || 0}
                  prefix={<ClockCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Avg Session Duration"
                  value={formatDuration(userEngagement?.averageSessionDuration || 0)}
                  prefix={<ClockCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Bounce Rate"
                  value={((userEngagement?.bounceRate || 0) * 100).toFixed(1)}
                  suffix="%"
                  valueStyle={{ 
                    color: (userEngagement?.bounceRate || 0) > 0.5 ? '#cf1322' : '#3f8600' 
                  }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16} className="mb-6">
            <Col span={16}>
              <Card title="User Engagement Over Time">
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={userEngagement?.engagementTrend || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="activeUsers" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8"
                      name="Active Users"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="newUsers" 
                      stackId="1"
                      stroke="#82ca9d" 
                      fill="#82ca9d"
                      name="New Users"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col span={8}>
              <Card title="User Retention">
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={userEngagement?.retentionData || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {(userEngagement?.retentionData || []).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {/* Component Analytics */}
      {selectedMetric === 'components' && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={12}>
              <Card title="Component Usage Distribution">
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={componentAnalytics?.topComponents?.slice(0, 10) || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ componentType, percent }) => 
                        `${componentType} ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="usageCount"
                    >
                      {(componentAnalytics?.topComponents || []).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Component Adoption Over Time">
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={componentAnalytics?.adoptionData || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {componentAnalytics?.topComponents?.slice(0, 5).map((component, index) => (
                      <Line
                        key={component.componentType}
                        type="monotone"
                        dataKey={component.componentType}
                        stroke={COLORS[index % COLORS.length]}
                        strokeWidth={2}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>

          <Card title="Component Performance Metrics">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Component</th>
                    <th className="text-right p-2">Usage Count</th>
                    <th className="text-right p-2">Unique Users</th>
                    <th className="text-right p-2">Avg Usage/User</th>
                    <th className="text-right p-2">Adoption Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {componentAnalytics?.topComponents?.map((component) => (
                    <tr key={component.componentType} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{component.componentType}</td>
                      <td className="p-2 text-right">{formatNumber(component.usageCount)}</td>
                      <td className="p-2 text-right">{formatNumber(component.uniqueUsers)}</td>
                      <td className="p-2 text-right">
                        {(component.usageCount / component.uniqueUsers).toFixed(1)}
                      </td>
                      <td className="p-2 text-right">
                        {((component.uniqueUsers / (workspaceAnalytics?.totalUsers || 1)) * 100).toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {/* Collaboration Analytics */}
      {selectedMetric === 'collaboration' && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Comments"
                  value={workspaceAnalytics?.totalComments || 0}
                  prefix={<CommentOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Active Collaborators"
                  value={workspaceAnalytics?.activeCollaborators || 0}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Change Requests"
                  value={workspaceAnalytics?.changeRequests || 0}
                  prefix={<EditOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Approval Rate"
                  value={((workspaceAnalytics?.approvalRate || 0) * 100).toFixed(1)}
                  suffix="%"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Card title="Collaboration Activity">
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={workspaceAnalytics?.collaborationTrend || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="comments" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8"
                      name="Comments"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="changeRequests" 
                      stackId="1"
                      stroke="#82ca9d" 
                      fill="#82ca9d"
                      name="Change Requests"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Most Active Collaborators">
                <div className="space-y-3">
                  {workspaceAnalytics?.topCollaborators?.map((collaborator, index) => (
                    <div key={collaborator.userId} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{collaborator.name}</div>
                          <div className="text-sm text-gray-500">{collaborator.email}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{collaborator.activityCount}</div>
                        <div className="text-sm text-gray-500">activities</div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {/* Recent Activity Feed */}
      <Card title="Recent Activity" className="mt-6">
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {realTimeAnalytics?.recentActivity?.map((activity, index) => (
            <div key={index} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <span className="font-medium">{activity.userName}</span>
                <span className="text-gray-600"> {activity.action} </span>
                <span className="font-medium">{activity.target}</span>
              </div>
              <div className="text-sm text-gray-500">
                {new Date(activity.timestamp).toLocaleTimeString()}
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
