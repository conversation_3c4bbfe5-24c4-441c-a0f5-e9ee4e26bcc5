package com.uibuilder.collaboration;

import com.uibuilder.entity.UIVersion;
import com.uibuilder.entity.UIBranch;
import com.uibuilder.entity.UIConfig;
import com.uibuilder.entity.User;
import com.uibuilder.repository.UIVersionRepository;
import com.uibuilder.repository.UIBranchRepository;
import com.uibuilder.repository.UIConfigRepository;
import com.uibuilder.repository.UserRepository;
import com.uibuilder.security.rbac.RoleBasedAccessControl;
import com.uibuilder.websocket.CollaborationWebSocketHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.fge.jsonpatch.JsonPatch;
import com.github.fge.jsonpatch.diff.JsonDiff;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class VersionControlService {

    private final UIVersionRepository versionRepository;
    private final UIBranchRepository branchRepository;
    private final UIConfigRepository configRepository;
    private final UserRepository userRepository;
    private final RoleBasedAccessControl rbac;
    private final CollaborationWebSocketHandler webSocketHandler;
    private final ObjectMapper objectMapper;

    /**
     * Create a new version of a UI config
     */
    @Transactional
    public UIVersion createVersion(String configId, String message, String userId, Object changes) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "create_version", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to create versions");
        }

        UIConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        User user = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("User not found"));

        // Get the latest version number
        int nextVersionNumber = getNextVersionNumber(configId);

        // Calculate diff from previous version
        String diff = calculateDiff(config, changes);

        // Create version
        UIVersion version = UIVersion.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(config)
            .versionNumber(nextVersionNumber)
            .message(message)
            .changes(serializeChanges(changes))
            .diff(diff)
            .createdBy(user)
            .createdAt(LocalDateTime.now())
            .configSnapshot(serializeConfig(config))
            .build();

        version = versionRepository.save(version);

        // Update config's current version
        config.setCurrentVersion(nextVersionNumber);
        config.setUpdatedAt(LocalDateTime.now());
        configRepository.save(config);

        // Broadcast version creation
        VersionEvent event = VersionEvent.builder()
            .type("VERSION_CREATED")
            .version(toVersionDto(version))
            .configId(configId)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(configId, event);

        log.info("Version {} created for config {} by user {}", nextVersionNumber, configId, userId);
        return version;
    }

    /**
     * Create a new branch from a specific version
     */
    @Transactional
    public UIBranch createBranch(String configId, String branchName, String sourceVersionId, 
                                String description, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "create_branch", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to create branches");
        }

        UIConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        UIVersion sourceVersion = versionRepository.findById(sourceVersionId)
            .orElseThrow(() -> new EntityNotFoundException("Source version not found"));

        User user = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("User not found"));

        // Check if branch name already exists
        if (branchRepository.existsByUiConfigAndName(config, branchName)) {
            throw new IllegalArgumentException("Branch name already exists");
        }

        // Create branch
        UIBranch branch = UIBranch.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(config)
            .name(branchName)
            .description(description)
            .sourceVersion(sourceVersion)
            .createdBy(user)
            .createdAt(LocalDateTime.now())
            .isActive(true)
            .build();

        branch = branchRepository.save(branch);

        // Create initial version for the branch
        UIVersion branchVersion = UIVersion.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(config)
            .branch(branch)
            .versionNumber(1)
            .message("Initial branch version from " + sourceVersion.getVersionNumber())
            .changes(sourceVersion.getChanges())
            .configSnapshot(sourceVersion.getConfigSnapshot())
            .createdBy(user)
            .createdAt(LocalDateTime.now())
            .build();

        versionRepository.save(branchVersion);

        // Broadcast branch creation
        BranchEvent event = BranchEvent.builder()
            .type("BRANCH_CREATED")
            .branch(toBranchDto(branch))
            .configId(configId)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(configId, event);

        log.info("Branch {} created for config {} by user {}", branchName, configId, userId);
        return branch;
    }

    /**
     * Merge a branch back to main
     */
    @Transactional
    public UIVersion mergeBranch(String branchId, String targetBranchId, String message, String userId) {
        UIBranch sourceBranch = branchRepository.findById(branchId)
            .orElseThrow(() -> new EntityNotFoundException("Source branch not found"));

        UIBranch targetBranch = targetBranchId != null ? 
            branchRepository.findById(targetBranchId)
                .orElseThrow(() -> new EntityNotFoundException("Target branch not found")) : null;

        // Validate access
        String workspaceId = getWorkspaceId(sourceBranch.getUiConfig().getId());
        if (!rbac.hasPermission(userId, "ui_config", "merge_branch", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to merge branches");
        }

        // Get latest versions from both branches
        UIVersion sourceVersion = getLatestBranchVersion(sourceBranch);
        UIVersion targetVersion = targetBranch != null ? 
            getLatestBranchVersion(targetBranch) : getLatestMainVersion(sourceBranch.getUiConfig());

        // Perform three-way merge
        MergeResult mergeResult = performMerge(sourceVersion, targetVersion, sourceBranch.getSourceVersion());

        if (mergeResult.hasConflicts()) {
            throw new MergeConflictException("Merge conflicts detected", mergeResult.getConflicts());
        }

        // Create merge version
        UIVersion mergeVersion = UIVersion.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(sourceBranch.getUiConfig())
            .branch(targetBranch) // null for main branch
            .versionNumber(getNextVersionNumber(sourceBranch.getUiConfig().getId(), targetBranch))
            .message(message)
            .changes(serializeChanges(mergeResult.getMergedConfig()))
            .configSnapshot(serializeConfig(mergeResult.getMergedConfig()))
            .parentVersions(List.of(sourceVersion.getId(), targetVersion.getId()))
            .createdBy(userRepository.findById(userId).orElseThrow())
            .createdAt(LocalDateTime.now())
            .build();

        mergeVersion = versionRepository.save(mergeVersion);

        // Update config if merging to main
        if (targetBranch == null) {
            UIConfig config = sourceBranch.getUiConfig();
            applyConfigChanges(config, mergeResult.getMergedConfig());
            config.setCurrentVersion(mergeVersion.getVersionNumber());
            config.setUpdatedAt(LocalDateTime.now());
            configRepository.save(config);
        }

        // Mark source branch as merged
        sourceBranch.setMergedAt(LocalDateTime.now());
        sourceBranch.setMergedBy(userId);
        sourceBranch.setIsActive(false);
        branchRepository.save(sourceBranch);

        // Broadcast merge
        MergeEvent event = MergeEvent.builder()
            .type("BRANCH_MERGED")
            .mergeVersion(toVersionDto(mergeVersion))
            .sourceBranch(toBranchDto(sourceBranch))
            .targetBranch(targetBranch != null ? toBranchDto(targetBranch) : null)
            .configId(sourceBranch.getUiConfig().getId())
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(sourceBranch.getUiConfig().getId(), event);

        log.info("Branch {} merged by user {}", sourceBranch.getName(), userId);
        return mergeVersion;
    }

    /**
     * Revert to a specific version
     */
    @Transactional
    public UIVersion revertToVersion(String configId, String versionId, String message, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "revert_version", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to revert versions");
        }

        UIConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        UIVersion targetVersion = versionRepository.findById(versionId)
            .orElseThrow(() -> new EntityNotFoundException("Target version not found"));

        // Create revert version
        int nextVersionNumber = getNextVersionNumber(configId);
        
        UIVersion revertVersion = UIVersion.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(config)
            .versionNumber(nextVersionNumber)
            .message(message + " (reverted to version " + targetVersion.getVersionNumber() + ")")
            .changes(targetVersion.getChanges())
            .configSnapshot(targetVersion.getConfigSnapshot())
            .revertedFromVersion(targetVersion.getId())
            .createdBy(userRepository.findById(userId).orElseThrow())
            .createdAt(LocalDateTime.now())
            .build();

        revertVersion = versionRepository.save(revertVersion);

        // Apply the reverted config
        Object revertedConfig = deserializeConfig(targetVersion.getConfigSnapshot());
        applyConfigChanges(config, revertedConfig);
        config.setCurrentVersion(nextVersionNumber);
        config.setUpdatedAt(LocalDateTime.now());
        configRepository.save(config);

        // Broadcast revert
        VersionEvent event = VersionEvent.builder()
            .type("VERSION_REVERTED")
            .version(toVersionDto(revertVersion))
            .revertedToVersion(toVersionDto(targetVersion))
            .configId(configId)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(configId, event);

        log.info("Config {} reverted to version {} by user {}", configId, targetVersion.getVersionNumber(), userId);
        return revertVersion;
    }

    /**
     * Get version history for a config
     */
    public Page<UIVersion> getVersionHistory(String configId, String branchId, Pageable pageable, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "read", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to view version history");
        }

        if (branchId != null) {
            UIBranch branch = branchRepository.findById(branchId)
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));
            return versionRepository.findByUiConfigAndBranchOrderByCreatedAtDesc(
                configRepository.findById(configId).orElseThrow(), branch, pageable);
        } else {
            return versionRepository.findByUiConfigAndBranchIsNullOrderByCreatedAtDesc(
                configRepository.findById(configId).orElseThrow(), pageable);
        }
    }

    /**
     * Get branches for a config
     */
    public List<UIBranch> getBranches(String configId, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "read", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to view branches");
        }

        UIConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        return branchRepository.findByUiConfigOrderByCreatedAtDesc(config);
    }

    /**
     * Compare two versions
     */
    public VersionComparison compareVersions(String version1Id, String version2Id, String userId) {
        UIVersion version1 = versionRepository.findById(version1Id)
            .orElseThrow(() -> new EntityNotFoundException("Version 1 not found"));

        UIVersion version2 = versionRepository.findById(version2Id)
            .orElseThrow(() -> new EntityNotFoundException("Version 2 not found"));

        // Validate access
        String workspaceId = getWorkspaceId(version1.getUiConfig().getId());
        if (!rbac.hasPermission(userId, "ui_config", "read", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to compare versions");
        }

        // Calculate diff between versions
        String diff = calculateVersionDiff(version1, version2);

        return VersionComparison.builder()
            .version1(toVersionDto(version1))
            .version2(toVersionDto(version2))
            .diff(diff)
            .timestamp(LocalDateTime.now())
            .build();
    }

    private int getNextVersionNumber(String configId) {
        return getNextVersionNumber(configId, null);
    }

    private int getNextVersionNumber(String configId, UIBranch branch) {
        UIConfig config = configRepository.findById(configId).orElseThrow();
        
        if (branch == null) {
            // Main branch
            return versionRepository.findMaxVersionNumberByUiConfigAndBranchIsNull(config)
                .orElse(0) + 1;
        } else {
            // Specific branch
            return versionRepository.findMaxVersionNumberByUiConfigAndBranch(config, branch)
                .orElse(0) + 1;
        }
    }

    private UIVersion getLatestBranchVersion(UIBranch branch) {
        return versionRepository.findFirstByUiConfigAndBranchOrderByCreatedAtDesc(
            branch.getUiConfig(), branch).orElseThrow();
    }

    private UIVersion getLatestMainVersion(UIConfig config) {
        return versionRepository.findFirstByUiConfigAndBranchIsNullOrderByCreatedAtDesc(config)
            .orElseThrow();
    }

    private String calculateDiff(UIConfig config, Object changes) {
        try {
            JsonNode currentConfig = objectMapper.valueToTree(config);
            JsonNode newConfig = objectMapper.valueToTree(changes);
            JsonPatch patch = JsonDiff.asJsonPatch(currentConfig, newConfig);
            return objectMapper.writeValueAsString(patch);
        } catch (Exception e) {
            log.error("Failed to calculate diff", e);
            return "{}";
        }
    }

    private String calculateVersionDiff(UIVersion version1, UIVersion version2) {
        try {
            JsonNode config1 = objectMapper.readTree(version1.getConfigSnapshot());
            JsonNode config2 = objectMapper.readTree(version2.getConfigSnapshot());
            JsonPatch patch = JsonDiff.asJsonPatch(config1, config2);
            return objectMapper.writeValueAsString(patch);
        } catch (Exception e) {
            log.error("Failed to calculate version diff", e);
            return "{}";
        }
    }

    private MergeResult performMerge(UIVersion sourceVersion, UIVersion targetVersion, UIVersion baseVersion) {
        // Simplified three-way merge implementation
        // In a real implementation, this would handle complex merge scenarios
        try {
            JsonNode sourceConfig = objectMapper.readTree(sourceVersion.getConfigSnapshot());
            JsonNode targetConfig = objectMapper.readTree(targetVersion.getConfigSnapshot());
            JsonNode baseConfig = objectMapper.readTree(baseVersion.getConfigSnapshot());

            // For now, just use source config as merged result
            // A real implementation would detect and resolve conflicts
            Object mergedConfig = objectMapper.treeToValue(sourceConfig, Object.class);

            return MergeResult.builder()
                .mergedConfig(mergedConfig)
                .hasConflicts(false)
                .conflicts(List.of())
                .build();

        } catch (Exception e) {
            log.error("Failed to perform merge", e);
            throw new RuntimeException("Merge failed", e);
        }
    }

    private String serializeChanges(Object changes) {
        try {
            return objectMapper.writeValueAsString(changes);
        } catch (Exception e) {
            log.error("Failed to serialize changes", e);
            return "{}";
        }
    }

    private String serializeConfig(UIConfig config) {
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("Failed to serialize config", e);
            return "{}";
        }
    }

    private Object deserializeConfig(String configSnapshot) {
        try {
            return objectMapper.readValue(configSnapshot, Object.class);
        } catch (Exception e) {
            log.error("Failed to deserialize config", e);
            return new Object();
        }
    }

    private void applyConfigChanges(UIConfig config, Object changes) {
        // Implementation would apply the changes to the config
        // This is simplified for the example
        config.setUpdatedAt(LocalDateTime.now());
    }

    private String getWorkspaceId(String configId) {
        return configRepository.findById(configId)
            .map(config -> config.getWorkspace().getId())
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));
    }

    private VersionDto toVersionDto(UIVersion version) {
        return VersionDto.builder()
            .id(version.getId())
            .versionNumber(version.getVersionNumber())
            .message(version.getMessage())
            .createdBy(toUserDto(version.getCreatedBy()))
            .createdAt(version.getCreatedAt())
            .branchId(version.getBranch() != null ? version.getBranch().getId() : null)
            .build();
    }

    private BranchDto toBranchDto(UIBranch branch) {
        return BranchDto.builder()
            .id(branch.getId())
            .name(branch.getName())
            .description(branch.getDescription())
            .createdBy(toUserDto(branch.getCreatedBy()))
            .createdAt(branch.getCreatedAt())
            .isActive(branch.getIsActive())
            .mergedAt(branch.getMergedAt())
            .build();
    }

    private UserDto toUserDto(User user) {
        return UserDto.builder()
            .id(user.getId())
            .name(user.getName())
            .email(user.getEmail())
            .avatar(user.getAvatar())
            .build();
    }
}
