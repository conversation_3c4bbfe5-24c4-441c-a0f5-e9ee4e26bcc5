package com.uiplatform.controller;

import com.uiplatform.dto.UIConfigDTO;
import com.uiplatform.dto.UIConfigCreateDTO;
import com.uiplatform.dto.UIConfigUpdateDTO;
import com.uiplatform.service.UIConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequestMapping("/api/v1/ui-configs")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "UI Configuration", description = "UI Configuration management API")
public class UIConfigController {

    private final UIConfigService uiConfigService;

    @Operation(summary = "Get all UI configurations", description = "Retrieve paginated list of UI configurations")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved UI configurations"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Page<UIConfigDTO>> getAllUIConfigs(
            @Parameter(description = "Pagination information") Pageable pageable,
            @Parameter(description = "Organization ID filter") @RequestParam(required = false) String organizationId,
            @Parameter(description = "Search query") @RequestParam(required = false) String search) {
        
        log.info("Fetching UI configurations with pagination: {}, organizationId: {}, search: {}", 
                pageable, organizationId, search);
        
        Page<UIConfigDTO> configs = uiConfigService.getAllUIConfigs(pageable, organizationId, search);
        return ResponseEntity.ok(configs);
    }

    @Operation(summary = "Get UI configuration by ID", description = "Retrieve a specific UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved UI configuration"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> getUIConfigById(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id) {
        
        log.info("Fetching UI configuration with ID: {}", id);
        UIConfigDTO config = uiConfigService.getUIConfigById(id);
        return ResponseEntity.ok(config);
    }

    @Operation(summary = "Create new UI configuration", description = "Create a new UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "UI configuration created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> createUIConfig(
            @Parameter(description = "UI configuration data") @Valid @RequestBody UIConfigCreateDTO createDTO) {
        
        log.info("Creating new UI configuration: {}", createDTO.getName());
        UIConfigDTO createdConfig = uiConfigService.createUIConfig(createDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdConfig);
    }

    @Operation(summary = "Update UI configuration", description = "Update an existing UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "UI configuration updated successfully"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> updateUIConfig(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id,
            @Parameter(description = "Updated UI configuration data") @Valid @RequestBody UIConfigUpdateDTO updateDTO) {
        
        log.info("Updating UI configuration with ID: {}", id);
        UIConfigDTO updatedConfig = uiConfigService.updateUIConfig(id, updateDTO);
        return ResponseEntity.ok(updatedConfig);
    }

    @Operation(summary = "Delete UI configuration", description = "Delete a UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "UI configuration deleted successfully"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @uiConfigService.isOwner(#id, authentication.name)")
    public ResponseEntity<Void> deleteUIConfig(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id) {
        
        log.info("Deleting UI configuration with ID: {}", id);
        uiConfigService.deleteUIConfig(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Duplicate UI configuration", description = "Create a copy of an existing UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "UI configuration duplicated successfully"),
        @ApiResponse(responseCode = "404", description = "Source UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{id}/duplicate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> duplicateUIConfig(
            @Parameter(description = "Source UI configuration ID") @PathVariable @NotBlank String id,
            @Parameter(description = "New configuration name") @RequestParam(required = false) String newName) {
        
        log.info("Duplicating UI configuration with ID: {}", id);
        UIConfigDTO duplicatedConfig = uiConfigService.duplicateUIConfig(id, newName);
        return ResponseEntity.status(HttpStatus.CREATED).body(duplicatedConfig);
    }

    @Operation(summary = "Publish UI configuration", description = "Publish a UI configuration for public use")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "UI configuration published successfully"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{id}/publish")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> publishUIConfig(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id) {
        
        log.info("Publishing UI configuration with ID: {}", id);
        UIConfigDTO publishedConfig = uiConfigService.publishUIConfig(id);
        return ResponseEntity.ok(publishedConfig);
    }

    @Operation(summary = "Get UI configuration versions", description = "Retrieve version history of a UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved version history"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}/versions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<UIConfigDTO>> getUIConfigVersions(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id) {
        
        log.info("Fetching versions for UI configuration with ID: {}", id);
        List<UIConfigDTO> versions = uiConfigService.getUIConfigVersions(id);
        return ResponseEntity.ok(versions);
    }

    @Operation(summary = "Restore UI configuration version", description = "Restore a specific version of UI configuration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Version restored successfully"),
        @ApiResponse(responseCode = "404", description = "UI configuration or version not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{id}/versions/{versionId}/restore")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> restoreUIConfigVersion(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id,
            @Parameter(description = "Version ID to restore") @PathVariable @NotBlank String versionId) {
        
        log.info("Restoring version {} for UI configuration with ID: {}", versionId, id);
        UIConfigDTO restoredConfig = uiConfigService.restoreUIConfigVersion(id, versionId);
        return ResponseEntity.ok(restoredConfig);
    }

    @Operation(summary = "Export UI configuration", description = "Export UI configuration as JSON")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "UI configuration exported successfully"),
        @ApiResponse(responseCode = "404", description = "UI configuration not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}/export")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<String> exportUIConfig(
            @Parameter(description = "UI configuration ID") @PathVariable @NotBlank String id,
            @Parameter(description = "Export format") @RequestParam(defaultValue = "json") String format) {
        
        log.info("Exporting UI configuration with ID: {} in format: {}", id, format);
        String exportData = uiConfigService.exportUIConfig(id, format);
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=ui-config-" + id + "." + format)
                .body(exportData);
    }

    @Operation(summary = "Import UI configuration", description = "Import UI configuration from JSON")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "UI configuration imported successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid import data"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/import")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UIConfigDTO> importUIConfig(
            @Parameter(description = "Import data") @RequestBody String importData,
            @Parameter(description = "Import format") @RequestParam(defaultValue = "json") String format) {
        
        log.info("Importing UI configuration in format: {}", format);
        UIConfigDTO importedConfig = uiConfigService.importUIConfig(importData, format);
        return ResponseEntity.status(HttpStatus.CREATED).body(importedConfig);
    }
}
