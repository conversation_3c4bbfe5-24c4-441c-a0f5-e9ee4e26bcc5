import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  UIMetadata, 
  ThemeConfiguration, 
  UserInfo, 
  RuntimeError,
  PerformanceMetrics,
  RealtimeUpdate 
} from '@types/index';

interface RuntimeState {
  // Core state
  metadata: UIMetadata | null;
  theme: ThemeConfiguration | null;
  data: Record<string, any>;
  loading: boolean;
  error: RuntimeError | null;
  
  // User and permissions
  user: UserInfo | null;
  permissions: string[];
  
  // Performance tracking
  performance: PerformanceMetrics | null;
  
  // Real-time updates
  connected: boolean;
  lastUpdate: string | null;
  
  // Cache
  cache: Map<string, { data: any; timestamp: number; ttl: number }>;
  
  // Actions
  setMetadata: (metadata: UIMetadata | null) => void;
  setTheme: (theme: ThemeConfiguration | null) => void;
  setData: (key: string, value: any) => void;
  updateData: (updates: Record<string, any>) => void;
  clearData: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: RuntimeError | null) => void;
  setUser: (user: UserInfo | null) => void;
  setPermissions: (permissions: string[]) => void;
  setPerformance: (metrics: PerformanceMetrics) => void;
  setConnected: (connected: boolean) => void;
  handleRealtimeUpdate: (update: RealtimeUpdate) => void;
  
  // Cache operations
  setCache: (key: string, data: any, ttl?: number) => void;
  getCache: (key: string) => any | null;
  clearCache: (key?: string) => void;
  
  // Utility actions
  reset: () => void;
  hydrate: (state: Partial<RuntimeState>) => void;
}

const initialState = {
  metadata: null,
  theme: null,
  data: {},
  loading: false,
  error: null,
  user: null,
  permissions: [],
  performance: null,
  connected: false,
  lastUpdate: null,
  cache: new Map(),
};

export const useRuntimeStore = create<RuntimeState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      setMetadata: (metadata) => {
        set((state) => {
          state.metadata = metadata;
          state.lastUpdate = new Date().toISOString();
        });
      },

      setTheme: (theme) => {
        set((state) => {
          state.theme = theme;
        });
      },

      setData: (key, value) => {
        set((state) => {
          state.data[key] = value;
        });
      },

      updateData: (updates) => {
        set((state) => {
          Object.assign(state.data, updates);
        });
      },

      clearData: () => {
        set((state) => {
          state.data = {};
        });
      },

      setLoading: (loading) => {
        set((state) => {
          state.loading = loading;
        });
      },

      setError: (error) => {
        set((state) => {
          state.error = error;
          if (error) {
            console.error('Runtime error:', error);
          }
        });
      },

      setUser: (user) => {
        set((state) => {
          state.user = user;
          // Update permissions when user changes
          if (user) {
            state.permissions = user.permissions || [];
          } else {
            state.permissions = [];
          }
        });
      },

      setPermissions: (permissions) => {
        set((state) => {
          state.permissions = permissions;
        });
      },

      setPerformance: (metrics) => {
        set((state) => {
          state.performance = metrics;
        });
      },

      setConnected: (connected) => {
        set((state) => {
          state.connected = connected;
        });
      },

      handleRealtimeUpdate: (update) => {
        set((state) => {
          state.lastUpdate = update.timestamp;
          
          switch (update.type) {
            case 'metadata':
              state.metadata = update.payload;
              break;
            case 'theme':
              state.theme = update.payload;
              break;
            case 'data':
              Object.assign(state.data, update.payload);
              break;
            case 'component':
              // Handle component-specific updates
              if (state.metadata && update.payload.componentId) {
                // Update specific component in metadata
                updateComponentInMetadata(state.metadata, update.payload);
              }
              break;
          }
        });
      },

      setCache: (key, data, ttl = 300000) => { // Default 5 minutes TTL
        set((state) => {
          state.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl,
          });
        });
      },

      getCache: (key) => {
        const cached = get().cache.get(key);
        if (!cached) return null;
        
        const now = Date.now();
        if (now - cached.timestamp > cached.ttl) {
          // Cache expired
          get().clearCache(key);
          return null;
        }
        
        return cached.data;
      },

      clearCache: (key) => {
        set((state) => {
          if (key) {
            state.cache.delete(key);
          } else {
            state.cache.clear();
          }
        });
      },

      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
          state.cache = new Map();
        });
      },

      hydrate: (newState) => {
        set((state) => {
          Object.assign(state, newState);
        });
      },
    }))
  )
);

// Helper function to update component in metadata
function updateComponentInMetadata(metadata: UIMetadata, update: any) {
  const { componentId, updates } = update;
  
  function updateComponent(components: any[]): boolean {
    for (const component of components) {
      if (component.id === componentId) {
        Object.assign(component, updates);
        return true;
      }
      if (component.children && updateComponent(component.children)) {
        return true;
      }
    }
    return false;
  }
  
  updateComponent(metadata.layout.components);
}

// Selectors for common state access patterns
export const useMetadata = () => useRuntimeStore((state) => state.metadata);
export const useTheme = () => useRuntimeStore((state) => state.theme);
export const useData = (key?: string) => 
  useRuntimeStore((state) => key ? state.data[key] : state.data);
export const useLoading = () => useRuntimeStore((state) => state.loading);
export const useError = () => useRuntimeStore((state) => state.error);
export const useUser = () => useRuntimeStore((state) => state.user);
export const usePermissions = () => useRuntimeStore((state) => state.permissions);
export const useConnected = () => useRuntimeStore((state) => state.connected);

// Computed selectors
export const useHasPermission = (permission: string) =>
  useRuntimeStore((state) => state.permissions.includes(permission));

export const useHasAnyPermission = (permissions: string[]) =>
  useRuntimeStore((state) => 
    permissions.some(permission => state.permissions.includes(permission))
  );

export const useIsAuthenticated = () =>
  useRuntimeStore((state) => state.user !== null);

// Performance tracking hook
export const usePerformanceMetrics = () => 
  useRuntimeStore((state) => state.performance);

// Cache hooks
export const useCachedData = (key: string) => {
  const getCache = useRuntimeStore((state) => state.getCache);
  return getCache(key);
};

// Subscribe to specific state changes
export const subscribeToMetadata = (callback: (metadata: UIMetadata | null) => void) =>
  useRuntimeStore.subscribe(
    (state) => state.metadata,
    callback
  );

export const subscribeToTheme = (callback: (theme: ThemeConfiguration | null) => void) =>
  useRuntimeStore.subscribe(
    (state) => state.theme,
    callback
  );

export const subscribeToData = (callback: (data: Record<string, any>) => void) =>
  useRuntimeStore.subscribe(
    (state) => state.data,
    callback
  );

export const subscribeToConnection = (callback: (connected: boolean) => void) =>
  useRuntimeStore.subscribe(
    (state) => state.connected,
    callback
  );

// Persistence helpers
export const persistState = () => {
  const state = useRuntimeStore.getState();
  const persistableState = {
    metadata: state.metadata,
    theme: state.theme,
    data: state.data,
    user: state.user,
    permissions: state.permissions,
  };
  
  try {
    localStorage.setItem('runtime-state', JSON.stringify(persistableState));
  } catch (error) {
    console.warn('Failed to persist state:', error);
  }
};

export const loadPersistedState = () => {
  try {
    const stored = localStorage.getItem('runtime-state');
    if (stored) {
      const state = JSON.parse(stored);
      useRuntimeStore.getState().hydrate(state);
    }
  } catch (error) {
    console.warn('Failed to load persisted state:', error);
  }
};

// Auto-persist state changes
useRuntimeStore.subscribe(
  (state) => ({
    metadata: state.metadata,
    theme: state.theme,
    data: state.data,
    user: state.user,
  }),
  () => {
    // Debounce persistence to avoid too frequent writes
    setTimeout(persistState, 1000);
  }
);
