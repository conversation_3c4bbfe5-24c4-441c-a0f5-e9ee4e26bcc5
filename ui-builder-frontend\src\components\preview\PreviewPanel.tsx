import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button, Space, Dropdown, Slider, Tooltip, Switch } from 'antd';
import {
  DesktopOutlined,
  TabletOutlined,
  MobileOutlined,
  RotateLeftOutlined,
  FullscreenOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@store/index';
import { setDevicePreview } from '@store/slices/uiBuilderSlice';

// Components
import ComponentRenderer from './ComponentRenderer';
import DeviceFrame from './DeviceFrame';

interface PreviewPanelProps {
  className?: string;
}

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
  type: 'desktop' | 'tablet' | 'mobile';
}

const devicePresets: DevicePreset[] = [
  // Desktop
  { name: 'Desktop Large', width: 1920, height: 1080, icon: <DesktopOutlined />, type: 'desktop' },
  { name: 'Desktop', width: 1440, height: 900, icon: <DesktopOutlined />, type: 'desktop' },
  { name: 'Laptop', width: 1366, height: 768, icon: <DesktopOutlined />, type: 'desktop' },
  
  // Tablet
  { name: 'iPad Pro', width: 1024, height: 1366, icon: <TabletOutlined />, type: 'tablet' },
  { name: 'iPad', width: 768, height: 1024, icon: <TabletOutlined />, type: 'tablet' },
  { name: 'Tablet', width: 800, height: 1280, icon: <TabletOutlined />, type: 'tablet' },
  
  // Mobile
  { name: 'iPhone 14 Pro', width: 393, height: 852, icon: <MobileOutlined />, type: 'mobile' },
  { name: 'iPhone 14', width: 390, height: 844, icon: <MobileOutlined />, type: 'mobile' },
  { name: 'Samsung Galaxy', width: 360, height: 800, icon: <MobileOutlined />, type: 'mobile' },
  { name: 'Mobile Small', width: 320, height: 568, icon: <MobileOutlined />, type: 'mobile' },
];

const PreviewPanel: React.FC<PreviewPanelProps> = ({ className = '' }) => {
  const dispatch = useAppDispatch();
  const previewRef = useRef<HTMLDivElement>(null);
  
  const [selectedDevice, setSelectedDevice] = useState<DevicePreset>(devicePresets[1]);
  const [isLandscape, setIsLandscape] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [showDeviceFrame, setShowDeviceFrame] = useState(true);
  const [showRulers, setShowRulers] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const { currentConfiguration, ui } = useAppSelector(state => state.uiBuilder);
  const { theme } = useAppSelector(state => state.theme);

  // Calculate preview dimensions
  const previewDimensions = {
    width: isLandscape ? selectedDevice.height : selectedDevice.width,
    height: isLandscape ? selectedDevice.width : selectedDevice.height,
  };

  // Handle device change
  const handleDeviceChange = useCallback((device: DevicePreset) => {
    setSelectedDevice(device);
    dispatch(setDevicePreview(device.type));
  }, [dispatch]);

  // Handle orientation toggle
  const handleOrientationToggle = useCallback(() => {
    setIsLandscape(!isLandscape);
  }, [isLandscape]);

  // Handle zoom change
  const handleZoomChange = useCallback((value: number) => {
    setZoom(value);
  }, []);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    if (!isFullscreen) {
      if (previewRef.current?.requestFullscreen) {
        previewRef.current.requestFullscreen();
        setIsFullscreen(true);
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  }, [isFullscreen]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    // Force re-render by updating a key
    window.location.reload();
  }, []);

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Device selector dropdown
  const deviceMenuItems = devicePresets.map(device => ({
    key: device.name,
    label: (
      <div className="flex items-center space-x-2">
        {device.icon}
        <span>{device.name}</span>
        <span className="text-xs text-gray-500">
          {device.width} × {device.height}
        </span>
      </div>
    ),
    onClick: () => handleDeviceChange(device),
  }));

  // Settings dropdown
  const settingsMenuItems = [
    {
      key: 'device-frame',
      label: (
        <div className="flex items-center justify-between">
          <span>Device Frame</span>
          <Switch
            size="small"
            checked={showDeviceFrame}
            onChange={setShowDeviceFrame}
          />
        </div>
      ),
    },
    {
      key: 'rulers',
      label: (
        <div className="flex items-center justify-between">
          <span>Rulers</span>
          <Switch
            size="small"
            checked={showRulers}
            onChange={setShowRulers}
          />
        </div>
      ),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'reset-zoom',
      label: 'Reset Zoom',
      onClick: () => setZoom(100),
    },
  ];

  if (!currentConfiguration) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 ${className}`}>
        <div className="text-center">
          <EyeOutlined className="text-4xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Configuration to Preview
          </h3>
          <p className="text-gray-600">
            Create or load a configuration to see the preview.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-gray-100 ${className}`} ref={previewRef}>
      {/* Preview toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        {/* Device controls */}
        <Space>
          <Dropdown menu={{ items: deviceMenuItems }} trigger={['click']}>
            <Button>
              {selectedDevice.icon}
              <span className="ml-2">{selectedDevice.name}</span>
            </Button>
          </Dropdown>

          <Tooltip title="Rotate device">
            <Button
              icon={<RotateLeftOutlined />}
              onClick={handleOrientationToggle}
            />
          </Tooltip>

          <div className="text-sm text-gray-500">
            {previewDimensions.width} × {previewDimensions.height}
          </div>
        </Space>

        {/* Zoom controls */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Zoom:</span>
            <div className="w-24">
              <Slider
                min={25}
                max={200}
                value={zoom}
                onChange={handleZoomChange}
                tooltip={{ formatter: (value) => `${value}%` }}
              />
            </div>
            <span className="text-sm text-gray-600 w-12">{zoom}%</span>
          </div>
        </div>

        {/* Action controls */}
        <Space>
          <Tooltip title="Refresh preview">
            <Button icon={<ReloadOutlined />} onClick={handleRefresh} />
          </Tooltip>

          <Tooltip title="Fullscreen">
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreenToggle}
            />
          </Tooltip>

          <Dropdown menu={{ items: settingsMenuItems }} trigger={['click']}>
            <Button icon={<SettingOutlined />} />
          </Dropdown>
        </Space>
      </div>

      {/* Preview area */}
      <div className="flex-1 overflow-auto p-8">
        <div className="flex items-center justify-center min-h-full">
          <div
            style={{
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'center center',
            }}
          >
            {showDeviceFrame ? (
              <DeviceFrame
                device={selectedDevice}
                isLandscape={isLandscape}
                showRulers={showRulers}
              >
                <ComponentRenderer
                  components={currentConfiguration.components}
                  theme={theme}
                  viewport={previewDimensions}
                  isPreview={true}
                />
              </DeviceFrame>
            ) : (
              <div
                style={{
                  width: previewDimensions.width,
                  height: previewDimensions.height,
                  backgroundColor: theme.colors.background.primary,
                  border: '1px solid #e5e7eb',
                  borderRadius: 8,
                  overflow: 'hidden',
                  position: 'relative',
                }}
              >
                {showRulers && (
                  <div className="absolute inset-0 pointer-events-none">
                    {/* Horizontal ruler */}
                    <div className="absolute top-0 left-0 right-0 h-4 bg-gray-200 border-b border-gray-300">
                      {Array.from({ length: Math.ceil(previewDimensions.width / 50) }, (_, i) => (
                        <div
                          key={i}
                          className="absolute top-0 bottom-0 border-l border-gray-400"
                          style={{ left: i * 50 }}
                        >
                          <span className="absolute top-0 left-1 text-xs text-gray-600">
                            {i * 50}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Vertical ruler */}
                    <div className="absolute top-0 left-0 bottom-0 w-4 bg-gray-200 border-r border-gray-300">
                      {Array.from({ length: Math.ceil(previewDimensions.height / 50) }, (_, i) => (
                        <div
                          key={i}
                          className="absolute left-0 right-0 border-t border-gray-400"
                          style={{ top: i * 50 }}
                        >
                          <span
                            className="absolute left-0 top-1 text-xs text-gray-600 transform -rotate-90 origin-left"
                            style={{ transformOrigin: '0 0' }}
                          >
                            {i * 50}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div
                  className="w-full h-full overflow-auto"
                  style={{
                    paddingTop: showRulers ? 16 : 0,
                    paddingLeft: showRulers ? 16 : 0,
                  }}
                >
                  <ComponentRenderer
                    components={currentConfiguration.components}
                    theme={theme}
                    viewport={previewDimensions}
                    isPreview={true}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Preview info */}
      <div className="p-4 bg-white border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div>
            Preview: {selectedDevice.name} {isLandscape ? '(Landscape)' : '(Portrait)'}
          </div>
          <div>
            Zoom: {zoom}% | Viewport: {previewDimensions.width} × {previewDimensions.height}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewPanel;
