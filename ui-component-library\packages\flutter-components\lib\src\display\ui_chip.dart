import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Chip component
class UIChip extends StatelessWidget {
  const UIChip({
    super.key,
    required this.label,
    this.avatar,
    this.deleteIcon,
    this.onDeleted,
    this.onPressed,
    this.selected = false,
    this.variant = UIColorVariant.neutral,
    this.size = UISize.md,
    this.enabled = true,
  });

  final String label;
  final Widget? avatar;
  final Widget? deleteIcon;
  final VoidCallback? onDeleted;
  final VoidCallback? onPressed;
  final bool selected;
  final UIColorVariant variant;
  final UISize size;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (onPressed != null) {
      return ActionChip(
        label: Text(label),
        avatar: avatar,
        onPressed: enabled ? onPressed : null,
        backgroundColor: variant.getColor(colorScheme).withOpacity(0.1),
        labelStyle: TextStyle(color: variant.getColor(colorScheme)),
      );
    }

    if (onDeleted != null) {
      return Chip(
        label: Text(label),
        avatar: avatar,
        deleteIcon: deleteIcon,
        onDeleted: enabled ? onDeleted : null,
        backgroundColor: variant.getColor(colorScheme).withOpacity(0.1),
        labelStyle: TextStyle(color: variant.getColor(colorScheme)),
      );
    }

    return Chip(
      label: Text(label),
      avatar: avatar,
      backgroundColor: selected 
        ? variant.getColor(colorScheme)
        : variant.getColor(colorScheme).withOpacity(0.1),
      labelStyle: TextStyle(
        color: selected 
          ? Colors.white
          : variant.getColor(colorScheme),
      ),
    );
  }
}
