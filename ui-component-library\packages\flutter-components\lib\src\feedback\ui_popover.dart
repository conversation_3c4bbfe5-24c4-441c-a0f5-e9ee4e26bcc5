import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Popover component
class UIPopover extends StatelessWidget {
  const UIPopover({
    super.key,
    required this.child,
    required this.content,
    this.onTap,
    this.width = 200,
    this.height,
  });

  final Widget child;
  final Widget content;
  final VoidCallback? onTap;
  final double width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
      onSelected: (_) {},
      itemBuilder: (context) => [
        PopupMenuItem(
          enabled: false,
          child: Container(
            width: width,
            height: height,
            child: content,
          ),
        ),
      ],
      child: GestureDetector(
        onTap: onTap,
        child: child,
      ),
    );
  }
}
