package com.uiplatform.repository;

import com.uiplatform.entity.UIConfiguration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for UIConfiguration entity.
 * Provides CRUD operations and custom queries for UI configuration management.
 */
@Repository
public interface UIConfigurationRepository extends JpaRepository<UIConfiguration, UUID>, JpaSpecificationExecutor<UIConfiguration> {

    /**
     * Find UI configuration by slug and organization.
     */
    Optional<UIConfiguration> findBySlugAndOrganizationIdAndDeletedFalse(String slug, UUID organizationId);

    /**
     * Find UI configurations by organization.
     */
    List<UIConfiguration> findByOrganizationIdAndDeletedFalse(UUID organizationId);

    /**
     * Find UI configurations by organization with pagination.
     */
    Page<UIConfiguration> findByOrganizationIdAndDeletedFalse(UUID organizationId, Pageable pageable);

    /**
     * Find UI configurations by owner.
     */
    List<UIConfiguration> findByOwnerIdAndDeletedFalse(UUID ownerId);

    /**
     * Find UI configurations by owner with pagination.
     */
    Page<UIConfiguration> findByOwnerIdAndDeletedFalse(UUID ownerId, Pageable pageable);

    /**
     * Find UI configurations by type.
     */
    List<UIConfiguration> findByTypeAndDeletedFalse(UIConfiguration.UIConfigurationType type);

    /**
     * Find UI configurations by status.
     */
    List<UIConfiguration> findByStatusAndDeletedFalse(UIConfiguration.UIConfigurationStatus status);

    /**
     * Find published UI configurations by organization.
     */
    List<UIConfiguration> findByOrganizationIdAndIsPublishedTrueAndDeletedFalse(UUID organizationId);

    /**
     * Find public UI configurations.
     */
    List<UIConfiguration> findByIsPublicTrueAndDeletedFalse();

    /**
     * Find template UI configurations.
     */
    List<UIConfiguration> findByIsTemplateTrueAndDeletedFalse();

    /**
     * Find UI configurations by theme.
     */
    List<UIConfiguration> findByThemeIdAndDeletedFalse(UUID themeId);

    /**
     * Find UI configurations by layout.
     */
    List<UIConfiguration> findByLayoutIdAndDeletedFalse(UUID layoutId);

    /**
     * Find child UI configurations.
     */
    List<UIConfiguration> findByParentIdAndDeletedFalse(UUID parentId);

    /**
     * Search UI configurations by name.
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organizationId = :organizationId AND " +
           "LOWER(u.name) LIKE LOWER(CONCAT('%', :name, '%')) AND u.deleted = false")
    Page<UIConfiguration> searchByName(@Param("organizationId") UUID organizationId, 
                                      @Param("name") String name, 
                                      Pageable pageable);

    /**
     * Search UI configurations by tags.
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organizationId = :organizationId AND " +
           "LOWER(u.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND u.deleted = false")
    Page<UIConfiguration> searchByTags(@Param("organizationId") UUID organizationId, 
                                      @Param("tag") String tag, 
                                      Pageable pageable);

    /**
     * Find UI configurations by multiple criteria.
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organizationId = :organizationId AND " +
           "(:type IS NULL OR u.type = :type) AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:ownerId IS NULL OR u.owner.id = :ownerId) AND " +
           "(:isPublished IS NULL OR u.isPublished = :isPublished) AND " +
           "u.deleted = false")
    Page<UIConfiguration> findByCriteria(@Param("organizationId") UUID organizationId,
                                        @Param("type") UIConfiguration.UIConfigurationType type,
                                        @Param("status") UIConfiguration.UIConfigurationStatus status,
                                        @Param("ownerId") UUID ownerId,
                                        @Param("isPublished") Boolean isPublished,
                                        Pageable pageable);

    /**
     * Count UI configurations by organization.
     */
    @Query("SELECT COUNT(u) FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.deleted = false")
    Long countByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Count published UI configurations by organization.
     */
    @Query("SELECT COUNT(u) FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.isPublished = true AND u.deleted = false")
    Long countPublishedByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Count UI configurations by type and organization.
     */
    @Query("SELECT COUNT(u) FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.type = :type AND u.deleted = false")
    Long countByTypeAndOrganizationId(@Param("type") UIConfiguration.UIConfigurationType type, 
                                     @Param("organizationId") UUID organizationId);

    /**
     * Check if slug exists in organization (excluding current configuration).
     */
    @Query("SELECT COUNT(u) > 0 FROM UIConfiguration u WHERE u.slug = :slug AND u.organization.id = :organizationId AND u.id != :excludeId AND u.deleted = false")
    boolean existsBySlugAndOrganizationIdAndIdNot(@Param("slug") String slug, 
                                                 @Param("organizationId") UUID organizationId, 
                                                 @Param("excludeId") UUID excludeId);

    /**
     * Find recently created UI configurations.
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.deleted = false ORDER BY u.createdAt DESC")
    List<UIConfiguration> findRecentByOrganizationId(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Find recently updated UI configurations.
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.deleted = false ORDER BY u.updatedAt DESC")
    List<UIConfiguration> findRecentlyUpdatedByOrganizationId(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Update publication status.
     */
    @Modifying
    @Query("UPDATE UIConfiguration u SET u.isPublished = :isPublished, u.status = :status WHERE u.id = :id")
    void updatePublicationStatus(@Param("id") UUID id, 
                                @Param("isPublished") Boolean isPublished, 
                                @Param("status") UIConfiguration.UIConfigurationStatus status);

    /**
     * Increment version number.
     */
    @Modifying
    @Query("UPDATE UIConfiguration u SET u.versionNumber = u.versionNumber + 1 WHERE u.id = :id")
    void incrementVersionNumber(@Param("id") UUID id);

    /**
     * Find UI configurations with component count.
     */
    @Query("SELECT u, COUNT(c) as componentCount FROM UIConfiguration u LEFT JOIN u.components c " +
           "WHERE u.organization.id = :organizationId AND u.deleted = false GROUP BY u")
    List<Object[]> findWithComponentCount(@Param("organizationId") UUID organizationId);

    /**
     * Find most popular UI configurations (by view count or usage).
     */
    @Query("SELECT u FROM UIConfiguration u WHERE u.organization.id = :organizationId AND u.isPublished = true AND u.deleted = false ORDER BY u.createdAt DESC")
    List<UIConfiguration> findPopularByOrganizationId(@Param("organizationId") UUID organizationId, Pageable pageable);
}
