import 'dart:io' show Platform;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// Platform-specific utilities for UI Builder components
class PlatformUtils {
  const PlatformUtils._();

  /// Check if running on Android
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  /// Check if running on iOS
  static bool get isIOS => !kIsWeb && Platform.isIOS;

  /// Check if running on macOS
  static bool get isMacOS => !kIsWeb && Platform.isMacOS;

  /// Check if running on Windows
  static bool get isWindows => !kIsWeb && Platform.isWindows;

  /// Check if running on Linux
  static bool get isLinux => !kIsWeb && Platform.isLinux;

  /// Check if running on Fuchsia
  static bool get isFuchsia => !kIsWeb && Platform.isFuchsia;

  /// Check if running on Web
  static bool get isWeb => kIsWeb;

  /// Check if running on mobile platform (Android or iOS)
  static bool get isMobile => isAndroid || isIOS;

  /// Check if running on desktop platform (Windows, macOS, or Linux)
  static bool get isDesktop => isWindows || isMacOS || isLinux;

  /// Check if platform supports Material Design
  static bool get supportsMaterial => isAndroid || isWeb || isDesktop;

  /// Check if platform supports Cupertino Design
  static bool get supportsCupertino => isIOS || isMacOS;

  /// Get platform name as string
  static String get platformName {
    if (isWeb) return 'Web';
    if (isAndroid) return 'Android';
    if (isIOS) return 'iOS';
    if (isMacOS) return 'macOS';
    if (isWindows) return 'Windows';
    if (isLinux) return 'Linux';
    if (isFuchsia) return 'Fuchsia';
    return 'Unknown';
  }

  /// Get appropriate theme brightness for platform
  static Brightness getPlatformBrightness(BuildContext context) {
    return MediaQuery.of(context).platformBrightness;
  }

  /// Check if platform prefers dark mode
  static bool prefersDarkMode(BuildContext context) {
    return getPlatformBrightness(context) == Brightness.dark;
  }

  /// Get platform-appropriate button style
  static ButtonStyle getPlatformButtonStyle(BuildContext context) {
    if (supportsCupertino && !isWeb) {
      return CupertinoButton.filled(
        onPressed: () {},
        child: const SizedBox(),
      ).runtimeType as ButtonStyle;
    }
    return ElevatedButton.styleFrom();
  }

  /// Get platform-appropriate text style
  static TextStyle getPlatformTextStyle(BuildContext context) {
    if (supportsCupertino && !isWeb) {
      return CupertinoTheme.of(context).textTheme.textStyle;
    }
    return Theme.of(context).textTheme.bodyMedium ?? const TextStyle();
  }

  /// Get platform-appropriate icon size
  static double getPlatformIconSize() {
    if (supportsCupertino && !isWeb) {
      return 28.0; // iOS standard
    }
    return 24.0; // Material standard
  }

  /// Get platform-appropriate border radius
  static BorderRadius getPlatformBorderRadius() {
    if (supportsCupertino && !isWeb) {
      return BorderRadius.circular(8.0); // iOS style
    }
    return BorderRadius.circular(4.0); // Material style
  }

  /// Get platform-appropriate padding
  static EdgeInsets getPlatformPadding() {
    if (supportsCupertino && !isWeb) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0);
    }
    return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
  }

  /// Get platform-appropriate margin
  static EdgeInsets getPlatformMargin() {
    if (supportsCupertino && !isWeb) {
      return const EdgeInsets.all(8.0);
    }
    return const EdgeInsets.all(4.0);
  }

  /// Get platform-appropriate elevation
  static double getPlatformElevation() {
    if (supportsCupertino && !isWeb) {
      return 0.0; // iOS doesn't use elevation
    }
    return 2.0; // Material elevation
  }

  /// Get platform-appropriate animation duration
  static Duration getPlatformAnimationDuration() {
    if (supportsCupertino && !isWeb) {
      return const Duration(milliseconds: 350); // iOS standard
    }
    return const Duration(milliseconds: 200); // Material standard
  }

  /// Get platform-appropriate animation curve
  static Curve getPlatformAnimationCurve() {
    if (supportsCupertino && !isWeb) {
      return Curves.easeInOut; // iOS style
    }
    return Curves.easeOut; // Material style
  }

  /// Show platform-appropriate dialog
  static Future<T?> showPlatformDialog<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) {
    if (supportsCupertino && !isWeb) {
      return showCupertinoDialog<T>(
        context: context,
        builder: builder,
        barrierDismissible: barrierDismissible,
      );
    }
    return showDialog<T>(
      context: context,
      builder: builder,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
    );
  }

  /// Show platform-appropriate modal bottom sheet
  static Future<T?> showPlatformModalBottomSheet<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    if (supportsCupertino && !isWeb) {
      return showCupertinoModalPopup<T>(
        context: context,
        builder: builder,
        useRootNavigator: useRootNavigator,
      );
    }
    return showModalBottomSheet<T>(
      context: context,
      builder: builder,
      isScrollControlled: isScrollControlled,
      useRootNavigator: useRootNavigator,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
    );
  }

  /// Get platform-appropriate loading indicator
  static Widget getPlatformLoadingIndicator({
    double? value,
    Color? backgroundColor,
    Color? valueColor,
    double strokeWidth = 4.0,
  }) {
    if (supportsCupertino && !isWeb) {
      return CupertinoActivityIndicator(
        color: valueColor,
      );
    }
    return CircularProgressIndicator(
      value: value,
      backgroundColor: backgroundColor,
      valueColor: valueColor != null ? AlwaysStoppedAnimation(valueColor) : null,
      strokeWidth: strokeWidth,
    );
  }

  /// Get platform-appropriate switch widget
  static Widget getPlatformSwitch({
    required bool value,
    required ValueChanged<bool>? onChanged,
    Color? activeColor,
    Color? inactiveColor,
  }) {
    if (supportsCupertino && !isWeb) {
      return CupertinoSwitch(
        value: value,
        onChanged: onChanged,
        activeColor: activeColor,
      );
    }
    return Switch(
      value: value,
      onChanged: onChanged,
      activeColor: activeColor,
      inactiveTrackColor: inactiveColor,
    );
  }

  /// Get platform-appropriate slider widget
  static Widget getPlatformSlider({
    required double value,
    required ValueChanged<double>? onChanged,
    double min = 0.0,
    double max = 1.0,
    int? divisions,
    Color? activeColor,
    Color? inactiveColor,
  }) {
    if (supportsCupertino && !isWeb) {
      return CupertinoSlider(
        value: value,
        onChanged: onChanged,
        min: min,
        max: max,
        divisions: divisions,
        activeColor: activeColor,
      );
    }
    return Slider(
      value: value,
      onChanged: onChanged,
      min: min,
      max: max,
      divisions: divisions,
      activeColor: activeColor,
      inactiveColor: inactiveColor,
    );
  }

  /// Get platform-appropriate navigation bar
  static PreferredSizeWidget getPlatformAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    if (supportsCupertino && !isWeb) {
      return CupertinoNavigationBar(
        middle: Text(title),
        trailing: actions != null && actions.isNotEmpty 
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: actions,
            )
          : null,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor,
      ) as PreferredSizeWidget;
    }
    return AppBar(
      title: Text(title),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Get platform-appropriate haptic feedback
  static void providePlatformFeedback() {
    if (isMobile) {
      // Provide haptic feedback on mobile platforms
      // This would typically use HapticFeedback.lightImpact()
      // but we'll keep it simple for now
    }
  }

  /// Check if platform supports haptic feedback
  static bool get supportsHapticFeedback => isMobile;

  /// Check if platform supports biometric authentication
  static bool get supportsBiometrics => isMobile;

  /// Get platform-appropriate safe area
  static Widget getPlatformSafeArea({
    required Widget child,
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
  }) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: child,
    );
  }
}
