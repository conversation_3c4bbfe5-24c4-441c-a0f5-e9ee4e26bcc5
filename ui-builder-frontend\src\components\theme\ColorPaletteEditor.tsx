import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Popover, Input, message } from 'antd';
import { PlusOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons';
import { SketchPicker, ColorResult } from 'react-color';
import { ColorPalette, ColorScale } from '@types/index';

interface ColorPaletteEditorProps {
  colors: ColorPalette;
  onChange: (colors: Partial<ColorPalette>) => void;
}

const ColorPaletteEditor: React.FC<ColorPaletteEditorProps> = ({
  colors,
  onChange,
}) => {
  const [activeColorPicker, setActiveColorPicker] = useState<string | null>(null);

  // Handle color change
  const handleColorChange = useCallback((
    category: keyof ColorPalette,
    shade: string | keyof ColorScale,
    color: string
  ) => {
    if (category === 'background' || category === 'text') {
      onChange({
        [category]: {
          ...colors[category],
          [shade]: color,
        },
      });
    } else {
      onChange({
        [category]: {
          ...colors[category as keyof Omit<ColorPalette, 'background' | 'text'>],
          [shade]: color,
        },
      });
    }
  }, [colors, onChange]);

  // Generate color scale from base color
  const generateColorScale = useCallback((baseColor: string): ColorScale => {
    // This is a simplified color scale generation
    // In a real implementation, you'd use a proper color manipulation library
    const hsl = hexToHsl(baseColor);
    
    return {
      50: hslToHex(hsl.h, hsl.s, Math.min(95, hsl.l + 40)),
      100: hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 30)),
      200: hslToHex(hsl.h, hsl.s, Math.min(80, hsl.l + 20)),
      300: hslToHex(hsl.h, hsl.s, Math.min(70, hsl.l + 10)),
      400: hslToHex(hsl.h, hsl.s, Math.min(60, hsl.l + 5)),
      500: baseColor,
      600: hslToHex(hsl.h, hsl.s, Math.max(40, hsl.l - 5)),
      700: hslToHex(hsl.h, hsl.s, Math.max(30, hsl.l - 10)),
      800: hslToHex(hsl.h, hsl.s, Math.max(20, hsl.l - 20)),
      900: hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 30)),
    };
  }, []);

  // Handle base color change (generates entire scale)
  const handleBaseColorChange = useCallback((
    category: keyof Omit<ColorPalette, 'background' | 'text'>,
    color: string
  ) => {
    const newScale = generateColorScale(color);
    onChange({
      [category]: newScale,
    });
  }, [generateColorScale, onChange]);

  // Copy color to clipboard
  const handleCopyColor = useCallback((color: string) => {
    navigator.clipboard.writeText(color);
    message.success('Color copied to clipboard');
  }, []);

  // Color picker component
  const ColorPicker: React.FC<{
    color: string;
    onChange: (color: string) => void;
    label: string;
  }> = ({ color, onChange, label }) => {
    const [visible, setVisible] = useState(false);

    return (
      <Popover
        content={
          <SketchPicker
            color={color}
            onChange={(result: ColorResult) => onChange(result.hex)}
            disableAlpha
          />
        }
        trigger="click"
        open={visible}
        onOpenChange={setVisible}
      >
        <div className="flex items-center space-x-2 cursor-pointer">
          <div
            className="w-8 h-8 rounded border border-gray-300 shadow-sm"
            style={{ backgroundColor: color }}
          />
          <div className="flex-1">
            <div className="text-sm font-medium">{label}</div>
            <div className="text-xs text-gray-500 font-mono">{color}</div>
          </div>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleCopyColor(color);
            }}
          />
        </div>
      </Popover>
    );
  };

  // Color scale component
  const ColorScaleEditor: React.FC<{
    title: string;
    scale: ColorScale;
    onChange: (shade: keyof ColorScale, color: string) => void;
    onBaseChange?: (color: string) => void;
  }> = ({ title, scale, onChange, onBaseChange }) => (
    <Card title={title} size="small" className="mb-4">
      {onBaseChange && (
        <div className="mb-4 p-3 bg-gray-50 rounded">
          <div className="text-sm font-medium mb-2">Base Color (500)</div>
          <ColorPicker
            color={scale[500]}
            onChange={onBaseChange}
            label="Generate scale from this color"
          />
        </div>
      )}
      
      <div className="grid grid-cols-2 gap-3">
        {Object.entries(scale).map(([shade, color]) => (
          <ColorPicker
            key={shade}
            color={color}
            onChange={(newColor) => onChange(shade as keyof ColorScale, newColor)}
            label={shade}
          />
        ))}
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Primary colors */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Primary Colors</h4>
        
        <ColorScaleEditor
          title="Primary"
          scale={colors.primary}
          onChange={(shade, color) => handleColorChange('primary', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('primary', color)}
        />

        <ColorScaleEditor
          title="Secondary"
          scale={colors.secondary}
          onChange={(shade, color) => handleColorChange('secondary', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('secondary', color)}
        />
      </div>

      {/* Semantic colors */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Semantic Colors</h4>
        
        <ColorScaleEditor
          title="Success"
          scale={colors.success}
          onChange={(shade, color) => handleColorChange('success', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('success', color)}
        />

        <ColorScaleEditor
          title="Warning"
          scale={colors.warning}
          onChange={(shade, color) => handleColorChange('warning', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('warning', color)}
        />

        <ColorScaleEditor
          title="Error"
          scale={colors.error}
          onChange={(shade, color) => handleColorChange('error', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('error', color)}
        />

        <ColorScaleEditor
          title="Info"
          scale={colors.info}
          onChange={(shade, color) => handleColorChange('info', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('info', color)}
        />
      </div>

      {/* Neutral colors */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Neutral Colors</h4>
        
        <ColorScaleEditor
          title="Neutral"
          scale={colors.neutral}
          onChange={(shade, color) => handleColorChange('neutral', shade, color)}
          onBaseChange={(color) => handleBaseColorChange('neutral', color)}
        />
      </div>

      {/* Background colors */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Background Colors</h4>
        
        <Card title="Background" size="small" className="mb-4">
          <div className="space-y-3">
            <ColorPicker
              color={colors.background.primary}
              onChange={(color) => handleColorChange('background', 'primary', color)}
              label="Primary Background"
            />
            <ColorPicker
              color={colors.background.secondary}
              onChange={(color) => handleColorChange('background', 'secondary', color)}
              label="Secondary Background"
            />
            <ColorPicker
              color={colors.background.tertiary}
              onChange={(color) => handleColorChange('background', 'tertiary', color)}
              label="Tertiary Background"
            />
          </div>
        </Card>
      </div>

      {/* Text colors */}
      <div>
        <h4 className="text-lg font-semibold mb-4">Text Colors</h4>
        
        <Card title="Text" size="small">
          <div className="space-y-3">
            <ColorPicker
              color={colors.text.primary}
              onChange={(color) => handleColorChange('text', 'primary', color)}
              label="Primary Text"
            />
            <ColorPicker
              color={colors.text.secondary}
              onChange={(color) => handleColorChange('text', 'secondary', color)}
              label="Secondary Text"
            />
            <ColorPicker
              color={colors.text.disabled}
              onChange={(color) => handleColorChange('text', 'disabled', color)}
              label="Disabled Text"
            />
            <ColorPicker
              color={colors.text.inverse}
              onChange={(color) => handleColorChange('text', 'inverse', color)}
              label="Inverse Text"
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

// Helper functions for color manipulation
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

function hslToHex(h: number, s: number, l: number): string {
  h /= 360;
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;
  let r = 0;
  let g = 0;
  let b = 0;

  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0;
  } else if (1/6 <= h && h < 1/3) {
    r = x; g = c; b = 0;
  } else if (1/3 <= h && h < 1/2) {
    r = 0; g = c; b = x;
  } else if (1/2 <= h && h < 2/3) {
    r = 0; g = x; b = c;
  } else if (2/3 <= h && h < 5/6) {
    r = x; g = 0; b = c;
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

export default ColorPaletteEditor;
