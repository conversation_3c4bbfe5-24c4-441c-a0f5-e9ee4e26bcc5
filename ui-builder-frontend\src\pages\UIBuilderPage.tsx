import React, { useEffect, useState, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout, Button, Space, Dropdown, message, Modal } from 'antd';
import {
  SaveOutlined,
  EyeOutlined,
  ShareAltOutlined,
  MoreOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  GridOutlined,
  ExpandOutlined,
  CompressOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@store/index';
import {
  setCurrentConfiguration,
  clearCurrentConfiguration,
  togglePreviewMode,
  toggleGrid,
  setCanvasZoom,
  undo,
  redo,
  setSaving,
} from '@store/slices/uiBuilderSlice';

// API hooks
import { 
  useGetUIConfigurationQuery,
  useUpdateUIConfigurationMutation,
  useCreateUIConfigurationMutation,
} from '@store/api/uiConfigurationApi';

// Components
import Canvas from '@components/canvas/Canvas';
import ComponentPalette from '@components/palette/ComponentPalette';
import PropertyPanel from '@components/properties/PropertyPanel';
import LayerPanel from '@components/layers/LayerPanel';
import ToolbarPanel from '@components/toolbar/ToolbarPanel';
import PreviewPanel from '@components/preview/PreviewPanel';
import CollaborationPanel from '@components/collaboration/CollaborationPanel';

// Hooks
import { useKeyboardShortcuts } from '@hooks/useKeyboardShortcuts';
import { useAutoSave } from '@hooks/useAutoSave';
import { useCollaboration } from '@hooks/useCollaboration';

const { Header, Sider, Content } = Layout;

const UIBuilderPage: React.FC = () => {
  const { configId } = useParams<{ configId?: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [leftSiderCollapsed, setLeftSiderCollapsed] = useState(false);
  const [rightSiderCollapsed, setRightSiderCollapsed] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);

  const {
    currentConfiguration,
    canvas,
    history,
    ui,
    loading,
    error,
  } = useAppSelector(state => state.uiBuilder);

  const { user } = useAppSelector(state => state.auth);

  // API hooks
  const {
    data: configData,
    isLoading: isLoadingConfig,
    error: loadError,
  } = useGetUIConfigurationQuery(configId!, {
    skip: !configId,
  });

  const [updateConfiguration] = useUpdateUIConfigurationMutation();
  const [createConfiguration] = useCreateUIConfigurationMutation();

  // Custom hooks
  useKeyboardShortcuts();
  useAutoSave(currentConfiguration, updateConfiguration);
  useCollaboration(configId);

  // Load configuration on mount
  useEffect(() => {
    if (configId && configData?.data) {
      dispatch(setCurrentConfiguration(configData.data));
    } else if (!configId) {
      // Create new configuration
      const newConfig = createNewConfiguration();
      dispatch(setCurrentConfiguration(newConfig));
    }

    return () => {
      dispatch(clearCurrentConfiguration());
    };
  }, [configId, configData, dispatch]);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!currentConfiguration) return;

    try {
      dispatch(setSaving(true));

      if (configId) {
        // Update existing configuration
        await updateConfiguration({
          id: configId,
          data: {
            components: currentConfiguration.components,
            theme: currentConfiguration.theme,
            layout: currentConfiguration.layout,
            settings: currentConfiguration.settings,
          },
        }).unwrap();
      } else {
        // Create new configuration
        const result = await createConfiguration({
          name: currentConfiguration.name,
          description: currentConfiguration.description,
          organizationId: user!.organizationId,
        }).unwrap();

        // Navigate to the new configuration
        navigate(`/builder/${result.data!.id}`, { replace: true });
      }

      message.success('Configuration saved successfully');
    } catch (error) {
      console.error('Save error:', error);
      message.error('Failed to save configuration');
    } finally {
      dispatch(setSaving(false));
    }
  }, [currentConfiguration, configId, updateConfiguration, createConfiguration, user, navigate, dispatch]);

  // Handle keyboard save
  useEffect(() => {
    const handleKeyboardSave = () => {
      handleSave();
    };

    window.addEventListener('keyboard-save', handleKeyboardSave);
    return () => window.removeEventListener('keyboard-save', handleKeyboardSave);
  }, [handleSave]);

  // Handle preview
  const handlePreview = useCallback(() => {
    if (ui.previewMode) {
      dispatch(togglePreviewMode());
    } else {
      setPreviewModalVisible(true);
    }
  }, [ui.previewMode, dispatch]);

  // Handle zoom
  const handleZoomIn = useCallback(() => {
    dispatch(setCanvasZoom(Math.min(canvas.zoom * 1.2, 5)));
  }, [canvas.zoom, dispatch]);

  const handleZoomOut = useCallback(() => {
    dispatch(setCanvasZoom(Math.max(canvas.zoom * 0.8, 0.1)));
  }, [canvas.zoom, dispatch]);

  const handleZoomReset = useCallback(() => {
    dispatch(setCanvasZoom(1));
  }, [dispatch]);

  // More actions menu
  const moreActionsMenu = {
    items: [
      {
        key: 'export',
        label: 'Export Configuration',
        icon: <ShareAltOutlined />,
      },
      {
        key: 'duplicate',
        label: 'Duplicate Configuration',
      },
      {
        key: 'settings',
        label: 'Configuration Settings',
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'help',
        label: 'Help & Shortcuts',
      },
    ],
  };

  if (isLoadingConfig) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="text-lg font-medium">Loading configuration...</div>
        </div>
      </div>
    );
  }

  if (loadError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-red-600 mb-2">
            Failed to load configuration
          </div>
          <Button onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Layout className="h-screen">
      {/* Header */}
      <Header className="bg-white border-b border-gray-200 px-4 flex items-center justify-between">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          <div className="text-lg font-semibold">
            {currentConfiguration?.name || 'Untitled Configuration'}
          </div>
          
          {loading.saving && (
            <div className="text-sm text-blue-600">Saving...</div>
          )}
        </div>

        {/* Center section - Toolbar */}
        <div className="flex items-center space-x-2">
          <Button
            icon={<UndoOutlined />}
            disabled={history.past.length === 0}
            onClick={() => dispatch(undo())}
            title="Undo (Ctrl+Z)"
          />
          <Button
            icon={<RedoOutlined />}
            disabled={history.future.length === 0}
            onClick={() => dispatch(redo())}
            title="Redo (Ctrl+Y)"
          />
          
          <div className="w-px h-6 bg-gray-300 mx-2" />
          
          <Button
            icon={<ZoomOutOutlined />}
            onClick={handleZoomOut}
            title="Zoom Out"
          />
          <Button
            onClick={handleZoomReset}
            className="min-w-[60px]"
            title="Reset Zoom (Ctrl+0)"
          >
            {Math.round(canvas.zoom * 100)}%
          </Button>
          <Button
            icon={<ZoomInOutlined />}
            onClick={handleZoomIn}
            title="Zoom In"
          />
          
          <div className="w-px h-6 bg-gray-300 mx-2" />
          
          <Button
            icon={<GridOutlined />}
            type={canvas.showGrid ? 'primary' : 'default'}
            onClick={() => dispatch(toggleGrid())}
            title="Toggle Grid (Ctrl+G)"
          />
        </div>

        {/* Right section */}
        <Space>
          <Button
            icon={<EyeOutlined />}
            onClick={handlePreview}
            title="Preview (Ctrl+Shift+P)"
          >
            Preview
          </Button>
          
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={loading.saving}
            title="Save (Ctrl+S)"
          >
            Save
          </Button>

          <Dropdown menu={moreActionsMenu} trigger={['click']}>
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      </Header>

      <Layout>
        {/* Left Sidebar */}
        <Sider
          width={ui.leftPanelWidth}
          collapsed={leftSiderCollapsed}
          onCollapse={setLeftSiderCollapsed}
          collapsible
          theme="light"
          className="border-r border-gray-200"
        >
          <div className="h-full flex flex-col">
            <ToolbarPanel />
            <div className="flex-1 overflow-hidden">
              {ui.activeLeftTab === 'components' && <ComponentPalette />}
              {ui.activeLeftTab === 'layers' && <LayerPanel />}
              {ui.activeLeftTab === 'collaboration' && <CollaborationPanel />}
            </div>
          </div>
        </Sider>

        {/* Main Content */}
        <Content className="flex-1 flex flex-col overflow-hidden">
          {ui.previewMode ? (
            <PreviewPanel />
          ) : (
            <Canvas />
          )}
        </Content>

        {/* Right Sidebar */}
        <Sider
          width={ui.rightPanelWidth}
          collapsed={rightSiderCollapsed}
          onCollapse={setRightSiderCollapsed}
          collapsible
          theme="light"
          className="border-l border-gray-200"
          reverseArrow
        >
          <PropertyPanel />
        </Sider>
      </Layout>

      {/* Preview Modal */}
      <Modal
        title="Preview"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        <PreviewPanel />
      </Modal>
    </Layout>
  );
};

// Helper function to create new configuration
function createNewConfiguration() {
  return {
    id: 'new',
    name: 'Untitled Configuration',
    description: '',
    version: '1.0.0',
    organizationId: '',
    createdBy: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isPublished: false,
    components: [],
    theme: {
      id: 'default',
      name: 'Default Theme',
      colors: {},
      typography: {},
      spacing: {},
      shadows: {},
      borders: {},
      animations: {},
    },
    layout: {
      type: 'fluid' as const,
    },
    settings: {
      enableGrid: true,
      snapToGrid: true,
      gridSize: 20,
      showRulers: true,
      showGuides: true,
      enableCollaboration: true,
      autoSave: true,
      autoSaveInterval: 30000,
    },
  };
}

export default UIBuilderPage;
