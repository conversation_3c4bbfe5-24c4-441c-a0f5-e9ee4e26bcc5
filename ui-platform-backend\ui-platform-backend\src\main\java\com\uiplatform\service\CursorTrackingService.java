package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import com.uiplatform.dto.collaboration.CursorPosition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Service for tracking and managing live cursor positions in real-time collaboration.
 */
@Service
public class CursorTrackingService {

    private static final Logger logger = LoggerFactory.getLogger(CursorTrackingService.class);
    
    private static final String CURSOR_POSITION_KEY = "cursor:position:";
    private static final String CURSOR_CONFIG_KEY = "cursor:config:";
    private static final String CURSOR_ELEMENT_KEY = "cursor:element:";
    private static final int CURSOR_TTL_SECONDS = 30;
    private static final int CURSOR_CLEANUP_INTERVAL_MS = 10000; // 10 seconds

    private final RedisTemplate<String, Object> redisTemplate;
    private final SimpMessagingTemplate messagingTemplate;
    private final PresenceService presenceService;
    private final ObjectMapper objectMapper;

    @Autowired
    public CursorTrackingService(RedisTemplate<String, Object> redisTemplate,
                                SimpMessagingTemplate messagingTemplate,
                                PresenceService presenceService,
                                ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.messagingTemplate = messagingTemplate;
        this.presenceService = presenceService;
        this.objectMapper = objectMapper;
    }

    /**
     * Update cursor position for a user.
     */
    public void updateCursorPosition(UUID userId, String username, UUID configId, 
                                   String elementId, double x, double y, 
                                   String selectionStart, String selectionEnd) {
        try {
            CursorPosition position = new CursorPosition();
            position.setUserId(userId);
            position.setUsername(username);
            position.setConfigId(configId);
            position.setElementId(elementId);
            position.setX(x);
            position.setY(y);
            position.setSelectionStart(selectionStart);
            position.setSelectionEnd(selectionEnd);
            position.setTimestamp(LocalDateTime.now());
            
            // Assign user color for cursor
            String userColor = presenceService.assignUserColor(userId);
            position.setCursorColor(userColor);
            position.setUserInitials(getUserInitials(username));
            
            // Store cursor position in Redis
            String positionKey = CURSOR_POSITION_KEY + configId + ":" + userId;
            redisTemplate.opsForValue().set(positionKey, position, CURSOR_TTL_SECONDS, TimeUnit.SECONDS);
            
            // Add to config cursor set
            String configKey = CURSOR_CONFIG_KEY + configId;
            redisTemplate.opsForSet().add(configKey, userId.toString());
            redisTemplate.expire(configKey, CURSOR_TTL_SECONDS, TimeUnit.SECONDS);
            
            // Add to element cursor set if element specified
            if (elementId != null && !elementId.isEmpty()) {
                String elementKey = CURSOR_ELEMENT_KEY + configId + ":" + elementId;
                redisTemplate.opsForSet().add(elementKey, userId.toString());
                redisTemplate.expire(elementKey, CURSOR_TTL_SECONDS, TimeUnit.SECONDS);
            }
            
            // Update user presence with current editing location
            presenceService.updateUserPresence(userId, username, null, "active", configId, elementId);
            
            // Broadcast cursor position to other users
            broadcastCursorPosition(configId, position, userId);
            
            logger.debug("Updated cursor position for user {} in config {} at ({}, {})", 
                        username, configId, x, y);
            
        } catch (Exception e) {
            logger.error("Error updating cursor position", e);
        }
    }

    /**
     * Remove cursor position for a user.
     */
    public void removeCursorPosition(UUID userId, UUID configId) {
        try {
            // Remove cursor position
            String positionKey = CURSOR_POSITION_KEY + configId + ":" + userId;
            redisTemplate.delete(positionKey);
            
            // Remove from config cursor set
            String configKey = CURSOR_CONFIG_KEY + configId;
            redisTemplate.opsForSet().remove(configKey, userId.toString());
            
            // Broadcast cursor removal
            broadcastCursorRemoval(configId, userId);
            
            logger.debug("Removed cursor position for user {} in config {}", userId, configId);
            
        } catch (Exception e) {
            logger.error("Error removing cursor position", e);
        }
    }

    /**
     * Get all cursor positions for a UI configuration.
     */
    public List<CursorPosition> getCursorPositions(UUID configId) {
        try {
            String configKey = CURSOR_CONFIG_KEY + configId;
            Set<Object> userIds = redisTemplate.opsForSet().members(configKey);
            
            List<CursorPosition> positions = new ArrayList<>();
            
            if (userIds != null) {
                for (Object userIdObj : userIds) {
                    UUID userId = UUID.fromString(userIdObj.toString());
                    CursorPosition position = getCursorPosition(userId, configId);
                    if (position != null) {
                        positions.add(position);
                    }
                }
            }
            
            return positions;
            
        } catch (Exception e) {
            logger.error("Error getting cursor positions", e);
            return Collections.emptyList();
        }
    }

    /**
     * Get cursor position for a specific user in a configuration.
     */
    public CursorPosition getCursorPosition(UUID userId, UUID configId) {
        try {
            String positionKey = CURSOR_POSITION_KEY + configId + ":" + userId;
            Object position = redisTemplate.opsForValue().get(positionKey);
            
            if (position instanceof CursorPosition) {
                return (CursorPosition) position;
            }
            
            return null;
            
        } catch (Exception e) {
            logger.error("Error getting cursor position", e);
            return null;
        }
    }

    /**
     * Get cursor positions for a specific element.
     */
    public List<CursorPosition> getCursorPositionsForElement(UUID configId, String elementId) {
        try {
            String elementKey = CURSOR_ELEMENT_KEY + configId + ":" + elementId;
            Set<Object> userIds = redisTemplate.opsForSet().members(elementKey);
            
            List<CursorPosition> positions = new ArrayList<>();
            
            if (userIds != null) {
                for (Object userIdObj : userIds) {
                    UUID userId = UUID.fromString(userIdObj.toString());
                    CursorPosition position = getCursorPosition(userId, configId);
                    if (position != null && elementId.equals(position.getElementId())) {
                        positions.add(position);
                    }
                }
            }
            
            return positions;
            
        } catch (Exception e) {
            logger.error("Error getting cursor positions for element", e);
            return Collections.emptyList();
        }
    }

    /**
     * Update text selection for a user.
     */
    public void updateTextSelection(UUID userId, String username, UUID configId, 
                                  String elementId, String selectedText, 
                                  String selectionStart, String selectionEnd) {
        try {
            CursorPosition existingPosition = getCursorPosition(userId, configId);
            
            if (existingPosition != null) {
                // Update existing position with selection info
                existingPosition.setSelectedText(selectedText);
                existingPosition.setSelectionStart(selectionStart);
                existingPosition.setSelectionEnd(selectionEnd);
                existingPosition.setTimestamp(LocalDateTime.now());
                
                // Store updated position
                String positionKey = CURSOR_POSITION_KEY + configId + ":" + userId;
                redisTemplate.opsForValue().set(positionKey, existingPosition, CURSOR_TTL_SECONDS, TimeUnit.SECONDS);
                
                // Broadcast selection change
                broadcastSelectionChange(configId, existingPosition, userId);
            } else {
                // Create new cursor position with selection
                updateCursorPosition(userId, username, configId, elementId, 0, 0, selectionStart, selectionEnd);
            }
            
        } catch (Exception e) {
            logger.error("Error updating text selection", e);
        }
    }

    /**
     * Clear text selection for a user.
     */
    public void clearTextSelection(UUID userId, UUID configId) {
        try {
            CursorPosition existingPosition = getCursorPosition(userId, configId);
            
            if (existingPosition != null) {
                existingPosition.setSelectedText(null);
                existingPosition.setSelectionStart(null);
                existingPosition.setSelectionEnd(null);
                existingPosition.setTimestamp(LocalDateTime.now());
                
                // Store updated position
                String positionKey = CURSOR_POSITION_KEY + configId + ":" + userId;
                redisTemplate.opsForValue().set(positionKey, existingPosition, CURSOR_TTL_SECONDS, TimeUnit.SECONDS);
                
                // Broadcast selection clear
                broadcastSelectionChange(configId, existingPosition, userId);
            }
            
        } catch (Exception e) {
            logger.error("Error clearing text selection", e);
        }
    }

    /**
     * Get cursor statistics for a configuration.
     */
    public CursorStatistics getCursorStatistics(UUID configId) {
        try {
            List<CursorPosition> positions = getCursorPositions(configId);
            
            long totalCursors = positions.size();
            long activeCursors = positions.stream()
                    .filter(pos -> pos.getTimestamp().isAfter(LocalDateTime.now().minusSeconds(10)))
                    .count();
            
            Map<String, Long> elementCounts = positions.stream()
                    .filter(pos -> pos.getElementId() != null)
                    .collect(Collectors.groupingBy(
                        CursorPosition::getElementId,
                        Collectors.counting()
                    ));
            
            return new CursorStatistics(totalCursors, activeCursors, elementCounts);
            
        } catch (Exception e) {
            logger.error("Error getting cursor statistics", e);
            return new CursorStatistics(0, 0, Collections.emptyMap());
        }
    }

    /**
     * Scheduled cleanup of expired cursor positions.
     */
    @Scheduled(fixedRate = CURSOR_CLEANUP_INTERVAL_MS)
    public void cleanupExpiredCursors() {
        try {
            // This would scan for expired cursor positions and clean them up
            // Redis TTL handles most of this, but we might want additional cleanup
            logger.debug("Running cursor cleanup");
            
        } catch (Exception e) {
            logger.error("Error during cursor cleanup", e);
        }
    }

    // Private helper methods

    private String getUserInitials(String username) {
        if (username == null || username.isEmpty()) {
            return "U";
        }
        
        String[] parts = username.split("\\s+");
        if (parts.length >= 2) {
            return (parts[0].substring(0, 1) + parts[1].substring(0, 1)).toUpperCase();
        } else if (username.length() >= 2) {
            return username.substring(0, 2).toUpperCase();
        } else {
            return username.toUpperCase();
        }
    }

    private void broadcastCursorPosition(UUID configId, CursorPosition position, UUID excludeUserId) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.CURSOR_MOVED);
            event.setUserId(position.getUserId());
            event.setUsername(position.getUsername());
            event.setConfigId(configId);
            event.setElementId(position.getElementId());
            event.setData(position);
            event.setTimestamp(LocalDateTime.now());
            
            // Broadcast to all users except the one who moved the cursor
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/cursors", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting cursor position", e);
        }
    }

    private void broadcastCursorRemoval(UUID configId, UUID userId) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.CURSOR_MOVED);
            event.setUserId(userId);
            event.setConfigId(configId);
            event.setData(null); // null data indicates cursor removal
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/cursors", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting cursor removal", e);
        }
    }

    private void broadcastSelectionChange(UUID configId, CursorPosition position, UUID excludeUserId) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.SELECTION_CHANGED);
            event.setUserId(position.getUserId());
            event.setUsername(position.getUsername());
            event.setConfigId(configId);
            event.setElementId(position.getElementId());
            event.setData(position);
            event.setTimestamp(LocalDateTime.now());
            
            messagingTemplate.convertAndSend(
                "/topic/ui-config/" + configId + "/selections", 
                event
            );
            
        } catch (Exception e) {
            logger.error("Error broadcasting selection change", e);
        }
    }

    // Statistics class

    public static class CursorStatistics {
        private final long totalCursors;
        private final long activeCursors;
        private final Map<String, Long> elementCounts;
        
        public CursorStatistics(long totalCursors, long activeCursors, Map<String, Long> elementCounts) {
            this.totalCursors = totalCursors;
            this.activeCursors = activeCursors;
            this.elementCounts = elementCounts;
        }
        
        public long getTotalCursors() { return totalCursors; }
        public long getActiveCursors() { return activeCursors; }
        public Map<String, Long> getElementCounts() { return elementCounts; }
    }
}
