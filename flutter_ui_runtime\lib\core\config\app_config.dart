import 'package:flutter/foundation.dart';

/// Application configuration and constants
class AppConfig {
  // App Information
  static const String appName = 'UI Runtime';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // Environment
  static const String environment = kDebugMode ? 'development' : 'production';
  static const bool isDebug = kDebugMode;
  static const bool isRelease = kReleaseMode;
  static const bool isProfile = kProfileMode;
  
  // API Configuration
  static const String baseUrl = String.fromEnvironment(
    'BASE_URL',
    defaultValue: 'https://api.uibuilder.dev/v1',
  );
  
  static const String websocketUrl = String.fromEnvironment(
    'WEBSOCKET_URL',
    defaultValue: 'wss://api.uibuilder.dev/ws',
  );
  
  // Authentication
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  
  // Storage Keys
  static const String userPreferencesKey = 'user_preferences';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String cacheKey = 'app_cache';
  static const String offlineDataKey = 'offline_data';
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration networkTimeout = Duration(seconds: 30);
  
  // Real-time Configuration
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const int maxReconnectAttempts = 5;
  static const Duration heartbeatInterval = Duration(seconds: 30);
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Performance
  static const int maxWidgetCacheSize = 1000;
  static const Duration widgetCacheExpiration = Duration(minutes: 30);
  static const int maxImageCacheSize = 200;
  
  // Monitoring
  static const String sentryDsn = String.fromEnvironment(
    'SENTRY_DSN',
    defaultValue: '',
  );
  
  // Feature Flags
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );
  
  static const bool enableCrashReporting = bool.fromEnvironment(
    'ENABLE_CRASH_REPORTING',
    defaultValue: true,
  );
  
  static const bool enablePerformanceMonitoring = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_MONITORING',
    defaultValue: true,
  );
  
  static const bool enableOfflineMode = bool.fromEnvironment(
    'ENABLE_OFFLINE_MODE',
    defaultValue: true,
  );
  
  static const bool enableRealtimeSync = bool.fromEnvironment(
    'ENABLE_REALTIME_SYNC',
    defaultValue: true,
  );
  
  // Platform-specific
  static const bool enableHapticFeedback = bool.fromEnvironment(
    'ENABLE_HAPTIC_FEEDBACK',
    defaultValue: true,
  );
  
  static const bool enableBiometricAuth = bool.fromEnvironment(
    'ENABLE_BIOMETRIC_AUTH',
    defaultValue: true,
  );
  
  // Development
  static const bool enableDebugLogging = bool.fromEnvironment(
    'ENABLE_DEBUG_LOGGING',
    defaultValue: kDebugMode,
  );
  
  static const bool enableNetworkLogging = bool.fromEnvironment(
    'ENABLE_NETWORK_LOGGING',
    defaultValue: kDebugMode,
  );
  
  static const bool enableWidgetInspector = bool.fromEnvironment(
    'ENABLE_WIDGET_INSPECTOR',
    defaultValue: kDebugMode,
  );
  
  // Validation
  static bool get isValidConfiguration {
    return baseUrl.isNotEmpty && websocketUrl.isNotEmpty;
  }
  
  // Helper methods
  static String get userAgent => '$appName/$appVersion ($environment)';
  
  static Map<String, String> get defaultHeaders => {
    'User-Agent': userAgent,
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  static Duration get adaptiveTimeout {
    if (isDebug) return const Duration(seconds: 60);
    return networkTimeout;
  }
  
  static int get adaptiveCacheSize {
    if (isDebug) return maxCacheSize * 2;
    return maxCacheSize;
  }
}
