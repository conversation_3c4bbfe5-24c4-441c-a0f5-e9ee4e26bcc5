# Production Deployment Guide

This guide covers the complete production deployment process for the UI Builder Platform, including infrastructure setup, security configuration, monitoring, and maintenance procedures.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Setup](#infrastructure-setup)
3. [Security Configuration](#security-configuration)
4. [Application Deployment](#application-deployment)
5. [Database Setup](#database-setup)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [SSL/TLS Configuration](#ssltls-configuration)
8. [Backup and Recovery](#backup-and-recovery)
9. [Performance Optimization](#performance-optimization)
10. [Maintenance Procedures](#maintenance-procedures)
11. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Kubernetes Cluster**: v1.28+ with at least 3 nodes
- **Node Resources**: Minimum 4 CPU cores, 16GB RAM per node
- **Storage**: 500GB+ SSD storage with backup capabilities
- **Network**: Load balancer with SSL termination support
- **Domain**: Registered domain with DNS management access

### Required Tools

```bash
# Install required CLI tools
kubectl version --client
helm version
docker version
terraform version
```

### Access Requirements

- Kubernetes cluster admin access
- Container registry push/pull permissions
- DNS management access
- SSL certificate management
- Monitoring system access

## Infrastructure Setup

### 1. Kubernetes Cluster Setup

#### Using Terraform (Recommended)

```hcl
# infrastructure/terraform/main.tf
module "kubernetes_cluster" {
  source = "./modules/kubernetes"
  
  cluster_name     = "ui-builder-prod"
  node_count       = 3
  node_size        = "Standard_D4s_v3"
  kubernetes_version = "1.28"
  
  tags = {
    Environment = "production"
    Project     = "ui-builder"
  }
}
```

#### Manual Setup Commands

```bash
# Create namespace
kubectl create namespace ui-builder-prod

# Set default namespace
kubectl config set-context --current --namespace=ui-builder-prod

# Create resource quotas
kubectl apply -f deployment/k8s/production/resource-quota.yaml
```

### 2. Storage Configuration

```yaml
# deployment/k8s/production/storage-class.yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
provisioner: kubernetes.io/azure-disk
parameters:
  storageaccounttype: Premium_LRS
  kind: Managed
reclaimPolicy: Retain
allowVolumeExpansion: true
```

### 3. Network Configuration

```yaml
# deployment/k8s/production/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ui-builder-network-policy
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ui-builder-prod
  egress:
  - to: []
```

## Security Configuration

### 1. RBAC Setup

```yaml
# deployment/k8s/production/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ui-builder-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ui-builder-binding
subjects:
- kind: ServiceAccount
  name: ui-builder-service-account
roleRef:
  kind: Role
  name: ui-builder-role
  apiGroup: rbac.authorization.k8s.io
```

### 2. Secrets Management

```bash
# Create secrets for sensitive data
kubectl create secret generic database-credentials \
  --from-literal=username=ui_builder_user \
  --from-literal=password=<secure-password>

kubectl create secret generic jwt-secret \
  --from-literal=secret=<jwt-secret-key>

kubectl create secret generic redis-password \
  --from-literal=password=<redis-password>
```

### 3. Pod Security Standards

```yaml
# deployment/k8s/production/pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: ui-builder-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## Application Deployment

### 1. Database Deployment

```bash
# Deploy PostgreSQL
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install postgresql bitnami/postgresql \
  --set auth.postgresPassword=<secure-password> \
  --set auth.database=ui_builder \
  --set primary.persistence.size=100Gi \
  --set primary.persistence.storageClass=fast-ssd \
  --set metrics.enabled=true
```

### 2. Redis Deployment

```bash
# Deploy Redis
helm install redis bitnami/redis \
  --set auth.password=<redis-password> \
  --set master.persistence.size=20Gi \
  --set master.persistence.storageClass=fast-ssd \
  --set metrics.enabled=true
```

### 3. Application Services

```bash
# Deploy backend services
kubectl apply -f deployment/k8s/production/backend/

# Deploy frontend applications
kubectl apply -f deployment/k8s/production/frontend/

# Deploy ingress controller
kubectl apply -f deployment/k8s/production/ingress/
```

### 4. Configuration Management

```yaml
# deployment/k8s/production/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-builder-config
data:
  SPRING_PROFILES_ACTIVE: "production"
  SPRING_DATASOURCE_URL: "********************************************"
  SPRING_REDIS_HOST: "redis-master"
  SPRING_REDIS_PORT: "6379"
  LOGGING_LEVEL_ROOT: "INFO"
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,metrics,prometheus"
```

## Database Setup

### 1. Database Initialization

```sql
-- Create production database
CREATE DATABASE ui_builder;
CREATE USER ui_builder_user WITH ENCRYPTED PASSWORD '<secure-password>';
GRANT ALL PRIVILEGES ON DATABASE ui_builder TO ui_builder_user;

-- Create schemas
\c ui_builder;
CREATE SCHEMA IF NOT EXISTS ui_metadata;
CREATE SCHEMA IF NOT EXISTS user_management;
CREATE SCHEMA IF NOT EXISTS analytics;
```

### 2. Migration Execution

```bash
# Run database migrations
kubectl exec -it deployment/api-gateway -- \
  java -jar app.jar --spring.profiles.active=production \
  --spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
```

### 3. Database Optimization

```sql
-- Performance optimizations
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

## Monitoring and Logging

### 1. Prometheus Setup

```bash
# Install Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack \
  --set prometheus.prometheusSpec.retention=30d \
  --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=100Gi
```

### 2. Grafana Configuration

```bash
# Access Grafana
kubectl port-forward svc/prometheus-grafana 3000:80

# Import dashboards
curl -X POST *****************************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @monitoring/grafana/ui-builder-dashboard.json
```

### 3. Log Aggregation

```bash
# Install ELK Stack
helm repo add elastic https://helm.elastic.co
helm install elasticsearch elastic/elasticsearch \
  --set replicas=3 \
  --set minimumMasterNodes=2

helm install kibana elastic/kibana
helm install filebeat elastic/filebeat
```

## SSL/TLS Configuration

### 1. Certificate Management

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

### 2. Ingress Configuration

```yaml
# deployment/k8s/production/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ui-builder-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    - app.yourdomain.com
    secretName: ui-builder-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 8080
  - host: app.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend
            port:
              number: 80
```

## Backup and Recovery

### 1. Database Backup

```bash
# Create backup job
kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              pg_dump -h postgresql -U ui_builder_user ui_builder | \
              gzip > /backup/ui_builder_$(date +%Y%m%d_%H%M%S).sql.gz
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: database-credentials
                  key: password
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
EOF
```

### 2. Application State Backup

```bash
# Backup Kubernetes resources
kubectl get all,configmaps,secrets,pvc -o yaml > ui-builder-backup-$(date +%Y%m%d).yaml

# Backup using Velero
velero backup create ui-builder-backup-$(date +%Y%m%d) \
  --include-namespaces ui-builder-prod \
  --storage-location default
```

## Performance Optimization

### 1. Resource Limits

```yaml
# Set appropriate resource limits
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

### 2. Horizontal Pod Autoscaling

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 3. CDN Configuration

```bash
# Configure CloudFlare or similar CDN
# Point static assets to CDN endpoints
# Enable caching for static resources
# Configure cache headers appropriately
```

## Maintenance Procedures

### 1. Rolling Updates

```bash
# Update application images
kubectl set image deployment/api-gateway api-gateway=ghcr.io/your-org/ui-builder/api-gateway:v2.0.0
kubectl rollout status deployment/api-gateway

# Rollback if needed
kubectl rollout undo deployment/api-gateway
```

### 2. Database Maintenance

```bash
# Run VACUUM and ANALYZE
kubectl exec -it postgresql-0 -- psql -U ui_builder_user -d ui_builder -c "VACUUM ANALYZE;"

# Update statistics
kubectl exec -it postgresql-0 -- psql -U ui_builder_user -d ui_builder -c "ANALYZE;"
```

### 3. Certificate Renewal

```bash
# Check certificate status
kubectl describe certificate ui-builder-tls

# Force renewal if needed
kubectl delete certificate ui-builder-tls
kubectl apply -f deployment/k8s/production/ingress.yaml
```

## Troubleshooting

### Common Issues

1. **Pod Startup Failures**
   ```bash
   kubectl describe pod <pod-name>
   kubectl logs <pod-name> --previous
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it <pod-name> -- nc -zv postgresql 5432
   ```

3. **SSL Certificate Problems**
   ```bash
   kubectl describe certificate ui-builder-tls
   kubectl logs -n cert-manager deployment/cert-manager
   ```

4. **Performance Issues**
   ```bash
   kubectl top nodes
   kubectl top pods
   ```

### Health Checks

```bash
# Check all services
kubectl get all

# Check ingress
kubectl get ingress

# Check certificates
kubectl get certificates

# Check persistent volumes
kubectl get pv,pvc
```

### Emergency Procedures

1. **Scale down services**
   ```bash
   kubectl scale deployment --replicas=0 --all
   ```

2. **Emergency database backup**
   ```bash
   kubectl exec postgresql-0 -- pg_dump -U ui_builder_user ui_builder > emergency-backup.sql
   ```

3. **Restore from backup**
   ```bash
   kubectl exec -i postgresql-0 -- psql -U ui_builder_user ui_builder < backup.sql
   ```

## Security Checklist

- [ ] All secrets are properly encrypted
- [ ] RBAC is configured with least privilege
- [ ] Network policies are in place
- [ ] Pod security policies are enforced
- [ ] SSL/TLS is properly configured
- [ ] Regular security scans are performed
- [ ] Backup encryption is enabled
- [ ] Access logs are monitored
- [ ] Vulnerability scanning is automated
- [ ] Security patches are regularly applied

## Post-Deployment Verification

1. **Functional Testing**
   - Verify all endpoints are accessible
   - Test user authentication and authorization
   - Validate UI builder functionality
   - Check real-time collaboration features

2. **Performance Testing**
   - Run load tests against API endpoints
   - Monitor response times and throughput
   - Verify auto-scaling behavior
   - Check database performance

3. **Security Testing**
   - Verify SSL/TLS configuration
   - Test authentication mechanisms
   - Validate authorization controls
   - Check for security vulnerabilities

4. **Monitoring Verification**
   - Confirm metrics collection
   - Verify alerting rules
   - Test log aggregation
   - Validate dashboard functionality

For additional support, refer to the [Operations Runbook](./operations-runbook.md) and [Troubleshooting Guide](./troubleshooting.md).
