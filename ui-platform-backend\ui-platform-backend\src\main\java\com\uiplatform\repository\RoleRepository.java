package com.uiplatform.repository;

import com.uiplatform.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Role entity.
 * Provides CRUD operations and custom queries for role management.
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, UUID>, JpaSpecificationExecutor<Role> {

    /**
     * Find role by name.
     */
    Optional<Role> findByNameAndDeletedFalse(String name);

    /**
     * Find roles by type.
     */
    List<Role> findByTypeAndDeletedFalse(Role.RoleType type);

    /**
     * Find system roles.
     */
    List<Role> findBySystemRoleTrueAndDeletedFalse();

    /**
     * Find custom roles.
     */
    List<Role> findBySystemRoleFalseAndDeletedFalse();

    /**
     * Find roles with specific permission.
     */
    @Query("SELECT r FROM Role r JOIN r.permissions p WHERE p.name = :permissionName AND r.deleted = false")
    List<Role> findByPermissionName(@Param("permissionName") String permissionName);

    /**
     * Find roles by user.
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId AND r.deleted = false")
    List<Role> findByUserId(@Param("userId") UUID userId);

    /**
     * Search roles by name.
     */
    @Query("SELECT r FROM Role r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%')) AND r.deleted = false")
    Page<Role> findByNameContainingIgnoreCase(@Param("name") String name, Pageable pageable);

    /**
     * Find roles with user count.
     */
    @Query("SELECT r, COUNT(u) as userCount FROM Role r LEFT JOIN r.users u WHERE r.deleted = false GROUP BY r")
    List<Object[]> findRolesWithUserCount();

    /**
     * Find roles with permission count.
     */
    @Query("SELECT r, COUNT(p) as permissionCount FROM Role r LEFT JOIN r.permissions p WHERE r.deleted = false GROUP BY r")
    List<Object[]> findRolesWithPermissionCount();

    /**
     * Check if role name exists (excluding current role).
     */
    @Query("SELECT COUNT(r) > 0 FROM Role r WHERE r.name = :name AND r.id != :excludeId AND r.deleted = false")
    boolean existsByNameAndIdNot(@Param("name") String name, @Param("excludeId") UUID excludeId);

    /**
     * Find roles by type with pagination.
     */
    Page<Role> findByTypeAndDeletedFalse(Role.RoleType type, Pageable pageable);

    /**
     * Find roles by multiple criteria.
     */
    @Query("SELECT r FROM Role r WHERE " +
           "(:type IS NULL OR r.type = :type) AND " +
           "(:systemRole IS NULL OR r.systemRole = :systemRole) AND " +
           "r.deleted = false")
    Page<Role> findRolesByCriteria(@Param("type") Role.RoleType type,
                                  @Param("systemRole") Boolean systemRole,
                                  Pageable pageable);

    /**
     * Count roles by type.
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.type = :type AND r.deleted = false")
    Long countByType(@Param("type") Role.RoleType type);

    /**
     * Find most assigned roles.
     */
    @Query("SELECT r, COUNT(u) as userCount FROM Role r LEFT JOIN r.users u " +
           "WHERE r.deleted = false GROUP BY r ORDER BY userCount DESC")
    List<Object[]> findMostAssignedRoles(Pageable pageable);
}
