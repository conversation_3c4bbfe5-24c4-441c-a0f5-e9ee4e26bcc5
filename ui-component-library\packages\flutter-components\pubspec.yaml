name: ui_builder_flutter_components
description: Flutter widget library for UI Builder platform
version: 1.0.0
homepage: https://github.com/ui-builder/component-library

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Design tokens
  # ui_builder_design_tokens:
  #   path: ../design-tokens/dist/flutter
  # Note: Design tokens will be available after building design-tokens package
  
  # Core dependencies
  cupertino_icons: ^1.0.6
  material_color_utilities: ^0.5.0
  
  # State management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Animations
  flutter_animate: ^4.2.0+1
  lottie: ^2.7.0
  
  # Icons
  lucide_icons: ^0.294.0
  phosphor_flutter: ^2.0.1
  
  # Utilities
  collection: ^1.17.2
  meta: ^1.9.1
  
  # Platform integration
  flutter_platform_widgets: ^6.0.2
  adaptive_theme: ^3.4.1
  
  # Accessibility
  semantics: ^0.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Linting
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0
  
  # Code generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  
  # Testing
  mocktail: ^1.0.1
  golden_toolkit: ^0.15.0
  patrol: ^2.6.0
  
  # Documentation
  widgetbook: ^3.7.1
  widgetbook_annotation: ^3.1.0
  widgetbook_generator: ^3.7.0

flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  # Fonts
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
    
    - family: JetBrainsMono
      fonts:
        - asset: assets/fonts/JetBrainsMono-Regular.ttf
        - asset: assets/fonts/JetBrainsMono-Medium.ttf
          weight: 500
        - asset: assets/fonts/JetBrainsMono-Bold.ttf
          weight: 700

# Widgetbook configuration
widgetbook:
  path: widgetbook
  name: UI Builder Components
  description: Flutter component library for UI Builder platform
