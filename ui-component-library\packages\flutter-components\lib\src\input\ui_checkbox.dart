import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Checkbox component
class UICheckbox extends StatelessWidget {
  const UICheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.subtitle,
    this.enabled = true,
    this.size = UISize.md,
    this.variant = UIColorVariant.primary,
    this.tristate = false,
    this.dense = false,
    this.controlAffinity = ListTileControlAffinity.leading,
  });

  final bool? value;
  final ValueChanged<bool?>? onChanged;
  final String? label;
  final String? subtitle;
  final bool enabled;
  final UISize size;
  final UIColorVariant variant;
  final bool tristate;
  final bool dense;
  final ListTileControlAffinity controlAffinity;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    if (label == null && subtitle == null) {
      // Simple checkbox without label
      return Checkbox(
        value: value,
        onChanged: enabled ? onChanged : null,
        tristate: tristate,
        activeColor: variant.getColor(colorScheme),
      );
    }

    // Checkbox with label
    return CheckboxListTile(
      value: value,
      onChanged: enabled ? onChanged : null,
      title: label != null ? Text(label!) : null,
      subtitle: subtitle != null ? Text(subtitle!) : null,
      tristate: tristate,
      dense: dense,
      controlAffinity: controlAffinity,
      activeColor: variant.getColor(colorScheme),
      enabled: enabled,
    );
  }
}

/// UI Builder Checkbox Group component
class UICheckboxGroup<T> extends StatefulWidget {
  const UICheckboxGroup({
    super.key,
    required this.items,
    this.values = const [],
    this.onChanged,
    this.label,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.direction = UIDirectionVariant.vertical,
    this.spacing = 8.0,
    this.required = false,
    this.maxSelection,
    this.minSelection,
  });

  final List<UICheckboxItem<T>> items;
  final List<T> values;
  final ValueChanged<List<T>>? onChanged;
  final String? label;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final UIDirectionVariant direction;
  final double spacing;
  final bool required;
  final int? maxSelection;
  final int? minSelection;

  @override
  State<UICheckboxGroup<T>> createState() => _UICheckboxGroupState<T>();
}

class _UICheckboxGroupState<T> extends State<UICheckboxGroup<T>> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    List<Widget> checkboxes = widget.items.map((item) {
      final isSelected = widget.values.contains(item.value);
      return UICheckbox(
        value: isSelected,
        onChanged: widget.enabled ? (bool? value) {
          List<T> newValues = List.from(widget.values);
          
          if (value == true) {
            if (widget.maxSelection == null || 
                newValues.length < widget.maxSelection!) {
              newValues.add(item.value);
            }
          } else {
            newValues.remove(item.value);
          }
          
          // Check minimum selection
          if (widget.minSelection != null && 
              newValues.length < widget.minSelection!) {
            return; // Don't allow deselection if it would go below minimum
          }
          
          widget.onChanged?.call(newValues);
        } : null,
        label: item.label,
        subtitle: item.subtitle,
        enabled: item.enabled && widget.enabled,
      );
    }).toList();

    Widget content;
    if (widget.direction == UIDirectionVariant.horizontal) {
      content = Wrap(
        spacing: widget.spacing,
        children: checkboxes,
      );
    } else {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: checkboxes.expand((checkbox) => [
          checkbox,
          if (checkbox != checkboxes.last) SizedBox(height: widget.spacing),
        ]).toList(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: tokens.spacing.size2),
        ],
        content,
        if (widget.helperText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.helperText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }
}

/// Checkbox item data class
class UICheckboxItem<T> {
  const UICheckboxItem({
    required this.value,
    required this.label,
    this.subtitle,
    this.enabled = true,
  });

  final T value;
  final String label;
  final String? subtitle;
  final bool enabled;
}
