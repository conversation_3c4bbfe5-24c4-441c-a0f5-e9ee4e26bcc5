import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import '../models/ui_metadata.dart';
import '../utils/logger.dart';
import 'dynamic_widget.dart';
import '../../widgets/layout/column_widget.dart';
import '../../widgets/layout/row_widget.dart';
import '../../widgets/layout/stack_widget.dart';
import '../../widgets/layout/container_widget.dart';
import '../../widgets/layout/grid_widget.dart';
import '../../widgets/display/text_widget.dart';
import '../../widgets/display/image_widget.dart';
import '../../widgets/display/card_widget.dart';
import '../../widgets/input/text_field_widget.dart';
import '../../widgets/input/button_widget.dart';
import '../../widgets/input/dropdown_widget.dart';
import '../../widgets/input/checkbox_widget.dart';
import '../../widgets/input/radio_widget.dart';
import '../../widgets/navigation/app_bar_widget.dart';
import '../../widgets/navigation/bottom_nav_widget.dart';
import '../../widgets/navigation/drawer_widget.dart';

/// Widget factory function type
typedef WidgetFactory = DynamicWidget Function(WidgetConfiguration config);

/// Widget registry that maps JSON types to Flutter widgets
class WidgetRegistry {
  static final WidgetRegistry _instance = WidgetRegistry._internal();
  factory WidgetRegistry() => _instance;
  WidgetRegistry._internal();

  static WidgetRegistry get instance => _instance;

  final Map<String, WidgetFactory> _widgets = {};
  final Map<String, WidgetFactory> _cupertinoWidgets = {};
  final Map<String, Widget> _widgetCache = {};

  /// Initialize the registry with default widgets
  void initialize() {
    _registerLayoutWidgets();
    _registerDisplayWidgets();
    _registerInputWidgets();
    _registerNavigationWidgets();
    _registerPlatformSpecificWidgets();
    
    AppLogger.info('Widget registry initialized with ${_widgets.length} widgets');
  }

  /// Register a widget factory
  void register(String type, WidgetFactory factory) {
    _widgets[type] = factory;
    AppLogger.debug('Registered widget: $type');
  }

  /// Register a Cupertino-specific widget factory
  void registerCupertino(String type, WidgetFactory factory) {
    _cupertinoWidgets[type] = factory;
    AppLogger.debug('Registered Cupertino widget: $type');
  }

  /// Build a widget from configuration
  Widget build(WidgetConfiguration config, {bool useCupertino = false}) {
    try {
      // Check cache first
      final cacheKey = '${config.id}_${config.hashCode}';
      if (_widgetCache.containsKey(cacheKey)) {
        return _widgetCache[cacheKey]!;
      }

      // Get the appropriate factory
      final factory = useCupertino 
          ? _cupertinoWidgets[config.type] ?? _widgets[config.type]
          : _widgets[config.type];

      if (factory == null) {
        AppLogger.warning('Unknown widget type: ${config.type}');
        return _buildUnknownWidget(config);
      }

      // Create the widget
      final dynamicWidget = factory(config);
      final widget = dynamicWidget.build();

      // Cache the widget if it's cacheable
      if (dynamicWidget.isCacheable) {
        _widgetCache[cacheKey] = widget;
      }

      return widget;
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to build widget: ${config.type}',
        error: error,
        stackTrace: stackTrace,
      );
      return _buildErrorWidget(config, error);
    }
  }

  /// Check if a widget type is registered
  bool isRegistered(String type) {
    return _widgets.containsKey(type) || _cupertinoWidgets.containsKey(type);
  }

  /// Get all registered widget types
  List<String> getRegisteredTypes() {
    return [..._widgets.keys, ..._cupertinoWidgets.keys];
  }

  /// Clear the widget cache
  void clearCache() {
    _widgetCache.clear();
    AppLogger.debug('Widget cache cleared');
  }

  /// Register layout widgets
  void _registerLayoutWidgets() {
    register('Column', (config) => ColumnWidget(config));
    register('Row', (config) => RowWidget(config));
    register('Stack', (config) => StackWidget(config));
    register('Container', (config) => ContainerWidget(config));
    register('Grid', (config) => GridWidget(config));
    register('Wrap', (config) => WrapWidget(config));
    register('Flex', (config) => FlexWidget(config));
    register('Expanded', (config) => ExpandedWidget(config));
    register('Flexible', (config) => FlexibleWidget(config));
    register('Positioned', (config) => PositionedWidget(config));
    register('Align', (config) => AlignWidget(config));
    register('Center', (config) => CenterWidget(config));
    register('Padding', (config) => PaddingWidget(config));
    register('Margin', (config) => MarginWidget(config));
    register('SizedBox', (config) => SizedBoxWidget(config));
    register('AspectRatio', (config) => AspectRatioWidget(config));
    register('FractionallySizedBox', (config) => FractionallySizedBoxWidget(config));
  }

  /// Register display widgets
  void _registerDisplayWidgets() {
    register('Text', (config) => TextWidget(config));
    register('RichText', (config) => RichTextWidget(config));
    register('Image', (config) => ImageWidget(config));
    register('Icon', (config) => IconWidget(config));
    register('Card', (config) => CardWidget(config));
    register('Chip', (config) => ChipWidget(config));
    register('Avatar', (config) => AvatarWidget(config));
    register('Badge', (config) => BadgeWidget(config));
    register('Divider', (config) => DividerWidget(config));
    register('VerticalDivider', (config) => VerticalDividerWidget(config));
    register('Spacer', (config) => SpacerWidget(config));
    register('CircularProgressIndicator', (config) => CircularProgressWidget(config));
    register('LinearProgressIndicator', (config) => LinearProgressWidget(config));
    register('Placeholder', (config) => PlaceholderWidget(config));
  }

  /// Register input widgets
  void _registerInputWidgets() {
    register('TextField', (config) => TextFieldWidget(config));
    register('Button', (config) => ButtonWidget(config));
    register('ElevatedButton', (config) => ElevatedButtonWidget(config));
    register('OutlinedButton', (config) => OutlinedButtonWidget(config));
    register('TextButton', (config) => TextButtonWidget(config));
    register('IconButton', (config) => IconButtonWidget(config));
    register('FloatingActionButton', (config) => FloatingActionButtonWidget(config));
    register('Dropdown', (config) => DropdownWidget(config));
    register('Checkbox', (config) => CheckboxWidget(config));
    register('Radio', (config) => RadioWidget(config));
    register('Switch', (config) => SwitchWidget(config));
    register('Slider', (config) => SliderWidget(config));
    register('RangeSlider', (config) => RangeSliderWidget(config));
    register('DatePicker', (config) => DatePickerWidget(config));
    register('TimePicker', (config) => TimePickerWidget(config));
    register('FilePicker', (config) => FilePickerWidget(config));
    register('ImagePicker', (config) => ImagePickerWidget(config));
  }

  /// Register navigation widgets
  void _registerNavigationWidgets() {
    register('AppBar', (config) => AppBarWidget(config));
    register('BottomNavigationBar', (config) => BottomNavWidget(config));
    register('TabBar', (config) => TabBarWidget(config));
    register('Drawer', (config) => DrawerWidget(config));
    register('NavigationRail', (config) => NavigationRailWidget(config));
    register('Breadcrumb', (config) => BreadcrumbWidget(config));
    register('Stepper', (config) => StepperWidget(config));
    register('PageView', (config) => PageViewWidget(config));
    register('BottomSheet', (config) => BottomSheetWidget(config));
    register('Dialog', (config) => DialogWidget(config));
    register('AlertDialog', (config) => AlertDialogWidget(config));
    register('SnackBar', (config) => SnackBarWidget(config));
  }

  /// Register platform-specific widgets
  void _registerPlatformSpecificWidgets() {
    // Cupertino widgets for iOS
    registerCupertino('Button', (config) => CupertinoButtonWidget(config));
    registerCupertino('TextField', (config) => CupertinoTextFieldWidget(config));
    registerCupertino('Switch', (config) => CupertinoSwitchWidget(config));
    registerCupertino('Slider', (config) => CupertinoSliderWidget(config));
    registerCupertino('AppBar', (config) => CupertinoAppBarWidget(config));
    registerCupertino('BottomNavigationBar', (config) => CupertinoBottomNavWidget(config));
    registerCupertino('Dialog', (config) => CupertinoDialogWidget(config));
    registerCupertino('ActionSheet', (config) => CupertinoActionSheetWidget(config));
    registerCupertino('DatePicker', (config) => CupertinoDatePickerWidget(config));
    registerCupertino('TimePicker', (config) => CupertinoTimePickerWidget(config));
    registerCupertino('Picker', (config) => CupertinoPickerWidget(config));
    registerCupertino('SegmentedControl', (config) => CupertinoSegmentedControlWidget(config));
  }

  /// Build widget for unknown types
  Widget _buildUnknownWidget(WidgetConfiguration config) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red, width: 2),
        borderRadius: BorderRadius.circular(8),
        color: Colors.red.withOpacity(0.1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error, color: Colors.red, size: 32),
          const SizedBox(height: 8),
          Text(
            'Unknown Widget',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Type: ${config.type}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.red,
            ),
          ),
          Text(
            'ID: ${config.id}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Build widget for errors
  Widget _buildErrorWidget(WidgetConfiguration config, dynamic error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange, width: 2),
        borderRadius: BorderRadius.circular(8),
        color: Colors.orange.withOpacity(0.1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.warning, color: Colors.orange, size: 32),
          const SizedBox(height: 8),
          Text(
            'Widget Error',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Type: ${config.type}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.orange,
            ),
          ),
          Text(
            'Error: ${error.toString()}',
            style: const TextStyle(
              fontSize: 10,
              color: Colors.orange,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

// Placeholder widget classes - these would be implemented in separate files
class WrapWidget extends DynamicWidget {
  WrapWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class FlexWidget extends DynamicWidget {
  FlexWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class ExpandedWidget extends DynamicWidget {
  ExpandedWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class FlexibleWidget extends DynamicWidget {
  FlexibleWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class PositionedWidget extends DynamicWidget {
  PositionedWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class AlignWidget extends DynamicWidget {
  AlignWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CenterWidget extends DynamicWidget {
  CenterWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class PaddingWidget extends DynamicWidget {
  PaddingWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class MarginWidget extends DynamicWidget {
  MarginWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class SizedBoxWidget extends DynamicWidget {
  SizedBoxWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class AspectRatioWidget extends DynamicWidget {
  AspectRatioWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class FractionallySizedBoxWidget extends DynamicWidget {
  FractionallySizedBoxWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

// Additional placeholder classes for other widget types...
class RichTextWidget extends DynamicWidget {
  RichTextWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class IconWidget extends DynamicWidget {
  IconWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class ChipWidget extends DynamicWidget {
  ChipWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class AvatarWidget extends DynamicWidget {
  AvatarWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class BadgeWidget extends DynamicWidget {
  BadgeWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class DividerWidget extends DynamicWidget {
  DividerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class VerticalDividerWidget extends DynamicWidget {
  VerticalDividerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class SpacerWidget extends DynamicWidget {
  SpacerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CircularProgressWidget extends DynamicWidget {
  CircularProgressWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class LinearProgressWidget extends DynamicWidget {
  LinearProgressWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class PlaceholderWidget extends DynamicWidget {
  PlaceholderWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class ElevatedButtonWidget extends DynamicWidget {
  ElevatedButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class OutlinedButtonWidget extends DynamicWidget {
  OutlinedButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class TextButtonWidget extends DynamicWidget {
  TextButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class IconButtonWidget extends DynamicWidget {
  IconButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class FloatingActionButtonWidget extends DynamicWidget {
  FloatingActionButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class SwitchWidget extends DynamicWidget {
  SwitchWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class SliderWidget extends DynamicWidget {
  SliderWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class RangeSliderWidget extends DynamicWidget {
  RangeSliderWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class DatePickerWidget extends DynamicWidget {
  DatePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class TimePickerWidget extends DynamicWidget {
  TimePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class FilePickerWidget extends DynamicWidget {
  FilePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class ImagePickerWidget extends DynamicWidget {
  ImagePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class TabBarWidget extends DynamicWidget {
  TabBarWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class NavigationRailWidget extends DynamicWidget {
  NavigationRailWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class BreadcrumbWidget extends DynamicWidget {
  BreadcrumbWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class StepperWidget extends DynamicWidget {
  StepperWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class PageViewWidget extends DynamicWidget {
  PageViewWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class BottomSheetWidget extends DynamicWidget {
  BottomSheetWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class DialogWidget extends DynamicWidget {
  DialogWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class AlertDialogWidget extends DynamicWidget {
  AlertDialogWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class SnackBarWidget extends DynamicWidget {
  SnackBarWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

// Cupertino widget placeholders
class CupertinoButtonWidget extends DynamicWidget {
  CupertinoButtonWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoTextFieldWidget extends DynamicWidget {
  CupertinoTextFieldWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoSwitchWidget extends DynamicWidget {
  CupertinoSwitchWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoSliderWidget extends DynamicWidget {
  CupertinoSliderWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoAppBarWidget extends DynamicWidget {
  CupertinoAppBarWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoBottomNavWidget extends DynamicWidget {
  CupertinoBottomNavWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoDialogWidget extends DynamicWidget {
  CupertinoDialogWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoActionSheetWidget extends DynamicWidget {
  CupertinoActionSheetWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoDatePickerWidget extends DynamicWidget {
  CupertinoDatePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoTimePickerWidget extends DynamicWidget {
  CupertinoTimePickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoPickerWidget extends DynamicWidget {
  CupertinoPickerWidget(super.config);
  @override
  Widget build() => const Placeholder();
}

class CupertinoSegmentedControlWidget extends DynamicWidget {
  CupertinoSegmentedControlWidget(super.config);
  @override
  Widget build() => const Placeholder();
}
