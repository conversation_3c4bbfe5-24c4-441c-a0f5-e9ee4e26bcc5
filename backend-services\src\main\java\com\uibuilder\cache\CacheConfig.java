package com.uibuilder.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Cache Configuration for UI Builder Platform
 * 
 * Implements multi-level caching strategy:
 * - L1: Local Caffeine cache for frequently accessed data
 * - L2: Redis distributed cache for shared data
 * - Custom cache configurations per data type
 * - Cache warming and invalidation strategies
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${cache.caffeine.maximum-size:10000}")
    private long caffeineMaximumSize;

    @Value("${cache.caffeine.expire-after-write:300}")
    private long caffeineExpireAfterWrite;

    @Value("${cache.redis.default-ttl:3600}")
    private long redisDefaultTtl;

    @Value("${cache.redis.enable-statistics:true}")
    private boolean enableStatistics;

    /**
     * Primary cache manager using Caffeine for local caching
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(caffeineMaximumSize)
                .expireAfterWrite(caffeineExpireAfterWrite, TimeUnit.SECONDS)
                .recordStats());
        
        // Pre-configure cache names
        cacheManager.setCacheNames(
            "configurations",
            "templates", 
            "users",
            "organizations",
            "components",
            "themes",
            "permissions",
            "analytics"
        );
        
        return cacheManager;
    }

    /**
     * Redis cache manager for distributed caching
     */
    @Bean
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(redisDefaultTtl))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        if (enableStatistics) {
            defaultConfig = defaultConfig.enableTimeToIdle();
        }

        // Custom configurations for different cache types
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // Short-lived caches
        cacheConfigurations.put("user-sessions", defaultConfig
                .entryTtl(Duration.ofMinutes(30)));
        
        cacheConfigurations.put("api-rate-limits", defaultConfig
                .entryTtl(Duration.ofMinutes(1)));
        
        // Medium-lived caches
        cacheConfigurations.put("configurations", defaultConfig
                .entryTtl(Duration.ofHours(2)));
        
        cacheConfigurations.put("templates", defaultConfig
                .entryTtl(Duration.ofHours(4)));
        
        cacheConfigurations.put("components", defaultConfig
                .entryTtl(Duration.ofHours(6)));
        
        // Long-lived caches
        cacheConfigurations.put("users", defaultConfig
                .entryTtl(Duration.ofHours(12)));
        
        cacheConfigurations.put("organizations", defaultConfig
                .entryTtl(Duration.ofHours(24)));
        
        cacheConfigurations.put("permissions", defaultConfig
                .entryTtl(Duration.ofHours(8)));
        
        // Analytics caches
        cacheConfigurations.put("analytics-daily", defaultConfig
                .entryTtl(Duration.ofHours(1)));
        
        cacheConfigurations.put("analytics-monthly", defaultConfig
                .entryTtl(Duration.ofHours(6)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * Configuration cache with custom loading
     */
    @Bean
    public LoadingCache<String, String> configurationCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .recordStats()
                .build(key -> loadConfigurationFromDatabase(key));
    }

    /**
     * Template cache with custom loading
     */
    @Bean
    public LoadingCache<String, String> templateCache() {
        return Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .refreshAfterWrite(15, TimeUnit.MINUTES)
                .recordStats()
                .build(key -> loadTemplateFromDatabase(key));
    }

    /**
     * User permission cache
     */
    @Bean
    public LoadingCache<String, Set<String>> userPermissionCache() {
        return Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .refreshAfterWrite(30, TimeUnit.MINUTES)
                .recordStats()
                .build(key -> loadUserPermissionsFromDatabase(key));
    }

    /**
     * Component library cache
     */
    @Bean
    public LoadingCache<String, List<ComponentDefinition>> componentLibraryCache() {
        return Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(2, TimeUnit.HOURS)
                .refreshAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build(key -> loadComponentLibraryFromDatabase(key));
    }

    /**
     * Analytics cache for frequently accessed metrics
     */
    @Bean
    public LoadingCache<String, Object> analyticsCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(15, TimeUnit.MINUTES)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .recordStats()
                .build(key -> loadAnalyticsFromDatabase(key));
    }

    // Cache loading methods (to be implemented by services)
    private String loadConfigurationFromDatabase(String key) {
        // This would be implemented by ConfigurationService
        return null;
    }

    private String loadTemplateFromDatabase(String key) {
        // This would be implemented by TemplateService
        return null;
    }

    private Set<String> loadUserPermissionsFromDatabase(String key) {
        // This would be implemented by UserService
        return Set.of();
    }

    private List<ComponentDefinition> loadComponentLibraryFromDatabase(String key) {
        // This would be implemented by ComponentService
        return List.of();
    }

    private Object loadAnalyticsFromDatabase(String key) {
        // This would be implemented by AnalyticsService
        return null;
    }
}

/**
 * Cache warming service for preloading frequently accessed data
 */
@Service
public class CacheWarmingService {

    private final ConfigurationService configurationService;
    private final TemplateService templateService;
    private final UserService userService;
    private final LoadingCache<String, String> configurationCache;
    private final LoadingCache<String, String> templateCache;

    public CacheWarmingService(
            ConfigurationService configurationService,
            TemplateService templateService,
            UserService userService,
            LoadingCache<String, String> configurationCache,
            LoadingCache<String, String> templateCache) {
        this.configurationService = configurationService;
        this.templateService = templateService;
        this.userService = userService;
        this.configurationCache = configurationCache;
        this.templateCache = templateCache;
    }

    /**
     * Warm up caches on application startup
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCaches() {
        CompletableFuture.runAsync(this::warmConfigurationCache);
        CompletableFuture.runAsync(this::warmTemplateCache);
        CompletableFuture.runAsync(this::warmUserCache);
    }

    /**
     * Warm configuration cache with popular configurations
     */
    private void warmConfigurationCache() {
        try {
            List<String> popularConfigIds = configurationService.getPopularConfigurationIds(100);
            popularConfigIds.parallelStream().forEach(configId -> {
                try {
                    configurationCache.get(configId);
                } catch (Exception e) {
                    log.warn("Failed to warm configuration cache for ID: {}", configId, e);
                }
            });
            log.info("Configuration cache warmed with {} entries", popularConfigIds.size());
        } catch (Exception e) {
            log.error("Failed to warm configuration cache", e);
        }
    }

    /**
     * Warm template cache with popular templates
     */
    private void warmTemplateCache() {
        try {
            List<String> popularTemplateIds = templateService.getPopularTemplateIds(50);
            popularTemplateIds.parallelStream().forEach(templateId -> {
                try {
                    templateCache.get(templateId);
                } catch (Exception e) {
                    log.warn("Failed to warm template cache for ID: {}", templateId, e);
                }
            });
            log.info("Template cache warmed with {} entries", popularTemplateIds.size());
        } catch (Exception e) {
            log.error("Failed to warm template cache", e);
        }
    }

    /**
     * Warm user cache with active users
     */
    private void warmUserCache() {
        try {
            List<String> activeUserIds = userService.getActiveUserIds(1000);
            activeUserIds.parallelStream().forEach(userId -> {
                try {
                    // Warm user permissions cache
                    userService.getUserPermissions(userId);
                } catch (Exception e) {
                    log.warn("Failed to warm user cache for ID: {}", userId, e);
                }
            });
            log.info("User cache warmed with {} entries", activeUserIds.size());
        } catch (Exception e) {
            log.error("Failed to warm user cache", e);
        }
    }

    /**
     * Scheduled cache refresh for critical data
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void refreshCriticalCaches() {
        // Refresh system configurations
        configurationService.getSystemConfigurations()
                .forEach(config -> configurationCache.refresh(config.getId()));
        
        // Refresh public templates
        templateService.getPublicTemplates()
                .forEach(template -> templateCache.refresh(template.getId()));
    }
}
