import { ComponentMetadata, DataSource, DataBinding } from '../types/runtime';

export interface DataProvider {
  id: string;
  name: string;
  type: 'rest' | 'graphql' | 'websocket' | 'static' | 'local';
  config: Record<string, any>;
  fetch: (query: DataQuery) => Promise<any>;
  subscribe?: (query: DataQuery, callback: (data: any) => void) => () => void;
}

export interface DataQuery {
  operation: 'read' | 'create' | 'update' | 'delete' | 'list';
  resource: string;
  params?: Record<string, any>;
  filters?: Record<string, any>;
  sort?: Array<{ field: string; direction: 'asc' | 'desc' }>;
  pagination?: { page: number; limit: number };
  fields?: string[];
}

export interface DataCache {
  get: (key: string) => any;
  set: (key: string, data: any, ttl?: number) => void;
  delete: (key: string) => void;
  clear: () => void;
}

export interface DataTransformer {
  name: string;
  transform: (data: any, params?: Record<string, any>) => any;
}

/**
 * Data Integration Layer for Web Runtime
 * 
 * Manages data sources, API integration, data binding, and CRUD operations.
 * Provides caching, transformation, and real-time data synchronization.
 */
export class DataIntegrationSystem {
  private providers = new Map<string, DataProvider>();
  private cache: DataCache;
  private transformers = new Map<string, DataTransformer>();
  private subscriptions = new Map<string, () => void>();
  private bindings = new Map<string, DataBinding>();

  constructor(cache?: DataCache) {
    this.cache = cache || this.createDefaultCache();
    this.registerBuiltInTransformers();
  }

  /**
   * Register a data provider
   */
  registerProvider(provider: DataProvider): void {
    this.providers.set(provider.id, provider);
  }

  /**
   * Register a data transformer
   */
  registerTransformer(transformer: DataTransformer): void {
    this.transformers.set(transformer.name, transformer);
  }

  /**
   * Execute a data query
   */
  async executeQuery(providerId: string, query: DataQuery): Promise<any> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Data provider not found: ${providerId}`);
    }

    // Check cache first for read operations
    if (query.operation === 'read' || query.operation === 'list') {
      const cacheKey = this.generateCacheKey(providerId, query);
      const cachedData = this.cache.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      const data = await provider.fetch(query);
      
      // Cache the result for read operations
      if (query.operation === 'read' || query.operation === 'list') {
        const cacheKey = this.generateCacheKey(providerId, query);
        this.cache.set(cacheKey, data, 300); // 5 minutes TTL
      }

      return data;
    } catch (error) {
      console.error('Data query failed:', error);
      throw error;
    }
  }

  /**
   * Create a data binding
   */
  createBinding(binding: DataBinding): string {
    const bindingId = `binding_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.bindings.set(bindingId, binding);

    // Set up real-time subscription if supported
    if (binding.realTime && binding.dataSource) {
      this.setupRealtimeBinding(bindingId, binding);
    }

    return bindingId;
  }

  /**
   * Update a data binding
   */
  updateBinding(bindingId: string, updates: Partial<DataBinding>): void {
    const binding = this.bindings.get(bindingId);
    if (binding) {
      const updatedBinding = { ...binding, ...updates };
      this.bindings.set(bindingId, updatedBinding);

      // Update real-time subscription if needed
      if (updatedBinding.realTime && updatedBinding.dataSource) {
        this.cleanupSubscription(bindingId);
        this.setupRealtimeBinding(bindingId, updatedBinding);
      }
    }
  }

  /**
   * Remove a data binding
   */
  removeBinding(bindingId: string): void {
    this.cleanupSubscription(bindingId);
    this.bindings.delete(bindingId);
  }

  /**
   * Get data for a binding
   */
  async getBindingData(bindingId: string): Promise<any> {
    const binding = this.bindings.get(bindingId);
    if (!binding || !binding.dataSource) {
      return null;
    }

    const query: DataQuery = {
      operation: 'read',
      resource: binding.dataSource.resource,
      params: binding.dataSource.params,
      filters: binding.dataSource.filters,
      fields: binding.dataSource.fields
    };

    let data = await this.executeQuery(binding.dataSource.providerId, query);

    // Apply transformations
    if (binding.transformations) {
      for (const transformation of binding.transformations) {
        const transformer = this.transformers.get(transformation.type);
        if (transformer) {
          data = transformer.transform(data, transformation.params);
        }
      }
    }

    return data;
  }

  /**
   * Perform CRUD operations
   */
  async create(providerId: string, resource: string, data: any): Promise<any> {
    const query: DataQuery = {
      operation: 'create',
      resource,
      params: data
    };

    const result = await this.executeQuery(providerId, query);
    
    // Invalidate related cache entries
    this.invalidateCache(providerId, resource);
    
    return result;
  }

  async read(providerId: string, resource: string, id: string): Promise<any> {
    const query: DataQuery = {
      operation: 'read',
      resource,
      params: { id }
    };

    return this.executeQuery(providerId, query);
  }

  async update(providerId: string, resource: string, id: string, data: any): Promise<any> {
    const query: DataQuery = {
      operation: 'update',
      resource,
      params: { id, ...data }
    };

    const result = await this.executeQuery(providerId, query);
    
    // Invalidate related cache entries
    this.invalidateCache(providerId, resource);
    
    return result;
  }

  async delete(providerId: string, resource: string, id: string): Promise<any> {
    const query: DataQuery = {
      operation: 'delete',
      resource,
      params: { id }
    };

    const result = await this.executeQuery(providerId, query);
    
    // Invalidate related cache entries
    this.invalidateCache(providerId, resource);
    
    return result;
  }

  async list(
    providerId: string, 
    resource: string, 
    options?: {
      filters?: Record<string, any>;
      sort?: Array<{ field: string; direction: 'asc' | 'desc' }>;
      pagination?: { page: number; limit: number };
      fields?: string[];
    }
  ): Promise<any> {
    const query: DataQuery = {
      operation: 'list',
      resource,
      filters: options?.filters,
      sort: options?.sort,
      pagination: options?.pagination,
      fields: options?.fields
    };

    return this.executeQuery(providerId, query);
  }

  /**
   * Transform data using registered transformers
   */
  transformData(data: any, transformerName: string, params?: Record<string, any>): any {
    const transformer = this.transformers.get(transformerName);
    if (!transformer) {
      throw new Error(`Transformer not found: ${transformerName}`);
    }

    return transformer.transform(data, params);
  }

  /**
   * Clear cache for a specific provider and resource
   */
  invalidateCache(providerId: string, resource?: string): void {
    if (resource) {
      // Clear specific resource cache
      const pattern = `${providerId}:${resource}:`;
      // Implementation would depend on cache implementation
      this.cache.clear(); // Simplified for now
    } else {
      // Clear all cache for provider
      this.cache.clear(); // Simplified for now
    }
  }

  // Private methods

  private createDefaultCache(): DataCache {
    const cache = new Map<string, { data: any; expires: number }>();

    return {
      get: (key: string) => {
        const entry = cache.get(key);
        if (entry && entry.expires > Date.now()) {
          return entry.data;
        }
        cache.delete(key);
        return null;
      },
      set: (key: string, data: any, ttl: number = 300) => {
        cache.set(key, {
          data,
          expires: Date.now() + (ttl * 1000)
        });
      },
      delete: (key: string) => {
        cache.delete(key);
      },
      clear: () => {
        cache.clear();
      }
    };
  }

  private generateCacheKey(providerId: string, query: DataQuery): string {
    return `${providerId}:${query.resource}:${JSON.stringify(query)}`;
  }

  private setupRealtimeBinding(bindingId: string, binding: DataBinding): void {
    if (!binding.dataSource) return;

    const provider = this.providers.get(binding.dataSource.providerId);
    if (!provider || !provider.subscribe) return;

    const query: DataQuery = {
      operation: 'read',
      resource: binding.dataSource.resource,
      params: binding.dataSource.params,
      filters: binding.dataSource.filters
    };

    const unsubscribe = provider.subscribe(query, (data) => {
      // Apply transformations
      let transformedData = data;
      if (binding.transformations) {
        for (const transformation of binding.transformations) {
          const transformer = this.transformers.get(transformation.type);
          if (transformer) {
            transformedData = transformer.transform(transformedData, transformation.params);
          }
        }
      }

      // Trigger binding update callback
      if (binding.onUpdate) {
        binding.onUpdate(transformedData);
      }
    });

    this.subscriptions.set(bindingId, unsubscribe);
  }

  private cleanupSubscription(bindingId: string): void {
    const unsubscribe = this.subscriptions.get(bindingId);
    if (unsubscribe) {
      unsubscribe();
      this.subscriptions.delete(bindingId);
    }
  }

  private registerBuiltInTransformers(): void {
    // Array transformers
    this.registerTransformer({
      name: 'filter',
      transform: (data: any[], params: { condition: (item: any) => boolean }) => {
        return Array.isArray(data) ? data.filter(params.condition) : data;
      }
    });

    this.registerTransformer({
      name: 'map',
      transform: (data: any[], params: { mapper: (item: any) => any }) => {
        return Array.isArray(data) ? data.map(params.mapper) : data;
      }
    });

    this.registerTransformer({
      name: 'sort',
      transform: (data: any[], params: { field: string; direction: 'asc' | 'desc' }) => {
        if (!Array.isArray(data)) return data;
        
        return [...data].sort((a, b) => {
          const aVal = a[params.field];
          const bVal = b[params.field];
          const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
          return params.direction === 'desc' ? -comparison : comparison;
        });
      }
    });

    // Object transformers
    this.registerTransformer({
      name: 'pick',
      transform: (data: any, params: { fields: string[] }) => {
        if (typeof data !== 'object' || !data) return data;
        
        const result: any = {};
        for (const field of params.fields) {
          if (field in data) {
            result[field] = data[field];
          }
        }
        return result;
      }
    });

    this.registerTransformer({
      name: 'omit',
      transform: (data: any, params: { fields: string[] }) => {
        if (typeof data !== 'object' || !data) return data;
        
        const result = { ...data };
        for (const field of params.fields) {
          delete result[field];
        }
        return result;
      }
    });

    // Format transformers
    this.registerTransformer({
      name: 'formatDate',
      transform: (data: any, params: { format: string; field?: string }) => {
        if (params.field && typeof data === 'object') {
          return {
            ...data,
            [params.field]: new Date(data[params.field]).toLocaleDateString()
          };
        }
        return new Date(data).toLocaleDateString();
      }
    });

    this.registerTransformer({
      name: 'formatCurrency',
      transform: (data: any, params: { currency: string; field?: string }) => {
        const formatter = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: params.currency
        });

        if (params.field && typeof data === 'object') {
          return {
            ...data,
            [params.field]: formatter.format(data[params.field])
          };
        }
        return formatter.format(data);
      }
    });
  }

  /**
   * Get all registered providers
   */
  getProviders(): DataProvider[] {
    return Array.from(this.providers.values());
  }

  /**
   * Get all registered transformers
   */
  getTransformers(): DataTransformer[] {
    return Array.from(this.transformers.values());
  }

  /**
   * Clean up all resources
   */
  cleanup(): void {
    // Clean up all subscriptions
    for (const unsubscribe of this.subscriptions.values()) {
      unsubscribe();
    }
    this.subscriptions.clear();
    
    // Clear cache
    this.cache.clear();
    
    // Clear bindings
    this.bindings.clear();
  }
}

// Global data integration instance
export const dataIntegration = new DataIntegrationSystem();
