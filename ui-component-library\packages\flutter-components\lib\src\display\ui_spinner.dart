import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Spinner component
class UISpinner extends StatelessWidget {
  const UISpinner({
    super.key,
    this.size = UISize.md,
    this.color,
    this.strokeWidth = 4.0,
    this.variant = UILoadingVariant.spinner,
  });

  final UISize size;
  final Color? color;
  final double strokeWidth;
  final UILoadingVariant variant;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final effectiveColor = color ?? colorScheme.primary;
    final effectiveSize = _getSize();

    switch (variant) {
      case UILoadingVariant.spinner:
        return SizedBox(
          width: effectiveSize,
          height: effectiveSize,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation(effectiveColor),
          ),
        );
      case UILoadingVariant.dots:
        return _DotsSpinner(
          size: effectiveSize,
          color: effectiveColor,
        );
      case UILoadingVariant.pulse:
        return _PulseSpinner(
          size: effectiveSize,
          color: effectiveColor,
        );
      case UILoadingVariant.skeleton:
        return Container(
          width: effectiveSize,
          height: effectiveSize,
          decoration: BoxDecoration(
            color: effectiveColor.withOpacity(0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        );
    }
  }

  double _getSize() {
    switch (size) {
      case UISize.xs:
        return 16;
      case UISize.sm:
        return 20;
      case UISize.md:
        return 24;
      case UISize.lg:
        return 32;
      case UISize.xl:
        return 40;
      case UISize.xxl:
        return 48;
    }
  }
}

class _DotsSpinner extends StatefulWidget {
  const _DotsSpinner({
    required this.size,
    required this.color,
  });

  final double size;
  final Color color;

  @override
  State<_DotsSpinner> createState() => _DotsSpinnerState();
}

class _DotsSpinnerState extends State<_DotsSpinner>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dotSize = widget.size / 6;

    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Transform.scale(
                scale: 0.5 + (_animations[index].value * 0.5),
                child: Container(
                  width: dotSize,
                  height: dotSize,
                  decoration: BoxDecoration(
                    color: widget.color.withOpacity(0.3 + (_animations[index].value * 0.7)),
                    shape: BoxShape.circle,
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

class _PulseSpinner extends StatefulWidget {
  const _PulseSpinner({
    required this.size,
    required this.color,
  });

  final double size;
  final Color color;

  @override
  State<_PulseSpinner> createState() => _PulseSpinnerState();
}

class _PulseSpinnerState extends State<_PulseSpinner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: widget.color.withOpacity(0.3 + (_animation.value * 0.7)),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }
}
