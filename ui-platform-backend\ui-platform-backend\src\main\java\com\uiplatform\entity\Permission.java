package com.uiplatform.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * Permission entity for fine-grained access control.
 */
@Entity
@Table(name = "permissions", indexes = {
    @Index(name = "idx_permission_name", columnList = "name", unique = true),
    @Index(name = "idx_permission_resource_action", columnList = "resource, action", unique = true)
})
public class Permission extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, unique = true, length = 100)
    private String name;

    @Size(max = 200)
    @Column(name = "description", length = 200)
    private String description;

    @NotBlank
    @Size(max = 50)
    @Column(name = "resource", nullable = false, length = 50)
    private String resource;

    @NotBlank
    @Size(max = 20)
    @Column(name = "action", nullable = false, length = 20)
    private String action;

    @Enumerated(EnumType.STRING)
    @Column(name = "scope", nullable = false)
    private PermissionScope scope = PermissionScope.ORGANIZATION;

    // Relationships
    @ManyToMany(mappedBy = "permissions", fetch = FetchType.LAZY)
    private Set<Role> roles = new HashSet<>();

    // Constructors
    public Permission() {}

    public Permission(String name, String description, String resource, String action, PermissionScope scope) {
        this.name = name;
        this.description = description;
        this.resource = resource;
        this.action = action;
        this.scope = scope;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public PermissionScope getScope() {
        return scope;
    }

    public void setScope(PermissionScope scope) {
        this.scope = scope;
    }

    public Set<Role> getRoles() {
        return roles;
    }

    public void setRoles(Set<Role> roles) {
        this.roles = roles;
    }

    // Enums
    public enum PermissionScope {
        SYSTEM,         // System-wide permissions
        ORGANIZATION,   // Organization-scoped permissions
        PROJECT,        // Project-scoped permissions
        RESOURCE        // Resource-specific permissions
    }

    // Static factory methods for common permissions
    public static Permission createUIConfigurationPermission(String action) {
        return new Permission(
            "UI_CONFIGURATION_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " UI configurations",
            "UI_CONFIGURATION",
            action.toUpperCase(),
            PermissionScope.ORGANIZATION
        );
    }

    public static Permission createComponentPermission(String action) {
        return new Permission(
            "COMPONENT_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " components",
            "COMPONENT",
            action.toUpperCase(),
            PermissionScope.ORGANIZATION
        );
    }

    public static Permission createTemplatePermission(String action) {
        return new Permission(
            "TEMPLATE_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " templates",
            "TEMPLATE",
            action.toUpperCase(),
            PermissionScope.ORGANIZATION
        );
    }

    public static Permission createThemePermission(String action) {
        return new Permission(
            "THEME_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " themes",
            "THEME",
            action.toUpperCase(),
            PermissionScope.ORGANIZATION
        );
    }

    public static Permission createUserPermission(String action) {
        return new Permission(
            "USER_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " users",
            "USER",
            action.toUpperCase(),
            PermissionScope.ORGANIZATION
        );
    }

    public static Permission createOrganizationPermission(String action) {
        return new Permission(
            "ORGANIZATION_" + action.toUpperCase(),
            "Permission to " + action.toLowerCase() + " organization",
            "ORGANIZATION",
            action.toUpperCase(),
            PermissionScope.SYSTEM
        );
    }

    // Common permission constants
    public static final class Actions {
        public static final String CREATE = "CREATE";
        public static final String READ = "READ";
        public static final String UPDATE = "UPDATE";
        public static final String DELETE = "DELETE";
        public static final String PUBLISH = "PUBLISH";
        public static final String SHARE = "SHARE";
        public static final String EXPORT = "EXPORT";
        public static final String IMPORT = "IMPORT";
        public static final String MANAGE = "MANAGE";
        public static final String VIEW = "VIEW";
    }

    public static final class Resources {
        public static final String UI_CONFIGURATION = "UI_CONFIGURATION";
        public static final String COMPONENT = "COMPONENT";
        public static final String TEMPLATE = "TEMPLATE";
        public static final String THEME = "THEME";
        public static final String USER = "USER";
        public static final String ORGANIZATION = "ORGANIZATION";
        public static final String ROLE = "ROLE";
        public static final String PERMISSION = "PERMISSION";
        public static final String FORM = "FORM";
        public static final String LAYOUT = "LAYOUT";
    }
}
