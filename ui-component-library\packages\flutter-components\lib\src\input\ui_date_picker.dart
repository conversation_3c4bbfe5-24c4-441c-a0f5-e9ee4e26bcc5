import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Date Picker component
class UIDatePicker extends StatefulWidget {
  const UIDatePicker({
    super.key,
    this.value,
    this.onChanged,
    this.firstDate,
    this.lastDate,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.variant = UIInputVariant.outlined,
    this.size = UISize.md,
    this.fullWidth = true,
    this.required = false,
    this.format,
  });

  final DateTime? value;
  final ValueChanged<DateTime?>? onChanged;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final UIInputVariant variant;
  final UISize size;
  final bool fullWidth;
  final bool required;
  final String? format;

  @override
  State<UIDatePicker> createState() => _UIDatePickerState();
}

class _UIDatePickerState extends State<UIDatePicker> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.value != null ? _formatDate(widget.value!) : '',
    );
  }

  @override
  void didUpdateWidget(UIDatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      _controller.text = widget.value != null ? _formatDate(widget.value!) : '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    if (widget.format != null) {
      // Simple format handling - in a real implementation, use intl package
      return widget.format!
          .replaceAll('yyyy', date.year.toString())
          .replaceAll('MM', date.month.toString().padLeft(2, '0'))
          .replaceAll('dd', date.day.toString().padLeft(2, '0'));
    }
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    Widget datePicker = TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: widget.hint ?? 'Select date',
        helperText: widget.helperText,
        errorText: widget.errorText,
        suffixIcon: Icon(Icons.calendar_today),
        border: OutlineInputBorder(
          borderRadius: tokens.borderRadius.md,
        ),
        contentPadding: EdgeInsets.all(tokens.spacing.size4),
      ),
      readOnly: true,
      enabled: widget.enabled,
      onTap: widget.enabled ? _showDatePicker : null,
    );

    if (!widget.fullWidth) {
      datePicker = IntrinsicWidth(child: datePicker);
    }

    return datePicker;
  }

  Future<void> _showDatePicker() async {
    final now = DateTime.now();
    final firstDate = widget.firstDate ?? DateTime(now.year - 100);
    final lastDate = widget.lastDate ?? DateTime(now.year + 100);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: widget.value ?? now,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (selectedDate != null) {
      widget.onChanged?.call(selectedDate);
    }
  }
}
