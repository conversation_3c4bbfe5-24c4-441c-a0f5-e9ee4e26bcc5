name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ui-builder

jobs:
  # Code Quality and Security
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Cache SonarCloud packages
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Run backend tests
        run: |
          cd ui-metadata-service
          mvn clean verify sonar:sonar \
            -Dsonar.projectKey=ui-builder-backend \
            -Dsonar.organization=ui-builder \
            -Dsonar.host.url=https://sonarcloud.io \
            -Dsonar.login=${{ secrets.SONAR_TOKEN }}

      - name: Install frontend dependencies
        run: |
          cd ui-builder-frontend
          npm ci

      - name: Run frontend tests
        run: |
          cd ui-builder-frontend
          npm run test:coverage

      - name: Run frontend linting
        run: |
          cd ui-builder-frontend
          npm run lint

      - name: Security scan with Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: ./ui-metadata-service/target/site/jacoco/jacoco.xml,./ui-builder-frontend/coverage/lcov.info

  # Build and Test
  build-backend:
    runs-on: ubuntu-latest
    needs: code-quality
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}

      - name: Build backend
        run: |
          cd ui-metadata-service
          mvn clean package -DskipTests

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/ui-metadata-service
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./ui-metadata-service
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  build-frontend:
    runs-on: ubuntu-latest
    needs: code-quality
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: ui-builder-frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd ui-builder-frontend
          npm ci

      - name: Build frontend
        run: |
          cd ui-builder-frontend
          npm run build

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/ui-builder-frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./ui-builder-frontend
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: [build-backend, build-frontend]
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build-backend.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [build-backend, build-frontend]
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: uibuilder_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Run integration tests
        run: |
          cd ui-metadata-service
          mvn test -Dspring.profiles.active=test
        env:
          DB_URL: ***********************************************
          DB_USERNAME: postgres
          DB_PASSWORD: postgres
          REDIS_HOST: localhost
          REDIS_PORT: 6379

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region us-west-2 --name ui-builder-cluster-staging

      - name: Deploy to staging
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|ui-builder/ui-metadata-service:latest|${{ needs.build-backend.outputs.image-tag }}|g" infrastructure/kubernetes/ui-metadata-service.yaml
          sed -i "s|ui-builder/ui-builder-frontend:latest|${{ needs.build-frontend.outputs.image-tag }}|g" infrastructure/kubernetes/ui-builder-frontend.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f infrastructure/kubernetes/ -n ui-builder-staging
          
          # Wait for rollout to complete
          kubectl rollout status deployment/ui-metadata-service -n ui-builder-staging --timeout=300s
          kubectl rollout status deployment/ui-builder-frontend -n ui-builder-staging --timeout=300s

      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          kubectl wait --for=condition=ready pod -l app=ui-metadata-service -n ui-builder-staging --timeout=300s
          
          # Run smoke tests
          curl -f http://staging.uibuilder.dev/api/v1/health || exit 1

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region us-west-2 --name ui-builder-cluster-production

      - name: Blue-Green Deployment
        run: |
          # Create new deployment with green suffix
          sed -i "s|ui-metadata-service|ui-metadata-service-green|g" infrastructure/kubernetes/ui-metadata-service.yaml
          sed -i "s|ui-builder/ui-metadata-service:latest|${{ needs.build-backend.outputs.image-tag }}|g" infrastructure/kubernetes/ui-metadata-service.yaml
          
          # Deploy green version
          kubectl apply -f infrastructure/kubernetes/ui-metadata-service.yaml -n ui-builder
          
          # Wait for green deployment to be ready
          kubectl rollout status deployment/ui-metadata-service-green -n ui-builder --timeout=600s
          
          # Run health checks on green deployment
          kubectl wait --for=condition=ready pod -l app=ui-metadata-service-green -n ui-builder --timeout=300s
          
          # Switch traffic to green deployment
          kubectl patch service ui-metadata-service -n ui-builder -p '{"spec":{"selector":{"app":"ui-metadata-service-green"}}}'
          
          # Wait and verify
          sleep 30
          curl -f https://api.uibuilder.dev/health || exit 1
          
          # Clean up old blue deployment
          kubectl delete deployment ui-metadata-service -n ui-builder --ignore-not-found=true
          
          # Rename green to blue for next deployment
          kubectl patch deployment ui-metadata-service-green -n ui-builder -p '{"metadata":{"name":"ui-metadata-service"}}'

      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: |
            🚀 Production deployment successful!
            
            **Service:** UI Builder
            **Version:** ${{ github.sha }}
            **Environment:** Production
            **Deployed by:** ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Rollback capability
  rollback:
    runs-on: ubuntu-latest
    if: failure() && github.ref == 'refs/heads/main'
    needs: [deploy-production]
    environment: production
    steps:
      - name: Rollback deployment
        run: |
          kubectl rollout undo deployment/ui-metadata-service -n ui-builder
          kubectl rollout status deployment/ui-metadata-service -n ui-builder --timeout=300s

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#deployments'
          text: |
            ⚠️ Production deployment failed and was rolled back!
            
            **Service:** UI Builder
            **Version:** ${{ github.sha }}
            **Environment:** Production
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
