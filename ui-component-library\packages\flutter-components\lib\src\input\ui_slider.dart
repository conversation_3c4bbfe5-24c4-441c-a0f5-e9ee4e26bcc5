import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Slider component
class UISlider extends StatelessWidget {
  const UISlider({
    super.key,
    required this.value,
    required this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
    this.label,
    this.enabled = true,
    this.variant = UIColorVariant.primary,
    this.showValue = false,
    this.prefix,
    this.suffix,
  });

  final double value;
  final ValueChanged<double>? onChanged;
  final double min;
  final double max;
  final int? divisions;
  final String? label;
  final bool enabled;
  final UIColorVariant variant;
  final bool showValue;
  final String? prefix;
  final String? suffix;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    Widget slider = Slider(
      value: value,
      onChanged: enabled ? onChanged : null,
      min: min,
      max: max,
      divisions: divisions,
      label: _getSliderLabel(),
      activeColor: variant.getColor(colorScheme),
    );

    if (label != null || showValue) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (showValue)
                  Text(
                    '${prefix ?? ''}${value.toStringAsFixed(divisions != null ? 0 : 1)}${suffix ?? ''}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
            SizedBox(height: tokens.spacing.size1),
          ],
          slider,
        ],
      );
    }

    return slider;
  }

  String? _getSliderLabel() {
    if (divisions != null) {
      return '${prefix ?? ''}${value.round()}${suffix ?? ''}';
    }
    return null;
  }
}
