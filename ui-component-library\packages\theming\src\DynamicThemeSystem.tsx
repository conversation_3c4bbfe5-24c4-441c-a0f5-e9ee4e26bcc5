import React, { createContext, useContext, useEffect, useState, useMemo } from 'react';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';

export interface ThemeToken {
  name: string;
  value: string | number;
  type: 'color' | 'spacing' | 'typography' | 'shadow' | 'border' | 'animation';
  category?: string;
  description?: string;
}

export interface ThemeVariant {
  id: string;
  name: string;
  description?: string;
  tokens: Record<string, ThemeToken>;
  extends?: string; // Base theme to extend
}

export interface ThemeBreakpoint {
  name: string;
  minWidth: number;
  maxWidth?: number;
}

export interface ResponsiveThemeValue<T = any> {
  default: T;
  breakpoints?: Partial<Record<string, T>>;
}

export interface DynamicTheme {
  id: string;
  name: string;
  description?: string;
  variants: Record<string, ThemeVariant>;
  breakpoints: ThemeBreakpoint[];
  defaultVariant: string;
  customProperties?: Record<string, any>;
}

export interface ThemeContextValue {
  theme: DynamicTheme;
  currentVariant: string;
  currentBreakpoint: string;
  tokens: Record<string, ThemeToken>;
  setVariant: (variantId: string) => void;
  getToken: (tokenName: string) => ThemeToken | undefined;
  getTokenValue: (tokenName: string) => string | number | undefined;
  getResponsiveValue: <T>(value: ResponsiveThemeValue<T>) => T;
  applyTheme: (element: HTMLElement) => void;
  generateCSS: () => string;
}

const ThemeContext = createContext<ThemeContextValue | null>(null);

/**
 * Dynamic Theme System for Component Library
 * 
 * Provides comprehensive theming capabilities including:
 * - Design token management
 * - Theme variants (light, dark, high contrast, etc.)
 * - Responsive theming
 * - CSS custom properties generation
 * - Runtime theme switching
 * - Theme inheritance and composition
 */
export class DynamicThemeSystem {
  private themes = new Map<string, DynamicTheme>();
  private currentTheme: DynamicTheme | null = null;
  private currentVariant = 'default';
  private currentBreakpoint = 'default';
  private listeners = new Set<() => void>();

  /**
   * Register a theme
   */
  registerTheme(theme: DynamicTheme): void {
    this.themes.set(theme.id, theme);
  }

  /**
   * Get theme by ID
   */
  getTheme(themeId: string): DynamicTheme | undefined {
    return this.themes.get(themeId);
  }

  /**
   * Set current theme
   */
  setTheme(themeId: string): void {
    const theme = this.themes.get(themeId);
    if (theme) {
      this.currentTheme = theme;
      this.currentVariant = theme.defaultVariant;
      this.notifyListeners();
    }
  }

  /**
   * Set current variant
   */
  setVariant(variantId: string): void {
    if (this.currentTheme?.variants[variantId]) {
      this.currentVariant = variantId;
      this.notifyListeners();
    }
  }

  /**
   * Set current breakpoint
   */
  setBreakpoint(breakpointName: string): void {
    this.currentBreakpoint = breakpointName;
    this.notifyListeners();
  }

  /**
   * Get current tokens with inheritance
   */
  getCurrentTokens(): Record<string, ThemeToken> {
    if (!this.currentTheme) return {};

    const variant = this.currentTheme.variants[this.currentVariant];
    if (!variant) return {};

    let tokens = { ...variant.tokens };

    // Apply inheritance
    if (variant.extends) {
      const baseVariant = this.currentTheme.variants[variant.extends];
      if (baseVariant) {
        tokens = { ...baseVariant.tokens, ...tokens };
      }
    }

    return tokens;
  }

  /**
   * Get token value
   */
  getTokenValue(tokenName: string): string | number | undefined {
    const tokens = this.getCurrentTokens();
    return tokens[tokenName]?.value;
  }

  /**
   * Get responsive value based on current breakpoint
   */
  getResponsiveValue<T>(value: ResponsiveThemeValue<T>): T {
    if (!value.breakpoints) {
      return value.default;
    }

    return value.breakpoints[this.currentBreakpoint] ?? value.default;
  }

  /**
   * Generate CSS custom properties
   */
  generateCSS(): string {
    const tokens = this.getCurrentTokens();
    const cssVars: string[] = [];

    Object.entries(tokens).forEach(([name, token]) => {
      const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      cssVars.push(`  ${cssVarName}: ${token.value};`);
    });

    return `:root {\n${cssVars.join('\n')}\n}`;
  }

  /**
   * Apply theme to DOM element
   */
  applyTheme(element: HTMLElement): void {
    const tokens = this.getCurrentTokens();

    Object.entries(tokens).forEach(([name, token]) => {
      const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      element.style.setProperty(cssVarName, String(token.value));
    });
  }

  /**
   * Subscribe to theme changes
   */
  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify listeners of theme changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  /**
   * Create theme variant
   */
  createVariant(
    themeId: string,
    variantId: string,
    variant: Omit<ThemeVariant, 'id'>
  ): void {
    const theme = this.themes.get(themeId);
    if (theme) {
      theme.variants[variantId] = { ...variant, id: variantId };
    }
  }

  /**
   * Update theme tokens
   */
  updateTokens(themeId: string, variantId: string, tokens: Record<string, ThemeToken>): void {
    const theme = this.themes.get(themeId);
    if (theme?.variants[variantId]) {
      theme.variants[variantId].tokens = { ...theme.variants[variantId].tokens, ...tokens };
      this.notifyListeners();
    }
  }

  /**
   * Get all themes
   */
  getAllThemes(): DynamicTheme[] {
    return Array.from(this.themes.values());
  }

  /**
   * Export theme configuration
   */
  exportTheme(themeId: string): DynamicTheme | null {
    return this.themes.get(themeId) || null;
  }

  /**
   * Import theme configuration
   */
  importTheme(themeData: DynamicTheme): void {
    this.registerTheme(themeData);
  }
}

// Global theme system instance
export const themeSystem = new DynamicThemeSystem();

/**
 * Theme Provider Component
 */
export interface ThemeProviderProps {
  theme: DynamicTheme;
  variant?: string;
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  theme,
  variant,
  children,
}) => {
  const [currentVariant, setCurrentVariant] = useState(variant || theme.defaultVariant);
  const [currentBreakpoint, setCurrentBreakpoint] = useState('default');

  // Set up responsive breakpoint detection
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      const breakpoint = theme.breakpoints.find(bp => 
        width >= bp.minWidth && (!bp.maxWidth || width <= bp.maxWidth)
      );
      setCurrentBreakpoint(breakpoint?.name || 'default');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, [theme.breakpoints]);

  // Memoize tokens with inheritance
  const tokens = useMemo(() => {
    const variant = theme.variants[currentVariant];
    if (!variant) return {};

    let tokens = { ...variant.tokens };

    // Apply inheritance
    if (variant.extends) {
      const baseVariant = theme.variants[variant.extends];
      if (baseVariant) {
        tokens = { ...baseVariant.tokens, ...tokens };
      }
    }

    return tokens;
  }, [theme, currentVariant]);

  // Apply CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    
    Object.entries(tokens).forEach(([name, token]) => {
      const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVarName, String(token.value));
    });

    return () => {
      Object.keys(tokens).forEach(name => {
        const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
        root.style.removeProperty(cssVarName);
      });
    };
  }, [tokens]);

  const contextValue: ThemeContextValue = {
    theme,
    currentVariant,
    currentBreakpoint,
    tokens,
    setVariant: setCurrentVariant,
    getToken: (tokenName: string) => tokens[tokenName],
    getTokenValue: (tokenName: string) => tokens[tokenName]?.value,
    getResponsiveValue: <T>(value: ResponsiveThemeValue<T>) => {
      if (!value.breakpoints) return value.default;
      return value.breakpoints[currentBreakpoint] ?? value.default;
    },
    applyTheme: (element: HTMLElement) => {
      Object.entries(tokens).forEach(([name, token]) => {
        const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
        element.style.setProperty(cssVarName, String(token.value));
      });
    },
    generateCSS: () => {
      const cssVars = Object.entries(tokens).map(([name, token]) => {
        const cssVarName = `--${name.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
        return `  ${cssVarName}: ${token.value};`;
      });
      return `:root {\n${cssVars.join('\n')}\n}`;
    },
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Hook to get theme token value
 */
export const useToken = (tokenName: string): string | number | undefined => {
  const { getTokenValue } = useTheme();
  return getTokenValue(tokenName);
};

/**
 * Hook to get responsive value
 */
export const useResponsiveValue = <T>(value: ResponsiveThemeValue<T>): T => {
  const { getResponsiveValue } = useTheme();
  return getResponsiveValue(value);
};

/**
 * Higher-order component for theme-aware components
 */
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: ThemeContextValue }>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const theme = useTheme();
    return <Component {...props} theme={theme} ref={ref} />;
  });
};

/**
 * Create default theme
 */
export const createDefaultTheme = (): DynamicTheme => ({
  id: 'default',
  name: 'Default Theme',
  description: 'Default UI Builder theme',
  defaultVariant: 'light',
  breakpoints: [
    { name: 'mobile', minWidth: 0, maxWidth: 767 },
    { name: 'tablet', minWidth: 768, maxWidth: 1023 },
    { name: 'desktop', minWidth: 1024 },
  ],
  variants: {
    light: {
      id: 'light',
      name: 'Light',
      description: 'Light theme variant',
      tokens: {
        // Colors
        colorPrimary: { name: 'colorPrimary', value: '#3b82f6', type: 'color', category: 'colors' },
        colorSecondary: { name: 'colorSecondary', value: '#64748b', type: 'color', category: 'colors' },
        colorSuccess: { name: 'colorSuccess', value: '#10b981', type: 'color', category: 'colors' },
        colorWarning: { name: 'colorWarning', value: '#f59e0b', type: 'color', category: 'colors' },
        colorError: { name: 'colorError', value: '#ef4444', type: 'color', category: 'colors' },
        colorBackground: { name: 'colorBackground', value: '#ffffff', type: 'color', category: 'colors' },
        colorSurface: { name: 'colorSurface', value: '#f8fafc', type: 'color', category: 'colors' },
        colorText: { name: 'colorText', value: '#1e293b', type: 'color', category: 'colors' },
        colorTextMuted: { name: 'colorTextMuted', value: '#64748b', type: 'color', category: 'colors' },
        colorBorder: { name: 'colorBorder', value: '#e2e8f0', type: 'color', category: 'colors' },

        // Spacing
        spacingXs: { name: 'spacingXs', value: '4px', type: 'spacing', category: 'spacing' },
        spacingSm: { name: 'spacingSm', value: '8px', type: 'spacing', category: 'spacing' },
        spacingMd: { name: 'spacingMd', value: '16px', type: 'spacing', category: 'spacing' },
        spacingLg: { name: 'spacingLg', value: '24px', type: 'spacing', category: 'spacing' },
        spacingXl: { name: 'spacingXl', value: '32px', type: 'spacing', category: 'spacing' },
        spacingXxl: { name: 'spacingXxl', value: '48px', type: 'spacing', category: 'spacing' },

        // Typography
        fontSizeXs: { name: 'fontSizeXs', value: '12px', type: 'typography', category: 'typography' },
        fontSizeSm: { name: 'fontSizeSm', value: '14px', type: 'typography', category: 'typography' },
        fontSizeMd: { name: 'fontSizeMd', value: '16px', type: 'typography', category: 'typography' },
        fontSizeLg: { name: 'fontSizeLg', value: '18px', type: 'typography', category: 'typography' },
        fontSizeXl: { name: 'fontSizeXl', value: '20px', type: 'typography', category: 'typography' },
        fontSizeXxl: { name: 'fontSizeXxl', value: '24px', type: 'typography', category: 'typography' },
        fontWeightNormal: { name: 'fontWeightNormal', value: '400', type: 'typography', category: 'typography' },
        fontWeightMedium: { name: 'fontWeightMedium', value: '500', type: 'typography', category: 'typography' },
        fontWeightSemibold: { name: 'fontWeightSemibold', value: '600', type: 'typography', category: 'typography' },
        fontWeightBold: { name: 'fontWeightBold', value: '700', type: 'typography', category: 'typography' },

        // Borders
        borderWidthThin: { name: 'borderWidthThin', value: '1px', type: 'border', category: 'borders' },
        borderWidthMedium: { name: 'borderWidthMedium', value: '2px', type: 'border', category: 'borders' },
        borderWidthThick: { name: 'borderWidthThick', value: '4px', type: 'border', category: 'borders' },
        borderRadiusSm: { name: 'borderRadiusSm', value: '4px', type: 'border', category: 'borders' },
        borderRadiusMd: { name: 'borderRadiusMd', value: '8px', type: 'border', category: 'borders' },
        borderRadiusLg: { name: 'borderRadiusLg', value: '12px', type: 'border', category: 'borders' },
        borderRadiusRound: { name: 'borderRadiusRound', value: '9999px', type: 'border', category: 'borders' },

        // Shadows
        shadowSm: { name: 'shadowSm', value: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', type: 'shadow', category: 'shadows' },
        shadowMd: { name: 'shadowMd', value: '0 4px 6px -1px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'shadows' },
        shadowLg: { name: 'shadowLg', value: '0 10px 15px -3px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'shadows' },
        shadowXl: { name: 'shadowXl', value: '0 20px 25px -5px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'shadows' },

        // Animations
        transitionFast: { name: 'transitionFast', value: '150ms ease-in-out', type: 'animation', category: 'animations' },
        transitionNormal: { name: 'transitionNormal', value: '300ms ease-in-out', type: 'animation', category: 'animations' },
        transitionSlow: { name: 'transitionSlow', value: '500ms ease-in-out', type: 'animation', category: 'animations' },
      },
    },
    dark: {
      id: 'dark',
      name: 'Dark',
      description: 'Dark theme variant',
      extends: 'light',
      tokens: {
        colorBackground: { name: 'colorBackground', value: '#0f172a', type: 'color', category: 'colors' },
        colorSurface: { name: 'colorSurface', value: '#1e293b', type: 'color', category: 'colors' },
        colorText: { name: 'colorText', value: '#f1f5f9', type: 'color', category: 'colors' },
        colorTextMuted: { name: 'colorTextMuted', value: '#94a3b8', type: 'color', category: 'colors' },
        colorBorder: { name: 'colorBorder', value: '#334155', type: 'color', category: 'colors' },
      },
    },
  },
});

// Initialize default theme
themeSystem.registerTheme(createDefaultTheme());
