import { setupServer } from 'msw/node';
import { rest } from 'msw';

// Mock API responses
const mockConfiguration = {
  id: 'test-config-1',
  name: 'Test Configuration',
  version: '1.0.0',
  components: [
    {
      id: 'comp-1',
      type: 'text',
      properties: {
        text: 'Hello World',
        fontSize: '16px',
        color: '#000000',
      },
      style: {
        margin: '10px',
        padding: '5px',
      },
      children: [],
    },
    {
      id: 'comp-2',
      type: 'button',
      properties: {
        text: 'Click Me',
        variant: 'primary',
        disabled: false,
      },
      actions: {
        onClick: {
          type: 'navigate',
          params: {
            route: '/dashboard',
          },
        },
      },
      children: [],
    },
  ],
  theme: {
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
      },
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
    },
    borderRadius: {
      sm: '4px',
      md: '8px',
      lg: '12px',
      full: '9999px',
    },
  },
  layout: {
    type: 'container',
    properties: {
      direction: 'column',
      gap: '16px',
      padding: '20px',
    },
  },
  metadata: {
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'test-user',
    tags: ['test', 'example'],
  },
};

const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Test User',
  avatar: 'https://example.com/avatar.jpg',
  roles: ['user'],
  permissions: ['read', 'write'],
  organizationId: 'org-1',
};

const mockAuthResponse = {
  user: mockUser,
  token: 'mock-jwt-token',
  refreshToken: 'mock-refresh-token',
  expiresIn: 3600,
};

// Request handlers
export const handlers = [
  // Configuration endpoints
  rest.get('/api/configurations/:id', (req, res, ctx) => {
    const { id } = req.params;
    
    if (id === 'not-found') {
      return res(ctx.status(404), ctx.json({ error: 'Configuration not found' }));
    }
    
    if (id === 'error') {
      return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        ...mockConfiguration,
        id: id as string,
      })
    );
  }),

  rest.get('/api/configurations', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1';
    const limit = req.url.searchParams.get('limit') || '10';
    const search = req.url.searchParams.get('search');
    
    let configurations = [mockConfiguration];
    
    if (search) {
      configurations = configurations.filter(config =>
        config.name.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        data: configurations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: configurations.length,
          totalPages: Math.ceil(configurations.length / parseInt(limit)),
        },
      })
    );
  }),

  rest.post('/api/configurations', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        ...mockConfiguration,
        id: 'new-config-id',
      })
    );
  }),

  rest.put('/api/configurations/:id', (req, res, ctx) => {
    const { id } = req.params;
    
    return res(
      ctx.status(200),
      ctx.json({
        ...mockConfiguration,
        id: id as string,
      })
    );
  }),

  rest.delete('/api/configurations/:id', (req, res, ctx) => {
    return res(ctx.status(204));
  }),

  // Component endpoints
  rest.get('/api/components', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: mockConfiguration.components,
      })
    );
  }),

  rest.get('/api/components/:id', (req, res, ctx) => {
    const { id } = req.params;
    const component = mockConfiguration.components.find(c => c.id === id);
    
    if (!component) {
      return res(ctx.status(404), ctx.json({ error: 'Component not found' }));
    }
    
    return res(ctx.status(200), ctx.json(component));
  }),

  // Theme endpoints
  rest.get('/api/themes', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: [
          {
            id: 'theme-1',
            name: 'Default Theme',
            ...mockConfiguration.theme,
          },
        ],
      })
    );
  }),

  rest.get('/api/themes/:id', (req, res, ctx) => {
    const { id } = req.params;
    
    return res(
      ctx.status(200),
      ctx.json({
        id: id as string,
        name: 'Test Theme',
        ...mockConfiguration.theme,
      })
    );
  }),

  // Authentication endpoints
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockAuthResponse));
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ message: 'Logged out successfully' }));
  }),

  rest.post('/api/auth/refresh', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockAuthResponse));
  }),

  rest.get('/api/auth/me', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }));
    }
    
    return res(ctx.status(200), ctx.json(mockUser));
  }),

  // Health check
  rest.get('/api/health', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      })
    );
  }),

  // Error simulation endpoints
  rest.get('/api/error/500', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('/api/error/timeout', (req, res, ctx) => {
    return res(ctx.delay('infinite'));
  }),

  rest.get('/api/error/network', (req, res, ctx) => {
    return res.networkError('Network error');
  }),

  // WebSocket simulation (for testing purposes)
  rest.get('/api/ws/connect', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        url: 'ws://localhost:3001/ws',
        token: 'ws-token',
      })
    );
  }),
];

// Create server instance
export const server = setupServer(...handlers);

// Helper functions for tests
export const mockApiError = (endpoint: string, status: number, message: string) => {
  server.use(
    rest.get(endpoint, (req, res, ctx) => {
      return res(ctx.status(status), ctx.json({ error: message }));
    })
  );
};

export const mockApiSuccess = (endpoint: string, data: any) => {
  server.use(
    rest.get(endpoint, (req, res, ctx) => {
      return res(ctx.status(200), ctx.json(data));
    })
  );
};

export const mockApiDelay = (endpoint: string, delay: number) => {
  server.use(
    rest.get(endpoint, (req, res, ctx) => {
      return res(ctx.delay(delay), ctx.status(200), ctx.json({}));
    })
  );
};

export { mockConfiguration, mockUser, mockAuthResponse };

// Additional mock data for testing
export const mockComponents = [
  {
    id: 'text-component',
    type: 'text',
    name: 'Text Component',
    category: 'Display',
    properties: {
      text: { type: 'string', default: 'Sample text' },
      fontSize: { type: 'string', default: '16px' },
      color: { type: 'string', default: '#000000' },
      fontWeight: { type: 'string', default: 'normal' },
    },
    requiredProperties: ['text'],
  },
  {
    id: 'button-component',
    type: 'button',
    name: 'Button Component',
    category: 'Interactive',
    properties: {
      text: { type: 'string', default: 'Button' },
      variant: { type: 'string', default: 'primary' },
      disabled: { type: 'boolean', default: false },
      size: { type: 'string', default: 'medium' },
    },
    requiredProperties: ['text'],
    actions: {
      onClick: { type: 'function' },
    },
  },
  {
    id: 'input-component',
    type: 'input',
    name: 'Input Component',
    category: 'Form',
    properties: {
      placeholder: { type: 'string', default: 'Enter text...' },
      type: { type: 'string', default: 'text' },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false },
      value: { type: 'string', default: '' },
    },
    actions: {
      onChange: { type: 'function' },
      onFocus: { type: 'function' },
      onBlur: { type: 'function' },
    },
  },
];

export const mockThemes = [
  {
    id: 'light-theme',
    name: 'Light Theme',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
      },
    },
  },
  {
    id: 'dark-theme',
    name: 'Dark Theme',
    colors: {
      primary: '#60a5fa',
      secondary: '#94a3b8',
      success: '#34d399',
      warning: '#fbbf24',
      error: '#f87171',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
      },
    },
  },
];
