import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage: number;
  bundleSize: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
}

interface PerformanceSettings {
  enableMetrics: boolean;
  enableLazyLoading: boolean;
  enableVirtualization: boolean;
  enableMemoization: boolean;
  enableCodeSplitting: boolean;
  maxComponentsPerChunk: number;
  lazyLoadThreshold: number;
  virtualizationThreshold: number;
}

interface PerformanceContextValue {
  metrics: PerformanceMetrics;
  settings: PerformanceSettings;
  updateSettings: (updates: Partial<PerformanceSettings>) => void;
  
  // Performance utilities
  measureRender: (componentName: string, renderFn: () => void) => void;
  trackMemoryUsage: () => void;
  optimizeBundle: () => void;
  
  // Component optimization
  shouldLazyLoad: (componentName: string) => boolean;
  shouldVirtualize: (itemCount: number) => boolean;
  shouldMemoize: (props: any) => boolean;
}

const defaultMetrics: PerformanceMetrics = {
  renderTime: 0,
  componentCount: 0,
  memoryUsage: 0,
  bundleSize: 0,
  firstContentfulPaint: 0,
  largestContentfulPaint: 0,
  cumulativeLayoutShift: 0,
  firstInputDelay: 0,
};

const defaultSettings: PerformanceSettings = {
  enableMetrics: true,
  enableLazyLoading: true,
  enableVirtualization: true,
  enableMemoization: true,
  enableCodeSplitting: true,
  maxComponentsPerChunk: 50,
  lazyLoadThreshold: 100,
  virtualizationThreshold: 1000,
};

const PerformanceContext = createContext<PerformanceContextValue | undefined>(undefined);

interface PerformanceProviderProps {
  children: ReactNode;
  initialSettings?: Partial<PerformanceSettings>;
  enableAutoOptimization?: boolean;
}

export const PerformanceProvider: React.FC<PerformanceProviderProps> = ({
  children,
  initialSettings = {},
  enableAutoOptimization = true,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>(defaultMetrics);
  const [settings, setSettings] = useState<PerformanceSettings>({
    ...defaultSettings,
    ...initialSettings,
  });

  // Performance observer for Web Vitals
  useEffect(() => {
    if (!settings.enableMetrics || typeof window === 'undefined') return;

    // Observe performance entries
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              setMetrics(prev => ({
                ...prev,
                firstContentfulPaint: entry.startTime,
              }));
            }
            break;
            
          case 'largest-contentful-paint':
            setMetrics(prev => ({
              ...prev,
              largestContentfulPaint: entry.startTime,
            }));
            break;
            
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              setMetrics(prev => ({
                ...prev,
                cumulativeLayoutShift: prev.cumulativeLayoutShift + (entry as any).value,
              }));
            }
            break;
            
          case 'first-input':
            setMetrics(prev => ({
              ...prev,
              firstInputDelay: (entry as any).processingStart - entry.startTime,
            }));
            break;
        }
      });
    });

    // Observe different entry types
    try {
      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift', 'first-input'] });
    } catch (error) {
      console.warn('Performance observer not supported:', error);
    }

    return () => observer.disconnect();
  }, [settings.enableMetrics]);

  // Memory usage tracking
  useEffect(() => {
    if (!settings.enableMetrics || typeof window === 'undefined') return;

    const trackMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize,
        }));
      }
    };

    const interval = setInterval(trackMemory, 5000);
    return () => clearInterval(interval);
  }, [settings.enableMetrics]);

  // Auto-optimization based on metrics
  useEffect(() => {
    if (!enableAutoOptimization) return;

    // Enable lazy loading if render time is high
    if (metrics.renderTime > 100 && !settings.enableLazyLoading) {
      setSettings(prev => ({ ...prev, enableLazyLoading: true }));
    }

    // Enable virtualization if component count is high
    if (metrics.componentCount > settings.virtualizationThreshold && !settings.enableVirtualization) {
      setSettings(prev => ({ ...prev, enableVirtualization: true }));
    }

    // Enable memoization if memory usage is high
    if (metrics.memoryUsage > 50 * 1024 * 1024 && !settings.enableMemoization) { // 50MB
      setSettings(prev => ({ ...prev, enableMemoization: true }));
    }
  }, [metrics, settings, enableAutoOptimization]);

  const updateSettings = useCallback((updates: Partial<PerformanceSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  }, []);

  const measureRender = useCallback((componentName: string, renderFn: () => void) => {
    if (!settings.enableMetrics) {
      renderFn();
      return;
    }

    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    setMetrics(prev => ({
      ...prev,
      renderTime: Math.max(prev.renderTime, renderTime),
      componentCount: prev.componentCount + 1,
    }));

    // Log slow renders
    if (renderTime > 16) { // 16ms for 60fps
      console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }, [settings.enableMetrics]);

  const trackMemoryUsage = useCallback(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) return;

    const memory = (performance as any).memory;
    setMetrics(prev => ({
      ...prev,
      memoryUsage: memory.usedJSHeapSize,
    }));
  }, []);

  const optimizeBundle = useCallback(() => {
    // This would trigger bundle optimization strategies
    console.log('Optimizing bundle...');
    
    // Example optimizations:
    // - Tree shaking unused components
    // - Code splitting by route
    // - Lazy loading heavy components
    // - Compressing assets
  }, []);

  const shouldLazyLoad = useCallback((componentName: string) => {
    if (!settings.enableLazyLoading) return false;
    
    // Lazy load components that are not immediately visible
    // or are heavy/rarely used
    const heavyComponents = ['Chart', 'DataTable', 'RichTextEditor', 'VideoPlayer'];
    return heavyComponents.some(heavy => componentName.includes(heavy));
  }, [settings.enableLazyLoading]);

  const shouldVirtualize = useCallback((itemCount: number) => {
    return settings.enableVirtualization && itemCount > settings.virtualizationThreshold;
  }, [settings.enableVirtualization, settings.virtualizationThreshold]);

  const shouldMemoize = useCallback((props: any) => {
    if (!settings.enableMemoization) return false;
    
    // Memoize components with complex props or expensive computations
    const hasComplexProps = typeof props === 'object' && 
      Object.keys(props).length > 5;
    const hasArrayProps = Object.values(props).some(value => Array.isArray(value));
    const hasObjectProps = Object.values(props).some(value => 
      typeof value === 'object' && value !== null && !Array.isArray(value)
    );
    
    return hasComplexProps || hasArrayProps || hasObjectProps;
  }, [settings.enableMemoization]);

  const contextValue: PerformanceContextValue = {
    metrics,
    settings,
    updateSettings,
    measureRender,
    trackMemoryUsage,
    optimizeBundle,
    shouldLazyLoad,
    shouldVirtualize,
    shouldMemoize,
  };

  return (
    <PerformanceContext.Provider value={contextValue}>
      {children}
    </PerformanceContext.Provider>
  );
};

export const usePerformance = (): PerformanceContextValue => {
  const context = useContext(PerformanceContext);
  if (context === undefined) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  return context;
};

// Performance monitoring hook
export const usePerformanceMonitor = (componentName: string) => {
  const { measureRender, trackMemoryUsage } = usePerformance();
  
  useEffect(() => {
    trackMemoryUsage();
  }, [trackMemoryUsage]);

  return {
    measureRender: (renderFn: () => void) => measureRender(componentName, renderFn),
    trackMemoryUsage,
  };
};

// Lazy loading hook
export const useLazyLoading = (componentName: string) => {
  const { shouldLazyLoad } = usePerformance();
  const [isLoaded, setIsLoaded] = useState(!shouldLazyLoad(componentName));
  
  const loadComponent = useCallback(() => {
    setIsLoaded(true);
  }, []);

  return {
    isLoaded,
    loadComponent,
    shouldLazyLoad: shouldLazyLoad(componentName),
  };
};

// Virtualization hook
export const useVirtualization = (itemCount: number) => {
  const { shouldVirtualize } = usePerformance();
  
  return {
    shouldVirtualize: shouldVirtualize(itemCount),
    estimatedItemSize: 50, // Default item height
    overscan: 5, // Number of items to render outside visible area
  };
};

// Memoization hook
export const useMemoization = (props: any) => {
  const { shouldMemoize } = usePerformance();
  
  return {
    shouldMemoize: shouldMemoize(props),
    memoizedProps: React.useMemo(() => props, [JSON.stringify(props)]),
  };
};

// Performance HOC
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const PerformantComponent = React.memo((props: P) => {
    const { measureRender } = usePerformance();
    const name = componentName || Component.displayName || Component.name || 'Unknown';
    
    const [renderCount, setRenderCount] = useState(0);
    
    useEffect(() => {
      setRenderCount(prev => prev + 1);
    });

    return React.createElement(Component, {
      ...props,
      ref: (ref: any) => {
        measureRender(name, () => {
          // Component render tracked
        });
        
        if (typeof (props as any).ref === 'function') {
          (props as any).ref(ref);
        } else if ((props as any).ref) {
          (props as any).ref.current = ref;
        }
      },
    });
  });

  PerformantComponent.displayName = `withPerformanceTracking(${componentName || Component.displayName || Component.name})`;
  
  return PerformantComponent;
};

// Bundle analyzer utility
export const analyzeBundleSize = () => {
  if (typeof window === 'undefined') return;

  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
  
  const totalSize = [...scripts, ...styles].reduce((total, element) => {
    const src = element.getAttribute('src') || element.getAttribute('href');
    if (src && !src.startsWith('http')) {
      // This would need to be implemented with actual bundle analysis
      return total + 1000; // Placeholder
    }
    return total;
  }, 0);

  console.log(`Estimated bundle size: ${(totalSize / 1024).toFixed(2)} KB`);
  return totalSize;
};

export default PerformanceProvider;
