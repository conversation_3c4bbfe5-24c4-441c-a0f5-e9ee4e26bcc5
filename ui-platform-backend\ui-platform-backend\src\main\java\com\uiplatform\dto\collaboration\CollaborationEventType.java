package com.uiplatform.dto.collaboration;

/**
 * Enum for different types of collaboration events.
 */
public enum CollaborationEventType {
    // User presence events
    USER_JOINED_CONFIG,
    USER_LEFT_CONFIG,
    USER_ONLINE,
    USER_OFFLINE,
    ACTIVE_USERS_UPDATE,
    
    // UI configuration events
    UI_CONFIG_UPDATED,
    COMPONENT_ADDED,
    COMPONENT_UPDATED,
    COMPONENT_DELETED,
    COMPONENT_MOVED,
    
    // Cursor and selection events
    CURSOR_MOVED,
    SELECTION_CHANGED,
    
    // Editing lock events
    LOCK_ACQUIRED,
    LOCK_RELEASED,
    LOCK_CONFLICT,
    
    // Comment events
    COMMENT_ADDED,
    COMMENT_UPDATED,
    COMMENT_DELETED,
    COMMENT_RESOLVED,
    
    // Change history events
    CHANGE_APPLIED,
    CHANGE_REVERTED,
    UNDO_PERFORMED,
    REDO_PERFORMED,
    
    // Notification events
    NOTIFICATION_BROADCAST,
    ERROR_OCCURRED,
    
    // Operational transformation events
    OPERATION_APPLIED,
    OPERATION_TRANSFORMED,
    O<PERSON><PERSON><PERSON>ION_CONFLICT
}
