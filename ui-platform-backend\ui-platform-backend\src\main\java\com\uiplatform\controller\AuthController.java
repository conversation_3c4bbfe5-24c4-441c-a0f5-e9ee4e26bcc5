package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.AuthDTO;
import com.uiplatform.service.AuthenticationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * REST Controller for Authentication and Authorization operations.
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    private final AuthenticationService authenticationService;

    @Autowired
    public AuthController(AuthenticationService authenticationService) {
        this.authenticationService = authenticationService;
    }

    /**
     * User login endpoint.
     */
    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user and return JWT tokens")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Login successful"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Invalid credentials"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "423", description = "Account locked")
    })
    public ResponseEntity<ApiResponse<AuthDTO.LoginResponse>> login(
            @Valid @RequestBody AuthDTO.LoginRequest loginRequest) {
        
        logger.info("Login attempt for user: {}", loginRequest.getUsernameOrEmail());
        
        try {
            AuthDTO.LoginResponse response = authenticationService.login(loginRequest);
            return ResponseEntity.ok(ApiResponse.success("Login successful", response));
        } catch (Exception e) {
            logger.error("Login failed for user: {}", loginRequest.getUsernameOrEmail(), e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Login failed: " + e.getMessage()));
        }
    }

    /**
     * User registration endpoint.
     */
    @PostMapping("/register")
    @Operation(summary = "User registration", description = "Register new user and organization")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Registration successful"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid registration data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "User or organization already exists")
    })
    public ResponseEntity<ApiResponse<AuthDTO.LoginResponse>> register(
            @Valid @RequestBody AuthDTO.RegisterRequest registerRequest) {
        
        logger.info("Registration attempt for user: {} with organization: {}", 
                   registerRequest.getUsername(), registerRequest.getOrganizationName());
        
        try {
            AuthDTO.LoginResponse response = authenticationService.register(registerRequest);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Registration successful", response));
        } catch (Exception e) {
            logger.error("Registration failed for user: {}", registerRequest.getUsername(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Registration failed: " + e.getMessage()));
        }
    }

    /**
     * Refresh JWT token endpoint.
     */
    @PostMapping("/refresh")
    @Operation(summary = "Refresh token", description = "Refresh JWT access token using refresh token")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Invalid refresh token")
    })
    public ResponseEntity<ApiResponse<AuthDTO.LoginResponse>> refreshToken(
            @Valid @RequestBody AuthDTO.RefreshTokenRequest refreshRequest) {
        
        logger.debug("Token refresh attempt");
        
        try {
            AuthDTO.LoginResponse response = authenticationService.refreshToken(refreshRequest);
            return ResponseEntity.ok(ApiResponse.success("Token refreshed successfully", response));
        } catch (Exception e) {
            logger.error("Token refresh failed", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Token refresh failed: " + e.getMessage()));
        }
    }

    /**
     * Change password endpoint.
     */
    @PostMapping("/change-password")
    @Operation(summary = "Change password", description = "Change user password")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Password changed successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid password data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ApiResponse<Void>> changePassword(
            @Valid @RequestBody AuthDTO.ChangePasswordRequest changePasswordRequest,
            Authentication authentication) {
        
        UUID userId = UUID.fromString(authentication.getName());
        logger.info("Password change attempt for user ID: {}", userId);
        
        try {
            authenticationService.changePassword(userId, changePasswordRequest);
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully"));
        } catch (Exception e) {
            logger.error("Password change failed for user ID: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Password change failed: " + e.getMessage()));
        }
    }

    /**
     * Request password reset endpoint.
     */
    @PostMapping("/forgot-password")
    @Operation(summary = "Request password reset", description = "Request password reset email")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Password reset email sent"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid email")
    })
    public ResponseEntity<ApiResponse<Void>> requestPasswordReset(
            @Valid @RequestBody AuthDTO.PasswordResetRequest resetRequest) {
        
        logger.info("Password reset requested for email: {}", resetRequest.getEmail());
        
        try {
            authenticationService.requestPasswordReset(resetRequest);
            return ResponseEntity.ok(ApiResponse.success("Password reset email sent"));
        } catch (Exception e) {
            logger.error("Password reset request failed for email: {}", resetRequest.getEmail(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Password reset request failed: " + e.getMessage()));
        }
    }

    /**
     * Confirm password reset endpoint.
     */
    @PostMapping("/reset-password")
    @Operation(summary = "Reset password", description = "Reset password with token")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Password reset successful"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid or expired token")
    })
    public ResponseEntity<ApiResponse<Void>> confirmPasswordReset(
            @Valid @RequestBody AuthDTO.PasswordResetConfirmRequest confirmRequest) {
        
        logger.info("Password reset confirmation attempt");
        
        try {
            authenticationService.confirmPasswordReset(confirmRequest);
            return ResponseEntity.ok(ApiResponse.success("Password reset successful"));
        } catch (Exception e) {
            logger.error("Password reset confirmation failed", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Password reset failed: " + e.getMessage()));
        }
    }

    /**
     * Verify email endpoint.
     */
    @PostMapping("/verify-email")
    @Operation(summary = "Verify email", description = "Verify user email with token")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Email verified successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid verification token")
    })
    public ResponseEntity<ApiResponse<Void>> verifyEmail(@RequestParam String token) {
        
        logger.info("Email verification attempt");
        
        try {
            authenticationService.verifyEmail(token);
            return ResponseEntity.ok(ApiResponse.success("Email verified successfully"));
        } catch (Exception e) {
            logger.error("Email verification failed", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Email verification failed: " + e.getMessage()));
        }
    }

    /**
     * Logout endpoint.
     */
    @PostMapping("/logout")
    @Operation(summary = "User logout", description = "Logout user and invalidate tokens")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Logout successful")
    })
    public ResponseEntity<ApiResponse<Void>> logout(
            @RequestHeader("Authorization") String authorizationHeader) {
        
        logger.info("Logout attempt");
        
        try {
            String token = authorizationHeader.substring(7); // Remove "Bearer " prefix
            authenticationService.logout(token);
            return ResponseEntity.ok(ApiResponse.success("Logout successful"));
        } catch (Exception e) {
            logger.error("Logout failed", e);
            return ResponseEntity.ok(ApiResponse.success("Logout successful")); // Always return success for logout
        }
    }

    /**
     * Get current user info endpoint.
     */
    @GetMapping("/me")
    @Operation(summary = "Get current user", description = "Get current authenticated user information")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "User information retrieved"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ApiResponse<String>> getCurrentUser(Authentication authentication) {
        
        logger.debug("Current user info request");
        
        try {
            String userId = authentication.getName();
            return ResponseEntity.ok(ApiResponse.success("Current user information", userId));
        } catch (Exception e) {
            logger.error("Failed to get current user info", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("Failed to get user information"));
        }
    }
}
