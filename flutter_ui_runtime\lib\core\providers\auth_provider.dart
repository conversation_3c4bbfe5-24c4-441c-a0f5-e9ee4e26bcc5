import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../services/api_service.dart';
import '../config/app_config.dart';
import '../utils/logger.dart';

/// Authentication state
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final String? token;
  final String? refreshToken;
  final Map<String, dynamic>? user;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.token,
    this.refreshToken,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    String? token,
    String? refreshToken,
    Map<String, dynamic>? user,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      token: token ?? this.token,
      refreshToken: refreshToken ?? this.refreshToken,
      user: user ?? this.user,
      error: error,
    );
  }
}

/// Authentication provider
class AuthNotifier extends StateNotifier<AuthState> {
  final ApiService _apiService;
  final FlutterSecureStorage _storage;

  AuthNotifier(this._apiService, this._storage) : super(const AuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    try {
      state = state.copyWith(isLoading: true);
      
      final token = await _storage.read(key: AppConfig.authTokenKey);
      final refreshToken = await _storage.read(key: AppConfig.refreshTokenKey);
      
      if (token != null && refreshToken != null) {
        // Validate token with backend
        final isValid = await _validateToken(token);
        if (isValid) {
          final user = await _fetchUserProfile(token);
          state = state.copyWith(
            isAuthenticated: true,
            token: token,
            refreshToken: refreshToken,
            user: user,
            isLoading: false,
          );
        } else {
          // Try to refresh token
          await _refreshToken();
        }
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize auth', error: error, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize authentication',
      );
    }
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.login(email, password);
      
      if (response['success'] == true) {
        final token = response['token'] as String;
        final refreshToken = response['refreshToken'] as String;
        final user = response['user'] as Map<String, dynamic>;
        
        // Store tokens securely
        await _storage.write(key: AppConfig.authTokenKey, value: token);
        await _storage.write(key: AppConfig.refreshTokenKey, value: refreshToken);
        
        state = state.copyWith(
          isAuthenticated: true,
          token: token,
          refreshToken: refreshToken,
          user: user,
          isLoading: false,
        );
        
        AppLogger.info('User logged in successfully');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? 'Login failed',
        );
        return false;
      }
    } catch (error, stackTrace) {
      AppLogger.error('Login failed', error: error, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed. Please try again.',
      );
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Clear stored tokens
      await _storage.delete(key: AppConfig.authTokenKey);
      await _storage.delete(key: AppConfig.refreshTokenKey);
      
      // Notify backend
      if (state.token != null) {
        await _apiService.logout(state.token!);
      }
      
      state = const AuthState();
      AppLogger.info('User logged out successfully');
    } catch (error, stackTrace) {
      AppLogger.error('Logout failed', error: error, stackTrace: stackTrace);
      // Still clear local state even if backend call fails
      state = const AuthState();
    }
  }

  /// Refresh authentication token
  Future<bool> _refreshToken() async {
    try {
      if (state.refreshToken == null) return false;
      
      final response = await _apiService.refreshToken(state.refreshToken!);
      
      if (response['success'] == true) {
        final newToken = response['token'] as String;
        final newRefreshToken = response['refreshToken'] as String;
        
        await _storage.write(key: AppConfig.authTokenKey, value: newToken);
        await _storage.write(key: AppConfig.refreshTokenKey, value: newRefreshToken);
        
        state = state.copyWith(
          token: newToken,
          refreshToken: newRefreshToken,
          isAuthenticated: true,
        );
        
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (error, stackTrace) {
      AppLogger.error('Token refresh failed', error: error, stackTrace: stackTrace);
      await logout();
      return false;
    }
  }

  /// Validate token with backend
  Future<bool> _validateToken(String token) async {
    try {
      final response = await _apiService.validateToken(token);
      return response['valid'] == true;
    } catch (error) {
      return false;
    }
  }

  /// Fetch user profile
  Future<Map<String, dynamic>?> _fetchUserProfile(String token) async {
    try {
      final response = await _apiService.getUserProfile(token);
      return response['user'] as Map<String, dynamic>?;
    } catch (error) {
      return null;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  const storage = FlutterSecureStorage();
  return AuthNotifier(apiService, storage);
});

/// Current user provider
final currentUserProvider = Provider<Map<String, dynamic>?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

/// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isAuthenticated;
});
