import { io, Socket } from 'socket.io-client';
import { ComponentMetadata, UIConfiguration } from '../types/runtime';

export interface RealtimeEvent {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
  sessionId?: string;
}

export interface ConfigurationUpdate {
  type: 'configuration_update';
  configurationId: string;
  changes: Partial<UIConfiguration>;
  version: number;
  userId: string;
}

export interface ComponentUpdate {
  type: 'component_update';
  componentId: string;
  changes: Partial<ComponentMetadata>;
  configurationId: string;
  userId: string;
}

export interface UserPresence {
  type: 'user_presence';
  userId: string;
  username: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away';
  lastSeen: number;
}

export interface RealtimeSubscription {
  id: string;
  event: string;
  callback: (event: RealtimeEvent) => void;
  filter?: (event: RealtimeEvent) => boolean;
}

export interface RealtimeOptions {
  url: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
  auth?: {
    token: string;
  };
}

/**
 * Real-time Update System for Web Runtime
 * 
 * Manages real-time synchronization of UI configurations, component updates,
 * user presence, and collaborative features using WebSocket connections.
 */
export class RealtimeUpdateSystem {
  private socket: Socket | null = null;
  private subscriptions = new Map<string, RealtimeSubscription>();
  private eventQueue: RealtimeEvent[] = [];
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private currentConfigurationId: string | null = null;
  private userPresence = new Map<string, UserPresence>();
  private conflictResolver: ConflictResolver;

  constructor(private options: RealtimeOptions) {
    this.conflictResolver = new ConflictResolver();
    
    if (options.autoConnect !== false) {
      this.connect();
    }
  }

  /**
   * Connect to the real-time server
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      return;
    }

    try {
      this.socket = io(this.options.url, {
        autoConnect: false,
        reconnection: this.options.reconnection !== false,
        reconnectionAttempts: this.options.reconnectionAttempts || 5,
        reconnectionDelay: this.options.reconnectionDelay || 1000,
        timeout: this.options.timeout || 20000,
        auth: this.options.auth
      });

      this.setupEventHandlers();
      this.socket.connect();

      return new Promise((resolve, reject) => {
        this.socket!.on('connect', () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.processEventQueue();
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          this.isConnected = false;
          reject(error);
        });
      });
    } catch (error) {
      console.error('Failed to connect to real-time server:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the real-time server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * Subscribe to real-time events
   */
  subscribe(event: string, callback: (event: RealtimeEvent) => void, filter?: (event: RealtimeEvent) => boolean): string {
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      event,
      callback,
      filter
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Subscribe to socket event if not already subscribed
    if (this.socket && !this.socket.hasListeners(event)) {
      this.socket.on(event, (data) => this.handleEvent(event, data));
    }

    return subscriptionId;
  }

  /**
   * Unsubscribe from real-time events
   */
  unsubscribe(subscriptionId: string): void {
    this.subscriptions.delete(subscriptionId);
  }

  /**
   * Join a configuration room for real-time updates
   */
  joinConfiguration(configurationId: string): void {
    if (!this.socket?.connected) {
      console.warn('Socket not connected, queuing join request');
      this.eventQueue.push({
        type: 'join_configuration',
        payload: { configurationId },
        timestamp: Date.now()
      });
      return;
    }

    this.currentConfigurationId = configurationId;
    this.socket.emit('join_configuration', { configurationId });
  }

  /**
   * Leave a configuration room
   */
  leaveConfiguration(configurationId: string): void {
    if (!this.socket?.connected) return;

    this.socket.emit('leave_configuration', { configurationId });
    
    if (this.currentConfigurationId === configurationId) {
      this.currentConfigurationId = null;
    }
  }

  /**
   * Send configuration update
   */
  sendConfigurationUpdate(configurationId: string, changes: Partial<UIConfiguration>, version: number): void {
    const update: ConfigurationUpdate = {
      type: 'configuration_update',
      configurationId,
      changes,
      version,
      userId: this.getCurrentUserId()
    };

    this.sendEvent(update);
  }

  /**
   * Send component update
   */
  sendComponentUpdate(componentId: string, changes: Partial<ComponentMetadata>, configurationId: string): void {
    const update: ComponentUpdate = {
      type: 'component_update',
      componentId,
      changes,
      configurationId,
      userId: this.getCurrentUserId()
    };

    this.sendEvent(update);
  }

  /**
   * Update user presence
   */
  updatePresence(status: 'online' | 'offline' | 'away'): void {
    const presence: UserPresence = {
      type: 'user_presence',
      userId: this.getCurrentUserId(),
      username: this.getCurrentUsername(),
      status,
      lastSeen: Date.now()
    };

    this.sendEvent(presence);
  }

  /**
   * Get current user presence
   */
  getUserPresence(): Map<string, UserPresence> {
    return new Map(this.userPresence);
  }

  /**
   * Send a real-time event
   */
  private sendEvent(event: RealtimeEvent): void {
    if (!this.socket?.connected) {
      console.warn('Socket not connected, queuing event');
      this.eventQueue.push(event);
      return;
    }

    this.socket.emit(event.type, event);
  }

  /**
   * Handle incoming real-time events
   */
  private handleEvent(eventType: string, data: any): void {
    const event: RealtimeEvent = {
      type: eventType,
      payload: data,
      timestamp: Date.now(),
      userId: data.userId,
      sessionId: data.sessionId
    };

    // Update user presence
    if (event.type === 'user_presence') {
      this.userPresence.set(event.userId!, data as UserPresence);
    }

    // Handle configuration conflicts
    if (event.type === 'configuration_update' || event.type === 'component_update') {
      const resolvedEvent = this.conflictResolver.resolve(event);
      if (!resolvedEvent) {
        console.warn('Event discarded due to conflict resolution');
        return;
      }
      event.payload = resolvedEvent.payload;
    }

    // Notify subscribers
    for (const subscription of this.subscriptions.values()) {
      if (subscription.event === eventType || subscription.event === '*') {
        if (!subscription.filter || subscription.filter(event)) {
          try {
            subscription.callback(event);
          } catch (error) {
            console.error('Error in subscription callback:', error);
          }
        }
      }
    }
  }

  /**
   * Setup socket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      console.log('Disconnected from real-time server:', reason);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true;
      console.log('Reconnected to real-time server after', attemptNumber, 'attempts');
      
      // Rejoin configuration if we were in one
      if (this.currentConfigurationId) {
        this.joinConfiguration(this.currentConfigurationId);
      }
      
      this.processEventQueue();
    });

    this.socket.on('reconnect_error', (error) => {
      this.reconnectAttempts++;
      console.error('Reconnection failed:', error);
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
        this.disconnect();
      }
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }

  /**
   * Process queued events when connection is restored
   */
  private processEventQueue(): void {
    while (this.eventQueue.length > 0 && this.socket?.connected) {
      const event = this.eventQueue.shift()!;
      
      if (event.type === 'join_configuration') {
        this.joinConfiguration(event.payload.configurationId);
      } else {
        this.sendEvent(event);
      }
    }
  }

  /**
   * Get current user ID
   */
  private getCurrentUserId(): string {
    // This would typically come from authentication context
    return 'current-user-id';
  }

  /**
   * Get current username
   */
  private getCurrentUsername(): string {
    // This would typically come from authentication context
    return 'current-username';
  }

  /**
   * Check if connected
   */
  isConnectedToServer(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    queuedEvents: number;
  } {
    return {
      connected: this.isConnectedToServer(),
      reconnectAttempts: this.reconnectAttempts,
      queuedEvents: this.eventQueue.length
    };
  }

  /**
   * Clear all subscriptions
   */
  clearSubscriptions(): void {
    this.subscriptions.clear();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.clearSubscriptions();
    this.disconnect();
    this.eventQueue = [];
    this.userPresence.clear();
  }
}

/**
 * Conflict Resolution System
 * 
 * Handles conflicts when multiple users edit the same configuration simultaneously
 */
class ConflictResolver {
  private lastKnownVersions = new Map<string, number>();

  /**
   * Resolve conflicts in real-time events
   */
  resolve(event: RealtimeEvent): RealtimeEvent | null {
    if (event.type === 'configuration_update') {
      return this.resolveConfigurationConflict(event);
    }
    
    if (event.type === 'component_update') {
      return this.resolveComponentConflict(event);
    }

    return event;
  }

  /**
   * Resolve configuration update conflicts
   */
  private resolveConfigurationConflict(event: RealtimeEvent): RealtimeEvent | null {
    const update = event.payload as ConfigurationUpdate;
    const lastKnownVersion = this.lastKnownVersions.get(update.configurationId) || 0;

    // If the incoming version is older than what we know, discard it
    if (update.version <= lastKnownVersion) {
      console.warn('Discarding outdated configuration update');
      return null;
    }

    // Update our known version
    this.lastKnownVersions.set(update.configurationId, update.version);

    return event;
  }

  /**
   * Resolve component update conflicts
   */
  private resolveComponentConflict(event: RealtimeEvent): RealtimeEvent | null {
    const update = event.payload as ComponentUpdate;
    
    // For component updates, we use last-write-wins strategy
    // In a more sophisticated system, we might use operational transformation
    
    return event;
  }

  /**
   * Clear version tracking for a configuration
   */
  clearVersions(configurationId: string): void {
    this.lastKnownVersions.delete(configurationId);
  }
}

// Global real-time update system instance
let realtimeSystem: RealtimeUpdateSystem | null = null;

/**
 * Initialize the real-time update system
 */
export function initializeRealtimeSystem(options: RealtimeOptions): RealtimeUpdateSystem {
  if (realtimeSystem) {
    realtimeSystem.cleanup();
  }
  
  realtimeSystem = new RealtimeUpdateSystem(options);
  return realtimeSystem;
}

/**
 * Get the global real-time update system instance
 */
export function getRealtimeSystem(): RealtimeUpdateSystem | null {
  return realtimeSystem;
}

/**
 * Cleanup the global real-time update system
 */
export function cleanupRealtimeSystem(): void {
  if (realtimeSystem) {
    realtimeSystem.cleanup();
    realtimeSystem = null;
  }
}
