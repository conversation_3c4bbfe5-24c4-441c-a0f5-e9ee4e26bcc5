import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Progress component
class UIProgress extends StatelessWidget {
  const UIProgress({
    super.key,
    this.value,
    this.backgroundColor,
    this.valueColor,
    this.strokeWidth = 4.0,
    this.variant = UIColorVariant.primary,
    this.size = UISize.md,
    this.showLabel = false,
    this.label,
  });

  final double? value;
  final Color? backgroundColor;
  final Color? valueColor;
  final double strokeWidth;
  final UIColorVariant variant;
  final UISize size;
  final bool showLabel;
  final String? label;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final effectiveValueColor = valueColor ?? variant.getColor(colorScheme);

    Widget progress = LinearProgressIndicator(
      value: value,
      backgroundColor: backgroundColor ?? colorScheme.surfaceVariant,
      valueColor: AlwaysStoppedAnimation(effectiveValueColor),
      minHeight: strokeWidth,
    );

    if (showLabel || label != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null || (showLabel && value != null)) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (label != null)
                  Text(
                    label!,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                if (showLabel && value != null)
                  Text(
                    '${(value! * 100).round()}%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
            SizedBox(height: DesignTokens.instance.spacing.size1),
          ],
          progress,
        ],
      );
    }

    return progress;
  }
}
