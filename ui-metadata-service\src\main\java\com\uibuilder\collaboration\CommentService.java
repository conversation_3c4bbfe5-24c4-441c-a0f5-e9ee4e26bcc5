package com.uibuilder.collaboration;

import com.uibuilder.entity.UIComment;
import com.uibuilder.entity.User;
import com.uibuilder.entity.UIConfig;
import com.uibuilder.repository.UICommentRepository;
import com.uibuilder.repository.UserRepository;
import com.uibuilder.repository.UIConfigRepository;
import com.uibuilder.security.rbac.RoleBasedAccessControl;
import com.uibuilder.websocket.CollaborationWebSocketHandler;
import com.uibuilder.notification.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommentService {

    private final UICommentRepository commentRepository;
    private final UserRepository userRepository;
    private final UIConfigRepository uiConfigRepository;
    private final RoleBasedAccessControl rbac;
    private final CollaborationWebSocketHandler webSocketHandler;
    private final NotificationService notificationService;

    /**
     * Add a comment to a UI element
     */
    @Transactional
    public UIComment addComment(String configId, String elementId, CommentRequest request, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "comment", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to add comments");
        }

        UIConfig config = uiConfigRepository.findById(configId)
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));

        User user = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("User not found"));

        // Create comment
        UIComment comment = UIComment.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(config)
            .elementId(elementId)
            .content(request.getContent())
            .position(request.getPosition())
            .user(user)
            .status(CommentStatus.OPEN)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        comment = commentRepository.save(comment);

        // Broadcast to collaborators
        CommentEvent event = CommentEvent.builder()
            .type("COMMENT_ADDED")
            .comment(toCommentDto(comment))
            .configId(configId)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(configId, event);

        // Send notifications to workspace members
        notifyWorkspaceMembers(config.getWorkspace().getId(), comment, "COMMENT_ADDED");

        log.info("Comment added to config {} by user {}", configId, userId);
        return comment;
    }

    /**
     * Reply to an existing comment
     */
    @Transactional
    public UIComment replyToComment(String parentCommentId, CommentRequest request, String userId) {
        UIComment parentComment = commentRepository.findById(parentCommentId)
            .orElseThrow(() -> new EntityNotFoundException("Parent comment not found"));

        // Validate access
        String workspaceId = getWorkspaceId(parentComment.getUiConfig().getId());
        if (!rbac.hasPermission(userId, "ui_config", "comment", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to reply to comments");
        }

        User user = userRepository.findById(userId)
            .orElseThrow(() -> new EntityNotFoundException("User not found"));

        // Create reply
        UIComment reply = UIComment.builder()
            .id(UUID.randomUUID().toString())
            .uiConfig(parentComment.getUiConfig())
            .elementId(parentComment.getElementId())
            .content(request.getContent())
            .position(parentComment.getPosition())
            .parentComment(parentComment)
            .user(user)
            .status(CommentStatus.OPEN)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        reply = commentRepository.save(reply);

        // Broadcast reply
        CommentEvent event = CommentEvent.builder()
            .type("COMMENT_REPLY_ADDED")
            .comment(toCommentDto(reply))
            .parentCommentId(parentCommentId)
            .configId(parentComment.getUiConfig().getId())
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(parentComment.getUiConfig().getId(), event);

        // Notify parent comment author
        if (!parentComment.getUser().getId().equals(userId)) {
            notificationService.sendCommentReplyNotification(
                parentComment.getUser().getId(),
                reply,
                parentComment
            );
        }

        return reply;
    }

    /**
     * Update an existing comment
     */
    @Transactional
    public UIComment updateComment(String commentId, CommentRequest request, String userId) {
        UIComment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("Comment not found"));

        // Only comment author or workspace admin can update
        if (!comment.getUser().getId().equals(userId) && 
            !rbac.hasPermission(userId, "workspace", "moderate_comments", 
                               getWorkspaceId(comment.getUiConfig().getId()))) {
            throw new AccessDeniedException("Insufficient permissions to update comment");
        }

        comment.setContent(request.getContent());
        comment.setUpdatedAt(LocalDateTime.now());
        
        if (request.getPosition() != null) {
            comment.setPosition(request.getPosition());
        }

        comment = commentRepository.save(comment);

        // Broadcast update
        CommentEvent event = CommentEvent.builder()
            .type("COMMENT_UPDATED")
            .comment(toCommentDto(comment))
            .configId(comment.getUiConfig().getId())
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(comment.getUiConfig().getId(), event);

        return comment;
    }

    /**
     * Resolve a comment
     */
    @Transactional
    public UIComment resolveComment(String commentId, String userId) {
        UIComment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("Comment not found"));

        // Validate access
        String workspaceId = getWorkspaceId(comment.getUiConfig().getId());
        if (!rbac.hasPermission(userId, "ui_config", "resolve_comments", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to resolve comments");
        }

        comment.setStatus(CommentStatus.RESOLVED);
        comment.setResolvedBy(userId);
        comment.setResolvedAt(LocalDateTime.now());
        comment.setUpdatedAt(LocalDateTime.now());

        comment = commentRepository.save(comment);

        // Resolve all replies as well
        List<UIComment> replies = commentRepository.findByParentCommentAndStatus(comment, CommentStatus.OPEN);
        replies.forEach(reply -> {
            reply.setStatus(CommentStatus.RESOLVED);
            reply.setResolvedBy(userId);
            reply.setResolvedAt(LocalDateTime.now());
            reply.setUpdatedAt(LocalDateTime.now());
        });
        commentRepository.saveAll(replies);

        // Broadcast resolution
        CommentEvent event = CommentEvent.builder()
            .type("COMMENT_RESOLVED")
            .comment(toCommentDto(comment))
            .configId(comment.getUiConfig().getId())
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(comment.getUiConfig().getId(), event);

        // Notify comment author
        if (!comment.getUser().getId().equals(userId)) {
            notificationService.sendCommentResolvedNotification(
                comment.getUser().getId(),
                comment,
                userId
            );
        }

        return comment;
    }

    /**
     * Delete a comment
     */
    @Transactional
    public void deleteComment(String commentId, String userId) {
        UIComment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("Comment not found"));

        // Only comment author or workspace admin can delete
        if (!comment.getUser().getId().equals(userId) && 
            !rbac.hasPermission(userId, "workspace", "moderate_comments", 
                               getWorkspaceId(comment.getUiConfig().getId()))) {
            throw new AccessDeniedException("Insufficient permissions to delete comment");
        }

        String configId = comment.getUiConfig().getId();

        // Delete all replies first
        List<UIComment> replies = commentRepository.findByParentComment(comment);
        commentRepository.deleteAll(replies);

        // Delete the comment
        commentRepository.delete(comment);

        // Broadcast deletion
        CommentEvent event = CommentEvent.builder()
            .type("COMMENT_DELETED")
            .commentId(commentId)
            .configId(configId)
            .userId(userId)
            .timestamp(LocalDateTime.now())
            .build();

        webSocketHandler.broadcastToConfig(configId, event);

        log.info("Comment {} deleted by user {}", commentId, userId);
    }

    /**
     * Get comments for a UI config
     */
    public Page<UIComment> getCommentsForConfig(String configId, CommentFilter filter, 
                                               Pageable pageable, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "read", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to view comments");
        }

        if (filter.getElementId() != null) {
            return commentRepository.findByUiConfigIdAndElementIdAndStatusIn(
                configId, filter.getElementId(), filter.getStatuses(), pageable);
        } else {
            return commentRepository.findByUiConfigIdAndStatusIn(
                configId, filter.getStatuses(), pageable);
        }
    }

    /**
     * Get comment thread (parent comment with all replies)
     */
    public CommentThread getCommentThread(String commentId, String userId) {
        UIComment parentComment = commentRepository.findById(commentId)
            .orElseThrow(() -> new EntityNotFoundException("Comment not found"));

        // If this is a reply, get the parent
        if (parentComment.getParentComment() != null) {
            parentComment = parentComment.getParentComment();
        }

        // Validate access
        String workspaceId = getWorkspaceId(parentComment.getUiConfig().getId());
        if (!rbac.hasPermission(userId, "ui_config", "read", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view comments");
        }

        List<UIComment> replies = commentRepository.findByParentCommentOrderByCreatedAt(parentComment);

        return CommentThread.builder()
            .parentComment(toCommentDto(parentComment))
            .replies(replies.stream().map(this::toCommentDto).toList())
            .totalReplies(replies.size())
            .build();
    }

    /**
     * Get comment statistics for a config
     */
    public CommentStatistics getCommentStatistics(String configId, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "ui_config", "read", getWorkspaceId(configId))) {
            throw new AccessDeniedException("Insufficient permissions to view comment statistics");
        }

        long totalComments = commentRepository.countByUiConfigId(configId);
        long openComments = commentRepository.countByUiConfigIdAndStatus(configId, CommentStatus.OPEN);
        long resolvedComments = commentRepository.countByUiConfigIdAndStatus(configId, CommentStatus.RESOLVED);

        return CommentStatistics.builder()
            .totalComments(totalComments)
            .openComments(openComments)
            .resolvedComments(resolvedComments)
            .build();
    }

    /**
     * Mention users in a comment
     */
    @Transactional
    public void processMentions(UIComment comment, List<String> mentionedUserIds) {
        for (String mentionedUserId : mentionedUserIds) {
            try {
                User mentionedUser = userRepository.findById(mentionedUserId)
                    .orElse(null);
                
                if (mentionedUser != null) {
                    notificationService.sendCommentMentionNotification(
                        mentionedUserId,
                        comment,
                        comment.getUser().getId()
                    );
                }
            } catch (Exception e) {
                log.warn("Failed to send mention notification to user {}", mentionedUserId, e);
            }
        }
    }

    private CommentDto toCommentDto(UIComment comment) {
        return CommentDto.builder()
            .id(comment.getId())
            .content(comment.getContent())
            .elementId(comment.getElementId())
            .position(comment.getPosition())
            .status(comment.getStatus())
            .user(UserDto.builder()
                .id(comment.getUser().getId())
                .name(comment.getUser().getName())
                .email(comment.getUser().getEmail())
                .avatar(comment.getUser().getAvatar())
                .build())
            .parentCommentId(comment.getParentComment() != null ? 
                           comment.getParentComment().getId() : null)
            .createdAt(comment.getCreatedAt())
            .updatedAt(comment.getUpdatedAt())
            .resolvedAt(comment.getResolvedAt())
            .resolvedBy(comment.getResolvedBy())
            .build();
    }

    private String getWorkspaceId(String configId) {
        return uiConfigRepository.findById(configId)
            .map(config -> config.getWorkspace().getId())
            .orElseThrow(() -> new EntityNotFoundException("UI Config not found"));
    }

    private void notifyWorkspaceMembers(String workspaceId, UIComment comment, String eventType) {
        try {
            List<String> memberIds = userRepository.findWorkspaceMemberIds(workspaceId);
            
            for (String memberId : memberIds) {
                if (!memberId.equals(comment.getUser().getId())) {
                    notificationService.sendCommentNotification(
                        memberId,
                        comment,
                        eventType
                    );
                }
            }
        } catch (Exception e) {
            log.error("Failed to notify workspace members", e);
        }
    }
}

// DTOs and supporting classes
@Data
@Builder
class CommentRequest {
    private String content;
    private CommentPosition position;
    private List<String> mentionedUsers;
}

@Data
@Builder
class CommentDto {
    private String id;
    private String content;
    private String elementId;
    private CommentPosition position;
    private CommentStatus status;
    private UserDto user;
    private String parentCommentId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime resolvedAt;
    private String resolvedBy;
}

@Data
@Builder
class CommentThread {
    private CommentDto parentComment;
    private List<CommentDto> replies;
    private int totalReplies;
}

@Data
@Builder
class CommentStatistics {
    private long totalComments;
    private long openComments;
    private long resolvedComments;
}

@Data
@Builder
class CommentFilter {
    private String elementId;
    private List<CommentStatus> statuses = List.of(CommentStatus.OPEN);
    private String userId;
    private LocalDateTime since;
}

@Data
@Builder
class CommentEvent {
    private String type;
    private CommentDto comment;
    private String commentId;
    private String parentCommentId;
    private String configId;
    private String userId;
    private LocalDateTime timestamp;
}

@Data
@Builder
class CommentPosition {
    private double x;
    private double y;
    private String anchor; // "top-left", "center", etc.
}

enum CommentStatus {
    OPEN, RESOLVED, ARCHIVED
}
