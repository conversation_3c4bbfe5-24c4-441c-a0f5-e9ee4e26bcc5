import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../config/app_config.dart';
import '../models/ui_metadata.dart';
import '../models/theme_config.dart';
import '../utils/logger.dart';
import 'auth_service.dart';

part 'api_service.g.dart';

/// API service provider
@riverpod
ApiService apiService(ApiServiceRef ref) {
  final dio = ref.watch(dioProvider);
  return ApiService(dio);
}

/// Dio provider with interceptors
@riverpod
Dio dio(DioRef ref) {
  final dio = Dio(BaseOptions(
    baseUrl: AppConfig.baseUrl,
    connectTimeout: AppConfig.adaptiveTimeout,
    receiveTimeout: AppConfig.adaptiveTimeout,
    sendTimeout: AppConfig.adaptiveTimeout,
    headers: AppConfig.defaultHeaders,
  ));

  // Add auth interceptor
  dio.interceptors.add(AuthInterceptor(ref));

  // Add logging interceptor in debug mode
  if (AppConfig.enableNetworkLogging) {
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: false,
      error: true,
      logPrint: (object) => AppLogger.debug('HTTP: $object'),
    ));
  }

  // Add retry interceptor
  dio.interceptors.add(RetryInterceptor(
    dio: dio,
    logPrint: (message) => AppLogger.debug('Retry: $message'),
    retries: 3,
    retryDelays: const [
      Duration(seconds: 1),
      Duration(seconds: 2),
      Duration(seconds: 3),
    ],
  ));

  return dio;
}

/// Main API service using Retrofit
@RestApi()
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  /// Fetch UI metadata for a page
  @GET('/ui-metadata/{pageId}')
  Future<UIMetadata> fetchUIMetadata(
    @Path('pageId') String pageId, {
    @Queries() Map<String, dynamic>? params,
  });

  /// Fetch theme configuration
  @GET('/themes/{themeId}')
  Future<ThemeConfig> fetchTheme(@Path('themeId') String themeId);

  /// Fetch data from a data source
  @GET('/data/{source}')
  Future<Map<String, dynamic>> fetchData(
    @Path('source') String source, {
    @Queries() Map<String, dynamic>? params,
  });

  /// Update data in a data source
  @PUT('/data/{source}/{id}')
  Future<Map<String, dynamic>> updateData(
    @Path('source') String source,
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  /// Create new data in a data source
  @POST('/data/{source}')
  Future<Map<String, dynamic>> createData(
    @Path('source') String source,
    @Body() Map<String, dynamic> data,
  );

  /// Delete data from a data source
  @DELETE('/data/{source}/{id}')
  Future<void> deleteData(
    @Path('source') String source,
    @Path('id') String id,
  );

  /// Submit form data
  @POST('/forms/{formId}/submit')
  Future<Map<String, dynamic>> submitForm(
    @Path('formId') String formId,
    @Body() Map<String, dynamic> data,
  );

  /// Upload file
  @POST('/upload')
  @MultiPart()
  Future<UploadResponse> uploadFile(
    @Part() File file, {
    @Part() String? path,
  });

  /// Get user profile
  @GET('/auth/me')
  Future<UserProfile> getUserProfile();

  /// Update user profile
  @PUT('/auth/me')
  Future<UserProfile> updateUserProfile(@Body() Map<String, dynamic> data);

  /// Get user permissions
  @GET('/auth/permissions')
  Future<List<String>> getUserPermissions();

  /// Search content
  @GET('/search')
  Future<SearchResponse> search(
    @Query('q') String query, {
    @Query('type') String? type,
    @Query('limit') int? limit,
    @Query('offset') int? offset,
  });

  /// Get analytics data
  @GET('/analytics/{metric}')
  Future<AnalyticsResponse> getAnalytics(
    @Path('metric') String metric, {
    @Query('startDate') String? startDate,
    @Query('endDate') String? endDate,
    @Queries() Map<String, dynamic>? filters,
  });
}

/// Auth interceptor for adding authentication headers
class AuthInterceptor extends Interceptor {
  final Ref ref;

  AuthInterceptor(this.ref);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final authService = ref.read(authServiceProvider);
    final token = authService.getAccessToken();

    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      _handleTokenRefresh(err, handler);
    } else {
      handler.next(err);
    }
  }

  Future<void> _handleTokenRefresh(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.refreshToken();

      // Retry the original request
      final response = await ref.read(dioProvider).fetch(err.requestOptions);
      handler.resolve(response);
    } catch (refreshError) {
      // Refresh failed, redirect to login
      final authService = ref.read(authServiceProvider);
      await authService.logout();
      handler.next(err);
    }
  }
}

/// Retry interceptor for handling network failures
class RetryInterceptor extends Interceptor {
  final Dio dio;
  final int retries;
  final List<Duration> retryDelays;
  final void Function(String message)? logPrint;

  RetryInterceptor({
    required this.dio,
    this.retries = 3,
    this.retryDelays = const [
      Duration(seconds: 1),
      Duration(seconds: 2),
      Duration(seconds: 3),
    ],
    this.logPrint,
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (_shouldRetry(err)) {
      _retry(err, handler);
    } else {
      handler.next(err);
    }
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.connectionError ||
        (err.response?.statusCode != null &&
            err.response!.statusCode! >= 500);
  }

  void _retry(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retryCount'] ?? 0;

    if (retryCount >= retries) {
      handler.next(err);
      return;
    }

    final delay = retryDelays.length > retryCount
        ? retryDelays[retryCount]
        : retryDelays.last;

    logPrint?.call('Retrying request (${retryCount + 1}/$retries) after ${delay.inSeconds}s');

    await Future.delayed(delay);

    err.requestOptions.extra['retryCount'] = retryCount + 1;

    try {
      final response = await dio.fetch(err.requestOptions);
      handler.resolve(response);
    } catch (retryError) {
      if (retryError is DioException) {
        onError(retryError, handler);
      } else {
        handler.next(err);
      }
    }
  }
}

/// Response models
class UploadResponse {
  final String url;
  final String filename;
  final int size;
  final String mimeType;

  UploadResponse({
    required this.url,
    required this.filename,
    required this.size,
    required this.mimeType,
  });

  factory UploadResponse.fromJson(Map<String, dynamic> json) {
    return UploadResponse(
      url: json['url'],
      filename: json['filename'],
      size: json['size'],
      mimeType: json['mimeType'],
    );
  }
}

class UserProfile {
  final String id;
  final String username;
  final String email;
  final String? avatar;
  final Map<String, dynamic> preferences;

  UserProfile({
    required this.id,
    required this.username,
    required this.email,
    this.avatar,
    this.preferences = const {},
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      avatar: json['avatar'],
      preferences: json['preferences'] ?? {},
    );
  }
}

class SearchResponse {
  final List<SearchResult> results;
  final int total;
  final int offset;
  final int limit;

  SearchResponse({
    required this.results,
    required this.total,
    required this.offset,
    required this.limit,
  });

  factory SearchResponse.fromJson(Map<String, dynamic> json) {
    return SearchResponse(
      results: (json['results'] as List)
          .map((item) => SearchResult.fromJson(item))
          .toList(),
      total: json['total'],
      offset: json['offset'],
      limit: json['limit'],
    );
  }
}

class SearchResult {
  final String id;
  final String type;
  final String title;
  final String? description;
  final Map<String, dynamic> metadata;

  SearchResult({
    required this.id,
    required this.type,
    required this.title,
    this.description,
    this.metadata = const {},
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      id: json['id'],
      type: json['type'],
      title: json['title'],
      description: json['description'],
      metadata: json['metadata'] ?? {},
    );
  }
}

class AnalyticsResponse {
  final String metric;
  final List<AnalyticsDataPoint> data;
  final Map<String, dynamic> summary;

  AnalyticsResponse({
    required this.metric,
    required this.data,
    required this.summary,
  });

  factory AnalyticsResponse.fromJson(Map<String, dynamic> json) {
    return AnalyticsResponse(
      metric: json['metric'],
      data: (json['data'] as List)
          .map((item) => AnalyticsDataPoint.fromJson(item))
          .toList(),
      summary: json['summary'] ?? {},
    );
  }
}

class AnalyticsDataPoint {
  final DateTime timestamp;
  final double value;
  final Map<String, dynamic> dimensions;

  AnalyticsDataPoint({
    required this.timestamp,
    required this.value,
    this.dimensions = const {},
  });

  factory AnalyticsDataPoint.fromJson(Map<String, dynamic> json) {
    return AnalyticsDataPoint(
      timestamp: DateTime.parse(json['timestamp']),
      value: json['value'].toDouble(),
      dimensions: json['dimensions'] ?? {},
    );
  }
}
