import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Radio component
class UIRadio<T> extends StatelessWidget {
  const UIRadio({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.label,
    this.subtitle,
    this.enabled = true,
    this.size = UISize.md,
    this.variant = UIColorVariant.primary,
    this.dense = false,
    this.controlAffinity = ListTileControlAffinity.leading,
  });

  final T value;
  final T? groupValue;
  final ValueChanged<T?>? onChanged;
  final String? label;
  final String? subtitle;
  final bool enabled;
  final UISize size;
  final UIColorVariant variant;
  final bool dense;
  final ListTileControlAffinity controlAffinity;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (label == null && subtitle == null) {
      // Simple radio without label
      return Radio<T>(
        value: value,
        groupValue: groupValue,
        onChanged: enabled ? onChanged : null,
        activeColor: variant.getColor(colorScheme),
      );
    }

    // Radio with label
    return RadioListTile<T>(
      value: value,
      groupValue: groupValue,
      onChanged: enabled ? onChanged : null,
      title: label != null ? Text(label!) : null,
      subtitle: subtitle != null ? Text(subtitle!) : null,
      dense: dense,
      controlAffinity: controlAffinity,
      activeColor: variant.getColor(colorScheme),
    );
  }
}

/// UI Builder Radio Group component
class UIRadioGroup<T> extends StatefulWidget {
  const UIRadioGroup({
    super.key,
    required this.items,
    this.value,
    this.onChanged,
    this.label,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.direction = UIDirectionVariant.vertical,
    this.spacing = 8.0,
    this.required = false,
    this.variant = UIColorVariant.primary,
  });

  final List<UIRadioItem<T>> items;
  final T? value;
  final ValueChanged<T?>? onChanged;
  final String? label;
  final String? helperText;
  final String? errorText;
  final bool enabled;
  final UIDirectionVariant direction;
  final double spacing;
  final bool required;
  final UIColorVariant variant;

  @override
  State<UIRadioGroup<T>> createState() => _UIRadioGroupState<T>();
}

class _UIRadioGroupState<T> extends State<UIRadioGroup<T>> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    List<Widget> radios = widget.items.map((item) {
      return UIRadio<T>(
        value: item.value,
        groupValue: widget.value,
        onChanged: widget.enabled ? (T? value) {
          widget.onChanged?.call(value);
        } : null,
        label: item.label,
        subtitle: item.subtitle,
        enabled: item.enabled && widget.enabled,
        variant: widget.variant,
      );
    }).toList();

    Widget content;
    if (widget.direction == UIDirectionVariant.horizontal) {
      content = Wrap(
        spacing: widget.spacing,
        children: radios,
      );
    } else {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: radios.expand((radio) => [
          radio,
          if (radio != radios.last) SizedBox(height: widget.spacing),
        ]).toList(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: tokens.spacing.size2),
        ],
        content,
        if (widget.helperText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.helperText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: tokens.spacing.size1),
          Text(
            widget.errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }
}

/// Radio item data class
class UIRadioItem<T> {
  const UIRadioItem({
    required this.value,
    required this.label,
    this.subtitle,
    this.enabled = true,
  });

  final T value;
  final String label;
  final String? subtitle;
  final bool enabled;
}

/// UI Builder Radio Card component for card-style radio selection
class UIRadioCard<T> extends StatelessWidget {
  const UIRadioCard({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.child,
    this.enabled = true,
    this.variant = UIColorVariant.primary,
    this.padding,
    this.margin,
  });

  final T value;
  final T? groupValue;
  final ValueChanged<T?>? onChanged;
  final Widget child;
  final bool enabled;
  final UIColorVariant variant;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;
    final isSelected = value == groupValue;

    return Container(
      margin: margin,
      child: Material(
        color: isSelected 
          ? variant.getColor(colorScheme).withOpacity(0.1)
          : colorScheme.surface,
        borderRadius: tokens.borderRadius.lg,
        child: InkWell(
          onTap: enabled ? () => onChanged?.call(value) : null,
          borderRadius: tokens.borderRadius.lg,
          child: Container(
            padding: padding ?? EdgeInsets.all(tokens.spacing.size4),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected 
                  ? variant.getColor(colorScheme)
                  : colorScheme.outline,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: tokens.borderRadius.lg,
            ),
            child: Row(
              children: [
                Radio<T>(
                  value: value,
                  groupValue: groupValue,
                  onChanged: enabled ? onChanged : null,
                  activeColor: variant.getColor(colorScheme),
                ),
                SizedBox(width: tokens.spacing.size2),
                Expanded(child: child),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
