# GraphQL Schema for UI Platform

scalar UUID
scalar DateTime
scalar JSON

# Root Query Type
type Query {
    # Organization queries
    organization(id: UUID!): Organization
    organizationBySlug(slug: String!): Organization
    organizations(page: Int = 0, size: Int = 20): OrganizationPage
    
    # User queries
    user(id: UUID!): User
    userByUsername(username: String!): User
    usersByOrganization(organizationId: UUID!, page: Int = 0, size: Int = 20): UserPage
    
    # UI Configuration queries
    uiConfiguration(id: UUID!): UIConfiguration
    uiConfigurationBySlug(slug: String!, organizationId: UUID!): UIConfiguration
    uiConfigurationsByOrganization(organizationId: UUID!, page: Int = 0, size: Int = 20): UIConfigurationPage
    
    # Component queries
    component(id: UUID!): Component
    componentsByUIConfiguration(uiConfigurationId: UUID!): [Component!]!
    componentTree(uiConfigurationId: UUID!): [Component!]!
    
    # Template queries
    template(id: UUID!): Template
    marketplaceTemplates(page: Int = 0, size: Int = 20): TemplatePage
    searchTemplates(input: TemplateSearchInput!, page: Int = 0, size: Int = 20): TemplatePage
    featuredTemplates: [Template!]!
    popularTemplates(limit: Int = 10): [Template!]!
    topRatedTemplates(limit: Int = 10): [Template!]!
    templateCategories: [String!]!
}

# Root Mutation Type
type Mutation {
    # Organization mutations
    createOrganization(input: CreateOrganizationInput!): Organization!
    updateOrganization(id: UUID!, input: UpdateOrganizationInput!): Organization!
    deleteOrganization(id: UUID!): Boolean!
    
    # User mutations
    createUser(input: CreateUserInput!): User!
    updateUser(id: UUID!, input: UpdateUserInput!): User!
    deleteUser(id: UUID!): Boolean!
    
    # Authentication mutations
    login(input: LoginInput!): AuthPayload!
    register(input: RegisterInput!): AuthPayload!
    refreshToken(refreshToken: String!): AuthPayload!
    changePassword(input: ChangePasswordInput!): Boolean!
    
    # UI Configuration mutations
    createUIConfiguration(input: CreateUIConfigurationInput!): UIConfiguration!
    updateUIConfiguration(id: UUID!, input: UpdateUIConfigurationInput!): UIConfiguration!
    deleteUIConfiguration(id: UUID!): Boolean!
    publishUIConfiguration(id: UUID!): UIConfiguration!
    unpublishUIConfiguration(id: UUID!): UIConfiguration!
    cloneUIConfiguration(id: UUID!, newName: String!, newSlug: String!): UIConfiguration!
    
    # Component mutations
    createComponent(input: CreateComponentInput!): Component!
    updateComponent(id: UUID!, input: UpdateComponentInput!): Component!
    deleteComponent(id: UUID!): Boolean!
    moveComponent(id: UUID!, newParentId: UUID, newSortOrder: Int): Component!
    reorderComponents(uiConfigurationId: UUID!, parentId: UUID, componentIds: [UUID!]!): Boolean!
    
    # Template mutations
    createTemplate(input: CreateTemplateInput!): Template!
    updateTemplate(id: UUID!, input: UpdateTemplateInput!): Template!
    deleteTemplate(id: UUID!): Boolean!
    publishTemplate(id: UUID!): Template!
    cloneTemplate(id: UUID!, newName: String!): Template!
}

# Organization Types
type Organization {
    id: UUID!
    name: String!
    slug: String!
    description: String
    domain: String
    logoUrl: String
    websiteUrl: String
    status: OrganizationStatus!
    subscriptionPlan: SubscriptionPlan!
    maxUsers: Int!
    maxProjects: Int!
    maxStorageMb: Long!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    users(page: Int = 0, size: Int = 20): UserPage
    uiConfigurations(page: Int = 0, size: Int = 20): UIConfigurationPage
    templates(page: Int = 0, size: Int = 20): TemplatePage
    
    # Statistics
    userCount: Long!
    projectCount: Long!
    templateCount: Long!
}

type OrganizationPage {
    content: [Organization!]!
    totalElements: Long!
    totalPages: Int!
    number: Int!
    size: Int!
    first: Boolean!
    last: Boolean!
}

enum OrganizationStatus {
    ACTIVE
    INACTIVE
    SUSPENDED
}

enum SubscriptionPlan {
    FREE
    BASIC
    PROFESSIONAL
    ENTERPRISE
}

# User Types
type User {
    id: UUID!
    username: String!
    email: String!
    firstName: String!
    lastName: String!
    fullName: String!
    avatarUrl: String
    phoneNumber: String
    timezone: String
    locale: String
    status: UserStatus!
    emailVerified: Boolean!
    lastLoginAt: DateTime
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    organization: Organization!
    roles: [Role!]!
    ownedUIConfigurations(page: Int = 0, size: Int = 20): UIConfigurationPage
    authoredTemplates(page: Int = 0, size: Int = 20): TemplatePage
}

type UserPage {
    content: [User!]!
    totalElements: Long!
    totalPages: Int!
    number: Int!
    size: Int!
    first: Boolean!
    last: Boolean!
}

enum UserStatus {
    ACTIVE
    INACTIVE
    SUSPENDED
}

type Role {
    id: UUID!
    name: String!
    description: String
    permissions: [Permission!]!
}

type Permission {
    id: UUID!
    name: String!
    description: String
    resource: String!
    action: String!
}

# UI Configuration Types
type UIConfiguration {
    id: UUID!
    name: String!
    slug: String!
    description: String
    type: UIConfigurationType!
    status: UIConfigurationStatus!
    metadata: JSON
    layoutConfig: JSON
    styleConfig: JSON
    responsiveConfig: JSON
    validationRules: JSON
    versionNumber: Int!
    isPublished: Boolean!
    isTemplate: Boolean!
    isPublic: Boolean!
    tags: String
    previewUrl: String
    publishedUrl: String
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    organization: Organization!
    owner: User!
    theme: Theme
    layout: Layout
    parent: UIConfiguration
    children: [UIConfiguration!]!
    components: [Component!]!
    formFields: [FormField!]!
    
    # Statistics
    componentCount: Long!
    formFieldCount: Long!
    childrenCount: Long!
}

type UIConfigurationPage {
    content: [UIConfiguration!]!
    totalElements: Long!
    totalPages: Int!
    number: Int!
    size: Int!
    first: Boolean!
    last: Boolean!
}

enum UIConfigurationType {
    PAGE
    COMPONENT
    FORM
    DASHBOARD
    MODAL
    WIDGET
}

enum UIConfigurationStatus {
    DRAFT
    REVIEW
    APPROVED
    PUBLISHED
    ARCHIVED
}

# Component Types
type Component {
    id: UUID!
    name: String!
    description: String
    componentType: String!
    category: String
    properties: JSON
    styleProperties: JSON
    eventHandlers: JSON
    validationRules: JSON
    responsiveSettings: JSON
    sortOrder: Int!
    isVisible: Boolean!
    isEnabled: Boolean!
    isRequired: Boolean!
    isReusable: Boolean!
    cssClasses: String
    customCss: String
    dataSource: String
    dataBinding: JSON
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    uiConfiguration: UIConfiguration!
    parent: Component
    children: [Component!]!
    
    # Computed fields
    depth: Int!
    childrenCount: Long!
}

# Form Field Types
type FormField {
    id: UUID!
    fieldName: String!
    fieldLabel: String
    fieldType: FieldType!
    description: String
    placeholder: String
    defaultValue: String
    isRequired: Boolean!
    isReadonly: Boolean!
    isDisabled: Boolean!
    isVisible: Boolean!
    sortOrder: Int!
    validationRules: JSON
    fieldOptions: JSON
    styleProperties: JSON
    conditionalLogic: JSON
    cssClasses: String
    dataSource: String
    dataBinding: JSON
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    uiConfiguration: UIConfiguration!
}

enum FieldType {
    TEXT
    EMAIL
    PASSWORD
    NUMBER
    TEXTAREA
    SELECT
    MULTISELECT
    CHECKBOX
    RADIO
    DATE
    DATETIME
    FILE
    IMAGE
    HIDDEN
}

# Template Types
type Template {
    id: UUID!
    name: String!
    description: String
    longDescription: String
    category: String!
    subcategory: String
    tags: String
    templateData: JSON
    previewImages: JSON
    demoUrl: String
    documentationUrl: String
    status: TemplateStatus!
    isPublic: Boolean!
    isPremium: Boolean!
    isFeatured: Boolean!
    price: Float!
    licenseType: String
    version: String!
    minPlatformVersion: String
    downloadCount: Long!
    rating: Float!
    ratingCount: Long!
    viewCount: Long!
    compatibility: JSON
    requirements: JSON
    installationGuide: JSON
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    organization: Organization
    author: User!
    reviews(page: Int = 0, size: Int = 20): TemplateReviewPage
    
    # Computed fields
    averageRating: Float!
    reviewCount: Long!
    isFree: Boolean!
}

type TemplatePage {
    content: [Template!]!
    totalElements: Long!
    totalPages: Int!
    number: Int!
    size: Int!
    first: Boolean!
    last: Boolean!
}

enum TemplateStatus {
    DRAFT
    REVIEW
    PUBLISHED
    REJECTED
    ARCHIVED
}

type TemplateReview {
    id: UUID!
    rating: Int!
    title: String
    comment: String
    isVerifiedPurchase: Boolean!
    helpfulCount: Int!
    isReported: Boolean!
    isApproved: Boolean!
    createdAt: DateTime!
    
    # Relationships
    template: Template!
    reviewer: User!
}

type TemplateReviewPage {
    content: [TemplateReview!]!
    totalElements: Long!
    totalPages: Int!
    number: Int!
    size: Int!
    first: Boolean!
    last: Boolean!
}

# Theme and Layout Types (simplified)
type Theme {
    id: UUID!
    name: String!
    description: String
    version: String!
    isDefault: Boolean!
    isPublic: Boolean!
    isSystem: Boolean!
    previewImageUrl: String
    colorPalette: JSON
    typography: JSON
    createdAt: DateTime!
}

type Layout {
    id: UUID!
    name: String!
    description: String
    layoutType: LayoutType!
    isDefault: Boolean!
    isPublic: Boolean!
    isSystem: Boolean!
    layoutDefinition: JSON
    responsiveBreakpoints: JSON
    createdAt: DateTime!
}

enum LayoutType {
    GRID
    FLEXBOX
    ABSOLUTE
    FLOW
}

# Authentication Types
type AuthPayload {
    accessToken: String!
    refreshToken: String!
    tokenType: String!
    expiresIn: Long!
    user: User!
}

# Input Types
input CreateOrganizationInput {
    name: String!
    slug: String!
    description: String
    domain: String
}

input UpdateOrganizationInput {
    name: String
    description: String
    domain: String
    logoUrl: String
    websiteUrl: String
}

input CreateUserInput {
    username: String!
    email: String!
    password: String!
    firstName: String!
    lastName: String!
    organizationId: UUID!
}

input UpdateUserInput {
    firstName: String
    lastName: String
    avatarUrl: String
    phoneNumber: String
    timezone: String
    locale: String
}

input LoginInput {
    usernameOrEmail: String!
    password: String!
    rememberMe: Boolean = false
}

input RegisterInput {
    username: String!
    email: String!
    password: String!
    firstName: String!
    lastName: String!
    organizationName: String!
    organizationSlug: String!
}

input ChangePasswordInput {
    currentPassword: String!
    newPassword: String!
}

input CreateUIConfigurationInput {
    name: String!
    slug: String!
    description: String
    type: UIConfigurationType!
    metadata: JSON
    themeId: UUID
    layoutId: UUID
    parentId: UUID
}

input UpdateUIConfigurationInput {
    name: String
    description: String
    metadata: JSON
    layoutConfig: JSON
    styleConfig: JSON
    responsiveConfig: JSON
    validationRules: JSON
    tags: String
}

input CreateComponentInput {
    name: String!
    description: String
    componentType: String!
    category: String
    properties: JSON
    styleProperties: JSON
    sortOrder: Int = 0
    isVisible: Boolean = true
    isEnabled: Boolean = true
    isRequired: Boolean = false
    isReusable: Boolean = false
    parentId: UUID
}

input UpdateComponentInput {
    name: String
    description: String
    properties: JSON
    styleProperties: JSON
    eventHandlers: JSON
    validationRules: JSON
    responsiveSettings: JSON
    isVisible: Boolean
    isEnabled: Boolean
    isRequired: Boolean
    cssClasses: String
    customCss: String
    dataSource: String
    dataBinding: JSON
}

input CreateTemplateInput {
    name: String!
    description: String
    category: String!
    subcategory: String
    tags: String
    templateData: JSON
    price: Float = 0.0
    licenseType: String = "MIT"
    version: String = "1.0.0"
}

input UpdateTemplateInput {
    name: String
    description: String
    longDescription: String
    subcategory: String
    tags: String
    templateData: JSON
    previewImages: JSON
    demoUrl: String
    documentationUrl: String
    price: Float
    licenseType: String
    compatibility: JSON
    requirements: JSON
    installationGuide: JSON
}

input TemplateSearchInput {
    name: String
    category: String
    subcategory: String
    isPremium: Boolean
    minPrice: Float
    maxPrice: Float
    minRating: Float
}

# Scalar type definitions
scalar Long
