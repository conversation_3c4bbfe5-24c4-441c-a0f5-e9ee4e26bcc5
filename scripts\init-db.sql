-- Database initialization script for UI Platform
-- This script runs when the PostgreSQL container starts for the first time

-- Create additional databases if needed
CREATE DATABASE uiplatform_test;
CREATE DATABASE uiplatform_dev;

-- Create additional users
CREATE USER test_user WITH PASSWORD 'test_password';
CREATE USER dev_user WITH PASSWORD 'dev_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE uiplatform TO uiplatform;
GRANT ALL PRIVILEGES ON DATABASE uiplatform_test TO test_user;
GRANT ALL PRIVILEGES ON DATABASE uiplatform_dev TO dev_user;

-- Connect to main database and create extensions
\c uiplatform;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS cache;

-- Grant schema permissions
GRANT USAGE ON SCHEMA audit TO uiplatform;
GRANT USAGE ON SCHEMA analytics TO uiplatform;
GRANT USAGE ON SCHEMA cache TO uiplatform;

GRANT CREATE ON SCHEMA audit TO uiplatform;
GRANT CREATE ON SCHEMA analytics TO uiplatform;
GRANT CREATE ON SCHEMA cache TO uiplatform;

-- Connect to test database and set up
\c uiplatform_test;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS cache;

GRANT USAGE ON SCHEMA audit TO test_user;
GRANT USAGE ON SCHEMA analytics TO test_user;
GRANT USAGE ON SCHEMA cache TO test_user;

GRANT CREATE ON SCHEMA audit TO test_user;
GRANT CREATE ON SCHEMA analytics TO test_user;
GRANT CREATE ON SCHEMA cache TO test_user;

-- Connect to dev database and set up
\c uiplatform_dev;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS cache;

GRANT USAGE ON SCHEMA audit TO dev_user;
GRANT USAGE ON SCHEMA analytics TO dev_user;
GRANT USAGE ON SCHEMA cache TO dev_user;

GRANT CREATE ON SCHEMA audit TO dev_user;
GRANT CREATE ON SCHEMA analytics TO dev_user;
GRANT CREATE ON SCHEMA cache TO dev_user;

-- Create sample data for development (only in dev database)
INSERT INTO organizations (id, name, slug, description, subscription_plan) VALUES
('550e8400-e29b-41d4-a716-************', 'Demo Organization', 'demo-org', 'A sample organization for development', 'pro');

INSERT INTO users (id, email, username, password_hash, first_name, last_name, email_verified, is_active) VALUES
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', true, true),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'user', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo', 'User', true, true);

-- Link users to organization
INSERT INTO user_organizations (user_id, organization_id, role) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'admin'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'member');

-- Create sample workspace
INSERT INTO workspaces (id, organization_id, name, description, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', 'Demo Workspace', 'Sample workspace for development', '550e8400-e29b-41d4-a716-************');

-- Create sample UI configuration
INSERT INTO ui_configs (id, workspace_id, name, description, config_data, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', 'Sample Dashboard', 'A sample dashboard configuration', 
'{"version": "1.0", "components": [{"id": "header", "type": "container", "props": {"padding": "20px", "backgroundColor": "#f5f5f5"}, "children": [{"id": "title", "type": "text", "props": {"content": "Sample Dashboard", "variant": "h1", "align": "center"}}]}, {"id": "content", "type": "row", "props": {"gap": "20px", "padding": "20px"}, "children": [{"id": "card1", "type": "container", "props": {"padding": "16px", "backgroundColor": "#ffffff", "borderRadius": "8px", "boxShadow": "0 2px 4px rgba(0,0,0,0.1)"}, "children": [{"id": "card1-title", "type": "text", "props": {"content": "Total Users", "variant": "h3"}}, {"id": "card1-value", "type": "text", "props": {"content": "1,234", "variant": "h2", "color": "#007bff"}}]}, {"id": "card2", "type": "container", "props": {"padding": "16px", "backgroundColor": "#ffffff", "borderRadius": "8px", "boxShadow": "0 2px 4px rgba(0,0,0,0.1)"}, "children": [{"id": "card2-title", "type": "text", "props": {"content": "Active Projects", "variant": "h3"}}, {"id": "card2-value", "type": "text", "props": {"content": "56", "variant": "h2", "color": "#28a745"}}]}]}]}', 
'550e8400-e29b-41d4-a716-************');

-- Create performance indexes for development
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ui_configs_search ON ui_configs USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_components_search ON components USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_performance ON audit_logs(created_at DESC, entity_type, entity_id);

-- Create materialized view for analytics (development only)
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.ui_config_stats AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    COUNT(*) as configs_created,
    COUNT(DISTINCT created_by) as unique_creators
FROM ui_configs 
WHERE deleted_at IS NULL
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

-- Create refresh function for materialized view
CREATE OR REPLACE FUNCTION analytics.refresh_ui_config_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW analytics.ui_config_stats;
END;
$$ LANGUAGE plpgsql;

-- Set up automatic refresh (every hour in development)
-- Note: In production, this should be handled by a scheduled job
SELECT cron.schedule('refresh-ui-config-stats', '0 * * * *', 'SELECT analytics.refresh_ui_config_stats();');

-- Create development-specific functions
CREATE OR REPLACE FUNCTION reset_demo_data()
RETURNS void AS $$
BEGIN
    -- Reset demo data to initial state
    DELETE FROM ui_config_comments WHERE ui_config_id = '550e8400-e29b-41d4-a716-446655440004';
    DELETE FROM collaboration_sessions WHERE ui_config_id = '550e8400-e29b-41d4-a716-446655440004';
    DELETE FROM ui_config_versions WHERE ui_config_id = '550e8400-e29b-41d4-a716-446655440004';
    
    UPDATE ui_configs 
    SET config_data = '{"version": "1.0", "components": [{"id": "header", "type": "container", "props": {"padding": "20px", "backgroundColor": "#f5f5f5"}, "children": [{"id": "title", "type": "text", "props": {"content": "Sample Dashboard", "variant": "h1", "align": "center"}}]}]}'
    WHERE id = '550e8400-e29b-41d4-a716-446655440004';
    
    RAISE NOTICE 'Demo data has been reset to initial state';
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on functions
GRANT EXECUTE ON FUNCTION reset_demo_data() TO dev_user;
GRANT EXECUTE ON FUNCTION analytics.refresh_ui_config_stats() TO dev_user;
