import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export interface AccessibilitySettings {
  // Visual accessibility
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  
  // Keyboard navigation
  focusVisible: boolean;
  skipLinks: boolean;
  keyboardNavigation: boolean;
  
  // Screen reader
  screenReaderOptimized: boolean;
  announcements: boolean;
  
  // Color and vision
  colorBlindnessSupport: boolean;
  darkMode: boolean;
  
  // Motor accessibility
  clickDelay: number;
  hoverDelay: number;
  
  // Cognitive accessibility
  simplifiedUI: boolean;
  autoplayDisabled: boolean;
  
  // Language and localization
  language: string;
  textDirection: 'ltr' | 'rtl';
}

interface AccessibilityContextValue {
  settings: AccessibilitySettings;
  updateSettings: (updates: Partial<AccessibilitySettings>) => void;
  resetSettings: () => void;
  
  // Utility functions
  announce: (message: string, priority?: 'polite' | 'assertive') => void;
  focus: (element: HTMLElement | null) => void;
  skipToContent: () => void;
  
  // Feature detection
  supportsReducedMotion: boolean;
  supportsHighContrast: boolean;
  prefersColorScheme: 'light' | 'dark' | 'no-preference';
}

const defaultSettings: AccessibilitySettings = {
  highContrast: false,
  largeText: false,
  reducedMotion: false,
  focusVisible: true,
  skipLinks: true,
  keyboardNavigation: true,
  screenReaderOptimized: false,
  announcements: true,
  colorBlindnessSupport: false,
  darkMode: false,
  clickDelay: 0,
  hoverDelay: 300,
  simplifiedUI: false,
  autoplayDisabled: false,
  language: 'en',
  textDirection: 'ltr',
};

const AccessibilityContext = createContext<AccessibilityContextValue | undefined>(undefined);

interface AccessibilityProviderProps {
  children: ReactNode;
  initialSettings?: Partial<AccessibilitySettings>;
  storageKey?: string;
  enableAutoDetection?: boolean;
}

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({
  children,
  initialSettings = {},
  storageKey = 'ui-builder-accessibility',
  enableAutoDetection = true,
}) => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    ...defaultSettings,
    ...initialSettings,
  });

  const [supportsReducedMotion, setSupportsReducedMotion] = useState(false);
  const [supportsHighContrast, setSupportsHighContrast] = useState(false);
  const [prefersColorScheme, setPrefersColorScheme] = useState<'light' | 'dark' | 'no-preference'>('no-preference');

  // Load settings from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const parsedSettings = JSON.parse(stored);
          setSettings(prev => ({ ...prev, ...parsedSettings }));
        }
      } catch (error) {
        console.warn('Failed to load accessibility settings:', error);
      }
    }
  }, [storageKey]);

  // Auto-detect system preferences
  useEffect(() => {
    if (!enableAutoDetection || typeof window === 'undefined') return;

    // Detect reduced motion preference
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setSupportsReducedMotion(reducedMotionQuery.matches);
    
    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setSupportsReducedMotion(e.matches);
      if (e.matches) {
        updateSettings({ reducedMotion: true });
      }
    };
    
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);

    // Detect high contrast preference
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    setSupportsHighContrast(highContrastQuery.matches);
    
    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setSupportsHighContrast(e.matches);
      if (e.matches) {
        updateSettings({ highContrast: true });
      }
    };
    
    highContrastQuery.addEventListener('change', handleHighContrastChange);

    // Detect color scheme preference
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)');
    
    const updateColorScheme = () => {
      if (darkModeQuery.matches) {
        setPrefersColorScheme('dark');
        updateSettings({ darkMode: true });
      } else if (lightModeQuery.matches) {
        setPrefersColorScheme('light');
        updateSettings({ darkMode: false });
      } else {
        setPrefersColorScheme('no-preference');
      }
    };
    
    updateColorScheme();
    darkModeQuery.addEventListener('change', updateColorScheme);
    lightModeQuery.addEventListener('change', updateColorScheme);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
      darkModeQuery.removeEventListener('change', updateColorScheme);
      lightModeQuery.removeEventListener('change', updateColorScheme);
    };
  }, [enableAutoDetection]);

  // Save settings to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(settings));
      } catch (error) {
        console.warn('Failed to save accessibility settings:', error);
      }
    }
  }, [settings, storageKey]);

  // Apply CSS classes and variables
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    
    // Apply accessibility classes
    root.classList.toggle('high-contrast', settings.highContrast);
    root.classList.toggle('large-text', settings.largeText);
    root.classList.toggle('reduced-motion', settings.reducedMotion);
    root.classList.toggle('focus-visible', settings.focusVisible);
    root.classList.toggle('screen-reader-optimized', settings.screenReaderOptimized);
    root.classList.toggle('simplified-ui', settings.simplifiedUI);
    root.classList.toggle('color-blindness-support', settings.colorBlindnessSupport);
    
    // Apply CSS custom properties
    root.style.setProperty('--click-delay', `${settings.clickDelay}ms`);
    root.style.setProperty('--hover-delay', `${settings.hoverDelay}ms`);
    root.style.setProperty('--text-direction', settings.textDirection);
    
    // Apply language and direction
    document.documentElement.lang = settings.language;
    document.documentElement.dir = settings.textDirection;
  }, [settings]);

  const updateSettings = (updates: Partial<AccessibilitySettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
  };

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!settings.announcements) return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  const focus = (element: HTMLElement | null) => {
    if (!element || !settings.keyboardNavigation) return;
    
    element.focus();
    
    // Ensure focus is visible
    if (settings.focusVisible) {
      element.classList.add('focus-visible');
    }
  };

  const skipToContent = () => {
    const mainContent = document.querySelector('main, [role="main"], #main-content');
    if (mainContent instanceof HTMLElement) {
      focus(mainContent);
    }
  };

  const contextValue: AccessibilityContextValue = {
    settings,
    updateSettings,
    resetSettings,
    announce,
    focus,
    skipToContent,
    supportsReducedMotion,
    supportsHighContrast,
    prefersColorScheme,
  };

  return (
    <AccessibilityContext.Provider value={contextValue}>
      {settings.skipLinks && <SkipLinks />}
      <LiveRegion />
      {children}
    </AccessibilityContext.Provider>
  );
};

// Skip links component
const SkipLinks: React.FC = () => {
  return (
    <div className="skip-links">
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      <a href="#navigation" className="skip-link">
        Skip to navigation
      </a>
      <a href="#footer" className="skip-link">
        Skip to footer
      </a>
    </div>
  );
};

// Live region for announcements
const LiveRegion: React.FC = () => {
  return (
    <>
      <div
        id="live-region-polite"
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />
      <div
        id="live-region-assertive"
        aria-live="assertive"
        aria-atomic="true"
        className="sr-only"
      />
    </>
  );
};

export const useAccessibility = (): AccessibilityContextValue => {
  const context = useContext(AccessibilityContext);
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
};

// Hook for specific accessibility features
export const useReducedMotion = () => {
  const { settings } = useAccessibility();
  return settings.reducedMotion;
};

export const useHighContrast = () => {
  const { settings } = useAccessibility();
  return settings.highContrast;
};

export const useFocusManagement = () => {
  const { focus, settings } = useAccessibility();
  return {
    focus,
    focusVisible: settings.focusVisible,
    keyboardNavigation: settings.keyboardNavigation,
  };
};

export const useAnnouncements = () => {
  const { announce, settings } = useAccessibility();
  return {
    announce: settings.announcements ? announce : () => {},
    enabled: settings.announcements,
  };
};

// Higher-order component for accessibility
export const withAccessibility = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const AccessibleComponent = (props: P) => {
    const accessibility = useAccessibility();
    return <Component {...props} accessibility={accessibility} />;
  };

  AccessibleComponent.displayName = `withAccessibility(${Component.displayName || Component.name})`;
  return AccessibleComponent;
};

export default AccessibilityProvider;
