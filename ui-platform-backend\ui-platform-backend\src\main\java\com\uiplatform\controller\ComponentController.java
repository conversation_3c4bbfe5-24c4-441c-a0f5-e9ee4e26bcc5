package com.uiplatform.controller;

import com.uiplatform.dto.ApiResponse;
import com.uiplatform.dto.ComponentDTO;
import com.uiplatform.service.ComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST Controller for Component management operations.
 */
@RestController
@RequestMapping("/api/v1/components")
@Tag(name = "Components", description = "UI component management endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ComponentController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentController.class);

    private final ComponentService componentService;

    @Autowired
    public ComponentController(ComponentService componentService) {
        this.componentService = componentService;
    }

    /**
     * Create new component.
     */
    @PostMapping
    @Operation(summary = "Create component", description = "Create a new UI component")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Component created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid component data")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'CREATE')")
    public ResponseEntity<ApiResponse<ComponentDTO>> createComponent(
            @Valid @RequestBody ComponentDTO.CreateDTO createDTO,
            @Parameter(description = "UI Configuration ID") @RequestParam UUID uiConfigurationId) {
        
        logger.info("Creating component '{}' for UI configuration: {}", createDTO.getName(), uiConfigurationId);
        
        try {
            ComponentDTO component = componentService.createComponent(createDTO, uiConfigurationId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Component created successfully", component));
        } catch (Exception e) {
            logger.error("Failed to create component", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to create component: " + e.getMessage()));
        }
    }

    /**
     * Get component by ID.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get component", description = "Get component by ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<ComponentDTO>> getComponent(
            @Parameter(description = "Component ID") @PathVariable UUID id) {
        
        logger.debug("Fetching component with ID: {}", id);
        
        try {
            ComponentDTO component = componentService.getComponentById(id);
            return ResponseEntity.ok(ApiResponse.success("Component found", component));
        } catch (Exception e) {
            logger.error("Failed to fetch component with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Component not found: " + e.getMessage()));
        }
    }

    /**
     * Update component.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update component", description = "Update component")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid component data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ResponseEntity<ApiResponse<ComponentDTO>> updateComponent(
            @Parameter(description = "Component ID") @PathVariable UUID id,
            @Valid @RequestBody ComponentDTO updateDTO) {
        
        logger.info("Updating component with ID: {}", id);
        
        try {
            ComponentDTO component = componentService.updateComponent(id, updateDTO);
            return ResponseEntity.ok(ApiResponse.success("Component updated successfully", component));
        } catch (Exception e) {
            logger.error("Failed to update component with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to update component: " + e.getMessage()));
        }
    }

    /**
     * Delete component.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete component", description = "Delete component (soft delete)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteComponent(
            @Parameter(description = "Component ID") @PathVariable UUID id) {
        
        logger.info("Deleting component with ID: {}", id);
        
        try {
            componentService.deleteComponent(id);
            return ResponseEntity.ok(ApiResponse.success("Component deleted successfully"));
        } catch (Exception e) {
            logger.error("Failed to delete component with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to delete component: " + e.getMessage()));
        }
    }

    /**
     * Get components by UI configuration.
     */
    @GetMapping("/ui-configuration/{uiConfigurationId}")
    @Operation(summary = "Get components by UI configuration", description = "Get all components for a UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Components retrieved successfully")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<List<ComponentDTO>>> getComponentsByUIConfiguration(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID uiConfigurationId) {
        
        logger.debug("Fetching components for UI configuration: {}", uiConfigurationId);
        
        try {
            List<ComponentDTO> components = componentService.getComponentsByUIConfiguration(uiConfigurationId);
            return ResponseEntity.ok(ApiResponse.success("Components retrieved successfully", components));
        } catch (Exception e) {
            logger.error("Failed to fetch components for UI configuration: {}", uiConfigurationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch components: " + e.getMessage()));
        }
    }

    /**
     * Get root components by UI configuration.
     */
    @GetMapping("/ui-configuration/{uiConfigurationId}/root")
    @Operation(summary = "Get root components", description = "Get root components (no parent) for a UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Root components retrieved successfully")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<List<ComponentDTO>>> getRootComponents(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID uiConfigurationId) {
        
        logger.debug("Fetching root components for UI configuration: {}", uiConfigurationId);
        
        try {
            List<ComponentDTO> components = componentService.getRootComponentsByUIConfiguration(uiConfigurationId);
            return ResponseEntity.ok(ApiResponse.success("Root components retrieved successfully", components));
        } catch (Exception e) {
            logger.error("Failed to fetch root components for UI configuration: {}", uiConfigurationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch root components: " + e.getMessage()));
        }
    }

    /**
     * Get child components.
     */
    @GetMapping("/{parentId}/children")
    @Operation(summary = "Get child components", description = "Get child components by parent ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Child components retrieved successfully")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<List<ComponentDTO>>> getChildComponents(
            @Parameter(description = "Parent Component ID") @PathVariable UUID parentId) {
        
        logger.debug("Fetching child components for parent: {}", parentId);
        
        try {
            List<ComponentDTO> components = componentService.getChildComponents(parentId);
            return ResponseEntity.ok(ApiResponse.success("Child components retrieved successfully", components));
        } catch (Exception e) {
            logger.error("Failed to fetch child components for parent: {}", parentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch child components: " + e.getMessage()));
        }
    }

    /**
     * Search components.
     */
    @GetMapping("/search")
    @Operation(summary = "Search components", description = "Search components by criteria")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<Page<ComponentDTO>>> searchComponents(
            @Parameter(description = "UI Configuration ID") @RequestParam UUID uiConfigurationId,
            @Parameter(description = "Component type") @RequestParam(required = false) String componentType,
            @Parameter(description = "Category") @RequestParam(required = false) String category,
            @Parameter(description = "Is visible") @RequestParam(required = false) Boolean isVisible,
            @Parameter(description = "Is enabled") @RequestParam(required = false) Boolean isEnabled,
            @PageableDefault(size = 20) Pageable pageable) {
        
        logger.debug("Searching components for UI configuration: {}", uiConfigurationId);
        
        try {
            Page<ComponentDTO> components = componentService.searchComponents(
                    uiConfigurationId, componentType, category, isVisible, isEnabled, pageable);
            
            ApiResponse<Page<ComponentDTO>> response = ApiResponse.success("Search completed successfully", components);
            response.setPagination(new ApiResponse.PaginationInfo(
                    components.getNumber(),
                    components.getSize(),
                    components.getTotalElements(),
                    components.getTotalPages()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search components", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to search components: " + e.getMessage()));
        }
    }

    /**
     * Move component.
     */
    @PostMapping("/{id}/move")
    @Operation(summary = "Move component", description = "Move component to new parent")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component moved successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid move operation"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ResponseEntity<ApiResponse<ComponentDTO>> moveComponent(
            @Parameter(description = "Component ID") @PathVariable UUID id,
            @Parameter(description = "New parent ID") @RequestParam(required = false) UUID newParentId,
            @Parameter(description = "New sort order") @RequestParam(required = false) Integer newSortOrder) {
        
        logger.info("Moving component {} to parent {} with sort order {}", id, newParentId, newSortOrder);
        
        try {
            ComponentDTO component = componentService.moveComponent(id, newParentId, newSortOrder);
            return ResponseEntity.ok(ApiResponse.success("Component moved successfully", component));
        } catch (Exception e) {
            logger.error("Failed to move component with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to move component: " + e.getMessage()));
        }
    }

    /**
     * Update component visibility.
     */
    @PutMapping("/{id}/visibility")
    @Operation(summary = "Update component visibility", description = "Update component visibility")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component visibility updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ResponseEntity<ApiResponse<Void>> updateComponentVisibility(
            @Parameter(description = "Component ID") @PathVariable UUID id,
            @Parameter(description = "Is visible") @RequestParam Boolean isVisible) {
        
        logger.info("Updating visibility for component ID: {} to {}", id, isVisible);
        
        try {
            componentService.updateComponentVisibility(id, isVisible);
            return ResponseEntity.ok(ApiResponse.success("Component visibility updated successfully"));
        } catch (Exception e) {
            logger.error("Failed to update visibility for component ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to update component visibility: " + e.getMessage()));
        }
    }

    /**
     * Update component enabled status.
     */
    @PutMapping("/{id}/enabled")
    @Operation(summary = "Update component enabled status", description = "Update component enabled status")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component enabled status updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ResponseEntity<ApiResponse<Void>> updateComponentEnabledStatus(
            @Parameter(description = "Component ID") @PathVariable UUID id,
            @Parameter(description = "Is enabled") @RequestParam Boolean isEnabled) {
        
        logger.info("Updating enabled status for component ID: {} to {}", id, isEnabled);
        
        try {
            componentService.updateComponentEnabledStatus(id, isEnabled);
            return ResponseEntity.ok(ApiResponse.success("Component enabled status updated successfully"));
        } catch (Exception e) {
            logger.error("Failed to update enabled status for component ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Failed to update component enabled status: " + e.getMessage()));
        }
    }

    /**
     * Reorder components.
     */
    @PostMapping("/reorder")
    @Operation(summary = "Reorder components", description = "Reorder components within a parent")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Components reordered successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid reorder operation")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'UPDATE')")
    public ResponseEntity<ApiResponse<Void>> reorderComponents(
            @Parameter(description = "UI Configuration ID") @RequestParam UUID uiConfigurationId,
            @Parameter(description = "Parent ID") @RequestParam(required = false) UUID parentId,
            @Parameter(description = "Component IDs in order") @RequestBody List<UUID> componentIds) {
        
        logger.info("Reordering {} components for UI configuration: {}", componentIds.size(), uiConfigurationId);
        
        try {
            componentService.reorderComponents(uiConfigurationId, parentId, componentIds);
            return ResponseEntity.ok(ApiResponse.success("Components reordered successfully"));
        } catch (Exception e) {
            logger.error("Failed to reorder components", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to reorder components: " + e.getMessage()));
        }
    }

    /**
     * Get component tree.
     */
    @GetMapping("/ui-configuration/{uiConfigurationId}/tree")
    @Operation(summary = "Get component tree", description = "Get hierarchical component tree for UI configuration")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Component tree retrieved successfully")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'READ')")
    public ResponseEntity<ApiResponse<List<ComponentDTO>>> getComponentTree(
            @Parameter(description = "UI Configuration ID") @PathVariable UUID uiConfigurationId) {
        
        logger.debug("Fetching component tree for UI configuration: {}", uiConfigurationId);
        
        try {
            List<ComponentDTO> componentTree = componentService.getComponentTree(uiConfigurationId);
            return ResponseEntity.ok(ApiResponse.success("Component tree retrieved successfully", componentTree));
        } catch (Exception e) {
            logger.error("Failed to fetch component tree for UI configuration: {}", uiConfigurationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to fetch component tree: " + e.getMessage()));
        }
    }

    /**
     * Clone component.
     */
    @PostMapping("/{id}/clone")
    @Operation(summary = "Clone component", description = "Clone component with all children")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Component cloned successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Component not found")
    })
    @PreAuthorize("hasPermission('COMPONENT', 'CREATE')")
    public ResponseEntity<ApiResponse<ComponentDTO>> cloneComponent(
            @Parameter(description = "Component ID") @PathVariable UUID id,
            @Parameter(description = "Target UI Configuration ID") @RequestParam UUID targetUIConfigurationId,
            @Parameter(description = "New parent ID") @RequestParam(required = false) UUID newParentId) {
        
        logger.info("Cloning component {} to UI configuration {} with parent {}", id, targetUIConfigurationId, newParentId);
        
        try {
            ComponentDTO component = componentService.cloneComponent(id, targetUIConfigurationId, newParentId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Component cloned successfully", component));
        } catch (Exception e) {
            logger.error("Failed to clone component with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Failed to clone component: " + e.getMessage()));
        }
    }
}
