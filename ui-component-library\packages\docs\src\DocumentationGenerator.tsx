import React from 'react';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';

export interface DocumentationConfig {
  includeProps: boolean;
  includeExamples: boolean;
  includeAccessibility: boolean;
  includePerformance: boolean;
  includeUsage: boolean;
  outputFormat: 'markdown' | 'html' | 'json';
  theme: 'light' | 'dark' | 'auto';
}

export interface ComponentDocumentation {
  name: string;
  description: string;
  category: string;
  props: PropDocumentation[];
  examples: ExampleDocumentation[];
  accessibility: AccessibilityDocumentation;
  performance: PerformanceDocumentation;
  usage: UsageDocumentation;
  changelog: ChangelogEntry[];
}

export interface PropDocumentation {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description: string;
  examples: any[];
}

export interface ExampleDocumentation {
  title: string;
  description: string;
  code: string;
  preview: React.ReactNode;
  props: ComponentProps;
}

export interface AccessibilityDocumentation {
  ariaLabels: string[];
  keyboardNavigation: string[];
  screenReaderSupport: string[];
  colorContrast: string;
  focusManagement: string[];
}

export interface PerformanceDocumentation {
  renderTime: string;
  bundleSize: string;
  memoryUsage: string;
  optimizations: string[];
}

export interface UsageDocumentation {
  installation: string;
  basicUsage: string;
  advancedUsage: string;
  bestPractices: string[];
  commonMistakes: string[];
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: string[];
  breaking: boolean;
}

/**
 * Documentation Generator for Component Library
 * 
 * Automatically generates comprehensive documentation for components including:
 * - Props documentation with types and examples
 * - Interactive examples and code snippets
 * - Accessibility guidelines and compliance
 * - Performance metrics and optimizations
 * - Usage patterns and best practices
 */
export class DocumentationGenerator {
  private config: DocumentationConfig;
  private components = new Map<string, ComponentDocumentation>();

  constructor(config: Partial<DocumentationConfig> = {}) {
    this.config = {
      includeProps: true,
      includeExamples: true,
      includeAccessibility: true,
      includePerformance: true,
      includeUsage: true,
      outputFormat: 'markdown',
      theme: 'auto',
      ...config,
    };
  }

  /**
   * Generate documentation for a component
   */
  generateComponentDocs(
    component: React.ComponentType<any>,
    config: ComponentConfig
  ): ComponentDocumentation {
    const componentName = component.displayName || component.name || 'Unknown';

    const documentation: ComponentDocumentation = {
      name: componentName,
      description: config.description || '',
      category: config.category || 'General',
      props: this.generatePropsDocumentation(config),
      examples: this.generateExamples(component, config),
      accessibility: this.generateAccessibilityDocs(config),
      performance: this.generatePerformanceDocs(config),
      usage: this.generateUsageDocs(componentName, config),
      changelog: this.generateChangelog(componentName),
    };

    this.components.set(componentName, documentation);
    return documentation;
  }

  /**
   * Generate props documentation
   */
  private generatePropsDocumentation(config: ComponentConfig): PropDocumentation[] {
    if (!this.config.includeProps || !config.properties) {
      return [];
    }

    return Object.entries(config.properties).map(([name, prop]) => ({
      name,
      type: prop.type,
      required: config.requiredProperties?.includes(name) || false,
      defaultValue: config.defaultProps?.[name],
      description: prop.description || '',
      examples: this.generatePropExamples(prop.type),
    }));
  }

  /**
   * Generate examples for prop types
   */
  private generatePropExamples(type: string): any[] {
    switch (type) {
      case 'string':
        return ['Hello World', 'Example text', ''];
      case 'number':
        return [0, 42, -10, 3.14];
      case 'boolean':
        return [true, false];
      case 'array':
        return [[], ['item1', 'item2'], [1, 2, 3]];
      case 'object':
        return [{}, { key: 'value' }, { nested: { prop: true } }];
      case 'function':
        return ['() => {}', '(event) => console.log(event)'];
      default:
        return [];
    }
  }

  /**
   * Generate component examples
   */
  private generateExamples(
    component: React.ComponentType<any>,
    config: ComponentConfig
  ): ExampleDocumentation[] {
    if (!this.config.includeExamples) {
      return [];
    }

    const examples: ExampleDocumentation[] = [];

    // Basic example
    const basicProps = this.generateBasicProps(config);
    examples.push({
      title: 'Basic Usage',
      description: 'Basic example showing default component usage',
      code: this.generateCodeExample(config.displayName || 'Component', basicProps),
      preview: React.createElement(component, basicProps),
      props: basicProps,
    });

    // Advanced examples based on component type
    if (config.category === 'Form') {
      examples.push(...this.generateFormExamples(component, config));
    } else if (config.category === 'Layout') {
      examples.push(...this.generateLayoutExamples(component, config));
    } else if (config.category === 'Navigation') {
      examples.push(...this.generateNavigationExamples(component, config));
    }

    return examples;
  }

  /**
   * Generate basic props for examples
   */
  private generateBasicProps(config: ComponentConfig): ComponentProps {
    const props: ComponentProps = {};

    if (config.properties) {
      Object.entries(config.properties).forEach(([name, prop]) => {
        if (config.defaultProps?.[name] !== undefined) {
          props[name] = config.defaultProps[name];
        } else {
          props[name] = this.getDefaultValueForType(prop.type);
        }
      });
    }

    return props;
  }

  /**
   * Get default value for prop type
   */
  private getDefaultValueForType(type: string): any {
    switch (type) {
      case 'string':
        return 'Example';
      case 'number':
        return 0;
      case 'boolean':
        return false;
      case 'array':
        return [];
      case 'object':
        return {};
      default:
        return undefined;
    }
  }

  /**
   * Generate code example string
   */
  private generateCodeExample(componentName: string, props: ComponentProps): string {
    const propsString = Object.entries(props)
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `${key}="${value}"`;
        } else if (typeof value === 'boolean') {
          return value ? key : `${key}={false}`;
        } else {
          return `${key}={${JSON.stringify(value)}}`;
        }
      })
      .join(' ');

    return `<${componentName} ${propsString} />`;
  }

  /**
   * Generate form-specific examples
   */
  private generateFormExamples(
    component: React.ComponentType<any>,
    config: ComponentConfig
  ): ExampleDocumentation[] {
    const examples: ExampleDocumentation[] = [];

    // Validation example
    const validationProps = {
      ...this.generateBasicProps(config),
      required: true,
      error: 'This field is required',
    };

    examples.push({
      title: 'With Validation',
      description: 'Example showing form validation',
      code: this.generateCodeExample(config.displayName || 'Component', validationProps),
      preview: React.createElement(component, validationProps),
      props: validationProps,
    });

    return examples;
  }

  /**
   * Generate layout-specific examples
   */
  private generateLayoutExamples(
    component: React.ComponentType<any>,
    config: ComponentConfig
  ): ExampleDocumentation[] {
    const examples: ExampleDocumentation[] = [];

    // Responsive example
    const responsiveProps = {
      ...this.generateBasicProps(config),
      responsive: true,
      breakpoints: { sm: 12, md: 6, lg: 4 },
    };

    examples.push({
      title: 'Responsive Layout',
      description: 'Example showing responsive behavior',
      code: this.generateCodeExample(config.displayName || 'Component', responsiveProps),
      preview: React.createElement(component, responsiveProps),
      props: responsiveProps,
    });

    return examples;
  }

  /**
   * Generate navigation-specific examples
   */
  private generateNavigationExamples(
    component: React.ComponentType<any>,
    config: ComponentConfig
  ): ExampleDocumentation[] {
    const examples: ExampleDocumentation[] = [];

    // Multi-level navigation
    const navigationProps = {
      ...this.generateBasicProps(config),
      items: [
        { label: 'Home', href: '/' },
        { label: 'About', href: '/about' },
        { 
          label: 'Products', 
          href: '/products',
          children: [
            { label: 'Category 1', href: '/products/category1' },
            { label: 'Category 2', href: '/products/category2' },
          ]
        },
      ],
    };

    examples.push({
      title: 'Multi-level Navigation',
      description: 'Example showing nested navigation items',
      code: this.generateCodeExample(config.displayName || 'Component', navigationProps),
      preview: React.createElement(component, navigationProps),
      props: navigationProps,
    });

    return examples;
  }

  /**
   * Generate accessibility documentation
   */
  private generateAccessibilityDocs(config: ComponentConfig): AccessibilityDocumentation {
    if (!this.config.includeAccessibility) {
      return {
        ariaLabels: [],
        keyboardNavigation: [],
        screenReaderSupport: [],
        colorContrast: '',
        focusManagement: [],
      };
    }

    return {
      ariaLabels: this.generateAriaLabels(config),
      keyboardNavigation: this.generateKeyboardNavigation(config),
      screenReaderSupport: this.generateScreenReaderSupport(config),
      colorContrast: this.generateColorContrast(config),
      focusManagement: this.generateFocusManagement(config),
    };
  }

  /**
   * Generate ARIA labels documentation
   */
  private generateAriaLabels(config: ComponentConfig): string[] {
    const labels: string[] = [];

    if (config.accessibility?.ariaLabel) {
      labels.push(`aria-label: ${config.accessibility.ariaLabel}`);
    }

    if (config.accessibility?.role) {
      labels.push(`role: ${config.accessibility.role}`);
    }

    // Add common ARIA attributes based on component type
    switch (config.category) {
      case 'Form':
        labels.push('aria-required for required fields');
        labels.push('aria-invalid for validation errors');
        break;
      case 'Navigation':
        labels.push('aria-current for active items');
        labels.push('aria-expanded for collapsible items');
        break;
      case 'Feedback':
        labels.push('aria-live for dynamic content');
        labels.push('aria-atomic for complete announcements');
        break;
    }

    return labels;
  }

  /**
   * Generate keyboard navigation documentation
   */
  private generateKeyboardNavigation(config: ComponentConfig): string[] {
    const navigation: string[] = [];

    // Add common keyboard patterns based on component type
    switch (config.category) {
      case 'Form':
        navigation.push('Tab: Move to next field');
        navigation.push('Shift+Tab: Move to previous field');
        navigation.push('Enter: Submit form');
        break;
      case 'Navigation':
        navigation.push('Arrow keys: Navigate between items');
        navigation.push('Enter/Space: Activate item');
        navigation.push('Escape: Close expanded items');
        break;
      case 'Interactive':
        navigation.push('Enter/Space: Activate component');
        navigation.push('Escape: Cancel action');
        break;
    }

    return navigation;
  }

  /**
   * Generate screen reader support documentation
   */
  private generateScreenReaderSupport(config: ComponentConfig): string[] {
    const support: string[] = [];

    support.push('Proper semantic HTML structure');
    support.push('Descriptive text alternatives');
    support.push('Clear focus indicators');

    if (config.category === 'Form') {
      support.push('Form labels and descriptions');
      support.push('Error message announcements');
    }

    return support;
  }

  /**
   * Generate color contrast documentation
   */
  private generateColorContrast(config: ComponentConfig): string {
    return 'Meets WCAG 2.1 AA standards (4.5:1 for normal text, 3:1 for large text)';
  }

  /**
   * Generate focus management documentation
   */
  private generateFocusManagement(config: ComponentConfig): string[] {
    const focus: string[] = [];

    focus.push('Visible focus indicators');
    focus.push('Logical tab order');

    if (config.category === 'Modal') {
      focus.push('Focus trapping within modal');
      focus.push('Focus restoration on close');
    }

    return focus;
  }

  /**
   * Generate performance documentation
   */
  private generatePerformanceDocs(config: ComponentConfig): PerformanceDocumentation {
    if (!this.config.includePerformance) {
      return {
        renderTime: '',
        bundleSize: '',
        memoryUsage: '',
        optimizations: [],
      };
    }

    return {
      renderTime: '< 16ms (60fps)',
      bundleSize: '< 10KB gzipped',
      memoryUsage: 'Minimal heap allocation',
      optimizations: [
        'React.memo for prop comparison',
        'useCallback for event handlers',
        'useMemo for expensive calculations',
        'Lazy loading for large components',
      ],
    };
  }

  /**
   * Generate usage documentation
   */
  private generateUsageDocs(componentName: string, config: ComponentConfig): UsageDocumentation {
    if (!this.config.includeUsage) {
      return {
        installation: '',
        basicUsage: '',
        advancedUsage: '',
        bestPractices: [],
        commonMistakes: [],
      };
    }

    return {
      installation: `npm install @ui-builder/components`,
      basicUsage: `import { ${componentName} } from '@ui-builder/components';\n\n<${componentName} />`,
      advancedUsage: this.generateAdvancedUsage(componentName, config),
      bestPractices: this.generateBestPractices(config),
      commonMistakes: this.generateCommonMistakes(config),
    };
  }

  /**
   * Generate advanced usage examples
   */
  private generateAdvancedUsage(componentName: string, config: ComponentConfig): string {
    return `// Advanced usage with custom styling and event handlers
<${componentName}
  className="custom-class"
  style={{ margin: '16px' }}
  onAction={(data) => handleAction(data)}
  theme="dark"
  responsive
/>`;
  }

  /**
   * Generate best practices
   */
  private generateBestPractices(config: ComponentConfig): string[] {
    const practices: string[] = [
      'Always provide meaningful labels and descriptions',
      'Use semantic HTML elements when possible',
      'Test with keyboard navigation',
      'Verify screen reader compatibility',
    ];

    switch (config.category) {
      case 'Form':
        practices.push('Validate input on both client and server');
        practices.push('Provide clear error messages');
        break;
      case 'Interactive':
        practices.push('Provide visual feedback for user actions');
        practices.push('Handle loading and error states');
        break;
    }

    return practices;
  }

  /**
   * Generate common mistakes
   */
  private generateCommonMistakes(config: ComponentConfig): string[] {
    const mistakes: string[] = [
      'Missing accessibility attributes',
      'Poor color contrast',
      'Inadequate keyboard support',
      'Missing error handling',
    ];

    switch (config.category) {
      case 'Form':
        mistakes.push('Not validating required fields');
        mistakes.push('Unclear error messages');
        break;
      case 'Navigation':
        mistakes.push('Inconsistent navigation patterns');
        mistakes.push('Missing active state indicators');
        break;
    }

    return mistakes;
  }

  /**
   * Generate changelog
   */
  private generateChangelog(componentName: string): ChangelogEntry[] {
    // This would typically come from version control or a changelog file
    return [
      {
        version: '1.0.0',
        date: '2024-01-01',
        changes: ['Initial release'],
        breaking: false,
      },
    ];
  }

  /**
   * Export documentation in specified format
   */
  exportDocumentation(componentName: string): string {
    const docs = this.components.get(componentName);
    if (!docs) {
      throw new Error(`Documentation not found for component: ${componentName}`);
    }

    switch (this.config.outputFormat) {
      case 'markdown':
        return this.exportAsMarkdown(docs);
      case 'html':
        return this.exportAsHTML(docs);
      case 'json':
        return JSON.stringify(docs, null, 2);
      default:
        return this.exportAsMarkdown(docs);
    }
  }

  /**
   * Export as Markdown
   */
  private exportAsMarkdown(docs: ComponentDocumentation): string {
    let markdown = `# ${docs.name}\n\n`;
    markdown += `${docs.description}\n\n`;

    if (this.config.includeProps && docs.props.length > 0) {
      markdown += `## Props\n\n`;
      markdown += `| Name | Type | Required | Default | Description |\n`;
      markdown += `|------|------|----------|---------|-------------|\n`;
      
      docs.props.forEach(prop => {
        markdown += `| ${prop.name} | ${prop.type} | ${prop.required ? 'Yes' : 'No'} | ${prop.defaultValue || '-'} | ${prop.description} |\n`;
      });
      markdown += '\n';
    }

    if (this.config.includeExamples && docs.examples.length > 0) {
      markdown += `## Examples\n\n`;
      docs.examples.forEach(example => {
        markdown += `### ${example.title}\n\n`;
        markdown += `${example.description}\n\n`;
        markdown += `\`\`\`jsx\n${example.code}\n\`\`\`\n\n`;
      });
    }

    return markdown;
  }

  /**
   * Export as HTML
   */
  private exportAsHTML(docs: ComponentDocumentation): string {
    let html = `<h1>${docs.name}</h1>\n`;
    html += `<p>${docs.description}</p>\n`;

    if (this.config.includeProps && docs.props.length > 0) {
      html += `<h2>Props</h2>\n`;
      html += `<table>\n`;
      html += `<thead><tr><th>Name</th><th>Type</th><th>Required</th><th>Default</th><th>Description</th></tr></thead>\n`;
      html += `<tbody>\n`;
      
      docs.props.forEach(prop => {
        html += `<tr><td>${prop.name}</td><td>${prop.type}</td><td>${prop.required ? 'Yes' : 'No'}</td><td>${prop.defaultValue || '-'}</td><td>${prop.description}</td></tr>\n`;
      });
      
      html += `</tbody>\n</table>\n`;
    }

    return html;
  }

  /**
   * Get all component documentation
   */
  getAllDocumentation(): Map<string, ComponentDocumentation> {
    return new Map(this.components);
  }

  /**
   * Clear all documentation
   */
  clearDocumentation(): void {
    this.components.clear();
  }
}

// Global documentation generator instance
export const documentationGenerator = new DocumentationGenerator();
