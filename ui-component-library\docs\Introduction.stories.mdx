import { Meta } from '@storybook/blocks';

<Meta title="Introduction" />

# UI Builder Component Library

Welcome to the UI Builder Component Library - a comprehensive design system and component library built for creating dynamic, accessible, and beautiful user interfaces.

## 🎯 Overview

This library provides a complete set of reusable components, design tokens, and utilities that enable developers to build consistent and high-quality user interfaces across web and mobile platforms.

### Key Features

- **🎨 Design System First**: Built on a solid foundation of design tokens and principles
- **♿ Accessibility**: WCAG 2.1 AA compliant components with full keyboard navigation
- **🌙 Dark Mode**: Built-in support for light, dark, and system themes
- **📱 Responsive**: Mobile-first design with responsive breakpoints
- **🎭 Customizable**: Extensive theming and customization options
- **🔧 Developer Experience**: TypeScript support, comprehensive documentation, and testing
- **🚀 Performance**: Optimized for bundle size and runtime performance

## 🏗️ Architecture

The library is organized into several packages:

### Core Packages

- **`@ui-builder/core`** - Core utilities, hooks, and theme system
- **`@ui-builder/react`** - React component implementations
- **`@ui-builder/icons`** - Icon library with multiple variants
- **`@ui-builder/tokens`** - Design tokens and CSS variables

### Platform Packages

- **`@ui-builder/flutter`** - Flutter widget implementations
- **`@ui-builder/web`** - Web-specific utilities and components

## 🎨 Design Principles

### Consistency
All components follow consistent patterns for props, styling, and behavior to ensure a predictable developer experience.

### Accessibility
Every component is built with accessibility in mind, including:
- Proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

### Performance
Components are optimized for:
- Small bundle sizes
- Fast rendering
- Minimal re-renders
- Efficient memory usage

### Flexibility
The system provides multiple levels of customization:
- Design tokens for global theming
- Component-level prop overrides
- CSS custom properties
- Styled component extensions

## 🚀 Getting Started

### Installation

```bash
# Install core packages
npm install @ui-builder/core @ui-builder/react @ui-builder/icons

# Or with yarn
yarn add @ui-builder/core @ui-builder/react @ui-builder/icons
```

### Basic Setup

```tsx
import React from 'react';
import { ThemeProvider, Button } from '@ui-builder/react';

function App() {
  return (
    <ThemeProvider>
      <Button variant="primary">
        Hello World
      </Button>
    </ThemeProvider>
  );
}
```

### With Custom Theme

```tsx
import React from 'react';
import { ThemeProvider, createTheme, Button } from '@ui-builder/react';

const customTheme = createTheme({
  colors: {
    primary: '#6366f1',
    secondary: '#8b5cf6',
  },
});

function App() {
  return (
    <ThemeProvider theme={customTheme}>
      <Button variant="primary">
        Custom Themed Button
      </Button>
    </ThemeProvider>
  );
}
```

## 📚 Documentation Structure

This Storybook contains comprehensive documentation organized into several sections:

### Design System
Learn about the foundational elements that make up our design system:
- **Colors** - Color palette and usage guidelines
- **Typography** - Font scales, weights, and text styles
- **Spacing** - Spacing scale and layout principles
- **Icons** - Icon library and usage patterns

### Components
Explore our component library organized by category:
- **Foundation** - Basic building blocks (Button, Input, etc.)
- **Layout** - Layout and structure components
- **Navigation** - Navigation and wayfinding components
- **Data Display** - Components for displaying data
- **Feedback** - User feedback and status components

### Patterns
Common UI patterns and best practices:
- **Forms** - Form layouts and validation patterns
- **Data Tables** - Complex data display patterns
- **Modals & Overlays** - Modal and overlay patterns
- **Loading States** - Loading and skeleton patterns

## 🛠️ Development

### Contributing

We welcome contributions! Please see our [Contributing Guide](https://github.com/ui-builder/component-library/blob/main/CONTRIBUTING.md) for details.

### Local Development

```bash
# Clone the repository
git clone https://github.com/ui-builder/component-library.git

# Install dependencies
npm install

# Start Storybook
npm run storybook

# Run tests
npm test

# Build packages
npm run build
```

### Testing

We use a comprehensive testing strategy:
- **Unit Tests** - Jest and React Testing Library
- **Visual Regression** - Chromatic
- **Accessibility** - axe-core
- **Performance** - Lighthouse CI

## 🔗 Resources

- [GitHub Repository](https://github.com/ui-builder/component-library)
- [NPM Packages](https://www.npmjs.com/org/ui-builder)
- [Design Tokens](https://tokens.ui-builder.com)
- [Figma Design System](https://figma.com/ui-builder)
- [Changelog](https://github.com/ui-builder/component-library/blob/main/CHANGELOG.md)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](https://github.com/ui-builder/component-library/blob/main/LICENSE) file for details.

---

Ready to start building? Check out our [Button component](?path=/docs/foundation-button--docs) to see the library in action!
