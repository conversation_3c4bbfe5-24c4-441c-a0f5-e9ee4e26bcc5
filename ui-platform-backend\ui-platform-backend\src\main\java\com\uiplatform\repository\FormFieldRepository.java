package com.uiplatform.repository;

import com.uiplatform.entity.FormField;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for FormField entity.
 * Provides CRUD operations and custom queries for form field management.
 */
@Repository
public interface FormFieldRepository extends JpaRepository<FormField, UUID>, JpaSpecificationExecutor<FormField> {

    /**
     * Find form field by name and UI configuration.
     */
    Optional<FormField> findByFieldNameAndUiConfigurationIdAndDeletedFalse(String fieldName, UUID uiConfigurationId);

    /**
     * Find form fields by UI configuration.
     */
    List<FormField> findByUiConfigurationIdAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find form fields by UI configuration with pagination.
     */
    Page<FormField> findByUiConfigurationIdAndDeletedFalse(UUID uiConfigurationId, Pageable pageable);

    /**
     * Find form fields by type.
     */
    List<FormField> findByFieldTypeAndDeletedFalse(FormField.FieldType fieldType);

    /**
     * Find form fields by type and UI configuration.
     */
    List<FormField> findByFieldTypeAndUiConfigurationIdAndDeletedFalseOrderBySortOrder(FormField.FieldType fieldType, UUID uiConfigurationId);

    /**
     * Find required form fields.
     */
    List<FormField> findByUiConfigurationIdAndIsRequiredTrueAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find visible form fields.
     */
    List<FormField> findByUiConfigurationIdAndIsVisibleTrueAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find enabled form fields.
     */
    List<FormField> findByUiConfigurationIdAndIsDisabledFalseAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Find readonly form fields.
     */
    List<FormField> findByUiConfigurationIdAndIsReadonlyTrueAndDeletedFalseOrderBySortOrder(UUID uiConfigurationId);

    /**
     * Search form fields by name or label.
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND " +
           "(LOWER(f.fieldName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(f.fieldLabel) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "f.deleted = false ORDER BY f.sortOrder")
    List<FormField> searchByNameOrLabel(@Param("uiConfigurationId") UUID uiConfigurationId, 
                                       @Param("searchTerm") String searchTerm);

    /**
     * Find form fields by multiple criteria.
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND " +
           "(:fieldType IS NULL OR f.fieldType = :fieldType) AND " +
           "(:isRequired IS NULL OR f.isRequired = :isRequired) AND " +
           "(:isVisible IS NULL OR f.isVisible = :isVisible) AND " +
           "(:isDisabled IS NULL OR f.isDisabled = :isDisabled) AND " +
           "(:isReadonly IS NULL OR f.isReadonly = :isReadonly) AND " +
           "f.deleted = false ORDER BY f.sortOrder")
    Page<FormField> findByCriteria(@Param("uiConfigurationId") UUID uiConfigurationId,
                                  @Param("fieldType") FormField.FieldType fieldType,
                                  @Param("isRequired") Boolean isRequired,
                                  @Param("isVisible") Boolean isVisible,
                                  @Param("isDisabled") Boolean isDisabled,
                                  @Param("isReadonly") Boolean isReadonly,
                                  Pageable pageable);

    /**
     * Count form fields by UI configuration.
     */
    @Query("SELECT COUNT(f) FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND f.deleted = false")
    Long countByUiConfigurationId(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Count form fields by type.
     */
    @Query("SELECT COUNT(f) FROM FormField f WHERE f.fieldType = :fieldType AND f.deleted = false")
    Long countByFieldType(@Param("fieldType") FormField.FieldType fieldType);

    /**
     * Count required form fields.
     */
    @Query("SELECT COUNT(f) FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND f.isRequired = true AND f.deleted = false")
    Long countRequiredByUiConfigurationId(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Check if field name exists in UI configuration (excluding current field).
     */
    @Query("SELECT COUNT(f) > 0 FROM FormField f WHERE f.fieldName = :fieldName AND f.uiConfiguration.id = :uiConfigurationId AND f.id != :excludeId AND f.deleted = false")
    boolean existsByFieldNameAndUiConfigurationIdAndIdNot(@Param("fieldName") String fieldName, 
                                                         @Param("uiConfigurationId") UUID uiConfigurationId, 
                                                         @Param("excludeId") UUID excludeId);

    /**
     * Find form fields with validation rules count.
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND " +
           "JSON_LENGTH(f.validationRules) > 0 AND f.deleted = false ORDER BY f.sortOrder")
    List<FormField> findWithValidationRules(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Find form fields by data source.
     */
    @Query("SELECT f FROM FormField f WHERE f.dataSource = :dataSource AND f.deleted = false")
    List<FormField> findByDataSource(@Param("dataSource") String dataSource);

    /**
     * Update form field visibility.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.isVisible = :isVisible WHERE f.id = :id")
    void updateVisibility(@Param("id") UUID id, @Param("isVisible") Boolean isVisible);

    /**
     * Update form field disabled status.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.isDisabled = :isDisabled WHERE f.id = :id")
    void updateDisabledStatus(@Param("id") UUID id, @Param("isDisabled") Boolean isDisabled);

    /**
     * Update form field readonly status.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.isReadonly = :isReadonly WHERE f.id = :id")
    void updateReadonlyStatus(@Param("id") UUID id, @Param("isReadonly") Boolean isReadonly);

    /**
     * Update form field required status.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.isRequired = :isRequired WHERE f.id = :id")
    void updateRequiredStatus(@Param("id") UUID id, @Param("isRequired") Boolean isRequired);

    /**
     * Update form field sort order.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.sortOrder = :sortOrder WHERE f.id = :id")
    void updateSortOrder(@Param("id") UUID id, @Param("sortOrder") Integer sortOrder);

    /**
     * Find most used field types.
     */
    @Query("SELECT f.fieldType, COUNT(f) as usageCount FROM FormField f WHERE f.deleted = false GROUP BY f.fieldType ORDER BY usageCount DESC")
    List<Object[]> findMostUsedFieldTypes();

    /**
     * Find form fields by organization (through UI configuration).
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.organization.id = :organizationId AND f.deleted = false")
    List<FormField> findByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Find all unique field types.
     */
    @Query("SELECT DISTINCT f.fieldType FROM FormField f WHERE f.deleted = false ORDER BY f.fieldType")
    List<FormField.FieldType> findAllFieldTypes();

    /**
     * Find form fields with default values.
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND f.defaultValue IS NOT NULL AND f.deleted = false ORDER BY f.sortOrder")
    List<FormField> findWithDefaultValues(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Find form fields with conditional logic.
     */
    @Query("SELECT f FROM FormField f WHERE f.uiConfiguration.id = :uiConfigurationId AND " +
           "JSON_LENGTH(f.conditionalLogic) > 0 AND f.deleted = false ORDER BY f.sortOrder")
    List<FormField> findWithConditionalLogic(@Param("uiConfigurationId") UUID uiConfigurationId);

    /**
     * Bulk update sort orders.
     */
    @Modifying
    @Query("UPDATE FormField f SET f.sortOrder = f.sortOrder + :increment WHERE f.uiConfiguration.id = :uiConfigurationId AND f.sortOrder >= :startOrder")
    void bulkUpdateSortOrders(@Param("uiConfigurationId") UUID uiConfigurationId, 
                             @Param("startOrder") Integer startOrder, 
                             @Param("increment") Integer increment);
}
