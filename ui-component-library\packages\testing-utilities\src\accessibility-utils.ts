import { axe, toHaveNoViolations } from 'jest-axe';
import { RenderResult } from '@testing-library/react';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Accessibility testing utilities
export interface AccessibilityTestOptions {
  /** Axe rules to include */
  rules?: Record<string, { enabled: boolean }>;
  /** Tags to include (e.g., 'wcag2a', 'wcag2aa', 'wcag21aa') */
  tags?: string[];
  /** Elements to exclude from testing */
  exclude?: string[];
  /** Custom timeout for async operations */
  timeout?: number;
}

/**
 * Test component for accessibility violations
 */
export const testAccessibility = async (
  renderResult: RenderResult,
  options: AccessibilityTestOptions = {}
) => {
  const {
    rules = {},
    tags = ['wcag2a', 'wcag2aa', 'wcag21aa'],
    exclude = [],
    timeout = 5000,
  } = options;

  const { container } = renderResult;

  const results = await axe(container, {
    rules: {
      // Default rules
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true },
      'aria-labels': { enabled: true },
      'semantic-markup': { enabled: true },
      ...rules,
    },
    tags,
    exclude,
    timeout,
  });

  expect(results).toHaveNoViolations();
  return results;
};

/**
 * Test keyboard navigation
 */
export const testKeyboardNavigation = async (
  user: any,
  elements: HTMLElement[],
  options: { reverse?: boolean } = {}
) => {
  const { reverse = false } = options;
  
  if (elements.length === 0) return;

  // Focus first element
  elements[0].focus();
  expect(elements[0]).toHaveFocus();

  // Navigate through elements
  for (let i = 1; i < elements.length; i++) {
    await user.keyboard(reverse ? '{Shift>}{Tab}{/Shift}' : '{Tab}');
    expect(elements[i]).toHaveFocus();
  }

  // Test wrap-around (optional)
  if (elements.length > 1) {
    await user.keyboard(reverse ? '{Shift>}{Tab}{/Shift}' : '{Tab}');
    // Should wrap to first element or move to next focusable element outside
  }
};

/**
 * Test focus management
 */
export const testFocusManagement = {
  /**
   * Test that focus is trapped within a container
   */
  async testFocusTrap(
    user: any,
    container: HTMLElement,
    firstFocusable: HTMLElement,
    lastFocusable: HTMLElement
  ) {
    // Focus first element
    firstFocusable.focus();
    expect(firstFocusable).toHaveFocus();

    // Tab to last element
    lastFocusable.focus();
    expect(lastFocusable).toHaveFocus();

    // Tab forward should wrap to first
    await user.keyboard('{Tab}');
    expect(firstFocusable).toHaveFocus();

    // Shift+Tab backward should wrap to last
    await user.keyboard('{Shift>}{Tab}{/Shift}');
    expect(lastFocusable).toHaveFocus();
  },

  /**
   * Test that focus is restored after modal/dialog closes
   */
  async testFocusRestore(
    user: any,
    triggerElement: HTMLElement,
    openModal: () => Promise<void>,
    closeModal: () => Promise<void>
  ) {
    // Focus trigger element
    triggerElement.focus();
    expect(triggerElement).toHaveFocus();

    // Open modal
    await openModal();

    // Close modal
    await closeModal();

    // Focus should be restored
    expect(triggerElement).toHaveFocus();
  },

  /**
   * Test that focus moves to appropriate element when content changes
   */
  async testFocusOnContentChange(
    expectedFocusElement: HTMLElement,
    triggerChange: () => Promise<void>
  ) {
    await triggerChange();
    expect(expectedFocusElement).toHaveFocus();
  },
};

/**
 * Test ARIA attributes
 */
export const testAriaAttributes = {
  /**
   * Test that element has required ARIA attributes
   */
  testRequiredAria(element: HTMLElement, requiredAttributes: Record<string, string>) {
    Object.entries(requiredAttributes).forEach(([attr, expectedValue]) => {
      const actualValue = element.getAttribute(attr);
      expect(actualValue).toBe(expectedValue);
    });
  },

  /**
   * Test ARIA live regions
   */
  async testLiveRegion(
    liveRegion: HTMLElement,
    triggerUpdate: () => Promise<void>,
    expectedText: string
  ) {
    expect(liveRegion).toHaveAttribute('aria-live');
    
    await triggerUpdate();
    
    // Wait for live region to update
    await waitFor(() => {
      expect(liveRegion).toHaveTextContent(expectedText);
    });
  },

  /**
   * Test ARIA expanded state
   */
  testExpandedState(
    trigger: HTMLElement,
    content: HTMLElement,
    isExpanded: boolean
  ) {
    expect(trigger).toHaveAttribute('aria-expanded', isExpanded.toString());
    
    if (isExpanded) {
      expect(content).toBeVisible();
    } else {
      expect(content).not.toBeVisible();
    }
  },

  /**
   * Test ARIA describedby relationships
   */
  testDescribedBy(element: HTMLElement, descriptionId: string) {
    expect(element).toHaveAttribute('aria-describedby', descriptionId);
    
    const description = document.getElementById(descriptionId);
    expect(description).toBeInTheDocument();
  },

  /**
   * Test ARIA labelledby relationships
   */
  testLabelledBy(element: HTMLElement, labelId: string) {
    expect(element).toHaveAttribute('aria-labelledby', labelId);
    
    const label = document.getElementById(labelId);
    expect(label).toBeInTheDocument();
  },
};

/**
 * Test screen reader announcements
 */
export const testScreenReaderAnnouncements = {
  /**
   * Test that status messages are announced
   */
  async testStatusAnnouncement(
    statusElement: HTMLElement,
    triggerStatus: () => Promise<void>,
    expectedMessage: string
  ) {
    expect(statusElement).toHaveAttribute('role', 'status');
    expect(statusElement).toHaveAttribute('aria-live', 'polite');
    
    await triggerStatus();
    
    await waitFor(() => {
      expect(statusElement).toHaveTextContent(expectedMessage);
    });
  },

  /**
   * Test that alert messages are announced
   */
  async testAlertAnnouncement(
    alertElement: HTMLElement,
    triggerAlert: () => Promise<void>,
    expectedMessage: string
  ) {
    expect(alertElement).toHaveAttribute('role', 'alert');
    expect(alertElement).toHaveAttribute('aria-live', 'assertive');
    
    await triggerAlert();
    
    await waitFor(() => {
      expect(alertElement).toHaveTextContent(expectedMessage);
    });
  },
};

/**
 * Test color contrast
 */
export const testColorContrast = async (element: HTMLElement) => {
  const results = await axe(element, {
    rules: {
      'color-contrast': { enabled: true },
    },
  });

  expect(results).toHaveNoViolations();
};

/**
 * Test with high contrast mode
 */
export const testHighContrastMode = (renderComponent: () => RenderResult) => {
  // Mock high contrast media query
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: query.includes('prefers-contrast: high'),
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  const result = renderComponent();
  
  // Test that component adapts to high contrast
  // This would depend on your specific implementation
  
  return result;
};

/**
 * Test with reduced motion
 */
export const testReducedMotion = (renderComponent: () => RenderResult) => {
  // Mock reduced motion media query
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: query.includes('prefers-reduced-motion: reduce'),
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  const result = renderComponent();
  
  // Test that animations are disabled or reduced
  // This would depend on your specific implementation
  
  return result;
};

/**
 * Comprehensive accessibility test suite
 */
export const runAccessibilityTestSuite = async (
  renderResult: RenderResult,
  options: {
    testKeyboard?: boolean;
    testFocus?: boolean;
    testAria?: boolean;
    testContrast?: boolean;
    customTests?: (() => Promise<void>)[];
  } = {}
) => {
  const {
    testKeyboard = true,
    testFocus = true,
    testAria = true,
    testContrast = true,
    customTests = [],
  } = options;

  // Run axe accessibility tests
  await testAccessibility(renderResult);

  // Run keyboard navigation tests
  if (testKeyboard) {
    const focusableElements = renderResult.container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      await testKeyboardNavigation(
        userEvent.setup(),
        Array.from(focusableElements) as HTMLElement[]
      );
    }
  }

  // Run color contrast tests
  if (testContrast) {
    await testColorContrast(renderResult.container);
  }

  // Run custom tests
  for (const customTest of customTests) {
    await customTest();
  }
};
