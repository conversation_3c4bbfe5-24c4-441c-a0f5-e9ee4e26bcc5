package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.HashMap;
import java.util.Map;

/**
 * FormField entity for dynamic form building.
 * Represents individual form fields with validation rules and properties.
 */
@Entity
@Table(name = "form_fields", indexes = {
    @Index(name = "idx_form_field_name", columnList = "field_name"),
    @Index(name = "idx_form_field_type", columnList = "field_type"),
    @Index(name = "idx_form_field_ui_config", columnList = "ui_configuration_id"),
    @Index(name = "idx_form_field_order", columnList = "sort_order")
})
public class FormField extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "field_name", nullable = false, length = 100)
    private String fieldName;

    @Size(max = 100)
    @Column(name = "field_label", length = 100)
    private String fieldLabel;

    @Enumerated(EnumType.STRING)
    @Column(name = "field_type", nullable = false)
    private FieldType fieldType;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @Size(max = 200)
    @Column(name = "placeholder", length = 200)
    private String placeholder;

    @Column(name = "default_value")
    private String defaultValue;

    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = false;

    @Column(name = "is_readonly", nullable = false)
    private Boolean isReadonly = false;

    @Column(name = "is_disabled", nullable = false)
    private Boolean isDisabled = false;

    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible = true;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "jsonb")
    private Map<String, Object> validationRules = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "field_options", columnDefinition = "jsonb")
    private Map<String, Object> fieldOptions = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "style_properties", columnDefinition = "jsonb")
    private Map<String, Object> styleProperties = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "conditional_logic", columnDefinition = "jsonb")
    private Map<String, Object> conditionalLogic = new HashMap<>();

    @Column(name = "css_classes")
    private String cssClasses;

    @Column(name = "data_source")
    private String dataSource;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_binding", columnDefinition = "jsonb")
    private Map<String, Object> dataBinding = new HashMap<>();

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ui_configuration_id", nullable = false)
    @JsonIgnore
    private UIConfiguration uiConfiguration;

    // Constructors
    public FormField() {}

    public FormField(String fieldName, FieldType fieldType, UIConfiguration uiConfiguration) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.uiConfiguration = uiConfiguration;
        this.fieldLabel = fieldName; // Default label to field name
    }

    // Getters and Setters
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldLabel() {
        return fieldLabel;
    }

    public void setFieldLabel(String fieldLabel) {
        this.fieldLabel = fieldLabel;
    }

    public FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Boolean getIsReadonly() {
        return isReadonly;
    }

    public void setIsReadonly(Boolean isReadonly) {
        this.isReadonly = isReadonly;
    }

    public Boolean getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Map<String, Object> getFieldOptions() {
        return fieldOptions;
    }

    public void setFieldOptions(Map<String, Object> fieldOptions) {
        this.fieldOptions = fieldOptions;
    }

    public Map<String, Object> getStyleProperties() {
        return styleProperties;
    }

    public void setStyleProperties(Map<String, Object> styleProperties) {
        this.styleProperties = styleProperties;
    }

    public Map<String, Object> getConditionalLogic() {
        return conditionalLogic;
    }

    public void setConditionalLogic(Map<String, Object> conditionalLogic) {
        this.conditionalLogic = conditionalLogic;
    }

    public String getCssClasses() {
        return cssClasses;
    }

    public void setCssClasses(String cssClasses) {
        this.cssClasses = cssClasses;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Map<String, Object> getDataBinding() {
        return dataBinding;
    }

    public void setDataBinding(Map<String, Object> dataBinding) {
        this.dataBinding = dataBinding;
    }

    public UIConfiguration getUiConfiguration() {
        return uiConfiguration;
    }

    public void setUiConfiguration(UIConfiguration uiConfiguration) {
        this.uiConfiguration = uiConfiguration;
    }

    // Utility methods
    public void addValidationRule(String rule, Object value) {
        this.validationRules.put(rule, value);
    }

    public Object getValidationRule(String rule) {
        return this.validationRules.get(rule);
    }

    public void addFieldOption(String key, Object value) {
        this.fieldOptions.put(key, value);
    }

    public Object getFieldOption(String key) {
        return this.fieldOptions.get(key);
    }

    public boolean hasValidationRule(String rule) {
        return this.validationRules.containsKey(rule);
    }

    // Enums
    public enum FieldType {
        TEXT,
        EMAIL,
        PASSWORD,
        NUMBER,
        PHONE,
        URL,
        TEXTAREA,
        SELECT,
        MULTISELECT,
        RADIO,
        CHECKBOX,
        CHECKBOX_GROUP,
        DATE,
        DATETIME,
        TIME,
        FILE,
        IMAGE,
        HIDDEN,
        BUTTON,
        SUBMIT,
        RESET,
        RANGE,
        COLOR,
        SEARCH,
        MONTH,
        WEEK,
        RICH_TEXT,
        CODE,
        JSON,
        SIGNATURE,
        RATING,
        TOGGLE,
        SLIDER
    }

    // Validation rule constants
    public static final class ValidationRules {
        public static final String REQUIRED = "required";
        public static final String MIN_LENGTH = "minLength";
        public static final String MAX_LENGTH = "maxLength";
        public static final String MIN_VALUE = "minValue";
        public static final String MAX_VALUE = "maxValue";
        public static final String PATTERN = "pattern";
        public static final String EMAIL = "email";
        public static final String URL = "url";
        public static final String PHONE = "phone";
        public static final String CUSTOM = "custom";
        public static final String UNIQUE = "unique";
        public static final String CONFIRM = "confirm";
        public static final String FILE_SIZE = "fileSize";
        public static final String FILE_TYPE = "fileType";
        public static final String IMAGE_DIMENSIONS = "imageDimensions";
    }

    // Field option constants
    public static final class FieldOptions {
        public static final String OPTIONS = "options";
        public static final String MULTIPLE = "multiple";
        public static final String SEARCHABLE = "searchable";
        public static final String CLEARABLE = "clearable";
        public static final String LOADING = "loading";
        public static final String DISABLED_OPTIONS = "disabledOptions";
        public static final String GROUP_BY = "groupBy";
        public static final String CUSTOM_OPTION_LABEL = "customOptionLabel";
        public static final String ALLOW_CREATE = "allowCreate";
        public static final String MAX_SELECTIONS = "maxSelections";
        public static final String MIN_SELECTIONS = "minSelections";
    }
}
