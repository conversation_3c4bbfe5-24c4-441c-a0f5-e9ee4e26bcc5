package com.uiplatform.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.AuthDTO;
import com.uiplatform.dto.UserDTO;
import com.uiplatform.service.AuthenticationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for AuthController.
 */
@WebMvcTest(AuthController.class)
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AuthenticationService authenticationService;

    @Autowired
    private ObjectMapper objectMapper;

    private AuthDTO.LoginRequest loginRequest;
    private AuthDTO.LoginResponse loginResponse;
    private AuthDTO.RegisterRequest registerRequest;
    private UserDTO userDTO;

    @BeforeEach
    void setUp() {
        // Setup login request
        loginRequest = new AuthDTO.LoginRequest();
        loginRequest.setUsernameOrEmail("testuser");
        loginRequest.setPassword("password123");

        // Setup user DTO
        userDTO = new UserDTO();
        userDTO.setId(UUID.randomUUID());
        userDTO.setUsername("testuser");
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("Test");
        userDTO.setLastName("User");

        // Setup login response
        loginResponse = new AuthDTO.LoginResponse(
                "access-token",
                "refresh-token",
                3600L,
                userDTO
        );

        // Setup register request
        registerRequest = new AuthDTO.RegisterRequest();
        registerRequest.setUsername("newuser");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password123");
        registerRequest.setFirstName("New");
        registerRequest.setLastName("User");
        registerRequest.setOrganizationName("New Organization");
        registerRequest.setOrganizationSlug("new-org");
    }

    @Test
    void login_Success() throws Exception {
        // Arrange
        when(authenticationService.login(any(AuthDTO.LoginRequest.class)))
                .thenReturn(loginResponse);

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/login")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value("access-token"))
                .andExpect(jsonPath("$.data.refreshToken").value("refresh-token"))
                .andExpect(jsonPath("$.data.user.username").value("testuser"));
    }

    @Test
    void login_InvalidCredentials() throws Exception {
        // Arrange
        when(authenticationService.login(any(AuthDTO.LoginRequest.class)))
                .thenThrow(new RuntimeException("Invalid credentials"));

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/login")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Login failed: Invalid credentials"));
    }

    @Test
    void register_Success() throws Exception {
        // Arrange
        when(authenticationService.register(any(AuthDTO.RegisterRequest.class)))
                .thenReturn(loginResponse);

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value("access-token"))
                .andExpect(jsonPath("$.data.user.username").value("testuser"));
    }

    @Test
    void register_DuplicateUser() throws Exception {
        // Arrange
        when(authenticationService.register(any(AuthDTO.RegisterRequest.class)))
                .thenThrow(new RuntimeException("Username already exists"));

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Registration failed: Username already exists"));
    }

    @Test
    void refreshToken_Success() throws Exception {
        // Arrange
        AuthDTO.RefreshTokenRequest refreshRequest = new AuthDTO.RefreshTokenRequest();
        refreshRequest.setRefreshToken("refresh-token");

        when(authenticationService.refreshToken(any(AuthDTO.RefreshTokenRequest.class)))
                .thenReturn(loginResponse);

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/refresh")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value("access-token"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void changePassword_Success() throws Exception {
        // Arrange
        AuthDTO.ChangePasswordRequest changePasswordRequest = new AuthDTO.ChangePasswordRequest();
        changePasswordRequest.setCurrentPassword("oldpassword");
        changePasswordRequest.setNewPassword("newpassword");

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/change-password")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(changePasswordRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Password changed successfully"));
    }

    @Test
    void requestPasswordReset_Success() throws Exception {
        // Arrange
        AuthDTO.PasswordResetRequest resetRequest = new AuthDTO.PasswordResetRequest();
        resetRequest.setEmail("<EMAIL>");

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/forgot-password")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(resetRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Password reset email sent"));
    }

    @Test
    void confirmPasswordReset_Success() throws Exception {
        // Arrange
        AuthDTO.PasswordResetConfirmRequest confirmRequest = new AuthDTO.PasswordResetConfirmRequest();
        confirmRequest.setToken("reset-token");
        confirmRequest.setNewPassword("newpassword");

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/reset-password")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(confirmRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Password reset successful"));
    }

    @Test
    void verifyEmail_Success() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/verify-email")
                .with(csrf())
                .param("token", "verification-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Email verified successfully"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void logout_Success() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/logout")
                .with(csrf())
                .header("Authorization", "Bearer access-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Logout successful"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void getCurrentUser_Success() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/v1/auth/me")
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("testuser"));
    }

    @Test
    void login_ValidationError() throws Exception {
        // Arrange
        AuthDTO.LoginRequest invalidRequest = new AuthDTO.LoginRequest();
        // Missing required fields

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/login")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void register_ValidationError() throws Exception {
        // Arrange
        AuthDTO.RegisterRequest invalidRequest = new AuthDTO.RegisterRequest();
        // Missing required fields

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }
}
