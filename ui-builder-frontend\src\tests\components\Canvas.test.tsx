import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { DndContext } from '@dnd-kit/core';
import { Canvas } from '../../components/Canvas/Canvas';
import { store } from '../../store';
import { setSelectedComponent, addComponent } from '../../store/slices/canvasSlice';

// Mock the drag and drop functionality
jest.mock('@dnd-kit/core', () => ({
  ...jest.requireActual('@dnd-kit/core'),
  useDraggable: () => ({
    attributes: {},
    listeners: {},
    setNodeRef: jest.fn(),
    transform: null,
  }),
  useDroppable: () => ({
    setNodeRef: jest.fn(),
    isOver: false,
  }),
}));

const renderCanvas = (props = {}) => {
  return render(
    <Provider store={store}>
      <DndContext>
        <Canvas {...props} />
      </DndContext>
    </Provider>
  );
};

describe('Canvas Component', () => {
  beforeEach(() => {
    store.dispatch({ type: 'canvas/reset' });
  });

  describe('Rendering', () => {
    it('should render empty canvas', () => {
      renderCanvas();
      expect(screen.getByTestId('canvas')).toBeInTheDocument();
      expect(screen.getByText(/drop components here/i)).toBeInTheDocument();
    });

    it('should render components when present', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 100, y: 100 },
      }));

      renderCanvas();
      expect(screen.getByTestId('canvas-component-test-component')).toBeInTheDocument();
    });

    it('should show grid when enabled', () => {
      renderCanvas({ showGrid: true });
      expect(screen.getByTestId('canvas')).toHaveClass('canvas--grid');
    });
  });

  describe('Component Selection', () => {
    it('should select component on click', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 100, y: 100 },
      }));

      renderCanvas();
      
      const component = screen.getByTestId('canvas-component-test-component');
      fireEvent.click(component);

      const state = store.getState();
      expect(state.canvas.selectedComponentId).toBe('test-component');
    });

    it('should deselect component when clicking canvas', () => {
      store.dispatch(setSelectedComponent('test-component'));
      
      renderCanvas();
      
      const canvas = screen.getByTestId('canvas');
      fireEvent.click(canvas);

      const state = store.getState();
      expect(state.canvas.selectedComponentId).toBeNull();
    });

    it('should show selection outline for selected component', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 100, y: 100 },
      }));
      store.dispatch(setSelectedComponent('test-component'));

      renderCanvas();
      
      const component = screen.getByTestId('canvas-component-test-component');
      expect(component).toHaveClass('canvas-component--selected');
    });
  });

  describe('Drag and Drop', () => {
    it('should handle component drop', async () => {
      renderCanvas();
      
      const canvas = screen.getByTestId('canvas');
      
      // Simulate drop event
      fireEvent.drop(canvas, {
        dataTransfer: {
          getData: jest.fn(() => JSON.stringify({
            type: 'button',
            properties: { text: 'New Button' },
          })),
        },
      });

      await waitFor(() => {
        const state = store.getState();
        expect(state.canvas.components).toHaveLength(1);
      });
    });

    it('should prevent drop on invalid targets', () => {
      renderCanvas();
      
      const canvas = screen.getByTestId('canvas');
      
      // Simulate invalid drop
      fireEvent.dragOver(canvas, {
        dataTransfer: {
          types: ['text/plain'],
        },
      });

      fireEvent.drop(canvas, {
        dataTransfer: {
          getData: jest.fn(() => 'invalid data'),
        },
      });

      const state = store.getState();
      expect(state.canvas.components).toHaveLength(0);
    });
  });

  describe('Component Positioning', () => {
    it('should position components correctly', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 150, y: 200 },
      }));

      renderCanvas();
      
      const component = screen.getByTestId('canvas-component-test-component');
      expect(component).toHaveStyle({
        transform: 'translate(150px, 200px)',
      });
    });

    it('should snap to grid when enabled', () => {
      renderCanvas({ snapToGrid: true, gridSize: 20 });
      
      const canvas = screen.getByTestId('canvas');
      
      fireEvent.drop(canvas, {
        dataTransfer: {
          getData: jest.fn(() => JSON.stringify({
            type: 'button',
            properties: { text: 'New Button' },
          })),
        },
        clientX: 155,
        clientY: 175,
      });

      const state = store.getState();
      const component = state.canvas.components[0];
      expect(component.position.x).toBe(160); // Snapped to grid
      expect(component.position.y).toBe(180); // Snapped to grid
    });
  });

  describe('Zoom and Pan', () => {
    it('should handle zoom in', () => {
      renderCanvas();
      
      const zoomInButton = screen.getByTestId('zoom-in');
      fireEvent.click(zoomInButton);

      const canvas = screen.getByTestId('canvas-viewport');
      expect(canvas).toHaveStyle({
        transform: expect.stringContaining('scale(1.1)'),
      });
    });

    it('should handle zoom out', () => {
      renderCanvas();
      
      const zoomOutButton = screen.getByTestId('zoom-out');
      fireEvent.click(zoomOutButton);

      const canvas = screen.getByTestId('canvas-viewport');
      expect(canvas).toHaveStyle({
        transform: expect.stringContaining('scale(0.9)'),
      });
    });

    it('should reset zoom', () => {
      renderCanvas();
      
      // Zoom in first
      const zoomInButton = screen.getByTestId('zoom-in');
      fireEvent.click(zoomInButton);
      
      // Then reset
      const resetZoomButton = screen.getByTestId('reset-zoom');
      fireEvent.click(resetZoomButton);

      const canvas = screen.getByTestId('canvas-viewport');
      expect(canvas).toHaveStyle({
        transform: expect.stringContaining('scale(1)'),
      });
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should delete selected component with Delete key', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 100, y: 100 },
      }));
      store.dispatch(setSelectedComponent('test-component'));

      renderCanvas();
      
      fireEvent.keyDown(document, { key: 'Delete' });

      const state = store.getState();
      expect(state.canvas.components).toHaveLength(0);
    });

    it('should copy component with Ctrl+C', () => {
      store.dispatch(addComponent({
        id: 'test-component',
        type: 'button',
        properties: { text: 'Test Button' },
        position: { x: 100, y: 100 },
      }));
      store.dispatch(setSelectedComponent('test-component'));

      renderCanvas();
      
      fireEvent.keyDown(document, { key: 'c', ctrlKey: true });

      // Check if component is copied to clipboard (mock implementation)
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
    });

    it('should paste component with Ctrl+V', async () => {
      // Mock clipboard data
      Object.assign(navigator, {
        clipboard: {
          readText: jest.fn(() => Promise.resolve(JSON.stringify({
            type: 'button',
            properties: { text: 'Pasted Button' },
          }))),
        },
      });

      renderCanvas();
      
      fireEvent.keyDown(document, { key: 'v', ctrlKey: true });

      await waitFor(() => {
        const state = store.getState();
        expect(state.canvas.components).toHaveLength(1);
      });
    });
  });

  describe('Responsive Preview', () => {
    it('should switch to mobile preview', () => {
      renderCanvas();
      
      const mobileButton = screen.getByTestId('preview-mobile');
      fireEvent.click(mobileButton);

      const canvas = screen.getByTestId('canvas');
      expect(canvas).toHaveClass('canvas--mobile');
    });

    it('should switch to tablet preview', () => {
      renderCanvas();
      
      const tabletButton = screen.getByTestId('preview-tablet');
      fireEvent.click(tabletButton);

      const canvas = screen.getByTestId('canvas');
      expect(canvas).toHaveClass('canvas--tablet');
    });

    it('should switch to desktop preview', () => {
      renderCanvas();
      
      const desktopButton = screen.getByTestId('preview-desktop');
      fireEvent.click(desktopButton);

      const canvas = screen.getByTestId('canvas');
      expect(canvas).toHaveClass('canvas--desktop');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid component data gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      store.dispatch(addComponent({
        id: 'invalid-component',
        type: 'invalid-type',
        properties: {},
        position: { x: 100, y: 100 },
      }));

      renderCanvas();
      
      expect(screen.getByTestId('canvas')).toBeInTheDocument();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('should show error boundary for component errors', () => {
      const ThrowError = () => {
        throw new Error('Component error');
      };

      const ErrorComponent = () => <ThrowError />;
      
      store.dispatch(addComponent({
        id: 'error-component',
        type: 'custom',
        component: ErrorComponent,
        properties: {},
        position: { x: 100, y: 100 },
      }));

      renderCanvas();
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should handle large number of components', () => {
      // Add 100 components
      for (let i = 0; i < 100; i++) {
        store.dispatch(addComponent({
          id: `component-${i}`,
          type: 'button',
          properties: { text: `Button ${i}` },
          position: { x: i * 10, y: i * 10 },
        }));
      }

      const startTime = performance.now();
      renderCanvas();
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
      expect(screen.getByTestId('canvas')).toBeInTheDocument();
    });

    it('should virtualize components outside viewport', () => {
      // Add components far outside viewport
      store.dispatch(addComponent({
        id: 'far-component',
        type: 'button',
        properties: { text: 'Far Button' },
        position: { x: 5000, y: 5000 },
      }));

      renderCanvas();
      
      // Component should not be rendered due to virtualization
      expect(screen.queryByTestId('canvas-component-far-component')).not.toBeInTheDocument();
    });
  });
});
