{"name": "@ui-builder/icons", "version": "1.0.0", "description": "Icon library for UI Builder platform", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src", "assets"], "scripts": {"build": "vite build", "dev": "vite build --watch", "generate": "node scripts/generate-icons.js", "optimize": "node scripts/optimize-svgs.js", "test": "vitest", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}"}, "dependencies": {"@ui-builder/design-tokens": "workspace:*", "clsx": "^2.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "glob": "^10.3.10", "svgo": "^3.0.5", "typescript": "^5.2.2", "vite": "^5.0.5", "vite-plugin-dts": "^3.6.4", "vitest": "^0.34.6"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/ui-builder/component-library.git", "directory": "packages/icons"}, "keywords": ["icons", "svg", "react", "ui-builder", "design-system"], "author": "UI Builder Team", "license": "MIT"}