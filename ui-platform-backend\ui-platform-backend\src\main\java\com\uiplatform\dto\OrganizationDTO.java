package com.uiplatform.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.uiplatform.entity.Organization;
import jakarta.validation.constraints.*;

/**
 * DTO for Organization entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizationDTO extends BaseDTO {

    @NotBlank(message = "Organization name is required")
    @Size(max = 100, message = "Organization name must not exceed 100 characters")
    private String name;

    @NotBlank(message = "Organization slug is required")
    @Size(max = 50, message = "Organization slug must not exceed 50 characters")
    @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
    private String slug;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @Size(max = 100, message = "Domain must not exceed 100 characters")
    @Pattern(regexp = "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "Invalid domain format")
    private String domain;

    @Pattern(regexp = "^https?://.*", message = "Logo URL must be a valid HTTP/HTTPS URL")
    private String logoUrl;

    @Pattern(regexp = "^https?://.*", message = "Website URL must be a valid HTTP/HTTPS URL")
    private String websiteUrl;

    private Organization.OrganizationStatus status;
    private Organization.SubscriptionPlan subscriptionPlan;

    @Min(value = 1, message = "Max users must be at least 1")
    @Max(value = 10000, message = "Max users cannot exceed 10000")
    private Integer maxUsers;

    @Min(value = 1, message = "Max projects must be at least 1")
    @Max(value = 1000, message = "Max projects cannot exceed 1000")
    private Integer maxProjects;

    @Min(value = 100, message = "Max storage must be at least 100 MB")
    @Max(value = 1000000, message = "Max storage cannot exceed 1000000 MB")
    private Long maxStorageMb;

    // Statistics (read-only)
    private Long userCount;
    private Long projectCount;
    private Long templateCount;

    // Constructors
    public OrganizationDTO() {}

    public OrganizationDTO(String name, String slug) {
        this.name = name;
        this.slug = slug;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public Organization.OrganizationStatus getStatus() {
        return status;
    }

    public void setStatus(Organization.OrganizationStatus status) {
        this.status = status;
    }

    public Organization.SubscriptionPlan getSubscriptionPlan() {
        return subscriptionPlan;
    }

    public void setSubscriptionPlan(Organization.SubscriptionPlan subscriptionPlan) {
        this.subscriptionPlan = subscriptionPlan;
    }

    public Integer getMaxUsers() {
        return maxUsers;
    }

    public void setMaxUsers(Integer maxUsers) {
        this.maxUsers = maxUsers;
    }

    public Integer getMaxProjects() {
        return maxProjects;
    }

    public void setMaxProjects(Integer maxProjects) {
        this.maxProjects = maxProjects;
    }

    public Long getMaxStorageMb() {
        return maxStorageMb;
    }

    public void setMaxStorageMb(Long maxStorageMb) {
        this.maxStorageMb = maxStorageMb;
    }

    public Long getUserCount() {
        return userCount;
    }

    public void setUserCount(Long userCount) {
        this.userCount = userCount;
    }

    public Long getProjectCount() {
        return projectCount;
    }

    public void setProjectCount(Long projectCount) {
        this.projectCount = projectCount;
    }

    public Long getTemplateCount() {
        return templateCount;
    }

    public void setTemplateCount(Long templateCount) {
        this.templateCount = templateCount;
    }

    /**
     * Create DTO for organization creation.
     */
    public static class CreateDTO {
        @NotBlank(message = "Organization name is required")
        @Size(max = 100, message = "Organization name must not exceed 100 characters")
        private String name;

        @NotBlank(message = "Organization slug is required")
        @Size(max = 50, message = "Organization slug must not exceed 50 characters")
        @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
        private String slug;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @Size(max = 100, message = "Domain must not exceed 100 characters")
        @Pattern(regexp = "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "Invalid domain format")
        private String domain;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSlug() {
            return slug;
        }

        public void setSlug(String slug) {
            this.slug = slug;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }
    }

    /**
     * Update DTO for organization updates.
     */
    public static class UpdateDTO {
        @Size(max = 100, message = "Organization name must not exceed 100 characters")
        private String name;

        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;

        @Size(max = 100, message = "Domain must not exceed 100 characters")
        @Pattern(regexp = "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "Invalid domain format")
        private String domain;

        @Pattern(regexp = "^https?://.*", message = "Logo URL must be a valid HTTP/HTTPS URL")
        private String logoUrl;

        @Pattern(regexp = "^https?://.*", message = "Website URL must be a valid HTTP/HTTPS URL")
        private String websiteUrl;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getLogoUrl() {
            return logoUrl;
        }

        public void setLogoUrl(String logoUrl) {
            this.logoUrl = logoUrl;
        }

        public String getWebsiteUrl() {
            return websiteUrl;
        }

        public void setWebsiteUrl(String websiteUrl) {
            this.websiteUrl = websiteUrl;
        }
    }
}
