import { useState, useCallback, useRef, useEffect } from 'react';
import { apiClient } from '../services/apiClient';

export interface AnalyticsData {
  summary: {
    totalViews: number;
    totalEdits: number;
    activeUsers: number;
    averageSessionTime: number;
    totalConfigurations: number;
    publishedConfigurations: number;
  };
  trends: {
    views: { percentage: number; isPositive: boolean };
    edits: { percentage: number; isPositive: boolean };
    users: { percentage: number; isPositive: boolean };
    sessionTime: { percentage: number; isPositive: boolean };
  };
  timeSeries: Array<{
    timestamp: string;
    views: number;
    edits: number;
    collaborators: number;
    timeSpent: number;
  }>;
  componentUsage: Array<{
    componentName: string;
    componentType: string;
    usageCount: number;
    percentage: number;
    trend: number;
  }>;
  userActivity: Array<{
    userId: string;
    userName: string;
    editCount: number;
    timeSpent: number;
    lastActiveAt: string;
    configurationsCreated: number;
  }>;
  performance: {
    loadTime: number;
    renderTime: number;
    memoryUsage: number;
    errorRate: number;
    cacheHitRate: number;
  };
  heatmap: Array<{
    elementId: string;
    elementType: string;
    interactions: number;
    position: { x: number; y: number };
  }>;
}

export interface AnalyticsParams {
  configId?: string;
  organizationId?: string;
  workspaceId?: string;
  startDate: string;
  endDate: string;
  granularity: 'hour' | 'day' | 'week' | 'month';
  metrics?: string[];
}

export interface UseAnalyticsReturn {
  data: AnalyticsData | null;
  loading: boolean;
  error: string | null;
  
  // Data fetching methods
  fetchAnalytics: (params: AnalyticsParams) => Promise<void>;
  fetchComponentUsage: (configId: string, timeRange?: { start: string; end: string }) => Promise<void>;
  fetchUserActivity: (configId: string, timeRange?: { start: string; end: string }) => Promise<void>;
  fetchPerformanceMetrics: (configId: string, timeRange?: { start: string; end: string }) => Promise<void>;
  fetchHeatmapData: (configId: string, timeRange?: { start: string; end: string }) => Promise<void>;
  
  // Event tracking methods
  trackEvent: (event: AnalyticsEvent) => void;
  trackPageView: (page: string, configId?: string) => void;
  trackUserAction: (action: string, details?: Record<string, any>) => void;
  trackPerformance: (metric: string, value: number, configId?: string) => void;
  trackError: (error: Error, context?: Record<string, any>) => void;
  
  // Batch operations
  flushEvents: () => Promise<void>;
  clearCache: () => void;
}

export interface AnalyticsEvent {
  type: 'page_view' | 'user_action' | 'performance' | 'error' | 'custom';
  name: string;
  configId?: string;
  userId?: string;
  timestamp?: string;
  properties?: Record<string, any>;
  value?: number;
}

export const useAnalytics = (): UseAnalyticsReturn => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const eventQueueRef = useRef<AnalyticsEvent[]>([]);
  const flushTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFlushingRef = useRef(false);

  // Configuration
  const BATCH_SIZE = 50;
  const FLUSH_INTERVAL = 10000; // 10 seconds
  const MAX_QUEUE_SIZE = 1000;

  // Fetch analytics data
  const fetchAnalytics = useCallback(async (params: AnalyticsParams) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get('/analytics', { params });
      setData(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch component usage data
  const fetchComponentUsage = useCallback(async (
    configId: string, 
    timeRange?: { start: string; end: string }
  ) => {
    try {
      const params = {
        configId,
        ...timeRange
      };
      
      const response = await apiClient.get('/analytics/component-usage', { params });
      
      setData(prevData => ({
        ...prevData!,
        componentUsage: response.data
      }));
    } catch (err: any) {
      console.error('Component usage fetch error:', err);
    }
  }, []);

  // Fetch user activity data
  const fetchUserActivity = useCallback(async (
    configId: string,
    timeRange?: { start: string; end: string }
  ) => {
    try {
      const params = {
        configId,
        ...timeRange
      };
      
      const response = await apiClient.get('/analytics/user-activity', { params });
      
      setData(prevData => ({
        ...prevData!,
        userActivity: response.data
      }));
    } catch (err: any) {
      console.error('User activity fetch error:', err);
    }
  }, []);

  // Fetch performance metrics
  const fetchPerformanceMetrics = useCallback(async (
    configId: string,
    timeRange?: { start: string; end: string }
  ) => {
    try {
      const params = {
        configId,
        ...timeRange
      };
      
      const response = await apiClient.get('/analytics/performance', { params });
      
      setData(prevData => ({
        ...prevData!,
        performance: response.data
      }));
    } catch (err: any) {
      console.error('Performance metrics fetch error:', err);
    }
  }, []);

  // Fetch heatmap data
  const fetchHeatmapData = useCallback(async (
    configId: string,
    timeRange?: { start: string; end: string }
  ) => {
    try {
      const params = {
        configId,
        ...timeRange
      };
      
      const response = await apiClient.get('/analytics/heatmap', { params });
      
      setData(prevData => ({
        ...prevData!,
        heatmap: response.data
      }));
    } catch (err: any) {
      console.error('Heatmap data fetch error:', err);
    }
  }, []);

  // Add event to queue
  const queueEvent = useCallback((event: AnalyticsEvent) => {
    const enrichedEvent: AnalyticsEvent = {
      ...event,
      timestamp: event.timestamp || new Date().toISOString(),
      userId: event.userId || getCurrentUserId()
    };

    eventQueueRef.current.push(enrichedEvent);

    // Prevent queue from growing too large
    if (eventQueueRef.current.length > MAX_QUEUE_SIZE) {
      eventQueueRef.current = eventQueueRef.current.slice(-MAX_QUEUE_SIZE);
    }

    // Schedule flush if not already scheduled
    if (!flushTimeoutRef.current && !isFlushingRef.current) {
      flushTimeoutRef.current = setTimeout(() => {
        flushEvents();
      }, FLUSH_INTERVAL);
    }

    // Immediate flush if queue is full
    if (eventQueueRef.current.length >= BATCH_SIZE) {
      flushEvents();
    }
  }, []);

  // Flush events to server
  const flushEvents = useCallback(async () => {
    if (isFlushingRef.current || eventQueueRef.current.length === 0) {
      return;
    }

    isFlushingRef.current = true;

    try {
      const eventsToFlush = eventQueueRef.current.splice(0, BATCH_SIZE);
      
      await apiClient.post('/analytics/events', {
        events: eventsToFlush
      });

      console.log(`Flushed ${eventsToFlush.length} analytics events`);
    } catch (err: any) {
      console.error('Failed to flush analytics events:', err);
      // Re-add events to queue on failure
      eventQueueRef.current.unshift(...eventQueueRef.current);
    } finally {
      isFlushingRef.current = false;
      
      // Clear timeout
      if (flushTimeoutRef.current) {
        clearTimeout(flushTimeoutRef.current);
        flushTimeoutRef.current = null;
      }

      // Schedule next flush if there are more events
      if (eventQueueRef.current.length > 0) {
        flushTimeoutRef.current = setTimeout(() => {
          flushEvents();
        }, FLUSH_INTERVAL);
      }
    }
  }, []);

  // Track generic event
  const trackEvent = useCallback((event: AnalyticsEvent) => {
    queueEvent(event);
  }, [queueEvent]);

  // Track page view
  const trackPageView = useCallback((page: string, configId?: string) => {
    queueEvent({
      type: 'page_view',
      name: 'page_view',
      configId,
      properties: {
        page,
        url: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent
      }
    });
  }, [queueEvent]);

  // Track user action
  const trackUserAction = useCallback((action: string, details?: Record<string, any>) => {
    queueEvent({
      type: 'user_action',
      name: action,
      properties: {
        ...details,
        timestamp: new Date().toISOString()
      }
    });
  }, [queueEvent]);

  // Track performance metric
  const trackPerformance = useCallback((metric: string, value: number, configId?: string) => {
    queueEvent({
      type: 'performance',
      name: metric,
      configId,
      value,
      properties: {
        metric,
        value,
        timestamp: new Date().toISOString()
      }
    });
  }, [queueEvent]);

  // Track error
  const trackError = useCallback((error: Error, context?: Record<string, any>) => {
    queueEvent({
      type: 'error',
      name: 'error',
      properties: {
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...context,
        timestamp: new Date().toISOString()
      }
    });
  }, [queueEvent]);

  // Clear cache
  const clearCache = useCallback(() => {
    setData(null);
    eventQueueRef.current = [];
    
    if (flushTimeoutRef.current) {
      clearTimeout(flushTimeoutRef.current);
      flushTimeoutRef.current = null;
    }
  }, []);

  // Get current user ID (implement based on your auth system)
  const getCurrentUserId = useCallback(() => {
    // This should be implemented based on your authentication system
    return localStorage.getItem('userId') || 'anonymous';
  }, []);

  // Auto-flush on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (eventQueueRef.current.length > 0) {
        // Use sendBeacon for reliable delivery on page unload
        const events = eventQueueRef.current.splice(0);
        navigator.sendBeacon(
          `${process.env.REACT_APP_API_BASE_URL}/analytics/events`,
          JSON.stringify({ events })
        );
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      handleBeforeUnload(); // Flush remaining events
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (flushTimeoutRef.current) {
        clearTimeout(flushTimeoutRef.current);
      }
      flushEvents(); // Final flush
    };
  }, [flushEvents]);

  return {
    data,
    loading,
    error,
    fetchAnalytics,
    fetchComponentUsage,
    fetchUserActivity,
    fetchPerformanceMetrics,
    fetchHeatmapData,
    trackEvent,
    trackPageView,
    trackUserAction,
    trackPerformance,
    trackError,
    flushEvents,
    clearCache
  };
};
