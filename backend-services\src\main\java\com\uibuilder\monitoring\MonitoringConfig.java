package com.uibuilder.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.metrics.MetricsEndpoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Monitoring and Observability Configuration
 * 
 * Provides comprehensive monitoring including:
 * - Application Performance Monitoring (APM)
 * - Custom business metrics
 * - Health checks and indicators
 * - Request/response tracking
 * - Error tracking and alerting
 * - Resource utilization monitoring
 */
@Configuration
@EnableScheduling
public class MonitoringConfig {

    @Bean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    @Bean
    public RequestMetricsFilter requestMetricsFilter(MeterRegistry meterRegistry) {
        return new RequestMetricsFilter(meterRegistry);
    }

    @Bean
    public BusinessMetricsService businessMetricsService(MeterRegistry meterRegistry) {
        return new BusinessMetricsService(meterRegistry);
    }

    @Bean
    public SystemHealthIndicator systemHealthIndicator() {
        return new SystemHealthIndicator();
    }

    @Bean
    public DatabaseHealthIndicator databaseHealthIndicator() {
        return new DatabaseHealthIndicator();
    }

    @Bean
    public RedisHealthIndicator redisHealthIndicator() {
        return new RedisHealthIndicator();
    }

    @Bean
    public ExternalServiceHealthIndicator externalServiceHealthIndicator() {
        return new ExternalServiceHealthIndicator();
    }
}

/**
 * Request Metrics Filter
 * Tracks HTTP request metrics including response times, status codes, and throughput
 */
@Component
public class RequestMetricsFilter extends OncePerRequestFilter {
    
    private final Timer requestTimer;
    private final Counter requestCounter;
    private final Counter errorCounter;
    private final Map<String, Timer> endpointTimers = new ConcurrentHashMap<>();
    
    public RequestMetricsFilter(MeterRegistry meterRegistry) {
        this.requestTimer = Timer.builder("http.requests")
                .description("HTTP request duration")
                .register(meterRegistry);
        
        this.requestCounter = Counter.builder("http.requests.total")
                .description("Total HTTP requests")
                .register(meterRegistry);
        
        this.errorCounter = Counter.builder("http.requests.errors")
                .description("HTTP request errors")
                .register(meterRegistry);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        long startTime = System.nanoTime();
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String endpoint = method + " " + uri;
        
        try {
            filterChain.doFilter(request, response);
        } finally {
            long duration = System.nanoTime() - startTime;
            int status = response.getStatus();
            
            // Record general metrics
            requestTimer.record(Duration.ofNanos(duration));
            requestCounter.increment();
            
            // Record endpoint-specific metrics
            Timer endpointTimer = endpointTimers.computeIfAbsent(endpoint, 
                key -> Timer.builder("http.requests.endpoint")
                    .tag("method", method)
                    .tag("uri", uri)
                    .tag("status", String.valueOf(status))
                    .register(requestTimer.getId().getMeterRegistry()));
            
            endpointTimer.record(Duration.ofNanos(duration));
            
            // Record errors
            if (status >= 400) {
                errorCounter.increment();
            }
        }
    }
}

/**
 * Business Metrics Service
 * Tracks business-specific metrics and KPIs
 */
@Component
public class BusinessMetricsService {
    
    private final Counter configurationsCreated;
    private final Counter configurationsUpdated;
    private final Counter configurationsDeleted;
    private final Counter templatesUsed;
    private final Counter usersRegistered;
    private final Counter collaborationSessions;
    private final Gauge activeUsers;
    private final Gauge totalConfigurations;
    
    private final AtomicLong activeUserCount = new AtomicLong(0);
    private final AtomicLong totalConfigurationCount = new AtomicLong(0);
    
    public BusinessMetricsService(MeterRegistry meterRegistry) {
        this.configurationsCreated = Counter.builder("business.configurations.created")
                .description("Number of configurations created")
                .register(meterRegistry);
        
        this.configurationsUpdated = Counter.builder("business.configurations.updated")
                .description("Number of configurations updated")
                .register(meterRegistry);
        
        this.configurationsDeleted = Counter.builder("business.configurations.deleted")
                .description("Number of configurations deleted")
                .register(meterRegistry);
        
        this.templatesUsed = Counter.builder("business.templates.used")
                .description("Number of templates used")
                .register(meterRegistry);
        
        this.usersRegistered = Counter.builder("business.users.registered")
                .description("Number of users registered")
                .register(meterRegistry);
        
        this.collaborationSessions = Counter.builder("business.collaboration.sessions")
                .description("Number of collaboration sessions started")
                .register(meterRegistry);
        
        this.activeUsers = Gauge.builder("business.users.active")
                .description("Number of currently active users")
                .register(meterRegistry, this, BusinessMetricsService::getActiveUserCount);
        
        this.totalConfigurations = Gauge.builder("business.configurations.total")
                .description("Total number of configurations")
                .register(meterRegistry, this, BusinessMetricsService::getTotalConfigurationCount);
    }
    
    public void recordConfigurationCreated() {
        configurationsCreated.increment();
        totalConfigurationCount.incrementAndGet();
    }
    
    public void recordConfigurationUpdated() {
        configurationsUpdated.increment();
    }
    
    public void recordConfigurationDeleted() {
        configurationsDeleted.increment();
        totalConfigurationCount.decrementAndGet();
    }
    
    public void recordTemplateUsed(String templateId) {
        templatesUsed.increment("template", templateId);
    }
    
    public void recordUserRegistered() {
        usersRegistered.increment();
    }
    
    public void recordCollaborationSession() {
        collaborationSessions.increment();
    }
    
    public void updateActiveUserCount(long count) {
        activeUserCount.set(count);
    }
    
    public void updateTotalConfigurationCount(long count) {
        totalConfigurationCount.set(count);
    }
    
    private double getActiveUserCount() {
        return activeUserCount.get();
    }
    
    private double getTotalConfigurationCount() {
        return totalConfigurationCount.get();
    }
}

/**
 * System Health Indicator
 * Monitors overall system health including CPU, memory, and disk usage
 */
@Component
public class SystemHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
            
            Health.Builder builder = Health.up()
                    .withDetail("memory.max", formatBytes(maxMemory))
                    .withDetail("memory.total", formatBytes(totalMemory))
                    .withDetail("memory.used", formatBytes(usedMemory))
                    .withDetail("memory.free", formatBytes(freeMemory))
                    .withDetail("memory.usage.percent", String.format("%.2f%%", memoryUsagePercent))
                    .withDetail("processors", runtime.availableProcessors());
            
            // Check memory usage threshold
            if (memoryUsagePercent > 90) {
                builder.down().withDetail("reason", "High memory usage");
            } else if (memoryUsagePercent > 80) {
                builder.status("WARNING").withDetail("reason", "Memory usage above 80%");
            }
            
            return builder.build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
    
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}

/**
 * Database Health Indicator
 * Monitors database connectivity and performance
 */
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    // This would be injected in a real implementation
    // @Autowired
    // private DataSource dataSource;
    
    @Override
    public Health health() {
        try {
            // Simulate database health check
            // In real implementation, this would execute a simple query
            // Connection connection = dataSource.getConnection();
            // PreparedStatement statement = connection.prepareStatement("SELECT 1");
            // ResultSet resultSet = statement.executeQuery();
            
            long startTime = System.currentTimeMillis();
            // Simulate query execution
            Thread.sleep(10);
            long responseTime = System.currentTimeMillis() - startTime;
            
            Health.Builder builder = Health.up()
                    .withDetail("database", "PostgreSQL")
                    .withDetail("responseTime", responseTime + "ms")
                    .withDetail("status", "Connected");
            
            if (responseTime > 1000) {
                builder.down().withDetail("reason", "Slow database response");
            } else if (responseTime > 500) {
                builder.status("WARNING").withDetail("reason", "Database response time above 500ms");
            }
            
            return builder.build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("database", "PostgreSQL")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}

/**
 * Redis Health Indicator
 * Monitors Redis connectivity and performance
 */
@Component
public class RedisHealthIndicator implements HealthIndicator {
    
    // This would be injected in a real implementation
    // @Autowired
    // private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public Health health() {
        try {
            long startTime = System.currentTimeMillis();
            // Simulate Redis ping
            // redisTemplate.opsForValue().get("health-check");
            Thread.sleep(5);
            long responseTime = System.currentTimeMillis() - startTime;
            
            return Health.up()
                    .withDetail("redis", "Connected")
                    .withDetail("responseTime", responseTime + "ms")
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("redis", "Disconnected")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}

/**
 * External Service Health Indicator
 * Monitors external service dependencies
 */
@Component
public class ExternalServiceHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        Health.Builder builder = Health.up();
        
        // Check external services
        Map<String, String> services = Map.of(
            "auth-service", "https://auth.example.com/health",
            "file-storage", "https://storage.example.com/health",
            "notification-service", "https://notifications.example.com/health"
        );
        
        boolean allHealthy = true;
        
        for (Map.Entry<String, String> service : services.entrySet()) {
            try {
                // Simulate external service health check
                // In real implementation, this would make HTTP requests
                boolean isHealthy = Math.random() > 0.1; // 90% success rate
                
                if (isHealthy) {
                    builder.withDetail(service.getKey(), "UP");
                } else {
                    builder.withDetail(service.getKey(), "DOWN");
                    allHealthy = false;
                }
            } catch (Exception e) {
                builder.withDetail(service.getKey(), "ERROR: " + e.getMessage());
                allHealthy = false;
            }
        }
        
        if (!allHealthy) {
            builder.down();
        }
        
        return builder.build();
    }
}

/**
 * Metrics Scheduler
 * Periodically updates business metrics and system statistics
 */
@Component
public class MetricsScheduler {
    
    private final BusinessMetricsService businessMetricsService;
    private final AtomicInteger scheduledTasksExecuted = new AtomicInteger(0);
    
    public MetricsScheduler(BusinessMetricsService businessMetricsService) {
        this.businessMetricsService = businessMetricsService;
    }
    
    @Scheduled(fixedRate = 60000) // Every minute
    public void updateActiveUserCount() {
        try {
            // In real implementation, this would query the database or cache
            long activeUsers = Math.round(Math.random() * 100);
            businessMetricsService.updateActiveUserCount(activeUsers);
            scheduledTasksExecuted.incrementAndGet();
        } catch (Exception e) {
            // Log error
            System.err.println("Failed to update active user count: " + e.getMessage());
        }
    }
    
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void updateTotalConfigurationCount() {
        try {
            // In real implementation, this would query the database
            long totalConfigs = Math.round(Math.random() * 1000);
            businessMetricsService.updateTotalConfigurationCount(totalConfigs);
            scheduledTasksExecuted.incrementAndGet();
        } catch (Exception e) {
            // Log error
            System.err.println("Failed to update total configuration count: " + e.getMessage());
        }
    }
    
    @Scheduled(fixedRate = 3600000) // Every hour
    public void performSystemMaintenance() {
        try {
            // Perform system maintenance tasks
            System.gc(); // Suggest garbage collection
            scheduledTasksExecuted.incrementAndGet();
        } catch (Exception e) {
            // Log error
            System.err.println("Failed to perform system maintenance: " + e.getMessage());
        }
    }
    
    public int getScheduledTasksExecuted() {
        return scheduledTasksExecuted.get();
    }
}
