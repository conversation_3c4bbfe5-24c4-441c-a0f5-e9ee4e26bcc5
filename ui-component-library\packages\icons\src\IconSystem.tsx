import React, { forwardRef, useMemo } from 'react';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';
import { useTheme } from '../../core/src/theme/ThemeProvider';
import './IconSystem.scss';

// Icon library imports
import * as FeatherIcons from 'react-feather';
import * as HeroIcons from '@heroicons/react/24/outline';
import * as LucideIcons from 'lucide-react';

export interface IconProps extends ComponentProps {
  // Icon identification
  name: string;
  library?: 'feather' | 'heroicons' | 'lucide' | 'custom';
  
  // Appearance
  size?: number | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  color?: string;
  strokeWidth?: number;
  
  // Behavior
  interactive?: boolean;
  disabled?: boolean;
  loading?: boolean;
  
  // Accessibility
  title?: string;
  description?: string;
  
  // Events
  onClick?: (event: React.MouseEvent<SVGElement>) => void;
  onHover?: (event: React.MouseEvent<SVGElement>) => void;
}

export interface IconLibrary {
  name: string;
  icons: Record<string, React.ComponentType<any>>;
  defaultProps?: Record<string, any>;
}

export interface IconSearchOptions {
  query?: string;
  library?: string;
  category?: string;
  tags?: string[];
  size?: string;
}

/**
 * Icon System Component
 * 
 * Provides a unified interface for rendering icons from multiple libraries
 * with consistent sizing, theming, and accessibility features.
 */
export const Icon = forwardRef<SVGElement, IconProps>(({
  name,
  library = 'feather',
  size = 'md',
  color,
  strokeWidth = 2,
  interactive = false,
  disabled = false,
  loading = false,
  title,
  description,
  className,
  style,
  onClick,
  onHover,
  ...props
}, ref) => {
  const { theme } = useTheme();

  // Resolve icon size
  const iconSize = useMemo(() => {
    if (typeof size === 'number') return size;
    
    const sizeMap = {
      xs: 12,
      sm: 16,
      md: 20,
      lg: 24,
      xl: 32,
      '2xl': 48
    };
    
    return sizeMap[size] || 20;
  }, [size]);

  // Resolve icon color
  const iconColor = useMemo(() => {
    if (color) return color;
    if (disabled) return 'var(--color-text-muted, #94a3b8)';
    return 'var(--color-text-primary, #1e293b)';
  }, [color, disabled]);

  // Get icon component from library
  const IconComponent = useMemo(() => {
    switch (library) {
      case 'feather':
        return (FeatherIcons as any)[name];
      case 'heroicons':
        return (HeroIcons as any)[name];
      case 'lucide':
        return (LucideIcons as any)[name];
      case 'custom':
        return IconRegistry.getCustomIcon(name);
      default:
        return null;
    }
  }, [name, library]);

  // Build class names
  const classNames = useMemo(() => {
    const classes = ['ui-icon'];
    
    classes.push(`ui-icon--${library}`);
    classes.push(`ui-icon--${typeof size === 'string' ? size : 'custom'}`);
    
    if (interactive) classes.push('ui-icon--interactive');
    if (disabled) classes.push('ui-icon--disabled');
    if (loading) classes.push('ui-icon--loading');
    if (onClick) classes.push('ui-icon--clickable');
    
    if (className) classes.push(className);
    
    return classes.join(' ');
  }, [library, size, interactive, disabled, loading, onClick, className]);

  // Handle click events
  const handleClick = (event: React.MouseEvent<SVGElement>) => {
    if (disabled || loading) return;
    onClick?.(event);
  };

  // Handle hover events
  const handleMouseEnter = (event: React.MouseEvent<SVGElement>) => {
    if (disabled) return;
    onHover?.(event);
  };

  // Render loading spinner if loading
  if (loading) {
    return (
      <div className={classNames} style={style}>
        <svg
          width={iconSize}
          height={iconSize}
          viewBox="0 0 24 24"
          fill="none"
          stroke={iconColor}
          strokeWidth={strokeWidth}
          className="ui-icon-spinner"
        >
          <circle cx="12" cy="12" r="10" opacity="0.25" />
          <path d="M12 2a10 10 0 0 1 10 10" strokeLinecap="round" />
        </svg>
      </div>
    );
  }

  // Render error state if icon not found
  if (!IconComponent) {
    return (
      <div className={`${classNames} ui-icon--error`} style={style}>
        <svg
          width={iconSize}
          height={iconSize}
          viewBox="0 0 24 24"
          fill="none"
          stroke={iconColor}
          strokeWidth={strokeWidth}
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
        {title && <span className="sr-only">{title}</span>}
      </div>
    );
  }

  return (
    <IconComponent
      ref={ref}
      size={iconSize}
      color={iconColor}
      strokeWidth={strokeWidth}
      className={classNames}
      style={style}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      aria-label={title}
      aria-describedby={description}
      role={onClick ? 'button' : 'img'}
      tabIndex={onClick && !disabled ? 0 : -1}
      {...props}
    />
  );
});

Icon.displayName = 'Icon';

/**
 * Icon Registry for managing custom icons and libraries
 */
export class IconRegistry {
  private static customIcons = new Map<string, React.ComponentType<any>>();
  private static libraries = new Map<string, IconLibrary>();

  /**
   * Register a custom icon
   */
  static registerCustomIcon(name: string, component: React.ComponentType<any>): void {
    this.customIcons.set(name, component);
  }

  /**
   * Get a custom icon
   */
  static getCustomIcon(name: string): React.ComponentType<any> | null {
    return this.customIcons.get(name) || null;
  }

  /**
   * Register an icon library
   */
  static registerLibrary(library: IconLibrary): void {
    this.libraries.set(library.name, library);
  }

  /**
   * Get all available icons from a library
   */
  static getLibraryIcons(libraryName: string): string[] {
    const library = this.libraries.get(libraryName);
    return library ? Object.keys(library.icons) : [];
  }

  /**
   * Search icons across libraries
   */
  static searchIcons(options: IconSearchOptions): Array<{
    name: string;
    library: string;
    component: React.ComponentType<any>;
  }> {
    const results: Array<{
      name: string;
      library: string;
      component: React.ComponentType<any>;
    }> = [];

    // Search in registered libraries
    for (const [libraryName, library] of this.libraries) {
      if (options.library && options.library !== libraryName) continue;

      for (const [iconName, component] of Object.entries(library.icons)) {
        if (options.query && !iconName.toLowerCase().includes(options.query.toLowerCase())) {
          continue;
        }

        results.push({
          name: iconName,
          library: libraryName,
          component,
        });
      }
    }

    // Search in custom icons
    if (!options.library || options.library === 'custom') {
      for (const [iconName, component] of this.customIcons) {
        if (options.query && !iconName.toLowerCase().includes(options.query.toLowerCase())) {
          continue;
        }

        results.push({
          name: iconName,
          library: 'custom',
          component,
        });
      }
    }

    return results;
  }

  /**
   * Get all available libraries
   */
  static getLibraries(): string[] {
    return Array.from(this.libraries.keys());
  }

  /**
   * Clear all custom icons
   */
  static clearCustomIcons(): void {
    this.customIcons.clear();
  }
}

/**
 * Icon Browser Component for development and design tools
 */
export interface IconBrowserProps {
  onIconSelect?: (iconName: string, library: string) => void;
  selectedLibrary?: string;
  searchQuery?: string;
  className?: string;
}

export const IconBrowser: React.FC<IconBrowserProps> = ({
  onIconSelect,
  selectedLibrary,
  searchQuery = '',
  className
}) => {
  const [currentLibrary, setCurrentLibrary] = React.useState(selectedLibrary || 'feather');
  const [search, setSearch] = React.useState(searchQuery);

  const icons = useMemo(() => {
    return IconRegistry.searchIcons({
      query: search,
      library: currentLibrary,
    });
  }, [search, currentLibrary]);

  const libraries = useMemo(() => {
    return IconRegistry.getLibraries();
  }, []);

  return (
    <div className={`icon-browser ${className || ''}`}>
      <div className="icon-browser-header">
        <div className="icon-browser-search">
          <input
            type="text"
            placeholder="Search icons..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="icon-search-input"
          />
        </div>
        <div className="icon-browser-library-selector">
          <select
            value={currentLibrary}
            onChange={(e) => setCurrentLibrary(e.target.value)}
            className="library-select"
          >
            {libraries.map((library) => (
              <option key={library} value={library}>
                {library}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="icon-browser-grid">
        {icons.map(({ name, library, component: IconComponent }) => (
          <div
            key={`${library}-${name}`}
            className="icon-browser-item"
            onClick={() => onIconSelect?.(name, library)}
          >
            <div className="icon-preview">
              <Icon name={name} library={library as any} size="lg" />
            </div>
            <div className="icon-name">{name}</div>
          </div>
        ))}
      </div>
      
      {icons.length === 0 && (
        <div className="icon-browser-empty">
          <p>No icons found matching "{search}"</p>
        </div>
      )}
    </div>
  );
};

// Component configuration for UI Builder
export const IconConfig: ComponentConfig = {
  id: 'icon',
  type: 'icon',
  displayName: 'Icon',
  description: 'Display icons from various icon libraries',
  category: 'media',
  tags: ['icon', 'media', 'visual'],
  version: '1.0.0',
  
  properties: {
    name: {
      type: 'string',
      label: 'Icon Name',
      description: 'Name of the icon to display',
      group: 'content',
      order: 1,
      required: true
    },
    library: {
      type: 'select',
      label: 'Icon Library',
      description: 'Icon library to use',
      defaultValue: 'feather',
      options: [
        { label: 'Feather', value: 'feather' },
        { label: 'Heroicons', value: 'heroicons' },
        { label: 'Lucide', value: 'lucide' },
        { label: 'Custom', value: 'custom' }
      ],
      group: 'content',
      order: 2
    },
    size: {
      type: 'select',
      label: 'Size',
      description: 'Icon size',
      defaultValue: 'md',
      options: [
        { label: 'Extra Small', value: 'xs' },
        { label: 'Small', value: 'sm' },
        { label: 'Medium', value: 'md' },
        { label: 'Large', value: 'lg' },
        { label: 'Extra Large', value: 'xl' },
        { label: '2X Large', value: '2xl' }
      ],
      group: 'appearance',
      order: 3
    },
    color: {
      type: 'color',
      label: 'Color',
      description: 'Icon color',
      group: 'appearance',
      order: 4
    },
    strokeWidth: {
      type: 'number',
      label: 'Stroke Width',
      description: 'Icon stroke width',
      defaultValue: 2,
      min: 0.5,
      max: 4,
      step: 0.5,
      group: 'appearance',
      order: 5
    },
    interactive: {
      type: 'boolean',
      label: 'Interactive',
      description: 'Whether the icon responds to hover states',
      defaultValue: false,
      group: 'behavior',
      order: 6
    },
    disabled: {
      type: 'boolean',
      label: 'Disabled',
      description: 'Whether the icon is disabled',
      defaultValue: false,
      group: 'state',
      order: 7
    }
  },
  
  requiredProperties: ['name'],
  
  defaultProps: {
    name: 'star',
    library: 'feather',
    size: 'md',
    strokeWidth: 2,
    interactive: false,
    disabled: false
  },
  
  styling: {
    supportedStyles: ['margin', 'padding'],
    customCSS: true,
    themes: ['light', 'dark']
  },
  
  layout: {
    canHaveChildren: false,
    maxChildren: 0
  },
  
  events: [
    {
      name: 'onClick',
      label: 'On Click',
      description: 'Triggered when icon is clicked'
    },
    {
      name: 'onHover',
      label: 'On Hover',
      description: 'Triggered when icon is hovered'
    }
  ],
  
  accessibility: {
    role: 'img',
    ariaLabels: ['aria-label', 'aria-describedby'],
    keyboardNavigation: true
  },
  
  performance: {
    lazy: false,
    preload: true,
    cacheStrategy: 'memory'
  }
};

// Initialize built-in icon libraries
IconRegistry.registerLibrary({
  name: 'feather',
  icons: FeatherIcons as any,
  defaultProps: { strokeWidth: 2 }
});

IconRegistry.registerLibrary({
  name: 'heroicons',
  icons: HeroIcons as any,
  defaultProps: { strokeWidth: 1.5 }
});

IconRegistry.registerLibrary({
  name: 'lucide',
  icons: LucideIcons as any,
  defaultProps: { strokeWidth: 2 }
});
