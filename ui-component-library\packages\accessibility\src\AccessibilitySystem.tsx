import React, { useEffect, useRef, useState, useCallback } from 'react';
import { ComponentConfig, ComponentProps } from '../../core/src/types/component';

export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  enableFocusManagement: boolean;
  announceChanges: boolean;
  skipLinks: boolean;
}

export interface AriaAttributes {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-disabled'?: boolean;
  'aria-required'?: boolean;
  'aria-invalid'?: boolean;
  'aria-live'?: 'off' | 'polite' | 'assertive';
  'aria-atomic'?: boolean;
  'aria-busy'?: boolean;
  'aria-controls'?: string;
  'aria-owns'?: string;
  'aria-activedescendant'?: string;
  'aria-current'?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  'aria-selected'?: boolean;
  'aria-checked'?: boolean | 'mixed';
  'aria-pressed'?: boolean | 'mixed';
  'aria-level'?: number;
  'aria-setsize'?: number;
  'aria-posinset'?: number;
  role?: string;
  tabIndex?: number;
}

export interface FocusManagementOptions {
  trapFocus?: boolean;
  restoreFocus?: boolean;
  initialFocus?: string | HTMLElement;
  skipLinks?: string[];
}

/**
 * Accessibility System for Component Library
 * 
 * Provides comprehensive accessibility features including:
 * - ARIA attributes management
 * - Keyboard navigation support
 * - Screen reader announcements
 * - Focus management
 * - High contrast mode
 * - Reduced motion support
 * - WCAG compliance utilities
 */
export class AccessibilitySystem {
  private config: AccessibilityConfig;
  private announcer: HTMLElement | null = null;
  private focusHistory: HTMLElement[] = [];

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = {
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      enableFocusManagement: true,
      announceChanges: true,
      skipLinks: true,
      ...config,
    };

    this.initializeAccessibility();
  }

  /**
   * Initialize accessibility features
   */
  private initializeAccessibility(): void {
    if (typeof window === 'undefined') return;

    // Create screen reader announcer
    if (this.config.announceChanges) {
      this.createAnnouncer();
    }

    // Setup global keyboard handlers
    if (this.config.enableKeyboardNavigation) {
      this.setupKeyboardNavigation();
    }

    // Apply user preferences
    this.applyUserPreferences();

    // Setup skip links
    if (this.config.skipLinks) {
      this.setupSkipLinks();
    }
  }

  /**
   * Create screen reader announcer element
   */
  private createAnnouncer(): void {
    this.announcer = document.createElement('div');
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    this.announcer.style.position = 'absolute';
    this.announcer.style.left = '-10000px';
    this.announcer.style.width = '1px';
    this.announcer.style.height = '1px';
    this.announcer.style.overflow = 'hidden';
    document.body.appendChild(this.announcer);
  }

  /**
   * Announce message to screen readers
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.announcer || !this.config.announceChanges) return;

    this.announcer.setAttribute('aria-live', priority);
    this.announcer.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      if (this.announcer) {
        this.announcer.textContent = '';
      }
    }, 1000);
  }

  /**
   * Setup global keyboard navigation
   */
  private setupKeyboardNavigation(): void {
    document.addEventListener('keydown', this.handleGlobalKeyDown.bind(this));
  }

  /**
   * Handle global keyboard events
   */
  private handleGlobalKeyDown(event: KeyboardEvent): void {
    // Escape key handling
    if (event.key === 'Escape') {
      this.handleEscapeKey(event);
    }

    // Tab key handling for focus management
    if (event.key === 'Tab') {
      this.handleTabKey(event);
    }

    // Arrow key navigation
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      this.handleArrowKeys(event);
    }
  }

  /**
   * Handle escape key press
   */
  private handleEscapeKey(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    
    // Close modals, dropdowns, etc.
    const modal = target.closest('[role="dialog"], [role="alertdialog"]');
    if (modal) {
      const closeButton = modal.querySelector('[data-dismiss], [aria-label*="close" i]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
      }
    }

    // Close expanded elements
    const expanded = target.closest('[aria-expanded="true"]');
    if (expanded) {
      expanded.setAttribute('aria-expanded', 'false');
    }
  }

  /**
   * Handle tab key for focus management
   */
  private handleTabKey(event: KeyboardEvent): void {
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.indexOf(event.target as HTMLElement);
    
    if (event.shiftKey) {
      // Shift+Tab (backward)
      if (currentIndex === 0) {
        event.preventDefault();
        focusableElements[focusableElements.length - 1]?.focus();
      }
    } else {
      // Tab (forward)
      if (currentIndex === focusableElements.length - 1) {
        event.preventDefault();
        focusableElements[0]?.focus();
      }
    }
  }

  /**
   * Handle arrow key navigation
   */
  private handleArrowKeys(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    const role = target.getAttribute('role');

    // Handle specific roles
    if (['menu', 'menubar', 'listbox', 'tree', 'grid'].includes(role || '')) {
      event.preventDefault();
      this.navigateWithArrows(target, event.key);
    }
  }

  /**
   * Navigate with arrow keys
   */
  private navigateWithArrows(element: HTMLElement, key: string): void {
    const role = element.getAttribute('role');
    let selector = '';

    switch (role) {
      case 'menu':
      case 'menubar':
        selector = '[role="menuitem"]';
        break;
      case 'listbox':
        selector = '[role="option"]';
        break;
      case 'tree':
        selector = '[role="treeitem"]';
        break;
      case 'grid':
        selector = '[role="gridcell"]';
        break;
    }

    if (selector) {
      const items = Array.from(element.querySelectorAll(selector)) as HTMLElement[];
      const currentIndex = items.indexOf(document.activeElement as HTMLElement);
      let nextIndex = currentIndex;

      switch (key) {
        case 'ArrowUp':
          nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
          break;
        case 'ArrowDown':
          nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
          break;
        case 'ArrowLeft':
          if (role === 'menubar') {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
          }
          break;
        case 'ArrowRight':
          if (role === 'menubar') {
            nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
          }
          break;
      }

      if (items[nextIndex]) {
        items[nextIndex].focus();
      }
    }
  }

  /**
   * Get all focusable elements
   */
  private getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    const selector = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(container.querySelectorAll(selector)) as HTMLElement[];
  }

  /**
   * Apply user accessibility preferences
   */
  private applyUserPreferences(): void {
    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.enableReducedMotion = true;
      document.documentElement.classList.add('reduce-motion');
    }

    // Check for high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.enableHighContrast = true;
      document.documentElement.classList.add('high-contrast');
    }

    // Check for color scheme preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.classList.add('dark-mode');
    }
  }

  /**
   * Setup skip links
   */
  private setupSkipLinks(): void {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 10000;
      transition: top 0.3s;
    `;

    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  /**
   * Manage focus for modal dialogs
   */
  manageFocus(element: HTMLElement, options: FocusManagementOptions = {}): () => void {
    if (!this.config.enableFocusManagement) return () => {};

    const previousFocus = document.activeElement as HTMLElement;
    
    // Store focus history
    if (options.restoreFocus) {
      this.focusHistory.push(previousFocus);
    }

    // Set initial focus
    if (options.initialFocus) {
      const initialElement = typeof options.initialFocus === 'string'
        ? element.querySelector(options.initialFocus) as HTMLElement
        : options.initialFocus;
      
      if (initialElement) {
        initialElement.focus();
      }
    } else {
      // Focus first focusable element
      const focusableElements = this.getFocusableElements(element);
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    }

    // Trap focus if requested
    let focusTrapHandler: ((event: KeyboardEvent) => void) | null = null;
    
    if (options.trapFocus) {
      focusTrapHandler = (event: KeyboardEvent) => {
        if (event.key === 'Tab') {
          const focusableElements = this.getFocusableElements(element);
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (event.shiftKey) {
            if (document.activeElement === firstElement) {
              event.preventDefault();
              lastElement?.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              event.preventDefault();
              firstElement?.focus();
            }
          }
        }
      };

      document.addEventListener('keydown', focusTrapHandler);
    }

    // Return cleanup function
    return () => {
      if (focusTrapHandler) {
        document.removeEventListener('keydown', focusTrapHandler);
      }

      if (options.restoreFocus && this.focusHistory.length > 0) {
        const previousElement = this.focusHistory.pop();
        if (previousElement && document.contains(previousElement)) {
          previousElement.focus();
        }
      }
    };
  }

  /**
   * Generate ARIA attributes for component
   */
  generateAriaAttributes(config: ComponentConfig, props: ComponentProps): AriaAttributes {
    const attributes: AriaAttributes = {};

    // Basic attributes
    if (props.ariaLabel || config.accessibility?.ariaLabel) {
      attributes['aria-label'] = props.ariaLabel || config.accessibility?.ariaLabel;
    }

    if (props.ariaLabelledBy) {
      attributes['aria-labelledby'] = props.ariaLabelledBy;
    }

    if (props.ariaDescribedBy) {
      attributes['aria-describedby'] = props.ariaDescribedBy;
    }

    // Role
    if (config.accessibility?.role) {
      attributes.role = config.accessibility.role;
    }

    // State attributes
    if (props.disabled) {
      attributes['aria-disabled'] = true;
    }

    if (props.required) {
      attributes['aria-required'] = true;
    }

    if (props.invalid) {
      attributes['aria-invalid'] = true;
    }

    // Interactive attributes
    if (props.expanded !== undefined) {
      attributes['aria-expanded'] = props.expanded;
    }

    if (props.selected !== undefined) {
      attributes['aria-selected'] = props.selected;
    }

    if (props.checked !== undefined) {
      attributes['aria-checked'] = props.checked;
    }

    // Live region attributes
    if (props.live) {
      attributes['aria-live'] = props.live;
      attributes['aria-atomic'] = props.atomic || false;
    }

    // Keyboard navigation
    if (config.accessibility?.keyboardNavigation !== false) {
      attributes.tabIndex = props.disabled ? -1 : (props.tabIndex || 0);
    }

    return attributes;
  }

  /**
   * Validate accessibility compliance
   */
  validateAccessibility(element: HTMLElement): string[] {
    const issues: string[] = [];

    // Check for missing alt text on images
    const images = element.querySelectorAll('img');
    images.forEach(img => {
      if (!img.alt && !img.getAttribute('aria-label')) {
        issues.push('Image missing alt text or aria-label');
      }
    });

    // Check for missing labels on form controls
    const formControls = element.querySelectorAll('input, select, textarea');
    formControls.forEach(control => {
      const hasLabel = control.getAttribute('aria-label') ||
                      control.getAttribute('aria-labelledby') ||
                      element.querySelector(`label[for="${control.id}"]`);
      
      if (!hasLabel) {
        issues.push('Form control missing label');
      }
    });

    // Check for proper heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > previousLevel + 1) {
        issues.push('Heading hierarchy skipped a level');
      }
      previousLevel = level;
    });

    // Check for sufficient color contrast
    // This would require a more complex implementation with color analysis

    return issues;
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...updates };
    this.applyUserPreferences();
  }

  /**
   * Get current configuration
   */
  getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  /**
   * Cleanup accessibility system
   */
  cleanup(): void {
    if (this.announcer) {
      document.body.removeChild(this.announcer);
      this.announcer = null;
    }

    document.removeEventListener('keydown', this.handleGlobalKeyDown);
    this.focusHistory = [];
  }
}

// Global accessibility system instance
export const accessibilitySystem = new AccessibilitySystem();

/**
 * React hook for accessibility features
 */
export const useAccessibility = (config?: Partial<AccessibilityConfig>) => {
  const [system] = useState(() => new AccessibilitySystem(config));

  useEffect(() => {
    return () => system.cleanup();
  }, [system]);

  return {
    announce: system.announce.bind(system),
    manageFocus: system.manageFocus.bind(system),
    generateAriaAttributes: system.generateAriaAttributes.bind(system),
    validateAccessibility: system.validateAccessibility.bind(system),
    updateConfig: system.updateConfig.bind(system),
    getConfig: system.getConfig.bind(system),
  };
};

/**
 * Higher-order component for accessibility
 */
export const withAccessibility = <P extends object>(
  Component: React.ComponentType<P>,
  accessibilityConfig?: Partial<AccessibilityConfig>
) => {
  return React.forwardRef<any, P & { accessibility?: Partial<AccessibilityConfig> }>((props, ref) => {
    const { accessibility, ...componentProps } = props;
    const finalConfig = { ...accessibilityConfig, ...accessibility };
    const accessibilityHook = useAccessibility(finalConfig);

    return (
      <Component
        {...(componentProps as P)}
        ref={ref}
        accessibility={accessibilityHook}
      />
    );
  });
};
