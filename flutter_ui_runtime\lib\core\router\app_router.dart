import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/auth_provider.dart';
import '../../widgets/screens/home_screen.dart';
import '../../widgets/screens/login_screen.dart';
import '../../widgets/screens/configuration_screen.dart';
import '../../widgets/screens/preview_screen.dart';

/// Application router configuration
final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  
  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final isLoggedIn = authState.isAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login';
      
      if (!isLoggedIn && !isLoggingIn) {
        return '/login';
      }
      
      if (isLoggedIn && isLoggingIn) {
        return '/';
      }
      
      return null;
    },
    routes: [
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
        routes: [
          GoRoute(
            path: 'configuration/:id',
            name: 'configuration',
            builder: (context, state) {
              final configId = state.pathParameters['id']!;
              return ConfigurationScreen(configurationId: configId);
            },
          ),
          GoRoute(
            path: 'preview/:id',
            name: 'preview',
            builder: (context, state) {
              final configId = state.pathParameters['id']!;
              return PreviewScreen(configurationId: configId);
            },
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});

/// Router helper methods
extension AppRouterExtension on GoRouter {
  void goToConfiguration(String configId) {
    go('/configuration/$configId');
  }
  
  void goToPreview(String configId) {
    go('/preview/$configId');
  }
  
  void goToHome() {
    go('/');
  }
  
  void goToLogin() {
    go('/login');
  }
}
