.preview-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  position: relative;

  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: #000;
  }

  &--empty {
    justify-content: center;
    align-items: center;
  }
}

.preview-controls {
  border-radius: 0;
  border-left: none;
  border-right: none;
  border-top: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;

  .ant-card-body {
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .control-divider {
    width: 1px;
    height: 20px;
    background: #d9d9d9;
    margin: 0 8px;
  }

  .sync-status {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;

    .sync-indicator {
      color: #faad14;
      margin-right: 4px;
      animation: pulse 2s infinite;
    }
  }
}

.preview-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  overflow: auto;
  background: 
    radial-gradient(circle, #ccc 1px, transparent 1px),
    linear-gradient(90deg, transparent 24px, rgba(0, 0, 0, 0.04) 25px, transparent 26px),
    linear-gradient(transparent 24px, rgba(0, 0, 0, 0.04) 25px, transparent 26px);
  background-size: 25px 25px;
}

.preview-ruler {
  position: absolute;
  background: #fff;
  border: 1px solid #d9d9d9;
  z-index: 5;

  &--horizontal {
    top: 0;
    left: 30px;
    right: 0;
    height: 30px;
    background-image: repeating-linear-gradient(
      90deg,
      transparent,
      transparent 9px,
      #d9d9d9 10px
    );
  }

  &--vertical {
    top: 30px;
    left: 0;
    bottom: 0;
    width: 30px;
    background-image: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 9px,
      #d9d9d9 10px
    );
  }
}

.preview-viewport {
  position: relative;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;

  &--framed {
    border: 8px solid #333;
    border-radius: 16px;
    
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #666;
      border-radius: 2px;
      z-index: 10;
    }
  }
}

.preview-content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: auto;
}

.preview-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 1;
}

.preview-debug-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 100;

  .debug-info {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    line-height: 1.4;

    div {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.preview-empty-state {
  text-align: center;
  color: #999;

  .preview-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
  }

  h3 {
    margin-bottom: 8px;
    color: #666;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .preview-controls {
    .ant-card-body {
      flex-direction: column;
      gap: 8px;
      padding: 12px;
    }

    .control-divider {
      display: none;
    }
  }

  .preview-area {
    padding: 10px;
  }
}

// Animation keyframes
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Dark theme support
.dark-theme {
  .preview-panel {
    background: #1f1f1f;
  }

  .preview-area {
    background: 
      radial-gradient(circle, #555 1px, transparent 1px),
      linear-gradient(90deg, transparent 24px, rgba(255, 255, 255, 0.04) 25px, transparent 26px),
      linear-gradient(transparent 24px, rgba(255, 255, 255, 0.04) 25px, transparent 26px);
  }

  .preview-viewport {
    background: #2a2a2a;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);

    &--framed {
      border-color: #555;

      &::before {
        background: #888;
      }
    }
  }

  .preview-ruler {
    background: #2a2a2a;
    border-color: #555;

    &--horizontal {
      background-image: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 9px,
        #555 10px
      );
    }

    &--vertical {
      background-image: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 9px,
        #555 10px
      );
    }
  }

  .preview-grid {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }
}
