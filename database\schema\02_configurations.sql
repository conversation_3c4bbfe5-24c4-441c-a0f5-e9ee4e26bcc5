-- UI Builder Database Schema - Configurations and Templates
-- This file contains tables for UI configurations, templates, and components

-- UI Configurations table
CREATE TABLE ui_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    tags JSONB DEFAULT '[]',
    configuration JSONB NOT NULL,
    theme JSONB DEFAULT '{}',
    layout JSONB DEFAULT '{}',
    components JSONB DEFAULT '[]',
    data_sources JSONB DEFAULT '[]',
    interactions JSONB DEFAULT '[]',
    responsive_settings JSONB DEFAULT '{}',
    seo_settings JSONB DEFAULT '{}',
    performance_settings JSONB DEFAULT '{}',
    accessibility_settings JSONB DEFAULT '{}',
    version INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    thumbnail_url VARCHAR(500),
    preview_urls JSONB DEFAULT '[]',
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    published_by UUID REFERENCES users(id),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Configuration versions for history tracking
CREATE TABLE ui_configuration_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_id UUID NOT NULL REFERENCES ui_configurations(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    configuration JSONB NOT NULL,
    theme JSONB DEFAULT '{}',
    layout JSONB DEFAULT '{}',
    components JSONB DEFAULT '[]',
    change_summary TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(config_id, version)
);

-- Templates table (extends configurations)
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_id UUID NOT NULL REFERENCES ui_configurations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    tags JSONB DEFAULT '[]',
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_time INTEGER, -- in minutes
    preview_images JSONB DEFAULT '[]',
    demo_url VARCHAR(500),
    documentation_url VARCHAR(500),
    requirements JSONB DEFAULT '[]',
    features JSONB DEFAULT '[]',
    compatibility JSONB DEFAULT '{}',
    rating DECIMAL(3,2) DEFAULT 0.0,
    download_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    is_premium BOOLEAN DEFAULT FALSE,
    price DECIMAL(10,2),
    license VARCHAR(100) DEFAULT 'MIT',
    author_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Template ratings and reviews
CREATE TABLE template_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, user_id)
);

-- Template downloads tracking
CREATE TABLE template_downloads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Template likes/favorites
CREATE TABLE template_likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, user_id)
);

-- Component library
CREATE TABLE components (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    tags JSONB DEFAULT '[]',
    component_type VARCHAR(100) NOT NULL,
    properties_schema JSONB NOT NULL,
    default_props JSONB DEFAULT '{}',
    styling_options JSONB DEFAULT '{}',
    events JSONB DEFAULT '[]',
    documentation TEXT,
    examples JSONB DEFAULT '[]',
    version VARCHAR(50) DEFAULT '1.0.0',
    is_built_in BOOLEAN DEFAULT FALSE,
    is_deprecated BOOLEAN DEFAULT FALSE,
    icon_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    bundle_url VARCHAR(500),
    source_code TEXT,
    dependencies JSONB DEFAULT '[]',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Component usage analytics
CREATE TABLE component_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    component_id UUID NOT NULL REFERENCES components(id) ON DELETE CASCADE,
    config_id UUID REFERENCES ui_configurations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    usage_count INTEGER DEFAULT 1,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Configuration sharing and collaboration
CREATE TABLE config_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_id UUID NOT NULL REFERENCES ui_configurations(id) ON DELETE CASCADE,
    shared_by UUID NOT NULL REFERENCES users(id),
    shared_with_user UUID REFERENCES users(id) ON DELETE CASCADE,
    shared_with_organization UUID REFERENCES organizations(id) ON DELETE CASCADE,
    permission_level VARCHAR(20) DEFAULT 'view' CHECK (permission_level IN ('view', 'edit', 'admin')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Configuration comments for collaboration
CREATE TABLE config_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_id UUID NOT NULL REFERENCES ui_configurations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES config_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position JSONB DEFAULT '{}', -- x, y coordinates and element reference
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Asset management
CREATE TABLE assets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    alt_text VARCHAR(255),
    tags JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    is_public BOOLEAN DEFAULT FALSE,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_ui_configurations_organization_id ON ui_configurations(organization_id);
CREATE INDEX idx_ui_configurations_created_by ON ui_configurations(created_by);
CREATE INDEX idx_ui_configurations_status ON ui_configurations(status);
CREATE INDEX idx_ui_configurations_is_template ON ui_configurations(is_template);
CREATE INDEX idx_ui_configurations_category ON ui_configurations(category);
CREATE INDEX idx_ui_configurations_deleted_at ON ui_configurations(deleted_at);
CREATE INDEX idx_ui_configurations_tags ON ui_configurations USING GIN(tags);

CREATE INDEX idx_ui_configuration_versions_config_id ON ui_configuration_versions(config_id);
CREATE INDEX idx_ui_configuration_versions_version ON ui_configuration_versions(config_id, version);

CREATE INDEX idx_templates_config_id ON templates(config_id);
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_author_id ON templates(author_id);
CREATE INDEX idx_templates_rating ON templates(rating);
CREATE INDEX idx_templates_tags ON templates USING GIN(tags);

CREATE INDEX idx_template_reviews_template_id ON template_reviews(template_id);
CREATE INDEX idx_template_reviews_user_id ON template_reviews(user_id);

CREATE INDEX idx_template_downloads_template_id ON template_downloads(template_id);
CREATE INDEX idx_template_downloads_created_at ON template_downloads(created_at);

CREATE INDEX idx_template_likes_template_id ON template_likes(template_id);
CREATE INDEX idx_template_likes_user_id ON template_likes(user_id);

CREATE INDEX idx_components_organization_id ON components(organization_id);
CREATE INDEX idx_components_category ON components(category);
CREATE INDEX idx_components_component_type ON components(component_type);
CREATE INDEX idx_components_tags ON components USING GIN(tags);

CREATE INDEX idx_component_usage_component_id ON component_usage(component_id);
CREATE INDEX idx_component_usage_config_id ON component_usage(config_id);

CREATE INDEX idx_config_shares_config_id ON config_shares(config_id);
CREATE INDEX idx_config_shares_shared_with_user ON config_shares(shared_with_user);

CREATE INDEX idx_config_comments_config_id ON config_comments(config_id);
CREATE INDEX idx_config_comments_user_id ON config_comments(user_id);

CREATE INDEX idx_assets_organization_id ON assets(organization_id);
CREATE INDEX idx_assets_file_type ON assets(file_type);
CREATE INDEX idx_assets_uploaded_by ON assets(uploaded_by);
CREATE INDEX idx_assets_tags ON assets USING GIN(tags);

-- Triggers for updated_at timestamps
CREATE TRIGGER update_ui_configurations_updated_at BEFORE UPDATE ON ui_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_template_reviews_updated_at BEFORE UPDATE ON template_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_components_updated_at BEFORE UPDATE ON components
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_config_comments_updated_at BEFORE UPDATE ON config_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assets_updated_at BEFORE UPDATE ON assets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE ui_configurations IS 'UI configurations and projects created by users';
COMMENT ON TABLE ui_configuration_versions IS 'Version history for UI configurations';
COMMENT ON TABLE templates IS 'Template marketplace with ratings and downloads';
COMMENT ON TABLE template_reviews IS 'User reviews and ratings for templates';
COMMENT ON TABLE template_downloads IS 'Download tracking for templates';
COMMENT ON TABLE template_likes IS 'User likes/favorites for templates';
COMMENT ON TABLE components IS 'Component library with custom and built-in components';
COMMENT ON TABLE component_usage IS 'Analytics for component usage';
COMMENT ON TABLE config_shares IS 'Configuration sharing and permissions';
COMMENT ON TABLE config_comments IS 'Collaboration comments on configurations';
COMMENT ON TABLE assets IS 'File and media asset management';
