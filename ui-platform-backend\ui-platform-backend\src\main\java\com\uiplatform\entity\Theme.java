package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Theme entity for managing UI styling and branding.
 * Themes define the visual appearance of UI configurations.
 */
@Entity
@Table(name = "themes", indexes = {
    @Index(name = "idx_theme_name", columnList = "name"),
    @Index(name = "idx_theme_organization", columnList = "organization_id"),
    @Index(name = "idx_theme_is_default", columnList = "is_default"),
    @Index(name = "idx_theme_is_public", columnList = "is_public")
})
public class Theme extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "color_palette", columnDefinition = "jsonb")
    private Map<String, Object> colorPalette = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "typography", columnDefinition = "jsonb")
    private Map<String, Object> typography = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "spacing", columnDefinition = "jsonb")
    private Map<String, Object> spacing = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "borders", columnDefinition = "jsonb")
    private Map<String, Object> borders = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "shadows", columnDefinition = "jsonb")
    private Map<String, Object> shadows = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "animations", columnDefinition = "jsonb")
    private Map<String, Object> animations = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "component_styles", columnDefinition = "jsonb")
    private Map<String, Object> componentStyles = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "responsive_breakpoints", columnDefinition = "jsonb")
    private Map<String, Object> responsiveBreakpoints = new HashMap<>();

    @Column(name = "custom_css", columnDefinition = "TEXT")
    private String customCss;

    @Column(name = "css_variables", columnDefinition = "TEXT")
    private String cssVariables;

    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    @Column(name = "preview_image_url")
    private String previewImageUrl;

    @Column(name = "version", nullable = false)
    private String version = "1.0.0";

    @Column(name = "tags")
    private String tags;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    @JsonIgnore
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id")
    @JsonIgnore
    private User author;

    @OneToMany(mappedBy = "theme", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<UIConfiguration> uiConfigurations = new HashSet<>();

    // Constructors
    public Theme() {}

    public Theme(String name, Organization organization, User author) {
        this.name = name;
        this.organization = organization;
        this.author = author;
        initializeDefaultValues();
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getColorPalette() {
        return colorPalette;
    }

    public void setColorPalette(Map<String, Object> colorPalette) {
        this.colorPalette = colorPalette;
    }

    public Map<String, Object> getTypography() {
        return typography;
    }

    public void setTypography(Map<String, Object> typography) {
        this.typography = typography;
    }

    public Map<String, Object> getSpacing() {
        return spacing;
    }

    public void setSpacing(Map<String, Object> spacing) {
        this.spacing = spacing;
    }

    public Map<String, Object> getBorders() {
        return borders;
    }

    public void setBorders(Map<String, Object> borders) {
        this.borders = borders;
    }

    public Map<String, Object> getShadows() {
        return shadows;
    }

    public void setShadows(Map<String, Object> shadows) {
        this.shadows = shadows;
    }

    public Map<String, Object> getAnimations() {
        return animations;
    }

    public void setAnimations(Map<String, Object> animations) {
        this.animations = animations;
    }

    public Map<String, Object> getComponentStyles() {
        return componentStyles;
    }

    public void setComponentStyles(Map<String, Object> componentStyles) {
        this.componentStyles = componentStyles;
    }

    public Map<String, Object> getResponsiveBreakpoints() {
        return responsiveBreakpoints;
    }

    public void setResponsiveBreakpoints(Map<String, Object> responsiveBreakpoints) {
        this.responsiveBreakpoints = responsiveBreakpoints;
    }

    public String getCustomCss() {
        return customCss;
    }

    public void setCustomCss(String customCss) {
        this.customCss = customCss;
    }

    public String getCssVariables() {
        return cssVariables;
    }

    public void setCssVariables(String cssVariables) {
        this.cssVariables = cssVariables;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }

    public Set<UIConfiguration> getUiConfigurations() {
        return uiConfigurations;
    }

    public void setUiConfigurations(Set<UIConfiguration> uiConfigurations) {
        this.uiConfigurations = uiConfigurations;
    }

    // Utility methods
    private void initializeDefaultValues() {
        // Initialize default color palette
        Map<String, String> defaultColors = new HashMap<>();
        defaultColors.put("primary", "#007bff");
        defaultColors.put("secondary", "#6c757d");
        defaultColors.put("success", "#28a745");
        defaultColors.put("danger", "#dc3545");
        defaultColors.put("warning", "#ffc107");
        defaultColors.put("info", "#17a2b8");
        defaultColors.put("light", "#f8f9fa");
        defaultColors.put("dark", "#343a40");
        this.colorPalette.put("colors", defaultColors);

        // Initialize default typography
        Map<String, Object> defaultTypography = new HashMap<>();
        defaultTypography.put("fontFamily", "Arial, sans-serif");
        defaultTypography.put("fontSize", "14px");
        defaultTypography.put("lineHeight", "1.5");
        this.typography = defaultTypography;

        // Initialize default spacing
        Map<String, String> defaultSpacing = new HashMap<>();
        defaultSpacing.put("xs", "4px");
        defaultSpacing.put("sm", "8px");
        defaultSpacing.put("md", "16px");
        defaultSpacing.put("lg", "24px");
        defaultSpacing.put("xl", "32px");
        this.spacing.put("spacing", defaultSpacing);

        // Initialize default responsive breakpoints
        Map<String, String> defaultBreakpoints = new HashMap<>();
        defaultBreakpoints.put("xs", "0px");
        defaultBreakpoints.put("sm", "576px");
        defaultBreakpoints.put("md", "768px");
        defaultBreakpoints.put("lg", "992px");
        defaultBreakpoints.put("xl", "1200px");
        this.responsiveBreakpoints.put("breakpoints", defaultBreakpoints);
    }

    public void addColorTopalette(String name, String value) {
        @SuppressWarnings("unchecked")
        Map<String, String> colors = (Map<String, String>) this.colorPalette.get("colors");
        if (colors == null) {
            colors = new HashMap<>();
            this.colorPalette.put("colors", colors);
        }
        colors.put(name, value);
    }

    public String getColorFromPalette(String name) {
        @SuppressWarnings("unchecked")
        Map<String, String> colors = (Map<String, String>) this.colorPalette.get("colors");
        return colors != null ? colors.get(name) : null;
    }

    public void setAsDefault() {
        this.isDefault = true;
    }

    public void unsetAsDefault() {
        this.isDefault = false;
    }
}
