package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Template entity for the template marketplace.
 * Templates are reusable UI configurations that can be shared and sold.
 */
@Entity
@Table(name = "templates", indexes = {
    @Index(name = "idx_template_name", columnList = "name"),
    @Index(name = "idx_template_category", columnList = "category"),
    @Index(name = "idx_template_organization", columnList = "organization_id"),
    @Index(name = "idx_template_author", columnList = "author_id"),
    @Index(name = "idx_template_status", columnList = "status"),
    @Index(name = "idx_template_is_public", columnList = "is_public"),
    @Index(name = "idx_template_is_premium", columnList = "is_premium")
})
public class Template extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @Size(max = 2000)
    @Column(name = "long_description", length = 2000)
    private String longDescription;

    @Size(max = 50)
    @Column(name = "category", length = 50)
    private String category;

    @Size(max = 100)
    @Column(name = "subcategory", length = 100)
    private String subcategory;

    @Column(name = "tags")
    private String tags;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "template_data", columnDefinition = "jsonb")
    private Map<String, Object> templateData = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "preview_images", columnDefinition = "jsonb")
    private Map<String, Object> previewImages = new HashMap<>();

    @Column(name = "demo_url")
    private String demoUrl;

    @Column(name = "documentation_url")
    private String documentationUrl;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TemplateStatus status = TemplateStatus.DRAFT;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    @Column(name = "is_premium", nullable = false)
    private Boolean isPremium = false;

    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price = BigDecimal.ZERO;

    @Column(name = "license_type", length = 50)
    private String licenseType = "MIT";

    @Column(name = "version", nullable = false, length = 20)
    private String version = "1.0.0";

    @Column(name = "min_platform_version", length = 20)
    private String minPlatformVersion;

    @Column(name = "download_count", nullable = false)
    private Long downloadCount = 0L;

    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    @Column(name = "rating_count", nullable = false)
    private Long ratingCount = 0L;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "compatibility", columnDefinition = "jsonb")
    private Map<String, Object> compatibility = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "requirements", columnDefinition = "jsonb")
    private Map<String, Object> requirements = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "installation_guide", columnDefinition = "jsonb")
    private Map<String, Object> installationGuide = new HashMap<>();

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    @JsonIgnore
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id", nullable = false)
    @JsonIgnore
    private User author;

    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<TemplateReview> reviews = new HashSet<>();

    // Constructors
    public Template() {}

    public Template(String name, String category, Organization organization, User author) {
        this.name = name;
        this.category = category;
        this.organization = organization;
        this.author = author;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLongDescription() {
        return longDescription;
    }

    public void setLongDescription(String longDescription) {
        this.longDescription = longDescription;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Map<String, Object> getTemplateData() {
        return templateData;
    }

    public void setTemplateData(Map<String, Object> templateData) {
        this.templateData = templateData;
    }

    public Map<String, Object> getPreviewImages() {
        return previewImages;
    }

    public void setPreviewImages(Map<String, Object> previewImages) {
        this.previewImages = previewImages;
    }

    public String getDemoUrl() {
        return demoUrl;
    }

    public void setDemoUrl(String demoUrl) {
        this.demoUrl = demoUrl;
    }

    public String getDocumentationUrl() {
        return documentationUrl;
    }

    public void setDocumentationUrl(String documentationUrl) {
        this.documentationUrl = documentationUrl;
    }

    public TemplateStatus getStatus() {
        return status;
    }

    public void setStatus(TemplateStatus status) {
        this.status = status;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsPremium() {
        return isPremium;
    }

    public void setIsPremium(Boolean isPremium) {
        this.isPremium = isPremium;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMinPlatformVersion() {
        return minPlatformVersion;
    }

    public void setMinPlatformVersion(String minPlatformVersion) {
        this.minPlatformVersion = minPlatformVersion;
    }

    public Long getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Long getRatingCount() {
        return ratingCount;
    }

    public void setRatingCount(Long ratingCount) {
        this.ratingCount = ratingCount;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Map<String, Object> getCompatibility() {
        return compatibility;
    }

    public void setCompatibility(Map<String, Object> compatibility) {
        this.compatibility = compatibility;
    }

    public Map<String, Object> getRequirements() {
        return requirements;
    }

    public void setRequirements(Map<String, Object> requirements) {
        this.requirements = requirements;
    }

    public Map<String, Object> getInstallationGuide() {
        return installationGuide;
    }

    public void setInstallationGuide(Map<String, Object> installationGuide) {
        this.installationGuide = installationGuide;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }

    public Set<TemplateReview> getReviews() {
        return reviews;
    }

    public void setReviews(Set<TemplateReview> reviews) {
        this.reviews = reviews;
    }

    // Utility methods
    public void incrementDownloadCount() {
        this.downloadCount++;
    }

    public void incrementViewCount() {
        this.viewCount++;
    }

    public void updateRating(BigDecimal newRating) {
        if (this.ratingCount == 0) {
            this.rating = newRating;
            this.ratingCount = 1L;
        } else {
            BigDecimal totalRating = this.rating.multiply(BigDecimal.valueOf(this.ratingCount));
            totalRating = totalRating.add(newRating);
            this.ratingCount++;
            this.rating = totalRating.divide(BigDecimal.valueOf(this.ratingCount), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

    public void publish() {
        this.status = TemplateStatus.PUBLISHED;
        this.isPublic = true;
    }

    public void unpublish() {
        this.status = TemplateStatus.DRAFT;
        this.isPublic = false;
    }

    public boolean isFree() {
        return price.compareTo(BigDecimal.ZERO) == 0;
    }

    // Enums
    public enum TemplateStatus {
        DRAFT,
        REVIEW,
        PUBLISHED,
        REJECTED,
        ARCHIVED,
        DEPRECATED
    }

    // Template categories
    public static final class Categories {
        public static final String BUSINESS = "BUSINESS";
        public static final String ECOMMERCE = "ECOMMERCE";
        public static final String PORTFOLIO = "PORTFOLIO";
        public static final String BLOG = "BLOG";
        public static final String LANDING_PAGE = "LANDING_PAGE";
        public static final String DASHBOARD = "DASHBOARD";
        public static final String ADMIN = "ADMIN";
        public static final String EDUCATION = "EDUCATION";
        public static final String HEALTHCARE = "HEALTHCARE";
        public static final String REAL_ESTATE = "REAL_ESTATE";
        public static final String RESTAURANT = "RESTAURANT";
        public static final String TRAVEL = "TRAVEL";
        public static final String NONPROFIT = "NONPROFIT";
        public static final String PERSONAL = "PERSONAL";
        public static final String OTHER = "OTHER";
    }
}
