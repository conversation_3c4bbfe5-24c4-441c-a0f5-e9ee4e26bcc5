import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/ui_metadata.dart';
import '../models/theme_config.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../utils/logger.dart';

part 'app_state_provider.g.dart';

/// Application state that holds the current UI configuration
@riverpod
class AppState extends _$AppState {
  @override
  AppStateData build() {
    return const AppStateData();
  }

  /// Load UI metadata for a specific page
  Future<void> loadUIMetadata(String pageId, {Map<String, dynamic>? params}) async {
    try {
      state = state.copyWith(loading: true, error: null);
      
      final apiService = ref.read(apiServiceProvider);
      final metadata = await apiService.fetchUIMetadata(pageId, params: params);
      
      // Cache the metadata locally
      final storageService = ref.read(storageServiceProvider);
      await storageService.cacheUIMetadata(pageId, metadata);
      
      state = state.copyWith(
        metadata: metadata,
        loading: false,
        currentPageId: pageId,
      );
      
      AppLogger.info('UI metadata loaded for page: $pageId');
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to load UI metadata',
        error: error,
        stackTrace: stackTrace,
      );
      
      // Try to load from cache
      try {
        final storageService = ref.read(storageServiceProvider);
        final cachedMetadata = await storageService.getCachedUIMetadata(pageId);
        
        if (cachedMetadata != null) {
          state = state.copyWith(
            metadata: cachedMetadata,
            loading: false,
            currentPageId: pageId,
            error: 'Loaded from cache - network unavailable',
          );
          AppLogger.info('UI metadata loaded from cache for page: $pageId');
        } else {
          state = state.copyWith(
            loading: false,
            error: error.toString(),
          );
        }
      } catch (cacheError) {
        state = state.copyWith(
          loading: false,
          error: error.toString(),
        );
      }
    }
  }

  /// Update UI metadata (for real-time updates)
  void updateUIMetadata(UIMetadata metadata) {
    state = state.copyWith(metadata: metadata);
    AppLogger.debug('UI metadata updated via real-time sync');
  }

  /// Clear current UI metadata
  void clearUIMetadata() {
    state = state.copyWith(
      metadata: null,
      currentPageId: null,
      error: null,
    );
  }

  /// Set loading state
  void setLoading(bool loading) {
    state = state.copyWith(loading: loading);
  }

  /// Set error state
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// Refresh current page
  Future<void> refresh() async {
    final currentPageId = state.currentPageId;
    if (currentPageId != null) {
      await loadUIMetadata(currentPageId);
    }
  }
}

/// Application state data class
class AppStateData {
  final UIMetadata? metadata;
  final String? currentPageId;
  final bool loading;
  final String? error;
  final DateTime? lastUpdated;

  const AppStateData({
    this.metadata,
    this.currentPageId,
    this.loading = false,
    this.error,
    this.lastUpdated,
  });

  AppStateData copyWith({
    UIMetadata? metadata,
    String? currentPageId,
    bool? loading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return AppStateData(
      metadata: metadata ?? this.metadata,
      currentPageId: currentPageId ?? this.currentPageId,
      loading: loading ?? this.loading,
      error: error ?? this.error,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  bool get hasMetadata => metadata != null;
  bool get hasError => error != null;
  bool get isReady => hasMetadata && !loading;
}

/// Theme state provider
@riverpod
class ThemeState extends _$ThemeState {
  @override
  ThemeStateData build() {
    _loadSavedTheme();
    return const ThemeStateData();
  }

  /// Load theme configuration
  Future<void> loadTheme(String themeId) async {
    try {
      state = state.copyWith(loading: true);
      
      final apiService = ref.read(apiServiceProvider);
      final themeConfig = await apiService.fetchTheme(themeId);
      
      // Cache the theme locally
      final storageService = ref.read(storageServiceProvider);
      await storageService.cacheTheme(themeId, themeConfig);
      
      state = state.copyWith(
        themeConfig: themeConfig,
        currentThemeId: themeId,
        loading: false,
      );
      
      AppLogger.info('Theme loaded: $themeId');
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to load theme',
        error: error,
        stackTrace: stackTrace,
      );
      
      // Try to load from cache
      try {
        final storageService = ref.read(storageServiceProvider);
        final cachedTheme = await storageService.getCachedTheme(themeId);
        
        if (cachedTheme != null) {
          state = state.copyWith(
            themeConfig: cachedTheme,
            currentThemeId: themeId,
            loading: false,
          );
          AppLogger.info('Theme loaded from cache: $themeId');
        }
      } catch (cacheError) {
        AppLogger.error('Failed to load theme from cache', error: cacheError);
      }
    }
  }

  /// Update theme configuration (for real-time updates)
  void updateTheme(ThemeConfig themeConfig) {
    state = state.copyWith(themeConfig: themeConfig);
    AppLogger.debug('Theme updated via real-time sync');
  }

  /// Set theme mode (light/dark/system)
  Future<void> setThemeMode(ThemeMode mode) async {
    state = state.copyWith(themeMode: mode);
    
    // Save preference
    final storageService = ref.read(storageServiceProvider);
    await storageService.saveThemeMode(mode);
    
    AppLogger.debug('Theme mode changed to: $mode');
  }

  /// Load saved theme preferences
  Future<void> _loadSavedTheme() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final savedMode = await storageService.getThemeMode();
      
      if (savedMode != null) {
        state = state.copyWith(themeMode: savedMode);
      }
    } catch (error) {
      AppLogger.warning('Failed to load saved theme preferences: $error');
    }
  }
}

/// Theme state data class
class ThemeStateData {
  final ThemeConfig? themeConfig;
  final String? currentThemeId;
  final ThemeMode themeMode;
  final bool loading;

  const ThemeStateData({
    this.themeConfig,
    this.currentThemeId,
    this.themeMode = ThemeMode.system,
    this.loading = false,
  });

  ThemeStateData copyWith({
    ThemeConfig? themeConfig,
    String? currentThemeId,
    ThemeMode? themeMode,
    bool? loading,
  }) {
    return ThemeStateData(
      themeConfig: themeConfig ?? this.themeConfig,
      currentThemeId: currentThemeId ?? this.currentThemeId,
      themeMode: themeMode ?? this.themeMode,
      loading: loading ?? this.loading,
    );
  }

  bool get hasTheme => themeConfig != null;
}

/// Data state provider for dynamic data binding
@riverpod
class DataState extends _$DataState {
  @override
  Map<String, dynamic> build() {
    return {};
  }

  /// Set data value
  void setData(String key, dynamic value) {
    state = {...state, key: value};
    AppLogger.debug('Data updated: $key');
  }

  /// Update multiple data values
  void updateData(Map<String, dynamic> updates) {
    state = {...state, ...updates};
    AppLogger.debug('Data batch updated: ${updates.keys}');
  }

  /// Remove data value
  void removeData(String key) {
    final newState = Map<String, dynamic>.from(state);
    newState.remove(key);
    state = newState;
    AppLogger.debug('Data removed: $key');
  }

  /// Clear all data
  void clearData() {
    state = {};
    AppLogger.debug('All data cleared');
  }

  /// Get nested data value
  dynamic getData(String path) {
    final keys = path.split('.');
    dynamic current = state;
    
    for (final key in keys) {
      if (current is Map<String, dynamic>) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }
}

/// User state provider
@riverpod
class UserState extends _$UserState {
  @override
  UserStateData build() {
    _loadSavedUser();
    return const UserStateData();
  }

  /// Set current user
  void setUser(UserInfo user) {
    state = state.copyWith(user: user, isAuthenticated: true);
    
    // Save user info
    final storageService = ref.read(storageServiceProvider);
    storageService.saveUserInfo(user);
    
    AppLogger.info('User authenticated: ${user.id}');
  }

  /// Clear current user (logout)
  void clearUser() {
    state = const UserStateData();
    
    // Clear saved user info
    final storageService = ref.read(storageServiceProvider);
    storageService.clearUserInfo();
    
    AppLogger.info('User logged out');
  }

  /// Update user permissions
  void updatePermissions(List<String> permissions) {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(permissions: permissions);
      state = state.copyWith(user: updatedUser);
      AppLogger.debug('User permissions updated');
    }
  }

  /// Load saved user info
  Future<void> _loadSavedUser() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final savedUser = await storageService.getUserInfo();
      
      if (savedUser != null) {
        state = state.copyWith(user: savedUser, isAuthenticated: true);
        AppLogger.info('Saved user loaded: ${savedUser.id}');
      }
    } catch (error) {
      AppLogger.warning('Failed to load saved user: $error');
    }
  }
}

/// User state data class
class UserStateData {
  final UserInfo? user;
  final bool isAuthenticated;
  final List<String> permissions;

  const UserStateData({
    this.user,
    this.isAuthenticated = false,
    this.permissions = const [],
  });

  UserStateData copyWith({
    UserInfo? user,
    bool? isAuthenticated,
    List<String>? permissions,
  }) {
    return UserStateData(
      user: user ?? this.user,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      permissions: permissions ?? this.permissions,
    );
  }
}

/// User info model
class UserInfo {
  final String id;
  final String username;
  final String email;
  final List<String> roles;
  final List<String> permissions;
  final Map<String, dynamic> preferences;

  const UserInfo({
    required this.id,
    required this.username,
    required this.email,
    this.roles = const [],
    this.permissions = const [],
    this.preferences = const {},
  });

  UserInfo copyWith({
    String? id,
    String? username,
    String? email,
    List<String>? roles,
    List<String>? permissions,
    Map<String, dynamic>? preferences,
  }) {
    return UserInfo(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
      preferences: preferences ?? this.preferences,
    );
  }
}
