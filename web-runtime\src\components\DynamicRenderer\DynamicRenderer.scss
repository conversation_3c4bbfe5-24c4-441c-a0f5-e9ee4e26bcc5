.dynamic-renderer {
  width: 100%;
  height: 100%;
  position: relative;

  &[data-mode="design"] {
    .dynamic-renderer-content {
      pointer-events: none;
      
      * {
        user-select: none;
      }
    }
  }

  &[data-mode="preview"] {
    .dynamic-renderer-content {
      pointer-events: auto;
    }
  }

  &[data-mode="interactive"] {
    .dynamic-renderer-content {
      pointer-events: auto;
    }
  }
}

.dynamic-renderer-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.dynamic-renderer-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-style: italic;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background: #fafafa;
}

.debug-info {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  z-index: 1000;
  min-width: 150px;

  .debug-header {
    font-weight: bold;
    margin-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 2px;
  }

  .debug-details {
    div {
      margin-bottom: 2px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.debug-error {
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #a8071a;
  font-size: 12px;
  font-family: monospace;
  margin: 4px 0;
  
  &::before {
    content: "⚠️ ";
    margin-right: 4px;
  }
}

// Component-specific styles
.dynamic-component {
  position: relative;
  
  &.debug-mode {
    outline: 1px dashed rgba(24, 144, 255, 0.5);
    
    &::before {
      content: attr(data-component-type);
      position: absolute;
      top: -1px;
      left: -1px;
      background: #1890ff;
      color: white;
      font-size: 10px;
      padding: 2px 4px;
      border-radius: 2px;
      z-index: 10;
      pointer-events: none;
    }
    
    &:hover {
      outline-color: #1890ff;
      
      &::before {
        background: #096dd9;
      }
    }
  }
}

// Loading states
.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  color: #999;
  
  .loading-spinner {
    margin-right: 8px;
  }
}

// Error states
.component-error {
  padding: 16px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  color: #a8071a;
  
  .error-title {
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    
    &::before {
      content: "❌";
      margin-right: 8px;
    }
  }
  
  .error-message {
    font-size: 14px;
    margin-bottom: 12px;
    font-family: monospace;
    background: rgba(255, 255, 255, 0.5);
    padding: 8px;
    border-radius: 4px;
  }
  
  .error-actions {
    display: flex;
    gap: 8px;
    
    button {
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      
      &:hover {
        background: #f5f5f5;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .debug-info {
    position: fixed;
    top: 8px;
    right: 8px;
    left: auto;
    max-width: 200px;
  }
  
  .dynamic-renderer {
    &[data-mode="design"] {
      .debug-info {
        background: rgba(0, 0, 0, 0.9);
      }
    }
  }
}

// Dark theme support
.dark-theme {
  .dynamic-renderer-empty {
    background: #2a2a2a;
    border-color: #555;
    color: #ccc;
  }
  
  .debug-error {
    background: #2a1f1f;
    border-color: #5c2c2c;
    color: #ff7875;
  }
  
  .component-error {
    background: #2a1f1f;
    border-color: #5c2c2c;
    color: #ff7875;
    
    .error-message {
      background: rgba(0, 0, 0, 0.3);
    }
    
    .error-actions button {
      background: #333;
      border-color: #555;
      color: #ccc;
      
      &:hover {
        background: #444;
      }
    }
  }
}

// Animation classes
.component-enter {
  opacity: 0;
  transform: translateY(10px);
}

.component-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease, transform 300ms ease;
}

.component-exit {
  opacity: 1;
  transform: translateY(0);
}

.component-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms ease, transform 300ms ease;
}

// Interaction states
.interactive-mode {
  .dynamic-component {
    &:hover {
      cursor: pointer;
    }
    
    &:focus {
      outline: 2px solid #1890ff;
      outline-offset: 2px;
    }
    
    &:active {
      transform: scale(0.98);
      transition: transform 100ms ease;
    }
  }
}

// Performance optimization classes
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// Accessibility improvements
.dynamic-renderer {
  &:focus-within {
    .debug-info {
      opacity: 0.7;
    }
  }
}

@media (prefers-reduced-motion: reduce) {
  .component-enter-active,
  .component-exit-active {
    transition: none;
  }
  
  .interactive-mode .dynamic-component:active {
    transform: none;
  }
}

// Print styles
@media print {
  .debug-info,
  .debug-error {
    display: none;
  }
  
  .dynamic-renderer {
    &[data-mode="design"],
    &[data-mode="preview"],
    &[data-mode="interactive"] {
      .dynamic-renderer-content {
        pointer-events: none;
      }
    }
  }
}
