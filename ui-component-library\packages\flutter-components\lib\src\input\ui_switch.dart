import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Switch component
class UISwitch extends StatelessWidget {
  const UISwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.subtitle,
    this.enabled = true,
    this.size = UISize.md,
    this.variant = UIColorVariant.primary,
    this.controlAffinity = ListTileControlAffinity.trailing,
  });

  final bool value;
  final ValueChanged<bool>? onChanged;
  final String? label;
  final String? subtitle;
  final bool enabled;
  final UISize size;
  final UIColorVariant variant;
  final ListTileControlAffinity controlAffinity;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (label == null && subtitle == null) {
      return Switch(
        value: value,
        onChanged: enabled ? onChanged : null,
        activeColor: variant.getColor(colorScheme),
      );
    }

    return SwitchListTile(
      value: value,
      onChanged: enabled ? onChanged : null,
      title: label != null ? Text(label!) : null,
      subtitle: subtitle != null ? Text(subtitle!) : null,
      controlAffinity: controlAffinity,
      activeColor: variant.getColor(colorScheme),
    );
  }
}
