package com.uiplatform.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing activity logs for tracking changes and user actions.
 */
@Entity
@Table(name = "activity_logs", indexes = {
    @Index(name = "idx_activity_config_id", columnList = "ui_configuration_id"),
    @Index(name = "idx_activity_user_id", columnList = "user_id"),
    @Index(name = "idx_activity_type", columnList = "activity_type"),
    @Index(name = "idx_activity_timestamp", columnList = "timestamp"),
    @Index(name = "idx_activity_organization", columnList = "organization_id")
})
public class ActivityLog {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "ui_configuration_id")
    private UUID uiConfigurationId;

    @Column(name = "organization_id", nullable = false)
    private UUID organizationId;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "username", nullable = false, length = 100)
    private String username;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type", nullable = false, length = 50)
    private ActivityType activityType;

    @Column(name = "entity_type", length = 50)
    private String entityType; // "component", "ui_config", "comment", etc.

    @Column(name = "entity_id", length = 255)
    private String entityId;

    @Column(name = "action", nullable = false, length = 50)
    private String action; // "create", "update", "delete", "move", etc.

    @Column(name = "description", nullable = false, length = 500)
    private String description;

    @Column(name = "details", columnDefinition = "TEXT")
    private String details; // JSON with detailed information

    @Column(name = "old_values", columnDefinition = "TEXT")
    private String oldValues; // JSON with previous values

    @Column(name = "new_values", columnDefinition = "TEXT")
    private String newValues; // JSON with new values

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "severity", length = 20)
    private Severity severity = Severity.INFO;

    @Column(name = "tags", length = 500)
    private String tags; // Comma-separated tags

    @CreationTimestamp
    @Column(name = "timestamp", nullable = false, updatable = false)
    private LocalDateTime timestamp;

    @Column(name = "duration_ms")
    private Long durationMs; // For performance tracking

    @Column(name = "is_system_action", nullable = false)
    private Boolean isSystemAction = false;

    // Constructors
    public ActivityLog() {}

    public ActivityLog(UUID organizationId, UUID userId, String username, 
                      ActivityType activityType, String action, String description) {
        this.organizationId = organizationId;
        this.userId = userId;
        this.username = username;
        this.activityType = activityType;
        this.action = action;
        this.description = description;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUiConfigurationId() {
        return uiConfigurationId;
    }

    public void setUiConfigurationId(UUID uiConfigurationId) {
        this.uiConfigurationId = uiConfigurationId;
    }

    public UUID getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(UUID organizationId) {
        this.organizationId = organizationId;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public ActivityType getActivityType() {
        return activityType;
    }

    public void setActivityType(ActivityType activityType) {
        this.activityType = activityType;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getOldValues() {
        return oldValues;
    }

    public void setOldValues(String oldValues) {
        this.oldValues = oldValues;
    }

    public String getNewValues() {
        return newValues;
    }

    public void setNewValues(String newValues) {
        this.newValues = newValues;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Severity getSeverity() {
        return severity;
    }

    public void setSeverity(Severity severity) {
        this.severity = severity;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public Long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(Long durationMs) {
        this.durationMs = durationMs;
    }

    public Boolean getIsSystemAction() {
        return isSystemAction;
    }

    public void setIsSystemAction(Boolean isSystemAction) {
        this.isSystemAction = isSystemAction;
    }

    // Enums
    public enum ActivityType {
        // UI Configuration activities
        UI_CONFIG_CREATED,
        UI_CONFIG_UPDATED,
        UI_CONFIG_DELETED,
        UI_CONFIG_PUBLISHED,
        UI_CONFIG_UNPUBLISHED,
        UI_CONFIG_CLONED,
        
        // Component activities
        COMPONENT_CREATED,
        COMPONENT_UPDATED,
        COMPONENT_DELETED,
        COMPONENT_MOVED,
        COMPONENT_STYLED,
        
        // Collaboration activities
        COMMENT_CREATED,
        COMMENT_UPDATED,
        COMMENT_DELETED,
        COMMENT_RESOLVED,
        
        // User activities
        USER_JOINED,
        USER_LEFT,
        USER_CONNECTED,
        USER_DISCONNECTED,
        
        // Template activities
        TEMPLATE_CREATED,
        TEMPLATE_UPDATED,
        TEMPLATE_PUBLISHED,
        TEMPLATE_DOWNLOADED,
        TEMPLATE_CLONED,
        
        // System activities
        SYSTEM_BACKUP,
        SYSTEM_RESTORE,
        SYSTEM_MAINTENANCE,
        
        // Security activities
        LOGIN_SUCCESS,
        LOGIN_FAILED,
        LOGOUT,
        PASSWORD_CHANGED,
        PERMISSION_GRANTED,
        PERMISSION_REVOKED,
        
        // Integration activities
        API_CALL,
        WEBHOOK_TRIGGERED,
        EXPORT_PERFORMED,
        IMPORT_PERFORMED
    }

    public enum Severity {
        DEBUG,
        INFO,
        WARNING,
        ERROR,
        CRITICAL
    }

    @Override
    public String toString() {
        return "ActivityLog{" +
                "id=" + id +
                ", activityType=" + activityType +
                ", action='" + action + '\'' +
                ", username='" + username + '\'' +
                ", description='" + description + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
