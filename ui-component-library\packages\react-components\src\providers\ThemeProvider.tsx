import React, { createContext, useContext, useEffect, useState } from 'react';
import { tokens } from '@ui-builder/design-tokens';

export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  fontFamily: string;
  borderRadius: number;
  density: 'compact' | 'comfortable' | 'standard';
  reducedMotion: boolean;
  highContrast: boolean;
}

export interface ThemeContextValue {
  theme: ThemeConfig;
  actualMode: 'light' | 'dark';
  setTheme: (theme: Partial<ThemeConfig>) => void;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  resetTheme: () => void;
}

const defaultTheme: ThemeConfig = {
  mode: 'system',
  primaryColor: tokens.colorBrandPrimary500,
  fontFamily: tokens.fontFamilySans.join(', '),
  borderRadius: parseInt(tokens.borderRadiusMd),
  density: 'comfortable',
  reducedMotion: false,
  highContrast: false,
};

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Partial<ThemeConfig>;
  storageKey?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
}

export function ThemeProvider({
  children,
  defaultTheme: defaultThemeProp,
  storageKey = 'ui-builder-theme',
  enableSystem = true,
  disableTransitionOnChange = false,
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<ThemeConfig>(() => {
    if (typeof window === 'undefined') {
      return { ...defaultTheme, ...defaultThemeProp };
    }

    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsedTheme = JSON.parse(stored);
        return { ...defaultTheme, ...defaultThemeProp, ...parsedTheme };
      }
    } catch (error) {
      console.warn('Failed to parse stored theme:', error);
    }

    return { ...defaultTheme, ...defaultThemeProp };
  });

  const [systemMode, setSystemMode] = useState<'light' | 'dark'>('light');

  // Listen for system theme changes
  useEffect(() => {
    if (!enableSystem) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemMode(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemMode(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [enableSystem]);

  // Listen for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setThemeState(prev => ({ ...prev, reducedMotion: e.matches }));
    };

    setThemeState(prev => ({ ...prev, reducedMotion: mediaQuery.matches }));
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Listen for high contrast preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setThemeState(prev => ({ ...prev, highContrast: e.matches }));
    };

    setThemeState(prev => ({ ...prev, highContrast: mediaQuery.matches }));
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const actualMode = theme.mode === 'system' ? systemMode : theme.mode;

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Disable transitions temporarily if requested
    if (disableTransitionOnChange) {
      root.style.setProperty('--transition-duration', '0ms');
    }

    // Set theme mode
    root.setAttribute('data-theme', actualMode);
    root.setAttribute('data-density', theme.density);
    
    // Set CSS custom properties
    root.style.setProperty('--primary-color', theme.primaryColor);
    root.style.setProperty('--font-family', theme.fontFamily);
    root.style.setProperty('--border-radius', `${theme.borderRadius}px`);
    
    // Set accessibility preferences
    if (theme.reducedMotion) {
      root.setAttribute('data-reduced-motion', 'true');
    } else {
      root.removeAttribute('data-reduced-motion');
    }
    
    if (theme.highContrast) {
      root.setAttribute('data-high-contrast', 'true');
    } else {
      root.removeAttribute('data-high-contrast');
    }

    // Re-enable transitions
    if (disableTransitionOnChange) {
      setTimeout(() => {
        root.style.removeProperty('--transition-duration');
      }, 1);
    }
  }, [theme, actualMode, disableTransitionOnChange]);

  // Save theme to localStorage
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(theme));
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }, [theme, storageKey]);

  const setTheme = (newTheme: Partial<ThemeConfig>) => {
    setThemeState(prev => ({ ...prev, ...newTheme }));
  };

  const setMode = (mode: ThemeMode) => {
    setThemeState(prev => ({ ...prev, mode }));
  };

  const toggleMode = () => {
    setThemeState(prev => ({
      ...prev,
      mode: prev.mode === 'light' ? 'dark' : 'light',
    }));
  };

  const resetTheme = () => {
    setThemeState({ ...defaultTheme, ...defaultThemeProp });
  };

  const value: ThemeContextValue = {
    theme,
    actualMode,
    setTheme,
    setMode,
    toggleMode,
    resetTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook for getting theme-aware values
export function useThemeValue<T>(lightValue: T, darkValue: T): T {
  const { actualMode } = useTheme();
  return actualMode === 'dark' ? darkValue : lightValue;
}

// Hook for responsive theme values
export function useResponsiveTheme() {
  const { theme, actualMode } = useTheme();
  
  return {
    ...theme,
    actualMode,
    colors: {
      primary: theme.primaryColor,
      background: actualMode === 'dark' ? tokens.colorNeutral900 : tokens.colorNeutralWhite,
      foreground: actualMode === 'dark' ? tokens.colorNeutral100 : tokens.colorNeutral900,
      muted: actualMode === 'dark' ? tokens.colorNeutral800 : tokens.colorNeutral100,
      border: actualMode === 'dark' ? tokens.colorNeutral700 : tokens.colorNeutral200,
    },
    spacing: {
      xs: theme.density === 'compact' ? '4px' : theme.density === 'comfortable' ? '6px' : '8px',
      sm: theme.density === 'compact' ? '8px' : theme.density === 'comfortable' ? '12px' : '16px',
      md: theme.density === 'compact' ? '12px' : theme.density === 'comfortable' ? '16px' : '20px',
      lg: theme.density === 'compact' ? '16px' : theme.density === 'comfortable' ? '20px' : '24px',
      xl: theme.density === 'compact' ? '20px' : theme.density === 'comfortable' ? '24px' : '32px',
    },
    animations: {
      duration: theme.reducedMotion ? '0ms' : tokens.durationNormal,
      easing: theme.reducedMotion ? 'linear' : tokens.easingEaseInOut,
    },
  };
}

// Theme customization hook
export function useThemeCustomization() {
  const { theme, setTheme } = useTheme();
  
  const updatePrimaryColor = (color: string) => {
    setTheme({ primaryColor: color });
  };
  
  const updateFontFamily = (fontFamily: string) => {
    setTheme({ fontFamily });
  };
  
  const updateBorderRadius = (borderRadius: number) => {
    setTheme({ borderRadius });
  };
  
  const updateDensity = (density: ThemeConfig['density']) => {
    setTheme({ density });
  };
  
  return {
    theme,
    updatePrimaryColor,
    updateFontFamily,
    updateBorderRadius,
    updateDensity,
  };
}
