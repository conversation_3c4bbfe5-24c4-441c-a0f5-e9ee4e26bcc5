import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import API services
import { uiConfigurationApi } from './api/uiConfigurationApi';
import { templateApi } from './api/templateApi';
import { userApi } from './api/userApi';
import { collaborationApi } from './api/collaborationApi';
import { themeApi } from './api/themeApi';
import { componentLibraryApi } from './api/componentLibraryApi';

// Import reducers
import authReducer from './slices/authSlice';
import uiBuilderReducer from './slices/uiBuilderSlice';
import collaborationReducer from './slices/collaborationSlice';
import themeReducer from './slices/themeSlice';
import notificationReducer from './slices/notificationSlice';
import settingsReducer from './slices/settingsSlice';

export const store = configureStore({
  reducer: {
    // API reducers
    [uiConfigurationApi.reducerPath]: uiConfigurationApi.reducer,
    [templateApi.reducerPath]: templateApi.reducer,
    [userApi.reducerPath]: userApi.reducer,
    [collaborationApi.reducerPath]: collaborationApi.reducer,
    [themeApi.reducerPath]: themeApi.reducer,
    [componentLibraryApi.reducerPath]: componentLibraryApi.reducer,
    
    // Feature reducers
    auth: authReducer,
    uiBuilder: uiBuilderReducer,
    collaboration: collaborationReducer,
    theme: themeReducer,
    notifications: notificationReducer,
    settings: settingsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // Ignore these action types
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
        ignoredPaths: [
          // Ignore these paths in the state
          'register',
          'rehydrate',
        ],
      },
    }).concat([
      uiConfigurationApi.middleware,
      templateApi.middleware,
      userApi.middleware,
      collaborationApi.middleware,
      themeApi.middleware,
      componentLibraryApi.middleware,
    ]),
  devTools: process.env.NODE_ENV !== 'production',
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Export store
export default store;
