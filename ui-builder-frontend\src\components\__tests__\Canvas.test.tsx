import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderWithProviders, createMockUIConfiguration, createMockUIComponent } from '@test/utils/test-utils';
import Canvas from '@components/canvas/Canvas';

// Mock the canvas utilities
vi.mock('@utils/canvasUtils', () => ({
  snapToGrid: vi.fn((position, gridSize) => ({
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  })),
  getDropPosition: vi.fn(() => 'inside'),
}));

// Mock the component renderer
vi.mock('@utils/componentRenderer', () => ({
  renderComponent: vi.fn(() => <div data-testid="rendered-component">Mock Component</div>),
}));

describe('Canvas', () => {
  const mockConfiguration = createMockUIConfiguration({
    components: [
      createMockUIComponent({
        id: 'comp-1',
        type: 'text',
        position: { x: 100, y: 100, z: 1 },
        size: { width: 200, height: 50 },
      }),
      createMockUIComponent({
        id: 'comp-2',
        type: 'button',
        position: { x: 200, y: 200, z: 1 },
        size: { width: 100, height: 40 },
      }),
    ],
  });

  const mockStore = {
    uiBuilder: {
      currentConfiguration: mockConfiguration,
      canvas: {
        zoom: 1,
        pan: { x: 0, y: 0 },
        gridSize: 20,
        showGrid: true,
        showRulers: true,
        showGuides: true,
        snapToGrid: true,
      },
      selection: {
        selectedComponentIds: [],
        hoveredComponentId: null,
        multiSelectMode: false,
      },
      dragDrop: {
        isDragging: false,
        draggedComponent: null,
        dropTarget: null,
        dropPosition: null,
      },
      ui: {
        previewMode: false,
        devicePreview: 'desktop',
      },
    },
    collaboration: {
      collaborationUsers: [],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders canvas with configuration', () => {
    renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    expect(screen.getByTestId('rendered-component')).toBeInTheDocument();
  });

  it('shows empty state when no configuration is loaded', () => {
    const emptyStore = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        currentConfiguration: null,
      },
    };

    renderWithProviders(<Canvas />, {
      store: emptyStore as any,
    });

    expect(screen.getByText('No Configuration Loaded')).toBeInTheDocument();
    expect(screen.getByText('Create a new configuration or open an existing one to start building.')).toBeInTheDocument();
  });

  it('displays canvas info overlay with zoom and pan information', () => {
    renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    expect(screen.getByText(/Zoom: 100%/)).toBeInTheDocument();
    expect(screen.getByText(/Pan: 0, 0/)).toBeInTheDocument();
    expect(screen.getByText(/Grid: 20px/)).toBeInTheDocument();
  });

  it('hides canvas info overlay in preview mode', () => {
    const previewStore = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        ui: {
          ...mockStore.uiBuilder.ui,
          previewMode: true,
        },
      },
    };

    renderWithProviders(<Canvas />, {
      store: previewStore as any,
    });

    expect(screen.queryByText(/Zoom: 100%/)).not.toBeInTheDocument();
  });

  it('applies correct canvas styles based on zoom and pan', () => {
    const zoomedStore = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        canvas: {
          ...mockStore.uiBuilder.canvas,
          zoom: 1.5,
          pan: { x: 50, y: 100 },
        },
      },
    };

    renderWithProviders(<Canvas />, {
      store: zoomedStore as any,
    });

    const canvasContent = document.querySelector('[style*="transform"]');
    expect(canvasContent).toHaveStyle({
      transform: 'scale(1.5) translate(50px, 100px)',
    });
  });

  it('shows grid when showGrid is enabled', () => {
    renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    const canvas = document.querySelector('.ui-builder-canvas');
    expect(canvas).toHaveStyle({
      backgroundSize: '20px 20px',
    });
  });

  it('hides grid when showGrid is disabled', () => {
    const noGridStore = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        canvas: {
          ...mockStore.uiBuilder.canvas,
          showGrid: false,
        },
      },
    };

    renderWithProviders(<Canvas />, {
      store: noGridStore as any,
    });

    const canvas = document.querySelector('.ui-builder-canvas');
    expect(canvas).toHaveStyle({
      backgroundSize: 'none',
    });
  });

  it('handles canvas click to clear selection', async () => {
    const { store } = renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    const canvas = document.querySelector('.ui-builder-canvas');
    fireEvent.click(canvas!);

    await waitFor(() => {
      const actions = store.getState();
      // In a real test, you'd check if the clearSelection action was dispatched
      // This is a simplified check
      expect(canvas).toBeInTheDocument();
    });
  });

  it('handles mouse wheel for zooming when ctrl key is pressed', async () => {
    const { store } = renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    const canvas = document.querySelector('.ui-builder-canvas');
    
    // Mock wheel event with ctrl key
    const wheelEvent = new WheelEvent('wheel', {
      deltaY: -100,
      ctrlKey: true,
    });

    fireEvent(canvas!, wheelEvent);

    // In a real implementation, you'd check if the zoom was updated
    expect(canvas).toBeInTheDocument();
  });

  it('renders components in correct order based on z-index', () => {
    const configWithZIndex = createMockUIConfiguration({
      components: [
        createMockUIComponent({
          id: 'comp-1',
          position: { x: 0, y: 0, z: 2 },
        }),
        createMockUIComponent({
          id: 'comp-2',
          position: { x: 0, y: 0, z: 1 },
        }),
      ],
    });

    const storeWithZIndex = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        currentConfiguration: configWithZIndex,
      },
    };

    renderWithProviders(<Canvas />, {
      store: storeWithZIndex as any,
    });

    const components = screen.getAllByTestId('rendered-component');
    expect(components).toHaveLength(2);
  });

  it('shows selection box for multiple selected components', () => {
    const multiSelectStore = {
      ...mockStore,
      uiBuilder: {
        ...mockStore.uiBuilder,
        selection: {
          ...mockStore.uiBuilder.selection,
          selectedComponentIds: ['comp-1', 'comp-2'],
        },
      },
    };

    renderWithProviders(<Canvas />, {
      store: multiSelectStore as any,
    });

    // In a real implementation, you'd check for the SelectionBox component
    expect(screen.getAllByTestId('rendered-component')).toHaveLength(2);
  });

  it('renders collaboration overlay with user cursors', () => {
    const collaborationStore = {
      ...mockStore,
      collaboration: {
        collaborationUsers: [
          {
            id: 'user-1',
            username: 'John Doe',
            color: '#ff0000',
            cursor: { x: 100, y: 100 },
            isActive: true,
          },
        ],
      },
    };

    renderWithProviders(<Canvas />, {
      store: collaborationStore as any,
    });

    // In a real implementation, you'd check for the CollaborationOverlay component
    expect(screen.getAllByTestId('rendered-component')).toHaveLength(2);
  });

  it('handles drag and drop from component palette', async () => {
    const { store } = renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    // Simulate drag start from palette
    const dragStartEvent = {
      active: {
        id: 'palette-text',
        data: {
          current: {
            type: 'component-palette',
            componentType: 'text',
          },
        },
      },
    };

    // In a real test, you'd simulate the actual drag and drop events
    expect(store).toBeDefined();
  });

  it('handles component reordering via drag and drop', async () => {
    const { store } = renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    // Simulate drag and drop reordering
    const dragEndEvent = {
      active: {
        id: 'comp-1',
        data: {
          current: {
            type: 'canvas-component',
            component: mockConfiguration.components[0],
          },
        },
      },
      over: {
        id: 'comp-2',
        data: {
          current: {
            type: 'canvas-component',
            component: mockConfiguration.components[1],
          },
        },
      },
    };

    // In a real test, you'd simulate the actual drag and drop events
    expect(store).toBeDefined();
  });
});

describe('Canvas Keyboard Interactions', () => {
  it('handles keyboard shortcuts for canvas operations', async () => {
    const { store } = renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    // Test zoom shortcuts
    fireEvent.keyDown(document, { key: '0', ctrlKey: true });
    fireEvent.keyDown(document, { key: '+', ctrlKey: true });
    fireEvent.keyDown(document, { key: '-', ctrlKey: true });

    // Test grid toggle
    fireEvent.keyDown(document, { key: 'g', ctrlKey: true });

    expect(store).toBeDefined();
  });
});

describe('Canvas Accessibility', () => {
  it('has proper ARIA labels and roles', () => {
    renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    const canvas = document.querySelector('.ui-builder-canvas');
    expect(canvas).toBeInTheDocument();
    // In a real implementation, you'd check for proper ARIA attributes
  });

  it('supports keyboard navigation', () => {
    renderWithProviders(<Canvas />, {
      store: mockStore as any,
    });

    // Test tab navigation
    fireEvent.keyDown(document, { key: 'Tab' });
    fireEvent.keyDown(document, { key: 'Tab', shiftKey: true });

    expect(document.querySelector('.ui-builder-canvas')).toBeInTheDocument();
  });
});
