import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ThemeProvider } from '@ui-builder/react-components';
import userEvent from '@testing-library/user-event';

// Custom render options
export interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  /** Theme mode for testing */
  theme?: 'light' | 'dark';
  /** Initial theme configuration */
  initialTheme?: Record<string, any>;
  /** Whether to disable animations for testing */
  disableAnimations?: boolean;
  /** Custom wrapper component */
  wrapper?: React.ComponentType<{ children: React.ReactNode }>;
}

// All providers wrapper
function AllTheProviders({
  children,
  theme = 'light',
  initialTheme = {},
  disableAnimations = true,
}: {
  children: React.ReactNode;
  theme?: 'light' | 'dark';
  initialTheme?: Record<string, any>;
  disableAnimations?: boolean;
}) {
  return (
    <ThemeProvider
      defaultTheme={{
        mode: theme,
        reducedMotion: disableAnimations,
        ...initialTheme,
      }}
      disableTransitionOnChange={disableAnimations}
    >
      {children}
    </ThemeProvider>
  );
}

// Custom render function
export function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult & { user: ReturnType<typeof userEvent.setup> } {
  const {
    theme = 'light',
    initialTheme = {},
    disableAnimations = true,
    wrapper,
    ...renderOptions
  } = options;

  const Wrapper = wrapper || AllTheProviders;

  const result = render(ui, {
    wrapper: ({ children }) => (
      <Wrapper>
        <AllTheProviders
          theme={theme}
          initialTheme={initialTheme}
          disableAnimations={disableAnimations}
        >
          {children}
        </AllTheProviders>
      </Wrapper>
    ),
    ...renderOptions,
  });

  return {
    ...result,
    user: userEvent.setup(),
  };
}

// Re-export everything from testing library
export * from '@testing-library/react';
export { customRender as render };

// Common test utilities
export const createMockProps = <T extends Record<string, any>>(
  overrides: Partial<T> = {}
): T => {
  const defaultProps = {
    onClick: vi.fn(),
    onChange: vi.fn(),
    onFocus: vi.fn(),
    onBlur: vi.fn(),
    onSubmit: vi.fn(),
    ...overrides,
  };

  return defaultProps as T;
};

// Wait for element utilities
export const waitForElementToBeRemoved = async (
  element: HTMLElement | (() => HTMLElement | null),
  options?: { timeout?: number }
) => {
  const { waitForElementToBeRemoved: originalWait } = await import('@testing-library/react');
  return originalWait(element, options);
};

// Screen size utilities for responsive testing
export const setViewportSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  window.dispatchEvent(new Event('resize'));
};

export const viewportSizes = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1024, height: 768 },
  wide: { width: 1440, height: 900 },
};

// Theme testing utilities
export const renderWithTheme = (
  ui: ReactElement,
  theme: 'light' | 'dark' = 'light',
  options: CustomRenderOptions = {}
) => {
  return customRender(ui, { ...options, theme });
};

export const renderWithBothThemes = (ui: ReactElement, options: CustomRenderOptions = {}) => {
  return {
    light: customRender(ui, { ...options, theme: 'light' }),
    dark: customRender(ui, { ...options, theme: 'dark' }),
  };
};

// Animation testing utilities
export const disableAnimations = () => {
  const style = document.createElement('style');
  style.innerHTML = `
    *, *::before, *::after {
      animation-duration: 0s !important;
      animation-delay: 0s !important;
      transition-duration: 0s !important;
      transition-delay: 0s !important;
    }
  `;
  document.head.appendChild(style);
  return () => document.head.removeChild(style);
};

// Form testing utilities
export const fillForm = async (
  user: ReturnType<typeof userEvent.setup>,
  formData: Record<string, string | boolean>
) => {
  for (const [name, value] of Object.entries(formData)) {
    const field = screen.getByRole('textbox', { name: new RegExp(name, 'i') }) ||
                  screen.getByLabelText(new RegExp(name, 'i'));
    
    if (typeof value === 'boolean') {
      const checkbox = field as HTMLInputElement;
      if (checkbox.checked !== value) {
        await user.click(checkbox);
      }
    } else {
      await user.clear(field);
      await user.type(field, value);
    }
  }
};

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error) {
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong</div>;
    }

    return this.props.children;
  }
}

// Mock intersection observer
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
  window.IntersectionObserverEntry = vi.fn();
};

// Mock resize observer
export const mockResizeObserver = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.ResizeObserver = mockResizeObserver;
  window.ResizeObserverEntry = vi.fn();
};

// Mock media query
export const mockMatchMedia = (matches: boolean = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
};

// Global test setup
export const setupTests = () => {
  // Mock APIs
  mockIntersectionObserver();
  mockResizeObserver();
  mockMatchMedia();
  
  // Disable animations by default
  disableAnimations();
  
  // Mock console methods in tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = (...args: any[]) => {
      if (
        typeof args[0] === 'string' &&
        args[0].includes('Warning: ReactDOM.render is no longer supported')
      ) {
        return;
      }
      originalError.call(console, ...args);
    };
  });
  
  afterAll(() => {
    console.error = originalError;
  });
};
