# Alert Rules for UI Builder Platform
groups:
  - name: ui-builder.rules
    rules:
      # High-level service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: HighLatency
        expr: |
          histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency on {{ $labels.instance }}"
          description: "95th percentile latency is {{ $value }}s on {{ $labels.instance }}"

      # Database alerts
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is down for more than 1 minute."

      - alert: PostgreSQLHighConnections
        expr: |
          (
            pg_stat_database_numbackends /
            pg_settings_max_connections
          ) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high connection usage"
          description: "PostgreSQL connection usage is {{ $value | humanizePercentage }}"

      - alert: PostgreSQLSlowQueries
        expr: |
          rate(pg_stat_database_tup_returned[5m]) /
          rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "PostgreSQL query efficiency is low: {{ $value | humanizePercentage }}"

      # Redis alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis instance is down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: |
          (
            redis_memory_used_bytes /
            redis_memory_max_bytes
          ) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high connection count"
          description: "Redis has {{ $value }} connected clients"

      # System resource alerts
      - alert: HighCPUUsage
        expr: |
          (
            100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: HighDiskUsage
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_avail_bytes) /
            node_filesystem_size_bytes
          ) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage on {{ $labels.instance }}"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: |
          (
            node_filesystem_avail_bytes /
            node_filesystem_size_bytes
          ) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Only {{ $value | humanizePercentage }} disk space remaining on {{ $labels.instance }}"

      # Application-specific alerts
      - alert: HighRequestVolume
        expr: rate(http_requests_total[5m]) > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request volume on {{ $labels.instance }}"
          description: "Request rate is {{ $value }} req/s on {{ $labels.instance }}"

      - alert: APIGatewayDown
        expr: up{job="api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway service is down for more than 1 minute."

      - alert: AuthServiceDown
        expr: up{job="auth-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Auth Service is down"
          description: "Authentication service is down for more than 1 minute."

      - alert: ConfigServiceDown
        expr: up{job="config-service"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Config Service is down"
          description: "Configuration service is down for more than 2 minutes."

      - alert: TemplateServiceDown
        expr: up{job="template-service"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Template Service is down"
          description: "Template service is down for more than 2 minutes."

      - alert: FrontendDown
        expr: up{job="frontend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Frontend is down"
          description: "Frontend application is down for more than 1 minute."

      # Security alerts
      - alert: HighFailedLoginAttempts
        expr: |
          rate(auth_login_attempts_total{status="failed"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login attempts"
          description: "{{ $value }} failed login attempts per second detected"

      - alert: SuspiciousActivity
        expr: |
          rate(http_requests_total{status="403"}[5m]) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Suspicious activity detected"
          description: "High rate of 403 responses: {{ $value }} req/s"

      # Business metrics alerts
      - alert: LowUserActivity
        expr: |
          rate(ui_builder_user_actions_total[1h]) < 10
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "Low user activity"
          description: "User activity is below normal levels: {{ $value }} actions/hour"

      - alert: HighComponentCreationFailures
        expr: |
          (
            rate(ui_builder_component_operations_total{operation="create",status="failed"}[5m]) /
            rate(ui_builder_component_operations_total{operation="create"}[5m])
          ) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High component creation failure rate"
          description: "Component creation failure rate is {{ $value | humanizePercentage }}"

      # Performance alerts
      - alert: SlowDatabaseQueries
        expr: |
          histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s"

      - alert: HighGarbageCollectionTime
        expr: |
          rate(jvm_gc_collection_seconds_sum[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High garbage collection time"
          description: "GC time is {{ $value }}s per second on {{ $labels.instance }}"

      # Kubernetes alerts
      - alert: KubernetesPodCrashLooping
        expr: |
          rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"

      - alert: KubernetesNodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes node {{ $labels.node }} is not ready"
          description: "Node {{ $labels.node }} has been not ready for more than 5 minutes"

      - alert: KubernetesPodNotReady
        expr: kube_pod_status_phase{phase!="Running"} == 1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} is not running"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is in {{ $labels.phase }} phase"

      # Certificate expiration alerts
      - alert: CertificateExpiringSoon
        expr: |
          (probe_ssl_earliest_cert_expiry - time()) / 86400 < 30
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Certificate expiring soon"
          description: "Certificate for {{ $labels.instance }} expires in {{ $value }} days"

      - alert: CertificateExpired
        expr: |
          probe_ssl_earliest_cert_expiry - time() <= 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Certificate expired"
          description: "Certificate for {{ $labels.instance }} has expired"
