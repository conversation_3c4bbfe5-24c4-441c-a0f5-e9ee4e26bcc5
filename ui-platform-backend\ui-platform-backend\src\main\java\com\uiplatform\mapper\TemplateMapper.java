package com.uiplatform.mapper;

import com.uiplatform.dto.TemplateDTO;
import com.uiplatform.entity.Template;
import org.mapstruct.*;

/**
 * Mapper interface for Template entity and DTO conversions.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TemplateMapper {

    /**
     * Convert Template entity to DTO.
     */
    @Mapping(target = "organizationId", source = "organization.id")
    @Mapping(target = "organizationName", source = "organization.name")
    @Mapping(target = "authorId", source = "author.id")
    @Mapping(target = "authorName", expression = "java(template.getAuthor().getFirstName() + \" \" + template.getAuthor().getLastName())")
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "reviewCount", ignore = true)
    @Mapping(target = "averageRating", ignore = true)
    TemplateDTO toDTO(Template template);

    /**
     * Convert CreateDTO to Template entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "longDescription", ignore = true)
    @Mapping(target = "previewImages", ignore = true)
    @Mapping(target = "demoUrl", ignore = true)
    @Mapping(target = "documentationUrl", ignore = true)
    @Mapping(target = "status", constant = "DRAFT")
    @Mapping(target = "isPublic", constant = "false")
    @Mapping(target = "isPremium", constant = "false")
    @Mapping(target = "isFeatured", constant = "false")
    @Mapping(target = "minPlatformVersion", ignore = true)
    @Mapping(target = "downloadCount", constant = "0L")
    @Mapping(target = "rating", constant = "0.0")
    @Mapping(target = "ratingCount", constant = "0L")
    @Mapping(target = "viewCount", constant = "0L")
    @Mapping(target = "compatibility", ignore = true)
    @Mapping(target = "requirements", ignore = true)
    @Mapping(target = "installationGuide", ignore = true)
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "author", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    Template toEntity(TemplateDTO.CreateDTO createDTO);

    /**
     * Update Template entity from DTO.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "downloadCount", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "ratingCount", ignore = true)
    @Mapping(target = "viewCount", ignore = true)
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "author", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void updateEntityFromDTO(TemplateDTO updateDTO, @MappingTarget Template template);
}
