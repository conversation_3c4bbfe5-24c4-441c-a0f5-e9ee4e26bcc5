import 'package:flutter/material.dart';
import 'design_tokens.dart';
import 'colors.dart';
import 'typography.dart';

/// UI Builder theme configuration
class UIBuilderTheme {
  const UIBuilderTheme._();

  static const UIBuilderTheme instance = UIBuilderTheme._();

  /// Light theme configuration
  ThemeData get lightTheme {
    final colorScheme = UIBuilderColors.lightColorScheme;
    final textTheme = UIBuilderTypography.textTheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: textTheme.titleLarge?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.instance.borderRadius.lg,
        ),
        color: colorScheme.surface,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size6,
            vertical: DesignTokens.instance.spacing.size3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size6,
            vertical: DesignTokens.instance.spacing.size3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          side: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size4,
            vertical: DesignTokens.instance.spacing.size2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surface,
        border: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: DesignTokens.instance.spacing.size4,
          vertical: DesignTokens.instance.spacing.size3,
        ),
      ),
    );
  }

  /// Dark theme configuration
  ThemeData get darkTheme {
    final colorScheme = UIBuilderColors.darkColorScheme;
    final textTheme = UIBuilderTypography.textTheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: textTheme.titleLarge?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.instance.borderRadius.lg,
        ),
        color: colorScheme.surface,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size6,
            vertical: DesignTokens.instance.spacing.size3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size6,
            vertical: DesignTokens.instance.spacing.size3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          side: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.instance.spacing.size4,
            vertical: DesignTokens.instance.spacing.size2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.instance.borderRadius.md,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surface,
        border: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.instance.borderRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: DesignTokens.instance.spacing.size4,
          vertical: DesignTokens.instance.spacing.size3,
        ),
      ),
    );
  }
}
