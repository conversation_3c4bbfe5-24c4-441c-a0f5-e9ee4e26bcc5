package com.uibuilder.analytics;

import com.uibuilder.entity.AnalyticsEvent;
import com.uibuilder.entity.UserSession;
import com.uibuilder.entity.ComponentUsage;
import com.uibuilder.repository.AnalyticsEventRepository;
import com.uibuilder.repository.UserSessionRepository;
import com.uibuilder.repository.ComponentUsageRepository;
import com.uibuilder.repository.UIConfigRepository;
import com.uibuilder.repository.UserRepository;
import com.uibuilder.security.rbac.RoleBasedAccessControl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnalyticsService {

    private final AnalyticsEventRepository analyticsEventRepository;
    private final UserSessionRepository userSessionRepository;
    private final ComponentUsageRepository componentUsageRepository;
    private final UIConfigRepository uiConfigRepository;
    private final UserRepository userRepository;
    private final RoleBasedAccessControl rbac;
    private final KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * Track user engagement events
     */
    @Async
    @Transactional
    public void trackEvent(String eventType, String userId, String workspaceId, 
                          Map<String, Object> properties) {
        try {
            AnalyticsEvent event = AnalyticsEvent.builder()
                .id(UUID.randomUUID().toString())
                .eventType(eventType)
                .userId(userId)
                .workspaceId(workspaceId)
                .properties(properties)
                .timestamp(LocalDateTime.now())
                .sessionId(getCurrentSessionId(userId))
                .build();

            analyticsEventRepository.save(event);

            // Send to Kafka for real-time processing
            kafkaTemplate.send("analytics-events", event);

            log.debug("Analytics event tracked: {} for user {}", eventType, userId);

        } catch (Exception e) {
            log.error("Failed to track analytics event", e);
        }
    }

    /**
     * Track UI config interactions
     */
    @Async
    public void trackUIConfigInteraction(String configId, String userId, String action, 
                                       Map<String, Object> metadata) {
        Map<String, Object> properties = Map.of(
            "configId", configId,
            "action", action,
            "metadata", metadata
        );

        String workspaceId = getWorkspaceId(configId);
        trackEvent("ui_config_interaction", userId, workspaceId, properties);
    }

    /**
     * Track component usage
     */
    @Async
    @Transactional
    public void trackComponentUsage(String componentType, String configId, String userId) {
        try {
            String workspaceId = getWorkspaceId(configId);
            
            // Update or create component usage record
            ComponentUsage usage = componentUsageRepository
                .findByComponentTypeAndWorkspaceIdAndUserId(componentType, workspaceId, userId)
                .orElse(ComponentUsage.builder()
                    .id(UUID.randomUUID().toString())
                    .componentType(componentType)
                    .workspaceId(workspaceId)
                    .userId(userId)
                    .usageCount(0L)
                    .firstUsedAt(LocalDateTime.now())
                    .build());

            usage.setUsageCount(usage.getUsageCount() + 1);
            usage.setLastUsedAt(LocalDateTime.now());

            componentUsageRepository.save(usage);

            // Track as analytics event
            Map<String, Object> properties = Map.of(
                "componentType", componentType,
                "configId", configId,
                "totalUsage", usage.getUsageCount()
            );

            trackEvent("component_used", userId, workspaceId, properties);

        } catch (Exception e) {
            log.error("Failed to track component usage", e);
        }
    }

    /**
     * Track user session
     */
    @Transactional
    public UserSession startSession(String userId, String userAgent, String ipAddress) {
        try {
            // End any existing active sessions
            List<UserSession> activeSessions = userSessionRepository.findByUserIdAndEndTimeIsNull(userId);
            activeSessions.forEach(session -> {
                session.setEndTime(LocalDateTime.now());
                session.setDuration(ChronoUnit.SECONDS.between(session.getStartTime(), session.getEndTime()));
            });
            userSessionRepository.saveAll(activeSessions);

            // Create new session
            UserSession session = UserSession.builder()
                .id(UUID.randomUUID().toString())
                .userId(userId)
                .startTime(LocalDateTime.now())
                .userAgent(userAgent)
                .ipAddress(ipAddress)
                .build();

            session = userSessionRepository.save(session);

            // Track session start event
            Map<String, Object> properties = Map.of(
                "sessionId", session.getId(),
                "userAgent", userAgent,
                "ipAddress", ipAddress
            );

            trackEvent("session_started", userId, null, properties);

            return session;

        } catch (Exception e) {
            log.error("Failed to start user session", e);
            throw new RuntimeException("Failed to start session", e);
        }
    }

    /**
     * End user session
     */
    @Transactional
    public void endSession(String sessionId, String userId) {
        try {
            UserSession session = userSessionRepository.findById(sessionId)
                .orElseThrow(() -> new EntityNotFoundException("Session not found"));

            session.setEndTime(LocalDateTime.now());
            session.setDuration(ChronoUnit.SECONDS.between(session.getStartTime(), session.getEndTime()));

            userSessionRepository.save(session);

            // Track session end event
            Map<String, Object> properties = Map.of(
                "sessionId", sessionId,
                "duration", session.getDuration()
            );

            trackEvent("session_ended", userId, null, properties);

        } catch (Exception e) {
            log.error("Failed to end user session", e);
        }
    }

    /**
     * Get user engagement metrics
     */
    public UserEngagementMetrics getUserEngagementMetrics(String workspaceId, 
                                                         LocalDateTime startDate, 
                                                         LocalDateTime endDate, 
                                                         String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_analytics", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view analytics");
        }

        // Active users
        long activeUsers = userSessionRepository.countDistinctUsersByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        // Total sessions
        long totalSessions = userSessionRepository.countByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        // Average session duration
        Double avgSessionDuration = userSessionRepository.averageSessionDurationByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        // Page views / interactions
        long totalInteractions = analyticsEventRepository.countByWorkspaceIdAndTimestampBetween(
            workspaceId, startDate, endDate);

        // Bounce rate (sessions with only one interaction)
        long bounceSessions = analyticsEventRepository.countBounceSessionsByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);
        double bounceRate = totalSessions > 0 ? (double) bounceSessions / totalSessions : 0.0;

        return UserEngagementMetrics.builder()
            .activeUsers(activeUsers)
            .totalSessions(totalSessions)
            .averageSessionDuration(avgSessionDuration != null ? avgSessionDuration : 0.0)
            .totalInteractions(totalInteractions)
            .bounceRate(bounceRate)
            .dateRange(DateRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build())
            .build();
    }

    /**
     * Get component usage analytics
     */
    public ComponentAnalytics getComponentAnalytics(String workspaceId, 
                                                   LocalDateTime startDate, 
                                                   LocalDateTime endDate, 
                                                   String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_analytics", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view analytics");
        }

        // Most used components
        List<ComponentUsageStats> topComponents = componentUsageRepository
            .findTopComponentsByWorkspaceAndDateRange(workspaceId, startDate, endDate, 10);

        // Component adoption over time
        List<ComponentAdoptionData> adoptionData = componentUsageRepository
            .getComponentAdoptionOverTime(workspaceId, startDate, endDate);

        // Component performance metrics
        Map<String, ComponentPerformanceMetrics> performanceMetrics = 
            calculateComponentPerformanceMetrics(workspaceId, startDate, endDate);

        return ComponentAnalytics.builder()
            .topComponents(topComponents)
            .adoptionData(adoptionData)
            .performanceMetrics(performanceMetrics)
            .dateRange(DateRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build())
            .build();
    }

    /**
     * Get UI config performance metrics
     */
    public UIConfigAnalytics getUIConfigAnalytics(String configId, 
                                                 LocalDateTime startDate, 
                                                 LocalDateTime endDate, 
                                                 String userId) {
        // Validate access
        String workspaceId = getWorkspaceId(configId);
        if (!rbac.hasPermission(userId, "ui_config", "view_analytics", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view config analytics");
        }

        // View count
        long viewCount = analyticsEventRepository.countByEventTypeAndConfigIdAndTimestampBetween(
            "ui_config_viewed", configId, startDate, endDate);

        // Edit count
        long editCount = analyticsEventRepository.countByEventTypeAndConfigIdAndTimestampBetween(
            "ui_config_edited", configId, startDate, endDate);

        // Unique viewers
        long uniqueViewers = analyticsEventRepository.countDistinctUsersByEventTypeAndConfigIdAndTimestampBetween(
            "ui_config_viewed", configId, startDate, endDate);

        // Average time spent
        Double avgTimeSpent = analyticsEventRepository.averageTimeSpentOnConfig(
            configId, startDate, endDate);

        // Collaboration metrics
        long commentsCount = analyticsEventRepository.countByEventTypeAndConfigIdAndTimestampBetween(
            "comment_added", configId, startDate, endDate);

        long collaborators = analyticsEventRepository.countDistinctUsersByConfigIdAndTimestampBetween(
            configId, startDate, endDate);

        return UIConfigAnalytics.builder()
            .configId(configId)
            .viewCount(viewCount)
            .editCount(editCount)
            .uniqueViewers(uniqueViewers)
            .averageTimeSpent(avgTimeSpent != null ? avgTimeSpent : 0.0)
            .commentsCount(commentsCount)
            .collaborators(collaborators)
            .dateRange(DateRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build())
            .build();
    }

    /**
     * Get workspace overview analytics
     */
    public WorkspaceAnalytics getWorkspaceAnalytics(String workspaceId, 
                                                   LocalDateTime startDate, 
                                                   LocalDateTime endDate, 
                                                   String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_analytics", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view workspace analytics");
        }

        // Total UI configs
        long totalConfigs = uiConfigRepository.countByWorkspaceId(workspaceId);

        // Active configs (edited in date range)
        long activeConfigs = analyticsEventRepository.countDistinctConfigsByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        // Total users
        long totalUsers = userRepository.countByWorkspaceId(workspaceId);

        // Active users
        long activeUsers = userSessionRepository.countDistinctUsersByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        // Templates created
        long templatesCreated = analyticsEventRepository.countByEventTypeAndWorkspaceIdAndTimestampBetween(
            "template_created", workspaceId, startDate, endDate);

        // Collaboration activity
        long collaborationEvents = analyticsEventRepository.countCollaborationEventsByWorkspaceAndDateRange(
            workspaceId, startDate, endDate);

        return WorkspaceAnalytics.builder()
            .workspaceId(workspaceId)
            .totalConfigs(totalConfigs)
            .activeConfigs(activeConfigs)
            .totalUsers(totalUsers)
            .activeUsers(activeUsers)
            .templatesCreated(templatesCreated)
            .collaborationEvents(collaborationEvents)
            .dateRange(DateRange.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build())
            .build();
    }

    /**
     * Get real-time analytics dashboard data
     */
    public RealTimeAnalytics getRealTimeAnalytics(String workspaceId, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_analytics", workspaceId)) {
            throw new AccessDeniedException("Insufficient permissions to view analytics");
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);

        // Current active users
        long currentActiveUsers = userSessionRepository.countActiveUsersByWorkspace(workspaceId);

        // Events in last hour
        long recentEvents = analyticsEventRepository.countByWorkspaceIdAndTimestampBetween(
            workspaceId, oneHourAgo, now);

        // Active configs being edited
        long activeConfigs = analyticsEventRepository.countActiveConfigsByWorkspace(workspaceId);

        // Recent activity feed
        List<RecentActivity> recentActivity = analyticsEventRepository
            .findRecentActivityByWorkspace(workspaceId, 20);

        return RealTimeAnalytics.builder()
            .currentActiveUsers(currentActiveUsers)
            .recentEvents(recentEvents)
            .activeConfigs(activeConfigs)
            .recentActivity(recentActivity)
            .timestamp(now)
            .build();
    }

    /**
     * Generate custom analytics report
     */
    public AnalyticsReport generateCustomReport(AnalyticsReportRequest request, String userId) {
        // Validate access
        if (!rbac.hasPermission(userId, "workspace", "view_analytics", request.getWorkspaceId())) {
            throw new AccessDeniedException("Insufficient permissions to generate reports");
        }

        AnalyticsReport.AnalyticsReportBuilder reportBuilder = AnalyticsReport.builder()
            .reportId(UUID.randomUUID().toString())
            .title(request.getTitle())
            .description(request.getDescription())
            .workspaceId(request.getWorkspaceId())
            .dateRange(request.getDateRange())
            .generatedBy(userId)
            .generatedAt(LocalDateTime.now());

        // Add requested metrics
        if (request.getMetrics().contains("user_engagement")) {
            UserEngagementMetrics engagement = getUserEngagementMetrics(
                request.getWorkspaceId(), 
                request.getDateRange().getStartDate(), 
                request.getDateRange().getEndDate(), 
                userId
            );
            reportBuilder.userEngagement(engagement);
        }

        if (request.getMetrics().contains("component_analytics")) {
            ComponentAnalytics components = getComponentAnalytics(
                request.getWorkspaceId(), 
                request.getDateRange().getStartDate(), 
                request.getDateRange().getEndDate(), 
                userId
            );
            reportBuilder.componentAnalytics(components);
        }

        if (request.getMetrics().contains("workspace_overview")) {
            WorkspaceAnalytics workspace = getWorkspaceAnalytics(
                request.getWorkspaceId(), 
                request.getDateRange().getStartDate(), 
                request.getDateRange().getEndDate(), 
                userId
            );
            reportBuilder.workspaceAnalytics(workspace);
        }

        return reportBuilder.build();
    }

    private String getCurrentSessionId(String userId) {
        return userSessionRepository.findActiveSessionByUserId(userId)
            .map(UserSession::getId)
            .orElse(null);
    }

    private String getWorkspaceId(String configId) {
        return uiConfigRepository.findById(configId)
            .map(config -> config.getWorkspace().getId())
            .orElse(null);
    }

    private Map<String, ComponentPerformanceMetrics> calculateComponentPerformanceMetrics(
            String workspaceId, LocalDateTime startDate, LocalDateTime endDate) {
        
        List<ComponentUsageStats> components = componentUsageRepository
            .findTopComponentsByWorkspaceAndDateRange(workspaceId, startDate, endDate, 50);

        return components.stream()
            .collect(Collectors.toMap(
                ComponentUsageStats::getComponentType,
                component -> ComponentPerformanceMetrics.builder()
                    .usageCount(component.getUsageCount())
                    .uniqueUsers(component.getUniqueUsers())
                    .averageUsagePerUser(component.getUsageCount() / (double) component.getUniqueUsers())
                    .adoptionRate(calculateAdoptionRate(component, workspaceId))
                    .build()
            ));
    }

    private double calculateAdoptionRate(ComponentUsageStats component, String workspaceId) {
        long totalUsers = userRepository.countByWorkspaceId(workspaceId);
        return totalUsers > 0 ? (double) component.getUniqueUsers() / totalUsers : 0.0;
    }
}
