{"name": "ui-builder-frontend", "version": "1.0.0", "description": "Advanced UI Builder Frontend Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@dnd-kit/modifiers": "^7.0.0", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@monaco-editor/react": "^4.6.0", "react-flow-renderer": "^10.3.17", "socket.io-client": "^4.7.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "tailwindcss": "^3.3.6", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "react-resizable-panels": "^0.0.63", "react-hotkeys-hook": "^4.4.1", "lodash-es": "^4.17.21", "uuid": "^9.0.1", "dayjs": "^1.11.10", "axios": "^1.6.2", "react-query": "^3.39.3", "immer": "^10.0.3", "react-virtualized": "^9.22.5", "react-color": "^2.19.3", "react-split-pane": "^0.1.92", "react-grid-layout": "^1.4.4", "fabric": "^5.3.0", "konva": "^9.2.0", "react-konva": "^18.2.10"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash-es": "^4.17.12", "@types/uuid": "^9.0.7", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.9", "@types/react-virtualized": "^9.21.29", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "prettier": "^3.1.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/testing-library": "^0.2.2", "msw": "^2.0.11", "msw-storybook-addon": "^1.10.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}