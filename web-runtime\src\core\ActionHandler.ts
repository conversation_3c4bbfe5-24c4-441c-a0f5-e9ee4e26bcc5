import { ComponentMetadata, ActionDefinition, ActionContext } from '../types/runtime';

export interface ActionResult {
  success: boolean;
  data?: any;
  error?: string;
  redirect?: string;
  preventDefault?: boolean;
}

export interface ActionHandler {
  type: string;
  execute: (action: ActionDefinition, context: ActionContext) => Promise<ActionResult>;
}

export interface NavigationAction extends ActionDefinition {
  type: 'navigate';
  url: string;
  target?: '_self' | '_blank' | '_parent' | '_top';
  replace?: boolean;
}

export interface ApiAction extends ActionDefinition {
  type: 'api';
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  headers?: Record<string, string>;
  body?: any;
  successMessage?: string;
  errorMessage?: string;
}

export interface StateAction extends ActionDefinition {
  type: 'state';
  operation: 'set' | 'update' | 'delete' | 'toggle';
  key: string;
  value?: any;
  path?: string;
}

export interface FormAction extends ActionDefinition {
  type: 'form';
  operation: 'submit' | 'reset' | 'validate' | 'setField';
  formId?: string;
  fieldName?: string;
  value?: any;
}

export interface ModalAction extends ActionDefinition {
  type: 'modal';
  operation: 'open' | 'close';
  modalId: string;
  data?: any;
}

export interface NotificationAction extends ActionDefinition {
  type: 'notification';
  level: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  duration?: number;
}

export interface CustomAction extends ActionDefinition {
  type: 'custom';
  handler: string;
  params?: Record<string, any>;
}

/**
 * Action Handler System for Web Runtime
 * 
 * Manages user interactions and executes actions triggered by components.
 * Supports navigation, API calls, state management, form operations, and custom handlers.
 */
export class ActionHandlerSystem {
  private handlers = new Map<string, ActionHandler>();
  private customHandlers = new Map<string, (params: any, context: ActionContext) => Promise<ActionResult>>();
  private middleware: Array<(action: ActionDefinition, context: ActionContext) => Promise<boolean>> = [];

  constructor() {
    this.registerBuiltInHandlers();
  }

  /**
   * Register a custom action handler
   */
  registerHandler(handler: ActionHandler): void {
    this.handlers.set(handler.type, handler);
  }

  /**
   * Register a custom action function
   */
  registerCustomHandler(
    name: string, 
    handler: (params: any, context: ActionContext) => Promise<ActionResult>
  ): void {
    this.customHandlers.set(name, handler);
  }

  /**
   * Add middleware to intercept actions
   */
  addMiddleware(
    middleware: (action: ActionDefinition, context: ActionContext) => Promise<boolean>
  ): void {
    this.middleware.push(middleware);
  }

  /**
   * Execute an action
   */
  async executeAction(action: ActionDefinition, context: ActionContext): Promise<ActionResult> {
    try {
      // Run middleware
      for (const middleware of this.middleware) {
        const shouldContinue = await middleware(action, context);
        if (!shouldContinue) {
          return { success: false, error: 'Action blocked by middleware' };
        }
      }

      // Get handler
      const handler = this.handlers.get(action.type);
      if (!handler) {
        return { success: false, error: `Unknown action type: ${action.type}` };
      }

      // Execute action
      const result = await handler.execute(action, context);
      
      // Log action execution
      console.log('Action executed:', { action: action.type, success: result.success });
      
      return result;
    } catch (error) {
      console.error('Action execution failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Execute multiple actions in sequence
   */
  async executeActions(actions: ActionDefinition[], context: ActionContext): Promise<ActionResult[]> {
    const results: ActionResult[] = [];
    
    for (const action of actions) {
      const result = await this.executeAction(action, context);
      results.push(result);
      
      // Stop execution if an action fails and has stopOnError flag
      if (!result.success && action.stopOnError) {
        break;
      }
    }
    
    return results;
  }

  /**
   * Register built-in action handlers
   */
  private registerBuiltInHandlers(): void {
    // Navigation handler
    this.registerHandler({
      type: 'navigate',
      execute: async (action: NavigationAction, context) => {
        try {
          if (action.target === '_blank') {
            window.open(action.url, '_blank');
          } else if (action.replace) {
            window.location.replace(action.url);
          } else {
            window.location.href = action.url;
          }
          return { success: true };
        } catch (error) {
          return { success: false, error: 'Navigation failed' };
        }
      }
    });

    // API handler
    this.registerHandler({
      type: 'api',
      execute: async (action: ApiAction, context) => {
        try {
          const response = await fetch(action.url, {
            method: action.method,
            headers: {
              'Content-Type': 'application/json',
              ...action.headers
            },
            body: action.body ? JSON.stringify(action.body) : undefined
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          
          // Show success message if configured
          if (action.successMessage && context.showNotification) {
            context.showNotification('success', action.successMessage);
          }

          return { success: true, data };
        } catch (error) {
          const errorMessage = action.errorMessage || 'API request failed';
          
          if (context.showNotification) {
            context.showNotification('error', errorMessage);
          }

          return { 
            success: false, 
            error: error instanceof Error ? error.message : errorMessage 
          };
        }
      }
    });

    // State handler
    this.registerHandler({
      type: 'state',
      execute: async (action: StateAction, context) => {
        try {
          if (!context.state) {
            return { success: false, error: 'State management not available' };
          }

          switch (action.operation) {
            case 'set':
              context.state.set(action.key, action.value);
              break;
            case 'update':
              const currentValue = context.state.get(action.key);
              if (typeof currentValue === 'object' && typeof action.value === 'object') {
                context.state.set(action.key, { ...currentValue, ...action.value });
              } else {
                context.state.set(action.key, action.value);
              }
              break;
            case 'delete':
              context.state.delete(action.key);
              break;
            case 'toggle':
              const toggleValue = context.state.get(action.key);
              context.state.set(action.key, !toggleValue);
              break;
          }

          return { success: true };
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'State operation failed' 
          };
        }
      }
    });

    // Form handler
    this.registerHandler({
      type: 'form',
      execute: async (action: FormAction, context) => {
        try {
          if (!context.forms) {
            return { success: false, error: 'Form management not available' };
          }

          const form = action.formId ? context.forms.get(action.formId) : context.forms.getCurrent();
          if (!form) {
            return { success: false, error: 'Form not found' };
          }

          switch (action.operation) {
            case 'submit':
              const isValid = await form.validate();
              if (isValid) {
                const formData = form.getValues();
                return { success: true, data: formData };
              } else {
                return { success: false, error: 'Form validation failed' };
              }
            case 'reset':
              form.reset();
              break;
            case 'validate':
              const validationResult = await form.validate();
              return { success: validationResult };
            case 'setField':
              if (action.fieldName) {
                form.setFieldValue(action.fieldName, action.value);
              }
              break;
          }

          return { success: true };
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Form operation failed' 
          };
        }
      }
    });

    // Modal handler
    this.registerHandler({
      type: 'modal',
      execute: async (action: ModalAction, context) => {
        try {
          if (!context.modals) {
            return { success: false, error: 'Modal management not available' };
          }

          switch (action.operation) {
            case 'open':
              context.modals.open(action.modalId, action.data);
              break;
            case 'close':
              context.modals.close(action.modalId);
              break;
          }

          return { success: true };
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Modal operation failed' 
          };
        }
      }
    });

    // Notification handler
    this.registerHandler({
      type: 'notification',
      execute: async (action: NotificationAction, context) => {
        try {
          if (!context.showNotification) {
            console.log(`${action.level.toUpperCase()}: ${action.message}`);
            return { success: true };
          }

          context.showNotification(action.level, action.message, action.title, action.duration);
          return { success: true };
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Notification failed' 
          };
        }
      }
    });

    // Custom handler
    this.registerHandler({
      type: 'custom',
      execute: async (action: CustomAction, context) => {
        try {
          const handler = this.customHandlers.get(action.handler);
          if (!handler) {
            return { success: false, error: `Custom handler not found: ${action.handler}` };
          }

          return await handler(action.params, context);
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Custom handler failed' 
          };
        }
      }
    });
  }

  /**
   * Get available action types
   */
  getAvailableActionTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get registered custom handlers
   */
  getCustomHandlers(): string[] {
    return Array.from(this.customHandlers.keys());
  }

  /**
   * Clear all handlers (useful for testing)
   */
  clear(): void {
    this.handlers.clear();
    this.customHandlers.clear();
    this.middleware = [];
    this.registerBuiltInHandlers();
  }
}

// Global action handler instance
export const actionHandler = new ActionHandlerSystem();

// Helper function to create action definitions
export const createAction = {
  navigate: (url: string, target?: string, replace?: boolean): NavigationAction => ({
    type: 'navigate',
    url,
    target: target as any,
    replace
  }),

  api: (method: string, url: string, body?: any, options?: Partial<ApiAction>): ApiAction => ({
    type: 'api',
    method: method as any,
    url,
    body,
    ...options
  }),

  setState: (key: string, value: any): StateAction => ({
    type: 'state',
    operation: 'set',
    key,
    value
  }),

  updateState: (key: string, value: any): StateAction => ({
    type: 'state',
    operation: 'update',
    key,
    value
  }),

  toggleState: (key: string): StateAction => ({
    type: 'state',
    operation: 'toggle',
    key
  }),

  submitForm: (formId?: string): FormAction => ({
    type: 'form',
    operation: 'submit',
    formId
  }),

  resetForm: (formId?: string): FormAction => ({
    type: 'form',
    operation: 'reset',
    formId
  }),

  openModal: (modalId: string, data?: any): ModalAction => ({
    type: 'modal',
    operation: 'open',
    modalId,
    data
  }),

  closeModal: (modalId: string): ModalAction => ({
    type: 'modal',
    operation: 'close',
    modalId
  }),

  notify: (level: string, message: string, title?: string, duration?: number): NotificationAction => ({
    type: 'notification',
    level: level as any,
    message,
    title,
    duration
  }),

  custom: (handler: string, params?: any): CustomAction => ({
    type: 'custom',
    handler,
    params
  })
};
