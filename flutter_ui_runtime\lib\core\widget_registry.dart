import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';

import '../models/widget_config.dart';
import '../models/widget_metadata.dart';
import '../widgets/base/base_widget.dart';
import '../widgets/base/widget_builder.dart';

/// Registry for managing dynamic widgets in the Flutter UI Runtime
/// Provides registration, retrieval, validation, and caching of widgets
class WidgetRegistry {
  static final WidgetRegistry _instance = WidgetRegistry._internal();
  factory WidgetRegistry() => _instance;
  WidgetRegistry._internal();

  final Map<String, RegisteredWidget> _widgets = {};
  final Map<String, WidgetBuilder> _builders = {};
  final Map<String, Widget> _cache = {};
  
  bool _cacheEnabled = true;
  int _maxCacheSize = 100;

  /// Register a widget with the registry
  void register({
    required String type,
    required WidgetBuilder builder,
    required WidgetMetadata metadata,
    List<WidgetExample>? examples,
    String? documentation,
  }) {
    if (_widgets.containsKey(type)) {
      debugPrint('Warning: Widget "$type" is already registered. Overwriting...');
    }

    final registeredWidget = RegisteredWidget(
      type: type,
      builder: builder,
      metadata: metadata,
      examples: examples ?? [],
      documentation: documentation,
      registeredAt: DateTime.now(),
    );

    _widgets[type] = registeredWidget;
    _builders[type] = builder;
    
    // Clear cache for this widget type
    _clearCacheForType(type);
    
    debugPrint('Registered widget: $type');
  }

  /// Register multiple widgets at once
  void registerBatch(List<WidgetRegistration> registrations) {
    for (final registration in registrations) {
      register(
        type: registration.type,
        builder: registration.builder,
        metadata: registration.metadata,
        examples: registration.examples,
        documentation: registration.documentation,
      );
    }
  }

  /// Get a widget builder by type
  WidgetBuilder? getBuilder(String type) {
    return _builders[type];
  }

  /// Build a widget from configuration
  Widget? buildWidget({
    required String type,
    required WidgetConfig config,
    Map<String, dynamic>? context,
    Key? key,
  }) {
    final builder = getBuilder(type);
    if (builder == null) {
      debugPrint('Widget type "$type" not found in registry');
      return _buildErrorWidget(type, 'Widget type not found');
    }

    try {
      // Check cache first
      final cacheKey = _generateCacheKey(type, config, context);
      if (_cacheEnabled && _cache.containsKey(cacheKey)) {
        return _cache[cacheKey];
      }

      // Build the widget
      final widget = builder(
        config: config,
        context: context ?? {},
        key: key,
      );

      // Cache the widget if caching is enabled
      if (_cacheEnabled && widget != null) {
        _addToCache(cacheKey, widget);
      }

      return widget;
    } catch (error, stackTrace) {
      debugPrint('Error building widget "$type": $error');
      debugPrint('Stack trace: $stackTrace');
      return _buildErrorWidget(type, error.toString());
    }
  }

  /// Get widget metadata
  WidgetMetadata? getMetadata(String type) {
    return _widgets[type]?.metadata;
  }

  /// Get registered widget information
  RegisteredWidget? getRegisteredWidget(String type) {
    return _widgets[type];
  }

  /// Check if a widget type is registered
  bool hasWidget(String type) {
    return _widgets.containsKey(type);
  }

  /// Get all registered widget types
  List<String> getWidgetTypes() {
    return _widgets.keys.toList();
  }

  /// Get widgets by category
  List<String> getWidgetsByCategory(String category) {
    return _widgets.entries
        .where((entry) => entry.value.metadata.category == category)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get widgets by tag
  List<String> getWidgetsByTag(String tag) {
    return _widgets.entries
        .where((entry) => entry.value.metadata.tags.contains(tag))
        .map((entry) => entry.key)
        .toList();
  }

  /// Search widgets by name or description
  List<String> searchWidgets(String query) {
    final lowerQuery = query.toLowerCase();
    return _widgets.entries
        .where((entry) {
          final metadata = entry.value.metadata;
          return entry.key.toLowerCase().contains(lowerQuery) ||
              metadata.displayName.toLowerCase().contains(lowerQuery) ||
              (metadata.description?.toLowerCase().contains(lowerQuery) ?? false);
        })
        .map((entry) => entry.key)
        .toList();
  }

  /// Get all categories
  List<String> getCategories() {
    return _widgets.values
        .map((widget) => widget.metadata.category)
        .toSet()
        .toList()
      ..sort();
  }

  /// Get all tags
  List<String> getTags() {
    final tags = <String>{};
    for (final widget in _widgets.values) {
      tags.addAll(widget.metadata.tags);
    }
    return tags.toList()..sort();
  }

  /// Validate widget configuration
  ValidationResult validateConfig(String type, WidgetConfig config) {
    final metadata = getMetadata(type);
    if (metadata == null) {
      return ValidationResult(
        isValid: false,
        errors: ['Widget type "$type" not found'],
      );
    }

    final errors = <String>[];

    // Validate required properties
    for (final requiredProp in metadata.requiredProperties) {
      if (!config.properties.containsKey(requiredProp)) {
        errors.add('Required property "$requiredProp" is missing');
      }
    }

    // Validate property types
    for (final entry in config.properties.entries) {
      final propName = entry.key;
      final propValue = entry.value;
      final propSchema = metadata.propertySchema[propName];

      if (propSchema != null) {
        final validationError = _validateProperty(propName, propValue, propSchema);
        if (validationError != null) {
          errors.add(validationError);
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Unregister a widget
  bool unregister(String type) {
    final removed = _widgets.remove(type) != null;
    _builders.remove(type);
    _clearCacheForType(type);
    
    if (removed) {
      debugPrint('Unregistered widget: $type');
    }
    
    return removed;
  }

  /// Clear all registered widgets
  void clear() {
    _widgets.clear();
    _builders.clear();
    _cache.clear();
    debugPrint('Cleared all registered widgets');
  }

  /// Get registry statistics
  RegistryStats getStats() {
    final categories = getCategories();
    final deprecatedCount = _widgets.values
        .where((widget) => widget.metadata.deprecated)
        .length;

    return RegistryStats(
      totalWidgets: _widgets.length,
      categories: categories,
      deprecatedWidgets: deprecatedCount,
      cachedWidgets: _cache.length,
    );
  }

  /// Cache management
  void enableCache() => _cacheEnabled = true;
  void disableCache() => _cacheEnabled = false;
  void clearCache() => _cache.clear();
  
  void setCacheSize(int maxSize) {
    _maxCacheSize = maxSize;
    _trimCache();
  }

  /// Private methods
  String _generateCacheKey(
    String type,
    WidgetConfig config,
    Map<String, dynamic>? context,
  ) {
    return '$type-${config.hashCode}-${context.hashCode}';
  }

  void _addToCache(String key, Widget widget) {
    if (_cache.length >= _maxCacheSize) {
      _trimCache();
    }
    _cache[key] = widget;
  }

  void _trimCache() {
    if (_cache.length > _maxCacheSize) {
      final keysToRemove = _cache.keys.take(_cache.length - _maxCacheSize);
      for (final key in keysToRemove) {
        _cache.remove(key);
      }
    }
  }

  void _clearCacheForType(String type) {
    _cache.removeWhere((key, _) => key.startsWith('$type-'));
  }

  Widget _buildErrorWidget(String type, String error) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(height: 4),
          Text(
            'Error: $type',
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          Text(
            error,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String? _validateProperty(
    String propName,
    dynamic propValue,
    PropertySchema schema,
  ) {
    // Type validation
    if (!_isValidType(propValue, schema.type)) {
      return 'Property "$propName" should be of type ${schema.type}';
    }

    // Range validation for numbers
    if (schema.type == PropertyType.number && propValue is num) {
      if (schema.minimum != null && propValue < schema.minimum!) {
        return 'Property "$propName" should be >= ${schema.minimum}';
      }
      if (schema.maximum != null && propValue > schema.maximum!) {
        return 'Property "$propName" should be <= ${schema.maximum}';
      }
    }

    // Length validation for strings
    if (schema.type == PropertyType.string && propValue is String) {
      if (schema.minLength != null && propValue.length < schema.minLength!) {
        return 'Property "$propName" should have at least ${schema.minLength} characters';
      }
      if (schema.maxLength != null && propValue.length > schema.maxLength!) {
        return 'Property "$propName" should have at most ${schema.maxLength} characters';
      }
    }

    // Enum validation
    if (schema.enumValues != null && !schema.enumValues!.contains(propValue)) {
      return 'Property "$propName" should be one of: ${schema.enumValues!.join(', ')}';
    }

    return null;
  }

  bool _isValidType(dynamic value, PropertyType type) {
    switch (type) {
      case PropertyType.string:
        return value is String;
      case PropertyType.number:
        return value is num;
      case PropertyType.boolean:
        return value is bool;
      case PropertyType.array:
        return value is List;
      case PropertyType.object:
        return value is Map;
      case PropertyType.color:
        return value is String || value is int;
      case PropertyType.icon:
        return value is String || value is IconData;
    }
  }
}

/// Data classes
class RegisteredWidget {
  final String type;
  final WidgetBuilder builder;
  final WidgetMetadata metadata;
  final List<WidgetExample> examples;
  final String? documentation;
  final DateTime registeredAt;

  const RegisteredWidget({
    required this.type,
    required this.builder,
    required this.metadata,
    required this.examples,
    this.documentation,
    required this.registeredAt,
  });
}

class WidgetRegistration {
  final String type;
  final WidgetBuilder builder;
  final WidgetMetadata metadata;
  final List<WidgetExample>? examples;
  final String? documentation;

  const WidgetRegistration({
    required this.type,
    required this.builder,
    required this.metadata,
    this.examples,
    this.documentation,
  });
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;

  const ValidationResult({
    required this.isValid,
    required this.errors,
  });
}

class RegistryStats {
  final int totalWidgets;
  final List<String> categories;
  final int deprecatedWidgets;
  final int cachedWidgets;

  const RegistryStats({
    required this.totalWidgets,
    required this.categories,
    required this.deprecatedWidgets,
    required this.cachedWidgets,
  });
}

/// Global registry instance
final widgetRegistry = WidgetRegistry();

/// Riverpod provider for the widget registry
final widgetRegistryProvider = Provider<WidgetRegistry>((ref) => widgetRegistry);
