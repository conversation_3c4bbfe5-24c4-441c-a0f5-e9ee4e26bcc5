package com.uiplatform.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing collaborative comments on UI elements.
 */
@Entity
@Table(name = "comments", indexes = {
    @Index(name = "idx_comment_config_id", columnList = "ui_configuration_id"),
    @Index(name = "idx_comment_element_id", columnList = "element_id"),
    @Index(name = "idx_comment_author", columnList = "author_id"),
    @Index(name = "idx_comment_status", columnList = "status"),
    @Index(name = "idx_comment_created", columnList = "created_at")
})
public class Comment {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "ui_configuration_id", nullable = false)
    private UUID uiConfigurationId;

    @Column(name = "element_id", nullable = false, length = 255)
    private String elementId;

    @Column(name = "author_id", nullable = false)
    private UUID authorId;

    @Column(name = "author_username", nullable = false, length = 100)
    private String authorUsername;

    @Column(name = "text", nullable = false, columnDefinition = "TEXT")
    private String text;

    @Column(name = "position_x")
    private Double positionX;

    @Column(name = "position_y")
    private Double positionY;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private CommentStatus status = CommentStatus.ACTIVE;

    @Column(name = "is_resolved", nullable = false)
    private Boolean isResolved = false;

    @Column(name = "resolved_by")
    private UUID resolvedBy;

    @Column(name = "resolved_at")
    private LocalDateTime resolvedAt;

    @Column(name = "resolution_text", columnDefinition = "TEXT")
    private String resolutionText;

    @Column(name = "parent_comment_id")
    private UUID parentCommentId;

    @Column(name = "thread_id")
    private UUID threadId;

    @Column(name = "mention_user_ids", columnDefinition = "TEXT")
    private String mentionUserIds; // JSON array of mentioned user IDs

    @Column(name = "attachment_urls", columnDefinition = "TEXT")
    private String attachmentUrls; // JSON array of attachment URLs

    @Enumerated(EnumType.STRING)
    @Column(name = "priority", length = 20)
    private CommentPriority priority = CommentPriority.NORMAL;

    @Column(name = "tags", length = 500)
    private String tags; // Comma-separated tags

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @Column(name = "deleted_by")
    private UUID deletedBy;

    // Constructors
    public Comment() {}

    public Comment(UUID uiConfigurationId, String elementId, UUID authorId, 
                  String authorUsername, String text) {
        this.uiConfigurationId = uiConfigurationId;
        this.elementId = elementId;
        this.authorId = authorId;
        this.authorUsername = authorUsername;
        this.text = text;
        this.threadId = UUID.randomUUID(); // New thread for top-level comments
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUiConfigurationId() {
        return uiConfigurationId;
    }

    public void setUiConfigurationId(UUID uiConfigurationId) {
        this.uiConfigurationId = uiConfigurationId;
    }

    public String getElementId() {
        return elementId;
    }

    public void setElementId(String elementId) {
        this.elementId = elementId;
    }

    public UUID getAuthorId() {
        return authorId;
    }

    public void setAuthorId(UUID authorId) {
        this.authorId = authorId;
    }

    public String getAuthorUsername() {
        return authorUsername;
    }

    public void setAuthorUsername(String authorUsername) {
        this.authorUsername = authorUsername;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Double getPositionX() {
        return positionX;
    }

    public void setPositionX(Double positionX) {
        this.positionX = positionX;
    }

    public Double getPositionY() {
        return positionY;
    }

    public void setPositionY(Double positionY) {
        this.positionY = positionY;
    }

    public CommentStatus getStatus() {
        return status;
    }

    public void setStatus(CommentStatus status) {
        this.status = status;
    }

    public Boolean getIsResolved() {
        return isResolved;
    }

    public void setIsResolved(Boolean isResolved) {
        this.isResolved = isResolved;
    }

    public UUID getResolvedBy() {
        return resolvedBy;
    }

    public void setResolvedBy(UUID resolvedBy) {
        this.resolvedBy = resolvedBy;
    }

    public LocalDateTime getResolvedAt() {
        return resolvedAt;
    }

    public void setResolvedAt(LocalDateTime resolvedAt) {
        this.resolvedAt = resolvedAt;
    }

    public String getResolutionText() {
        return resolutionText;
    }

    public void setResolutionText(String resolutionText) {
        this.resolutionText = resolutionText;
    }

    public UUID getParentCommentId() {
        return parentCommentId;
    }

    public void setParentCommentId(UUID parentCommentId) {
        this.parentCommentId = parentCommentId;
    }

    public UUID getThreadId() {
        return threadId;
    }

    public void setThreadId(UUID threadId) {
        this.threadId = threadId;
    }

    public String getMentionUserIds() {
        return mentionUserIds;
    }

    public void setMentionUserIds(String mentionUserIds) {
        this.mentionUserIds = mentionUserIds;
    }

    public String getAttachmentUrls() {
        return attachmentUrls;
    }

    public void setAttachmentUrls(String attachmentUrls) {
        this.attachmentUrls = attachmentUrls;
    }

    public CommentPriority getPriority() {
        return priority;
    }

    public void setPriority(CommentPriority priority) {
        this.priority = priority;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    public UUID getDeletedBy() {
        return deletedBy;
    }

    public void setDeletedBy(UUID deletedBy) {
        this.deletedBy = deletedBy;
    }

    /**
     * Check if this is a reply to another comment.
     */
    public boolean isReply() {
        return parentCommentId != null;
    }

    /**
     * Mark comment as resolved.
     */
    public void resolve(UUID resolvedByUserId, String resolution) {
        this.isResolved = true;
        this.resolvedBy = resolvedByUserId;
        this.resolvedAt = LocalDateTime.now();
        this.resolutionText = resolution;
        this.status = CommentStatus.RESOLVED;
    }

    /**
     * Mark comment as deleted (soft delete).
     */
    public void markAsDeleted(UUID deletedByUserId) {
        this.deleted = true;
        this.deletedAt = LocalDateTime.now();
        this.deletedBy = deletedByUserId;
        this.status = CommentStatus.DELETED;
    }

    // Enums
    public enum CommentStatus {
        ACTIVE,
        RESOLVED,
        DELETED,
        ARCHIVED
    }

    public enum CommentPriority {
        LOW,
        NORMAL,
        HIGH,
        URGENT
    }

    @Override
    public String toString() {
        return "Comment{" +
                "id=" + id +
                ", uiConfigurationId=" + uiConfigurationId +
                ", elementId='" + elementId + '\'' +
                ", authorUsername='" + authorUsername + '\'' +
                ", status=" + status +
                ", isResolved=" + isResolved +
                ", createdAt=" + createdAt +
                '}';
    }
}
