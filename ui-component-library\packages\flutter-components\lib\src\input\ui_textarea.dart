import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Textarea component for multi-line text input
class UITextarea extends StatefulWidget {
  const UITextarea({
    super.key,
    this.controller,
    this.initialValue,
    this.focusNode,
    this.decoration,
    this.style,
    this.maxLines = 4,
    this.minLines = 2,
    this.maxLength,
    this.onChanged,
    this.onSubmitted,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
    // UI Builder specific properties
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.variant = UIInputVariant.outlined,
    this.size = UISize.md,
    this.fullWidth = true,
    this.required = false,
    this.resize = true,
  });

  final TextEditingController? controller;
  final String? initialValue;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextStyle? style;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final bool autocorrect;
  final bool enableSuggestions;

  // UI Builder specific properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final UIInputVariant variant;
  final UISize size;
  final bool fullWidth;
  final bool required;
  final bool resize;

  @override
  State<UITextarea> createState() => _UITextareaState();
}

class _UITextareaState extends State<UITextarea> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    // Build label with required indicator
    String? labelText = widget.label;
    if (widget.required && labelText != null) {
      labelText = '$labelText *';
    }

    InputDecoration decoration = InputDecoration(
      labelText: labelText,
      hintText: widget.hint,
      helperText: widget.helperText,
      errorText: widget.errorText,
      border: OutlineInputBorder(
        borderRadius: tokens.borderRadius.md,
      ),
      contentPadding: EdgeInsets.all(tokens.spacing.size4),
    );

    if (widget.decoration != null) {
      decoration = decoration.copyWith(
        labelText: widget.decoration!.labelText ?? decoration.labelText,
        hintText: widget.decoration!.hintText ?? decoration.hintText,
        helperText: widget.decoration!.helperText ?? decoration.helperText,
        errorText: widget.decoration!.errorText ?? decoration.errorText,
      );
    }

    Widget textarea = TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      decoration: decoration,
      style: widget.style,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
      keyboardType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
    );

    if (!widget.fullWidth) {
      textarea = IntrinsicWidth(child: textarea);
    }

    return textarea;
  }
}
