import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Table component
class UITable extends StatelessWidget {
  const UITable({
    super.key,
    required this.columns,
    required this.rows,
    this.showHeader = true,
    this.showBorder = true,
    this.striped = false,
    this.hoverable = false,
    this.sortable = false,
    this.onSort,
    this.sortColumnIndex,
    this.sortAscending = true,
  });

  final List<UITableColumn> columns;
  final List<UITableRow> rows;
  final bool showHeader;
  final bool showBorder;
  final bool striped;
  final bool hoverable;
  final bool sortable;
  final Function(int columnIndex, bool ascending)? onSort;
  final int? sortColumnIndex;
  final bool sortAscending;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final tokens = DesignTokens.instance;

    return Container(
      decoration: showBorder
          ? BoxDecoration(
              border: Border.all(color: colorScheme.outline),
              borderRadius: tokens.borderRadius.lg,
            )
          : null,
      child: DataTable(
        showCheckboxColumn: false,
        headingRowColor: showHeader
            ? MaterialStateProperty.all(colorScheme.surfaceVariant)
            : null,
        dataRowColor: striped
            ? MaterialStateProperty.resolveWith((states) {
                final index = states.contains(MaterialState.selected) ? 0 : 1;
                return index.isEven
                    ? colorScheme.surface
                    : colorScheme.surfaceVariant.withOpacity(0.3);
              })
            : null,
        columns: columns.map((column) {
          return DataColumn(
            label: Text(
              column.label,
              style: Theme.of(context).textTheme.titleSmall,
            ),
            onSort: sortable && onSort != null
                ? (columnIndex, ascending) => onSort!(columnIndex, ascending)
                : null,
          );
        }).toList(),
        rows: rows.asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          
          return DataRow(
            cells: row.cells.map((cell) {
              return DataCell(
                cell.child ?? Text(cell.value?.toString() ?? ''),
                onTap: cell.onTap,
              );
            }).toList(),
            onSelectChanged: row.onTap != null ? (_) => row.onTap!() : null,
            color: striped
                ? MaterialStateProperty.all(
                    index.isEven
                        ? colorScheme.surface
                        : colorScheme.surfaceVariant.withOpacity(0.3),
                  )
                : null,
          );
        }).toList(),
        sortColumnIndex: sortColumnIndex,
        sortAscending: sortAscending,
      ),
    );
  }
}

/// Table column definition
class UITableColumn {
  const UITableColumn({
    required this.label,
    this.width,
    this.alignment = Alignment.centerLeft,
    this.sortable = false,
  });

  final String label;
  final double? width;
  final Alignment alignment;
  final bool sortable;
}

/// Table row definition
class UITableRow {
  const UITableRow({
    required this.cells,
    this.onTap,
    this.selected = false,
  });

  final List<UITableCell> cells;
  final VoidCallback? onTap;
  final bool selected;
}

/// Table cell definition
class UITableCell {
  const UITableCell({
    this.value,
    this.child,
    this.onTap,
  });

  final dynamic value;
  final Widget? child;
  final VoidCallback? onTap;
}
