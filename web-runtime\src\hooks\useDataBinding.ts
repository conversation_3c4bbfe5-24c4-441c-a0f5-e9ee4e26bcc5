import { useMemo } from 'react';
import { useRuntimeStore } from '@stores/runtimeStore';
import { DataBindingConfiguration } from '@types/index';

/**
 * Hook for resolving data bindings in component props
 */
export function useDataBinding(
  props: Record<string, any>,
  bindings?: DataBindingConfiguration
): Record<string, any> {
  const { data, user } = useRuntimeStore();

  return useMemo(() => {
    if (!bindings) {
      return props;
    }

    const resolvedProps = { ...props };

    // Create evaluation context
    const context = {
      data,
      user,
      props,
      // Helper functions for expressions
      formatDate: (date: string | Date, format?: string) => {
        const d = new Date(date);
        if (format === 'short') {
          return d.toLocaleDateString();
        }
        if (format === 'long') {
          return d.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          });
        }
        return d.toISOString();
      },
      formatNumber: (num: number, decimals = 2) => {
        return num.toFixed(decimals);
      },
      formatCurrency: (amount: number, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency,
        }).format(amount);
      },
      // Array helpers
      filter: (array: any[], predicate: string) => {
        try {
          const fn = new Function('item', 'index', 'array', `return ${predicate}`);
          return array.filter(fn);
        } catch {
          return array;
        }
      },
      map: (array: any[], transform: string) => {
        try {
          const fn = new Function('item', 'index', 'array', `return ${transform}`);
          return array.map(fn);
        } catch {
          return array;
        }
      },
      sort: (array: any[], key?: string) => {
        if (!key) return [...array].sort();
        return [...array].sort((a, b) => {
          const aVal = getNestedValue(a, key);
          const bVal = getNestedValue(b, key);
          if (aVal < bVal) return -1;
          if (aVal > bVal) return 1;
          return 0;
        });
      },
      // String helpers
      capitalize: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
      uppercase: (str: string) => str.toUpperCase(),
      lowercase: (str: string) => str.toLowerCase(),
      truncate: (str: string, length: number) => 
        str.length > length ? str.slice(0, length) + '...' : str,
    };

    // Resolve each binding
    Object.entries(bindings).forEach(([propName, binding]) => {
      try {
        let value;

        if (typeof binding === 'string') {
          // Simple data path binding
          value = getNestedValue(data, binding);
        } else if (typeof binding === 'object' && binding.source) {
          // Complex binding with source and transform
          const sourceValue = getNestedValue(data, binding.source);
          
          if (binding.transform) {
            // Apply transformation
            value = evaluateExpression(binding.transform, {
              ...context,
              value: sourceValue,
            });
          } else {
            value = sourceValue;
          }
        }

        // Use fallback if value is undefined/null
        if (value === undefined || value === null) {
          value = binding.fallback !== undefined ? binding.fallback : props[propName];
        }

        resolvedProps[propName] = value;
      } catch (error) {
        console.warn(`Failed to resolve binding for ${propName}:`, error);
        // Use fallback or original prop value
        resolvedProps[propName] = binding.fallback !== undefined 
          ? binding.fallback 
          : props[propName];
      }
    });

    return resolvedProps;
  }, [props, bindings, data, user]);
}

/**
 * Hook for evaluating data expressions
 */
export function useDataExpression(
  expression: string,
  dependencies: string[] = []
): any {
  const { data, user } = useRuntimeStore();

  return useMemo(() => {
    try {
      const context = {
        data,
        user,
        // Add any additional context needed
      };

      return evaluateExpression(expression, context);
    } catch (error) {
      console.warn('Failed to evaluate expression:', expression, error);
      return null;
    }
  }, [expression, data, user, ...dependencies]);
}

/**
 * Hook for reactive data queries
 */
export function useDataQuery(query: {
  source: string;
  filter?: string;
  sort?: string;
  limit?: number;
  offset?: number;
}): any[] {
  const { data } = useRuntimeStore();

  return useMemo(() => {
    try {
      let result = getNestedValue(data, query.source);
      
      if (!Array.isArray(result)) {
        return [];
      }

      // Apply filter
      if (query.filter) {
        const filterFn = new Function('item', 'index', `return ${query.filter}`);
        result = result.filter(filterFn);
      }

      // Apply sort
      if (query.sort) {
        const sortFn = new Function('a', 'b', `return ${query.sort}`);
        result = [...result].sort(sortFn);
      }

      // Apply pagination
      if (query.offset || query.limit) {
        const start = query.offset || 0;
        const end = query.limit ? start + query.limit : undefined;
        result = result.slice(start, end);
      }

      return result;
    } catch (error) {
      console.warn('Failed to execute data query:', query, error);
      return [];
    }
  }, [data, query]);
}

/**
 * Hook for computed values based on data
 */
export function useComputedValue(
  computation: string,
  dependencies: string[] = []
): any {
  const { data, user } = useRuntimeStore();

  return useMemo(() => {
    try {
      const context = {
        data,
        user,
        // Math helpers
        Math,
        // Date helpers
        Date,
        // Array helpers
        Array,
        Object,
      };

      return evaluateExpression(computation, context);
    } catch (error) {
      console.warn('Failed to compute value:', computation, error);
      return null;
    }
  }, [computation, data, user, ...dependencies]);
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  
  return path.split('.').reduce((current, key) => {
    if (current === null || current === undefined) return undefined;
    
    // Handle array indices
    if (key.includes('[') && key.includes(']')) {
      const [arrayKey, indexStr] = key.split('[');
      const index = parseInt(indexStr.replace(']', ''), 10);
      
      if (arrayKey) {
        current = current[arrayKey];
      }
      
      if (Array.isArray(current) && !isNaN(index)) {
        return current[index];
      }
      
      return undefined;
    }
    
    return current[key];
  }, obj);
}

// Helper function to safely evaluate expressions
function evaluateExpression(expression: string, context: Record<string, any>): any {
  try {
    // Create a function with the context as parameters
    const contextKeys = Object.keys(context);
    const contextValues = Object.values(context);
    
    // Wrap expression in return statement if it doesn't have one
    const wrappedExpression = expression.trim().startsWith('return ') 
      ? expression 
      : `return ${expression}`;
    
    const fn = new Function(...contextKeys, wrappedExpression);
    return fn(...contextValues);
  } catch (error) {
    console.warn('Expression evaluation error:', error);
    throw error;
  }
}

// Helper function to set nested object values
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  if (!lastKey) return;
  
  const target = keys.reduce((current, key) => {
    if (current[key] === undefined) {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
}

// Helper function to watch for data changes
export function useDataWatcher(
  path: string,
  callback: (newValue: any, oldValue: any) => void
): void {
  const { data } = useRuntimeStore();
  const currentValue = getNestedValue(data, path);

  // Use a ref to track previous value
  const previousValueRef = React.useRef(currentValue);

  React.useEffect(() => {
    const oldValue = previousValueRef.current;
    if (oldValue !== currentValue) {
      callback(currentValue, oldValue);
      previousValueRef.current = currentValue;
    }
  }, [currentValue, callback]);
}
