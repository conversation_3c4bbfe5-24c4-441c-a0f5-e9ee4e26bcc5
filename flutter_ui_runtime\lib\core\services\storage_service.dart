import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../config/app_config.dart';
import '../models/ui_metadata.dart';
import '../models/theme_config.dart';
import '../providers/app_state_provider.dart';
import '../utils/logger.dart';

part 'storage_service.g.dart';

/// Storage service provider
@riverpod
StorageService storageService(StorageServiceRef ref) {
  return StorageService();
}

/// Local storage service using Hive
class StorageService {
  static const String _uiMetadataBox = 'ui_metadata';
  static const String _themeBox = 'themes';
  static const String _userBox = 'user';
  static const String _cacheBox = 'cache';
  static const String _preferencesBox = 'preferences';
  static const String _offlineBox = 'offline';

  Box<UIMetadata>? _uiMetadataBoxInstance;
  Box<ThemeConfig>? _themeBoxInstance;
  Box<UserInfo>? _userBoxInstance;
  Box<dynamic>? _cacheBoxInstance;
  Box<dynamic>? _preferencesBoxInstance;
  Box<dynamic>? _offlineBoxInstance;

  /// Initialize storage service
  static Future<void> initialize() async {
    try {
      await Hive.initFlutter();
      
      // Register adapters
      _registerAdapters();
      
      AppLogger.info('Storage service initialized');
    } catch (error, stackTrace) {
      AppLogger.error(
        'Failed to initialize storage service',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Register Hive type adapters
  static void _registerAdapters() {
    // Register adapters for custom types
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(UIMetadataAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(LayoutConfigurationAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(WidgetConfigurationAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(PositionConfigurationAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(SizeConfigurationAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(StyleConfigurationAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(EdgeInsetsDataAdapter());
    }
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(ThemeConfigAdapter());
    }
    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(ColorPaletteAdapter());
    }
    if (!Hive.isAdapterRegistered(12)) {
      Hive.registerAdapter(ColorScaleAdapter());
    }
    if (!Hive.isAdapterRegistered(20)) {
      Hive.registerAdapter(LayoutTypeAdapter());
    }
  }

  /// Get UI metadata box
  Future<Box<UIMetadata>> get _uiMetadataBox async {
    return _uiMetadataBoxInstance ??= await Hive.openBox<UIMetadata>(_uiMetadataBox);
  }

  /// Get theme box
  Future<Box<ThemeConfig>> get _themeBox async {
    return _themeBoxInstance ??= await Hive.openBox<ThemeConfig>(_themeBox);
  }

  /// Get user box
  Future<Box<UserInfo>> get _userBox async {
    return _userBoxInstance ??= await Hive.openBox<UserInfo>(_userBox);
  }

  /// Get cache box
  Future<Box<dynamic>> get _cacheBox async {
    return _cacheBoxInstance ??= await Hive.openBox<dynamic>(_cacheBox);
  }

  /// Get preferences box
  Future<Box<dynamic>> get _preferencesBox async {
    return _preferencesBoxInstance ??= await Hive.openBox<dynamic>(_preferencesBox);
  }

  /// Get offline box
  Future<Box<dynamic>> get _offlineBox async {
    return _offlineBoxInstance ??= await Hive.openBox<dynamic>(_offlineBox);
  }

  /// Cache UI metadata
  Future<void> cacheUIMetadata(String pageId, UIMetadata metadata) async {
    try {
      final box = await _uiMetadataBox;
      await box.put(pageId, metadata);
      AppLogger.debug('UI metadata cached for page: $pageId');
    } catch (error) {
      AppLogger.error('Failed to cache UI metadata: $error');
    }
  }

  /// Get cached UI metadata
  Future<UIMetadata?> getCachedUIMetadata(String pageId) async {
    try {
      final box = await _uiMetadataBox;
      return box.get(pageId);
    } catch (error) {
      AppLogger.error('Failed to get cached UI metadata: $error');
      return null;
    }
  }

  /// Cache theme
  Future<void> cacheTheme(String themeId, ThemeConfig theme) async {
    try {
      final box = await _themeBox;
      await box.put(themeId, theme);
      AppLogger.debug('Theme cached: $themeId');
    } catch (error) {
      AppLogger.error('Failed to cache theme: $error');
    }
  }

  /// Get cached theme
  Future<ThemeConfig?> getCachedTheme(String themeId) async {
    try {
      final box = await _themeBox;
      return box.get(themeId);
    } catch (error) {
      AppLogger.error('Failed to get cached theme: $error');
      return null;
    }
  }

  /// Save user info
  Future<void> saveUserInfo(UserInfo user) async {
    try {
      final box = await _userBox;
      await box.put('current_user', user);
      AppLogger.debug('User info saved');
    } catch (error) {
      AppLogger.error('Failed to save user info: $error');
    }
  }

  /// Get user info
  Future<UserInfo?> getUserInfo() async {
    try {
      final box = await _userBox;
      return box.get('current_user');
    } catch (error) {
      AppLogger.error('Failed to get user info: $error');
      return null;
    }
  }

  /// Clear user info
  Future<void> clearUserInfo() async {
    try {
      final box = await _userBox;
      await box.delete('current_user');
      AppLogger.debug('User info cleared');
    } catch (error) {
      AppLogger.error('Failed to clear user info: $error');
    }
  }

  /// Save theme mode preference
  Future<void> saveThemeMode(ThemeMode mode) async {
    try {
      final box = await _preferencesBox;
      await box.put(AppConfig.themeKey, mode.index);
      AppLogger.debug('Theme mode saved: $mode');
    } catch (error) {
      AppLogger.error('Failed to save theme mode: $error');
    }
  }

  /// Get theme mode preference
  Future<ThemeMode?> getThemeMode() async {
    try {
      final box = await _preferencesBox;
      final index = box.get(AppConfig.themeKey);
      if (index != null) {
        return ThemeMode.values[index];
      }
      return null;
    } catch (error) {
      AppLogger.error('Failed to get theme mode: $error');
      return null;
    }
  }

  /// Save user preferences
  Future<void> savePreferences(Map<String, dynamic> preferences) async {
    try {
      final box = await _preferencesBox;
      await box.put(AppConfig.userPreferencesKey, preferences);
      AppLogger.debug('User preferences saved');
    } catch (error) {
      AppLogger.error('Failed to save preferences: $error');
    }
  }

  /// Get user preferences
  Future<Map<String, dynamic>> getPreferences() async {
    try {
      final box = await _preferencesBox;
      final preferences = box.get(AppConfig.userPreferencesKey);
      return Map<String, dynamic>.from(preferences ?? {});
    } catch (error) {
      AppLogger.error('Failed to get preferences: $error');
      return {};
    }
  }

  /// Cache data with expiration
  Future<void> cacheData(String key, dynamic data, {Duration? ttl}) async {
    try {
      final box = await _cacheBox;
      final expiration = ttl != null 
          ? DateTime.now().add(ttl).millisecondsSinceEpoch
          : null;
      
      await box.put(key, {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiration': expiration,
      });
      
      AppLogger.debug('Data cached: $key');
    } catch (error) {
      AppLogger.error('Failed to cache data: $error');
    }
  }

  /// Get cached data
  Future<T?> getCachedData<T>(String key) async {
    try {
      final box = await _cacheBox;
      final cached = box.get(key);
      
      if (cached == null) return null;
      
      final expiration = cached['expiration'] as int?;
      if (expiration != null && DateTime.now().millisecondsSinceEpoch > expiration) {
        // Cache expired
        await box.delete(key);
        return null;
      }
      
      return cached['data'] as T?;
    } catch (error) {
      AppLogger.error('Failed to get cached data: $error');
      return null;
    }
  }

  /// Store offline data for later sync
  Future<void> storeOfflineData(String key, Map<String, dynamic> data) async {
    try {
      final box = await _offlineBox;
      final offlineData = box.get(AppConfig.offlineDataKey, defaultValue: <String, dynamic>{});
      
      offlineData[key] = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      await box.put(AppConfig.offlineDataKey, offlineData);
      AppLogger.debug('Offline data stored: $key');
    } catch (error) {
      AppLogger.error('Failed to store offline data: $error');
    }
  }

  /// Get all offline data
  Future<Map<String, dynamic>> getOfflineData() async {
    try {
      final box = await _offlineBox;
      return Map<String, dynamic>.from(
        box.get(AppConfig.offlineDataKey, defaultValue: <String, dynamic>{}),
      );
    } catch (error) {
      AppLogger.error('Failed to get offline data: $error');
      return {};
    }
  }

  /// Clear offline data
  Future<void> clearOfflineData([String? key]) async {
    try {
      final box = await _offlineBox;
      
      if (key != null) {
        final offlineData = box.get(AppConfig.offlineDataKey, defaultValue: <String, dynamic>{});
        offlineData.remove(key);
        await box.put(AppConfig.offlineDataKey, offlineData);
        AppLogger.debug('Offline data cleared: $key');
      } else {
        await box.delete(AppConfig.offlineDataKey);
        AppLogger.debug('All offline data cleared');
      }
    } catch (error) {
      AppLogger.error('Failed to clear offline data: $error');
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      final box = await _cacheBox;
      await box.clear();
      AppLogger.debug('Cache cleared');
    } catch (error) {
      AppLogger.error('Failed to clear cache: $error');
    }
  }

  /// Get cache size in bytes
  Future<int> getCacheSize() async {
    try {
      final box = await _cacheBox;
      int size = 0;
      
      for (final value in box.values) {
        final json = jsonEncode(value);
        size += json.length;
      }
      
      return size;
    } catch (error) {
      AppLogger.error('Failed to get cache size: $error');
      return 0;
    }
  }

  /// Clean expired cache entries
  Future<void> cleanExpiredCache() async {
    try {
      final box = await _cacheBox;
      final now = DateTime.now().millisecondsSinceEpoch;
      final keysToDelete = <String>[];
      
      for (final entry in box.toMap().entries) {
        final cached = entry.value;
        if (cached is Map) {
          final expiration = cached['expiration'] as int?;
          if (expiration != null && now > expiration) {
            keysToDelete.add(entry.key.toString());
          }
        }
      }
      
      for (final key in keysToDelete) {
        await box.delete(key);
      }
      
      AppLogger.debug('Cleaned ${keysToDelete.length} expired cache entries');
    } catch (error) {
      AppLogger.error('Failed to clean expired cache: $error');
    }
  }

  /// Close all boxes
  Future<void> close() async {
    try {
      await _uiMetadataBoxInstance?.close();
      await _themeBoxInstance?.close();
      await _userBoxInstance?.close();
      await _cacheBoxInstance?.close();
      await _preferencesBoxInstance?.close();
      await _offlineBoxInstance?.close();
      
      AppLogger.debug('Storage service closed');
    } catch (error) {
      AppLogger.error('Failed to close storage service: $error');
    }
  }
}

// Placeholder adapter classes - these would be generated by Hive
class UIMetadataAdapter extends TypeAdapter<UIMetadata> {
  @override
  final int typeId = 0;

  @override
  UIMetadata read(BinaryReader reader) {
    // Implementation would be generated
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, UIMetadata obj) {
    // Implementation would be generated
    throw UnimplementedError();
  }
}

class LayoutConfigurationAdapter extends TypeAdapter<LayoutConfiguration> {
  @override
  final int typeId = 1;

  @override
  LayoutConfiguration read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, LayoutConfiguration obj) {
    throw UnimplementedError();
  }
}

class WidgetConfigurationAdapter extends TypeAdapter<WidgetConfiguration> {
  @override
  final int typeId = 2;

  @override
  WidgetConfiguration read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, WidgetConfiguration obj) {
    throw UnimplementedError();
  }
}

class PositionConfigurationAdapter extends TypeAdapter<PositionConfiguration> {
  @override
  final int typeId = 3;

  @override
  PositionConfiguration read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, PositionConfiguration obj) {
    throw UnimplementedError();
  }
}

class SizeConfigurationAdapter extends TypeAdapter<SizeConfiguration> {
  @override
  final int typeId = 4;

  @override
  SizeConfiguration read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, SizeConfiguration obj) {
    throw UnimplementedError();
  }
}

class StyleConfigurationAdapter extends TypeAdapter<StyleConfiguration> {
  @override
  final int typeId = 5;

  @override
  StyleConfiguration read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, StyleConfiguration obj) {
    throw UnimplementedError();
  }
}

class EdgeInsetsDataAdapter extends TypeAdapter<EdgeInsetsData> {
  @override
  final int typeId = 6;

  @override
  EdgeInsetsData read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, EdgeInsetsData obj) {
    throw UnimplementedError();
  }
}

class ThemeConfigAdapter extends TypeAdapter<ThemeConfig> {
  @override
  final int typeId = 10;

  @override
  ThemeConfig read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, ThemeConfig obj) {
    throw UnimplementedError();
  }
}

class ColorPaletteAdapter extends TypeAdapter<ColorPalette> {
  @override
  final int typeId = 11;

  @override
  ColorPalette read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, ColorPalette obj) {
    throw UnimplementedError();
  }
}

class ColorScaleAdapter extends TypeAdapter<ColorScale> {
  @override
  final int typeId = 12;

  @override
  ColorScale read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, ColorScale obj) {
    throw UnimplementedError();
  }
}

class LayoutTypeAdapter extends TypeAdapter<LayoutType> {
  @override
  final int typeId = 20;

  @override
  LayoutType read(BinaryReader reader) {
    throw UnimplementedError();
  }

  @override
  void write(BinaryWriter writer, LayoutType obj) {
    throw UnimplementedError();
  }
}
