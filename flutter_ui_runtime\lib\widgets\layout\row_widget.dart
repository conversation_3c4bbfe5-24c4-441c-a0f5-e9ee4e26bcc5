import 'package:flutter/material.dart';
import '../../core/widgets/dynamic_widget.dart';
import '../../core/widgets/dynamic_widget_renderer.dart';
import '../../core/models/ui_metadata.dart';

/// Dynamic row widget that renders horizontal layouts from configuration
class RowWidget extends DynamicWidget {
  const RowWidget(super.config);

  @override
  Widget build() {
    final mainAxisAlignment = getProp<String>('mainAxisAlignment', 'start');
    final crossAxisAlignment = getProp<String>('crossAxisAlignment', 'center');
    final mainAxisSize = getProp<String>('mainAxisSize', 'max');
    final spacing = getProp<double>('spacing', 0.0);

    final children = _buildChildren();

    return Row(
      mainAxisAlignment: buildMainAxisAlignment(mainAxisAlignment),
      crossAxisAlignment: buildCrossAxisAlignment(crossAxisAlignment),
      mainAxisSize: buildMainAxisSize(mainAxisSize),
      children: spacing > 0 ? _addSpacing(children, spacing) : children,
    );
  }

  List<Widget> _buildChildren() {
    return children.map((childConfig) {
      return DynamicWidgetRenderer(
        config: childConfig,
        parentId: id,
      );
    }).toList();
  }

  List<Widget> _addSpacing(List<Widget> children, double spacing) {
    if (children.isEmpty) return children;

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(width: spacing));
      }
    }
    return spacedChildren;
  }
}
