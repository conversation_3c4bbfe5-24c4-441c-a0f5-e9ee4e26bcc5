import 'package:flutter/material.dart';
import '../../core/widgets/dynamic_widget.dart';
import '../../core/models/ui_metadata.dart';

/// Dynamic text widget that renders text from configuration
class TextWidget extends DynamicWidget {
  const TextWidget(super.config);

  @override
  Widget build() {
    final text = getRequiredProp<String>('text');
    final style = getProp<Map<String, dynamic>>('style');
    final textAlign = getProp<String>('textAlign', 'left');
    final maxLines = getProp<int>('maxLines');
    final overflow = getProp<String>('overflow', 'clip');
    final selectable = getProp<bool>('selectable', false);

    final textStyle = buildTextStyle(style);
    final alignment = _buildTextAlign(textAlign);
    final textOverflow = _buildTextOverflow(overflow);

    if (selectable) {
      return SelectableText(
        text,
        style: textStyle,
        textAlign: alignment,
        maxLines: maxLines,
      );
    }

    return Text(
      text,
      style: textStyle,
      textAlign: alignment,
      maxLines: maxLines,
      overflow: textOverflow,
    );
  }

  TextAlign _buildTextAlign(String align) {
    switch (align.toLowerCase()) {
      case 'left':
        return TextAlign.left;
      case 'right':
        return TextAlign.right;
      case 'center':
        return TextAlign.center;
      case 'justify':
        return TextAlign.justify;
      case 'start':
        return TextAlign.start;
      case 'end':
        return TextAlign.end;
      default:
        return TextAlign.left;
    }
  }

  TextOverflow _buildTextOverflow(String overflow) {
    switch (overflow.toLowerCase()) {
      case 'clip':
        return TextOverflow.clip;
      case 'fade':
        return TextOverflow.fade;
      case 'ellipsis':
        return TextOverflow.ellipsis;
      case 'visible':
        return TextOverflow.visible;
      default:
        return TextOverflow.clip;
    }
  }
}
