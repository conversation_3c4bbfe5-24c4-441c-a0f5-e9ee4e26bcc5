package com.uiplatform.repository;

import com.uiplatform.entity.Layout;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Layout entity.
 * Provides CRUD operations and custom queries for layout management.
 */
@Repository
public interface LayoutRepository extends JpaRepository<Layout, UUID>, JpaSpecificationExecutor<Layout> {

    /**
     * Find layout by name and organization.
     */
    Optional<Layout> findByNameAndOrganizationIdAndDeletedFalse(String name, UUID organizationId);

    /**
     * Find layouts by organization.
     */
    List<Layout> findByOrganizationIdAndDeletedFalse(UUID organizationId);

    /**
     * Find layouts by organization with pagination.
     */
    Page<Layout> findByOrganizationIdAndDeletedFalse(UUID organizationId, Pageable pageable);

    /**
     * Find layouts by author.
     */
    List<Layout> findByAuthorIdAndDeletedFalse(UUID authorId);

    /**
     * Find layouts by type.
     */
    List<Layout> findByLayoutTypeAndDeletedFalse(Layout.LayoutType layoutType);

    /**
     * Find default layout by organization.
     */
    Optional<Layout> findByOrganizationIdAndIsDefaultTrueAndDeletedFalse(UUID organizationId);

    /**
     * Find public layouts.
     */
    List<Layout> findByIsPublicTrueAndDeletedFalse();

    /**
     * Find public layouts with pagination.
     */
    Page<Layout> findByIsPublicTrueAndDeletedFalse(Pageable pageable);

    /**
     * Find system layouts.
     */
    List<Layout> findByIsSystemTrueAndDeletedFalse();

    /**
     * Search layouts by name.
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(:organizationId IS NULL OR l.organization.id = :organizationId OR l.isPublic = true) AND " +
           "LOWER(l.name) LIKE LOWER(CONCAT('%', :name, '%')) AND l.deleted = false")
    Page<Layout> searchByName(@Param("organizationId") UUID organizationId, 
                             @Param("name") String name, 
                             Pageable pageable);

    /**
     * Search layouts by tags.
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(:organizationId IS NULL OR l.organization.id = :organizationId OR l.isPublic = true) AND " +
           "LOWER(l.tags) LIKE LOWER(CONCAT('%', :tag, '%')) AND l.deleted = false")
    Page<Layout> searchByTags(@Param("organizationId") UUID organizationId, 
                             @Param("tag") String tag, 
                             Pageable pageable);

    /**
     * Find layouts by multiple criteria.
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(:organizationId IS NULL OR l.organization.id = :organizationId OR l.isPublic = true) AND " +
           "(:layoutType IS NULL OR l.layoutType = :layoutType) AND " +
           "(:authorId IS NULL OR l.author.id = :authorId) AND " +
           "(:isPublic IS NULL OR l.isPublic = :isPublic) AND " +
           "(:isSystem IS NULL OR l.isSystem = :isSystem) AND " +
           "l.deleted = false")
    Page<Layout> findByCriteria(@Param("organizationId") UUID organizationId,
                               @Param("layoutType") Layout.LayoutType layoutType,
                               @Param("authorId") UUID authorId,
                               @Param("isPublic") Boolean isPublic,
                               @Param("isSystem") Boolean isSystem,
                               Pageable pageable);

    /**
     * Count layouts by organization.
     */
    @Query("SELECT COUNT(l) FROM Layout l WHERE l.organization.id = :organizationId AND l.deleted = false")
    Long countByOrganizationId(@Param("organizationId") UUID organizationId);

    /**
     * Count layouts by type.
     */
    @Query("SELECT COUNT(l) FROM Layout l WHERE l.layoutType = :layoutType AND l.deleted = false")
    Long countByLayoutType(@Param("layoutType") Layout.LayoutType layoutType);

    /**
     * Count public layouts.
     */
    @Query("SELECT COUNT(l) FROM Layout l WHERE l.isPublic = true AND l.deleted = false")
    Long countPublicLayouts();

    /**
     * Check if layout name exists in organization (excluding current layout).
     */
    @Query("SELECT COUNT(l) > 0 FROM Layout l WHERE l.name = :name AND l.organization.id = :organizationId AND l.id != :excludeId AND l.deleted = false")
    boolean existsByNameAndOrganizationIdAndIdNot(@Param("name") String name, 
                                                 @Param("organizationId") UUID organizationId, 
                                                 @Param("excludeId") UUID excludeId);

    /**
     * Find layouts with usage count.
     */
    @Query("SELECT l, COUNT(u) as usageCount FROM Layout l LEFT JOIN l.uiConfigurations u " +
           "WHERE (:organizationId IS NULL OR l.organization.id = :organizationId) AND l.deleted = false GROUP BY l")
    List<Object[]> findWithUsageCount(@Param("organizationId") UUID organizationId);

    /**
     * Find most popular layouts.
     */
    @Query("SELECT l, COUNT(u) as usageCount FROM Layout l LEFT JOIN l.uiConfigurations u " +
           "WHERE l.isPublic = true AND l.deleted = false GROUP BY l ORDER BY usageCount DESC")
    List<Object[]> findMostPopularLayouts(Pageable pageable);

    /**
     * Set layout as default for organization.
     */
    @Modifying
    @Query("UPDATE Layout l SET l.isDefault = CASE WHEN l.id = :layoutId THEN true ELSE false END WHERE l.organization.id = :organizationId")
    void setAsDefaultForOrganization(@Param("layoutId") UUID layoutId, @Param("organizationId") UUID organizationId);

    /**
     * Find recently created layouts.
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(:organizationId IS NULL OR l.organization.id = :organizationId OR l.isPublic = true) AND " +
           "l.deleted = false ORDER BY l.createdAt DESC")
    List<Layout> findRecentLayouts(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Find available layouts for organization (own + public + system).
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(l.organization.id = :organizationId OR l.isPublic = true OR l.isSystem = true) AND " +
           "l.deleted = false ORDER BY l.isSystem DESC, l.isDefault DESC, l.name ASC")
    List<Layout> findAvailableForOrganization(@Param("organizationId") UUID organizationId);

    /**
     * Find available layouts for organization with pagination.
     */
    @Query("SELECT l FROM Layout l WHERE " +
           "(l.organization.id = :organizationId OR l.isPublic = true OR l.isSystem = true) AND " +
           "l.deleted = false ORDER BY l.isSystem DESC, l.isDefault DESC, l.name ASC")
    Page<Layout> findAvailableForOrganization(@Param("organizationId") UUID organizationId, Pageable pageable);

    /**
     * Find layouts by type with pagination.
     */
    Page<Layout> findByLayoutTypeAndDeletedFalse(Layout.LayoutType layoutType, Pageable pageable);

    /**
     * Find layouts by author with pagination.
     */
    Page<Layout> findByAuthorIdAndDeletedFalse(UUID authorId, Pageable pageable);

    /**
     * Update layout public status.
     */
    @Modifying
    @Query("UPDATE Layout l SET l.isPublic = :isPublic WHERE l.id = :id")
    void updatePublicStatus(@Param("id") UUID id, @Param("isPublic") Boolean isPublic);

    /**
     * Find layouts by type and organization.
     */
    List<Layout> findByLayoutTypeAndOrganizationIdAndDeletedFalse(Layout.LayoutType layoutType, UUID organizationId);

    /**
     * Find layouts by type and organization with pagination.
     */
    Page<Layout> findByLayoutTypeAndOrganizationIdAndDeletedFalse(Layout.LayoutType layoutType, UUID organizationId, Pageable pageable);
}
