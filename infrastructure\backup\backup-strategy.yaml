apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
  namespace: ui-builder
data:
  backup-schedule.yaml: |
    # UI Builder Backup Strategy Configuration
    
    # Database Backup Configuration
    database:
      # PostgreSQL backup settings
      postgres:
        enabled: true
        schedule: "0 2 * * *"  # Daily at 2 AM
        retention:
          daily: 7    # Keep 7 daily backups
          weekly: 4   # Keep 4 weekly backups
          monthly: 12 # Keep 12 monthly backups
        compression: true
        encryption: true
        storage:
          type: "s3"
          bucket: "ui-builder-backups"
          path: "database/postgres"
        
        # Backup verification
        verification:
          enabled: true
          schedule: "0 6 * * 0"  # Weekly verification on Sunday at 6 AM
          restore_test: true
        
        # Point-in-time recovery
        pitr:
          enabled: true
          wal_archive: true
          retention_days: 30
    
    # Redis Backup Configuration
    redis:
      enabled: true
      schedule: "0 3 * * *"  # Daily at 3 AM
      retention:
        daily: 7
        weekly: 4
      storage:
        type: "s3"
        bucket: "ui-builder-backups"
        path: "redis"
    
    # File Storage Backup
    files:
      # User uploaded assets
      assets:
        enabled: true
        schedule: "0 1 * * *"  # Daily at 1 AM
        retention:
          daily: 30
          monthly: 12
        storage:
          type: "s3"
          bucket: "ui-builder-backups"
          path: "assets"
        incremental: true
      
      # Application logs
      logs:
        enabled: true
        schedule: "0 4 * * *"  # Daily at 4 AM
        retention:
          daily: 30
          monthly: 6
        compression: true
        storage:
          type: "s3"
          bucket: "ui-builder-logs"
          path: "application-logs"
    
    # Configuration Backup
    configuration:
      # Kubernetes configurations
      kubernetes:
        enabled: true
        schedule: "0 0 * * 0"  # Weekly on Sunday
        retention:
          weekly: 8
          monthly: 6
        items:
          - configmaps
          - secrets
          - deployments
          - services
          - ingress
        storage:
          type: "git"
          repository: "**************:ui-builder/k8s-configs-backup.git"
      
      # Application configurations
      application:
        enabled: true
        schedule: "0 0 * * 0"  # Weekly on Sunday
        retention:
          weekly: 8
        storage:
          type: "s3"
          bucket: "ui-builder-backups"
          path: "configurations"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: ui-builder
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-credentials
                  key: password
            - name: POSTGRES_HOST
              value: "postgres-service"
            - name: POSTGRES_DB
              value: "uibuilder"
            - name: POSTGRES_USER
              value: "uibuilder"
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: BACKUP_BUCKET
              value: "ui-builder-backups"
            command:
            - /bin/bash
            - -c
            - |
              set -e
              
              # Create backup filename with timestamp
              BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="postgres_backup_${BACKUP_DATE}.sql"
              COMPRESSED_FILE="${BACKUP_FILE}.gz"
              
              echo "Starting PostgreSQL backup at $(date)"
              
              # Create database dump
              pg_dump -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DB \
                --verbose --clean --if-exists --create \
                > /tmp/$BACKUP_FILE
              
              # Compress the backup
              gzip /tmp/$BACKUP_FILE
              
              # Upload to S3
              aws s3 cp /tmp/$COMPRESSED_FILE \
                s3://$BACKUP_BUCKET/database/postgres/$COMPRESSED_FILE \
                --storage-class STANDARD_IA
              
              # Verify backup integrity
              aws s3api head-object \
                --bucket $BACKUP_BUCKET \
                --key database/postgres/$COMPRESSED_FILE
              
              echo "Backup completed successfully: $COMPRESSED_FILE"
              
              # Clean up old backups (keep last 7 days)
              aws s3 ls s3://$BACKUP_BUCKET/database/postgres/ | \
                awk '{print $4}' | \
                sort -r | \
                tail -n +8 | \
                while read file; do
                  echo "Deleting old backup: $file"
                  aws s3 rm s3://$BACKUP_BUCKET/database/postgres/$file
                done
              
              echo "Backup process completed at $(date)"
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
          restartPolicy: OnFailure
          
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: ui-builder
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: redis-backup
            image: redis:7
            env:
            - name: REDIS_HOST
              value: "redis-service"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: BACKUP_BUCKET
              value: "ui-builder-backups"
            command:
            - /bin/bash
            - -c
            - |
              set -e
              
              # Install AWS CLI
              apt-get update && apt-get install -y awscli
              
              BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="redis_backup_${BACKUP_DATE}.rdb"
              
              echo "Starting Redis backup at $(date)"
              
              # Create Redis backup using BGSAVE
              redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD BGSAVE
              
              # Wait for backup to complete
              while [ $(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD LASTSAVE) -eq $(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD LASTSAVE) ]; do
                sleep 1
              done
              
              # Copy the RDB file
              redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD \
                --rdb /tmp/$BACKUP_FILE
              
              # Upload to S3
              aws s3 cp /tmp/$BACKUP_FILE \
                s3://$BACKUP_BUCKET/redis/$BACKUP_FILE \
                --storage-class STANDARD_IA
              
              echo "Redis backup completed: $BACKUP_FILE"
              
              # Clean up old backups
              aws s3 ls s3://$BACKUP_BUCKET/redis/ | \
                awk '{print $4}' | \
                sort -r | \
                tail -n +8 | \
                while read file; do
                  aws s3 rm s3://$BACKUP_BUCKET/redis/$file
                done
          restartPolicy: OnFailure

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-scripts
  namespace: ui-builder
data:
  restore-postgres.sh: |
    #!/bin/bash
    set -e
    
    if [ -z "$1" ]; then
      echo "Usage: $0 <backup-file-name>"
      echo "Available backups:"
      aws s3 ls s3://$BACKUP_BUCKET/database/postgres/ | tail -10
      exit 1
    fi
    
    BACKUP_FILE=$1
    
    echo "Restoring PostgreSQL from backup: $BACKUP_FILE"
    
    # Download backup from S3
    aws s3 cp s3://$BACKUP_BUCKET/database/postgres/$BACKUP_FILE /tmp/$BACKUP_FILE
    
    # Decompress if needed
    if [[ $BACKUP_FILE == *.gz ]]; then
      gunzip /tmp/$BACKUP_FILE
      BACKUP_FILE=${BACKUP_FILE%.gz}
    fi
    
    # Restore database
    psql -h $POSTGRES_HOST -U $POSTGRES_USER -d postgres < /tmp/$BACKUP_FILE
    
    echo "Database restore completed successfully"
  
  restore-redis.sh: |
    #!/bin/bash
    set -e
    
    if [ -z "$1" ]; then
      echo "Usage: $0 <backup-file-name>"
      echo "Available backups:"
      aws s3 ls s3://$BACKUP_BUCKET/redis/ | tail -10
      exit 1
    fi
    
    BACKUP_FILE=$1
    
    echo "Restoring Redis from backup: $BACKUP_FILE"
    
    # Download backup from S3
    aws s3 cp s3://$BACKUP_BUCKET/redis/$BACKUP_FILE /tmp/$BACKUP_FILE
    
    # Stop Redis temporarily
    redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD SHUTDOWN NOSAVE || true
    
    # Copy backup file to Redis data directory
    # This would need to be adapted based on your Redis setup
    
    # Restart Redis
    # This would need to be adapted based on your Redis setup
    
    echo "Redis restore completed successfully"
  
  verify-backup.sh: |
    #!/bin/bash
    set -e
    
    echo "Starting backup verification at $(date)"
    
    # Verify PostgreSQL backup
    LATEST_PG_BACKUP=$(aws s3 ls s3://$BACKUP_BUCKET/database/postgres/ | sort | tail -1 | awk '{print $4}')
    if [ -n "$LATEST_PG_BACKUP" ]; then
      echo "Verifying PostgreSQL backup: $LATEST_PG_BACKUP"
      
      # Download and test restore to temporary database
      aws s3 cp s3://$BACKUP_BUCKET/database/postgres/$LATEST_PG_BACKUP /tmp/
      
      # Create temporary database for testing
      createdb -h $POSTGRES_HOST -U $POSTGRES_USER test_restore_$(date +%s)
      
      # Test restore (this is a simplified version)
      if [[ $LATEST_PG_BACKUP == *.gz ]]; then
        gunzip -c /tmp/$LATEST_PG_BACKUP | head -100 | grep -q "PostgreSQL database dump"
      else
        head -100 /tmp/$LATEST_PG_BACKUP | grep -q "PostgreSQL database dump"
      fi
      
      echo "PostgreSQL backup verification passed"
    fi
    
    # Verify Redis backup
    LATEST_REDIS_BACKUP=$(aws s3 ls s3://$BACKUP_BUCKET/redis/ | sort | tail -1 | awk '{print $4}')
    if [ -n "$LATEST_REDIS_BACKUP" ]; then
      echo "Verifying Redis backup: $LATEST_REDIS_BACKUP"
      
      # Download and verify RDB file format
      aws s3 cp s3://$BACKUP_BUCKET/redis/$LATEST_REDIS_BACKUP /tmp/
      
      # Check RDB file magic number
      if file /tmp/$LATEST_REDIS_BACKUP | grep -q "Redis RDB"; then
        echo "Redis backup verification passed"
      else
        echo "Redis backup verification failed"
        exit 1
      fi
    fi
    
    echo "Backup verification completed at $(date)"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: ui-builder
spec:
  schedule: "0 6 * * 0"  # Weekly on Sunday at 6 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup-verification
            image: postgres:15
            env:
            - name: POSTGRES_HOST
              value: "postgres-service"
            - name: POSTGRES_USER
              value: "uibuilder"
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-credentials
                  key: password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: BACKUP_BUCKET
              value: "ui-builder-backups"
            command:
            - /bin/bash
            - /scripts/verify-backup.sh
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          restartPolicy: OnFailure
