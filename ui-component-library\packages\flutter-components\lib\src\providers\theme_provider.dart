import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:adaptive_theme/adaptive_theme.dart';

import '../foundation/design_tokens.dart';
import '../types/theme_types.dart';

part 'theme_provider.g.dart';

/// Theme mode enum
enum UIThemeMode {
  light,
  dark,
  system,
}

/// Theme configuration class
class UIThemeConfig {
  const UIThemeConfig({
    this.mode = UIThemeMode.system,
    this.primaryColor = const Color(0xFF3B82F6),
    this.fontFamily = 'Inter',
    this.borderRadius = 8.0,
    this.density = UIComponentDensity.comfortable,
    this.reducedMotion = false,
    this.highContrast = false,
    this.customColors,
  });

  final UIThemeMode mode;
  final Color primaryColor;
  final String fontFamily;
  final double borderRadius;
  final UIComponentDensity density;
  final bool reducedMotion;
  final bool highContrast;
  final Map<String, Color>? customColors;

  UIThemeConfig copyWith({
    UIThemeMode? mode,
    Color? primaryColor,
    String? fontFamily,
    double? borderRadius,
    UIComponentDensity? density,
    bool? reducedMotion,
    bool? highContrast,
    Map<String, Color>? customColors,
  }) {
    return UIThemeConfig(
      mode: mode ?? this.mode,
      primaryColor: primaryColor ?? this.primaryColor,
      fontFamily: fontFamily ?? this.fontFamily,
      borderRadius: borderRadius ?? this.borderRadius,
      density: density ?? this.density,
      reducedMotion: reducedMotion ?? this.reducedMotion,
      highContrast: highContrast ?? this.highContrast,
      customColors: customColors ?? this.customColors,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mode': mode.index,
      'primaryColor': primaryColor.value,
      'fontFamily': fontFamily,
      'borderRadius': borderRadius,
      'density': density.index,
      'reducedMotion': reducedMotion,
      'highContrast': highContrast,
      'customColors': customColors?.map((k, v) => MapEntry(k, v.value)),
    };
  }

  factory UIThemeConfig.fromJson(Map<String, dynamic> json) {
    return UIThemeConfig(
      mode: UIThemeMode.values[json['mode'] ?? 0],
      primaryColor: Color(json['primaryColor'] ?? 0xFF3B82F6),
      fontFamily: json['fontFamily'] ?? 'Inter',
      borderRadius: json['borderRadius']?.toDouble() ?? 8.0,
      density: UIComponentDensity.values[json['density'] ?? 1],
      reducedMotion: json['reducedMotion'] ?? false,
      highContrast: json['highContrast'] ?? false,
      customColors: json['customColors']?.map<String, Color>(
        (k, v) => MapEntry(k, Color(v)),
      ),
    );
  }
}

/// Theme state notifier
@riverpod
class UIThemeNotifier extends _$UIThemeNotifier {
  @override
  UIThemeConfig build() {
    return const UIThemeConfig();
  }

  /// Update theme configuration
  void updateTheme(UIThemeConfig newTheme) {
    state = newTheme;
    _saveThemeToStorage(newTheme);
  }

  /// Set theme mode
  void setMode(UIThemeMode mode) {
    state = state.copyWith(mode: mode);
    _saveThemeToStorage(state);
  }

  /// Toggle between light and dark mode
  void toggleMode() {
    final newMode = state.mode == UIThemeMode.light 
        ? UIThemeMode.dark 
        : UIThemeMode.light;
    setMode(newMode);
  }

  /// Update primary color
  void updatePrimaryColor(Color color) {
    state = state.copyWith(primaryColor: color);
    _saveThemeToStorage(state);
  }

  /// Update font family
  void updateFontFamily(String fontFamily) {
    state = state.copyWith(fontFamily: fontFamily);
    _saveThemeToStorage(state);
  }

  /// Update border radius
  void updateBorderRadius(double borderRadius) {
    state = state.copyWith(borderRadius: borderRadius);
    _saveThemeToStorage(state);
  }

  /// Update component density
  void updateDensity(UIComponentDensity density) {
    state = state.copyWith(density: density);
    _saveThemeToStorage(state);
  }

  /// Update reduced motion preference
  void updateReducedMotion(bool reducedMotion) {
    state = state.copyWith(reducedMotion: reducedMotion);
    _saveThemeToStorage(state);
  }

  /// Update high contrast preference
  void updateHighContrast(bool highContrast) {
    state = state.copyWith(highContrast: highContrast);
    _saveThemeToStorage(state);
  }

  /// Reset theme to default
  void resetTheme() {
    state = const UIThemeConfig();
    _saveThemeToStorage(state);
  }

  /// Load theme from storage
  Future<void> loadThemeFromStorage() async {
    // Implementation would load from SharedPreferences or similar
    // For now, we'll use a placeholder
  }

  /// Save theme to storage
  void _saveThemeToStorage(UIThemeConfig theme) {
    // Implementation would save to SharedPreferences or similar
    // For now, we'll use a placeholder
  }
}

/// Current theme mode provider (resolves system mode)
@riverpod
UIThemeMode currentThemeMode(CurrentThemeModeRef ref) {
  final config = ref.watch(uIThemeNotifierProvider);
  final brightness = ref.watch(platformBrightnessProvider);
  
  if (config.mode == UIThemeMode.system) {
    return brightness == Brightness.dark ? UIThemeMode.dark : UIThemeMode.light;
  }
  
  return config.mode;
}

/// Platform brightness provider
@riverpod
Brightness platformBrightness(PlatformBrightnessRef ref) {
  return WidgetsBinding.instance.platformDispatcher.platformBrightness;
}

/// Theme data provider
@riverpod
ThemeData themeData(ThemeDataRef ref) {
  final config = ref.watch(uIThemeNotifierProvider);
  final mode = ref.watch(currentThemeModeProvider);
  final tokens = ref.watch(designTokensProvider);
  
  return _buildThemeData(config, mode, tokens);
}

/// Dark theme data provider
@riverpod
ThemeData darkThemeData(DarkThemeDataRef ref) {
  final config = ref.watch(uIThemeNotifierProvider);
  final tokens = ref.watch(designTokensProvider);
  
  return _buildThemeData(config, UIThemeMode.dark, tokens);
}

/// Build theme data from configuration
ThemeData _buildThemeData(
  UIThemeConfig config,
  UIThemeMode mode,
  DesignTokens tokens,
) {
  final isDark = mode == UIThemeMode.dark;
  
  // Create color scheme
  final colorScheme = isDark
      ? ColorScheme.dark(
          primary: config.primaryColor,
          secondary: tokens.colors.secondary.shade500,
          surface: tokens.colors.neutral.shade900,
          background: tokens.colors.neutral.shade950,
          error: tokens.colors.error.shade500,
        )
      : ColorScheme.light(
          primary: config.primaryColor,
          secondary: tokens.colors.secondary.shade500,
          surface: tokens.colors.neutral.shade50,
          background: tokens.colors.neutral.white,
          error: tokens.colors.error.shade500,
        );

  // Create text theme
  final textTheme = _buildTextTheme(config, tokens);
  
  // Create component themes
  final componentThemes = _buildComponentThemes(config, colorScheme, tokens);

  return ThemeData(
    useMaterial3: true,
    colorScheme: colorScheme,
    fontFamily: config.fontFamily,
    textTheme: textTheme,
    visualDensity: _getVisualDensity(config.density),
    animationDuration: config.reducedMotion 
        ? Duration.zero 
        : Duration(milliseconds: tokens.duration.normal),
    ...componentThemes,
  );
}

/// Build text theme
TextTheme _buildTextTheme(UIThemeConfig config, DesignTokens tokens) {
  return TextTheme(
    displayLarge: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.display.xl.fontSize,
      fontWeight: FontWeight.w700,
      height: tokens.typography.display.xl.lineHeight,
    ),
    displayMedium: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.display.lg.fontSize,
      fontWeight: FontWeight.w600,
      height: tokens.typography.display.lg.lineHeight,
    ),
    displaySmall: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.display.md.fontSize,
      fontWeight: FontWeight.w600,
      height: tokens.typography.display.md.lineHeight,
    ),
    headlineLarge: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h1.fontSize,
      fontWeight: FontWeight.w600,
      height: tokens.typography.heading.h1.lineHeight,
    ),
    headlineMedium: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h2.fontSize,
      fontWeight: FontWeight.w600,
      height: tokens.typography.heading.h2.lineHeight,
    ),
    headlineSmall: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h3.fontSize,
      fontWeight: FontWeight.w500,
      height: tokens.typography.heading.h3.lineHeight,
    ),
    titleLarge: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h4.fontSize,
      fontWeight: FontWeight.w500,
      height: tokens.typography.heading.h4.lineHeight,
    ),
    titleMedium: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h5.fontSize,
      fontWeight: FontWeight.w500,
      height: tokens.typography.heading.h5.lineHeight,
    ),
    titleSmall: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.heading.h6.fontSize,
      fontWeight: FontWeight.w500,
      height: tokens.typography.heading.h6.lineHeight,
    ),
    bodyLarge: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.body.lg.fontSize,
      fontWeight: FontWeight.w400,
      height: tokens.typography.body.lg.lineHeight,
    ),
    bodyMedium: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.body.md.fontSize,
      fontWeight: FontWeight.w400,
      height: tokens.typography.body.md.lineHeight,
    ),
    bodySmall: TextStyle(
      fontFamily: config.fontFamily,
      fontSize: tokens.typography.body.sm.fontSize,
      fontWeight: FontWeight.w400,
      height: tokens.typography.body.sm.lineHeight,
    ),
  );
}

/// Build component themes
Map<String, dynamic> _buildComponentThemes(
  UIThemeConfig config,
  ColorScheme colorScheme,
  DesignTokens tokens,
) {
  return {
    'elevatedButtonTheme': ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borderRadius),
        ),
      ),
    ),
    'outlinedButtonTheme': OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borderRadius),
        ),
      ),
    ),
    'textButtonTheme': TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borderRadius),
        ),
      ),
    ),
    'inputDecorationTheme': InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(config.borderRadius),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(config.borderRadius),
        borderSide: BorderSide(color: colorScheme.primary),
      ),
    ),
    'cardTheme': CardTheme(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(config.borderRadius),
      ),
    ),
  };
}

/// Get visual density from component density
VisualDensity _getVisualDensity(UIComponentDensity density) {
  switch (density) {
    case UIComponentDensity.compact:
      return VisualDensity.compact;
    case UIComponentDensity.comfortable:
      return VisualDensity.comfortable;
    case UIComponentDensity.standard:
      return VisualDensity.standard;
  }
}
