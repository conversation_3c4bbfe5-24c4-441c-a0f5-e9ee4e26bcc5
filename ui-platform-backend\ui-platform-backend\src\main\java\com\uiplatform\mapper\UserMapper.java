package com.uiplatform.mapper;

import com.uiplatform.dto.UserDTO;
import com.uiplatform.entity.Role;
import com.uiplatform.entity.User;
import org.mapstruct.*;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Mapper interface for User entity and DTO conversions.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMapper {

    /**
     * Convert User entity to DTO.
     */
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "organizationId", source = "organization.id")
    @Mapping(target = "organizationName", source = "organization.name")
    @Mapping(target = "roleNames", source = "roles", qualifiedByName = "rolesToRoleNames")
    @Mapping(target = "ownedConfigurationsCount", ignore = true)
    @Mapping(target = "authoredTemplatesCount", ignore = true)
    UserDTO toDTO(User user);

    /**
     * Convert CreateDTO to User entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "password", ignore = true) // Will be set in service
    @Mapping(target = "status", constant = "ACTIVE")
    @Mapping(target = "emailVerified", constant = "false")
    @Mapping(target = "loginAttempts", constant = "0")
    @Mapping(target = "timezone", constant = "UTC")
    @Mapping(target = "locale", constant = "en_US")
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "ownedUIConfigurations", ignore = true)
    @Mapping(target = "authoredTemplates", ignore = true)
    @Mapping(target = "templateReviews", ignore = true)
    @Mapping(target = "authoredThemes", ignore = true)
    @Mapping(target = "authoredLayouts", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetExpiresAt", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "lockedUntil", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    User toEntity(UserDTO.CreateDTO createDTO);

    /**
     * Update User entity from UpdateDTO.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "username", ignore = true)
    @Mapping(target = "email", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "emailVerified", ignore = true)
    @Mapping(target = "loginAttempts", ignore = true)
    @Mapping(target = "organization", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "ownedUIConfigurations", ignore = true)
    @Mapping(target = "authoredTemplates", ignore = true)
    @Mapping(target = "templateReviews", ignore = true)
    @Mapping(target = "authoredThemes", ignore = true)
    @Mapping(target = "authoredLayouts", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetExpiresAt", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "lockedUntil", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void updateEntityFromDTO(UserDTO.UpdateDTO updateDTO, @MappingTarget User user);

    /**
     * Convert roles to role names.
     */
    @Named("rolesToRoleNames")
    default Set<String> rolesToRoleNames(Set<Role> roles) {
        if (roles == null) {
            return null;
        }
        return roles.stream()
                .map(Role::getName)
                .collect(Collectors.toSet());
    }
}
