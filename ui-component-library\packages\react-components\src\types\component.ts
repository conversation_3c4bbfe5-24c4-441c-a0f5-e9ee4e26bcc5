import { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes } from 'react';

/**
 * Base component props that all components should extend
 */
export interface BaseComponentProps {
  /** Additional CSS classes */
  className?: string;
  /** Component children */
  children?: ReactNode;
  /** Test ID for testing */
  testId?: string;
  /** Accessibility label */
  'aria-label'?: string;
  /** Accessibility description */
  'aria-describedby'?: string;
}

/**
 * Size variants used across components
 */
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Color variants used across components
 */
export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'accent'
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info'
  | 'neutral';

/**
 * Button variants
 */
export type ButtonVariant = 
  | 'primary'
  | 'secondary' 
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive';

/**
 * Input variants
 */
export type InputVariant = 'default' | 'filled' | 'outline' | 'underline';

/**
 * Alert variants
 */
export type AlertVariant = 'info' | 'success' | 'warning' | 'error';

/**
 * Badge variants
 */
export type BadgeVariant = 
  | 'default'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'outline';

/**
 * Loading state
 */
export interface LoadingState {
  loading?: boolean;
  loadingText?: string;
}

/**
 * Disabled state
 */
export interface DisabledState {
  disabled?: boolean;
}

/**
 * Form field state
 */
export interface FormFieldState {
  error?: boolean;
  errorMessage?: string;
  helperText?: string;
  required?: boolean;
}

/**
 * Animation props
 */
export interface AnimationProps {
  /** Animation duration in milliseconds */
  duration?: number;
  /** Animation delay in milliseconds */
  delay?: number;
  /** Animation easing function */
  easing?: string;
  /** Whether to animate on mount */
  animateOnMount?: boolean;
}

/**
 * Responsive props
 */
export interface ResponsiveProps<T> {
  /** Base value */
  base?: T;
  /** Small screens and up */
  sm?: T;
  /** Medium screens and up */
  md?: T;
  /** Large screens and up */
  lg?: T;
  /** Extra large screens and up */
  xl?: T;
  /** 2x extra large screens and up */
  '2xl'?: T;
}

/**
 * Spacing props
 */
export interface SpacingProps {
  /** Margin */
  m?: string | number;
  /** Margin top */
  mt?: string | number;
  /** Margin right */
  mr?: string | number;
  /** Margin bottom */
  mb?: string | number;
  /** Margin left */
  ml?: string | number;
  /** Margin horizontal (left and right) */
  mx?: string | number;
  /** Margin vertical (top and bottom) */
  my?: string | number;
  /** Padding */
  p?: string | number;
  /** Padding top */
  pt?: string | number;
  /** Padding right */
  pr?: string | number;
  /** Padding bottom */
  pb?: string | number;
  /** Padding left */
  pl?: string | number;
  /** Padding horizontal (left and right) */
  px?: string | number;
  /** Padding vertical (top and bottom) */
  py?: string | number;
}

/**
 * Layout props
 */
export interface LayoutProps {
  /** Width */
  width?: string | number;
  /** Height */
  height?: string | number;
  /** Min width */
  minWidth?: string | number;
  /** Min height */
  minHeight?: string | number;
  /** Max width */
  maxWidth?: string | number;
  /** Max height */
  maxHeight?: string | number;
  /** Display */
  display?: 'block' | 'inline' | 'inline-block' | 'flex' | 'inline-flex' | 'grid' | 'none';
  /** Position */
  position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';
  /** Z-index */
  zIndex?: number;
}

/**
 * Flex props
 */
export interface FlexProps {
  /** Flex direction */
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /** Justify content */
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  /** Align items */
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  /** Flex wrap */
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  /** Gap between items */
  gap?: string | number;
  /** Flex grow */
  grow?: boolean | number;
  /** Flex shrink */
  shrink?: boolean | number;
  /** Flex basis */
  basis?: string | number;
}

/**
 * Grid props
 */
export interface GridProps {
  /** Grid template columns */
  columns?: number | string;
  /** Grid template rows */
  rows?: number | string;
  /** Gap between items */
  gap?: string | number;
  /** Column gap */
  columnGap?: string | number;
  /** Row gap */
  rowGap?: string | number;
  /** Grid auto flow */
  autoFlow?: 'row' | 'column' | 'dense' | 'row-dense' | 'column-dense';
  /** Grid auto columns */
  autoColumns?: string;
  /** Grid auto rows */
  autoRows?: string;
}

/**
 * Extended HTML props for common elements
 */
export interface ExtendedHTMLProps extends HTMLAttributes<HTMLElement>, BaseComponentProps {}
export interface ExtendedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, BaseComponentProps {}
export interface ExtendedInputProps extends InputHTMLAttributes<HTMLInputElement>, BaseComponentProps {}

/**
 * Component ref types
 */
export type ComponentRef<T = HTMLElement> = React.Ref<T>;

/**
 * Event handler types
 */
export type ClickHandler = (event: React.MouseEvent) => void;
export type ChangeHandler<T = any> = (value: T) => void;
export type FocusHandler = (event: React.FocusEvent) => void;
export type BlurHandler = (event: React.FocusEvent) => void;
export type KeyboardHandler = (event: React.KeyboardEvent) => void;
