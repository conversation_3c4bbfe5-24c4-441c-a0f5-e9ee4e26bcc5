{"version": 2, "projects": {"design-tokens": {"root": "packages/design-tokens", "sourceRoot": "packages/design-tokens/src", "projectType": "library", "targets": {"build": {"executor": "@nx/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/design-tokens", "main": "packages/design-tokens/src/index.js", "tsConfig": "packages/design-tokens/tsconfig.lib.json"}}, "tokens": {"executor": "@nx/workspace:run-commands", "options": {"command": "style-dictionary build", "cwd": "packages/design-tokens"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/design-tokens"], "options": {"jestConfig": "packages/design-tokens/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/design-tokens/**/*.{ts,tsx,js,jsx}"]}}}}, "react-components": {"root": "packages/react-components", "sourceRoot": "packages/react-components/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/react-components"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/react-components"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/react-components"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/react-components/**/*.{ts,tsx,js,jsx}"]}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "configDir": "packages/react-components/.storybook"}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/react-components", "configDir": "packages/react-components/.storybook"}}}}, "flutter-components": {"root": "packages/flutter-components", "sourceRoot": "packages/flutter-components/lib", "projectType": "library", "targets": {"build": {"executor": "@nx/workspace:run-commands", "options": {"command": "flutter packages get && flutter analyze", "cwd": "packages/flutter-components"}}, "test": {"executor": "@nx/workspace:run-commands", "options": {"command": "flutter test --coverage", "cwd": "packages/flutter-components"}}, "lint": {"executor": "@nx/workspace:run-commands", "options": {"command": "dart analyze", "cwd": "packages/flutter-components"}}, "format": {"executor": "@nx/workspace:run-commands", "options": {"command": "dart format .", "cwd": "packages/flutter-components"}}}}, "icons": {"root": "packages/icons", "sourceRoot": "packages/icons/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/icons"}}, "generate": {"executor": "@nx/workspace:run-commands", "options": {"command": "node scripts/generate-icons.js", "cwd": "packages/icons"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/icons"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/icons"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/icons/**/*.{ts,tsx,js,jsx}"]}}}}, "core": {"root": "packages/core", "sourceRoot": "packages/core/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/core"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/core"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/core"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/core/**/*.{ts,tsx,js,jsx}"]}}}}, "theming": {"root": "packages/theming", "sourceRoot": "packages/theming/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/theming"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/theming"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/theming"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/theming/**/*.{ts,tsx,js,jsx}"]}}}}, "accessibility": {"root": "packages/accessibility", "sourceRoot": "packages/accessibility/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/accessibility"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/accessibility"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/accessibility"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/accessibility/**/*.{ts,tsx,js,jsx}"]}}}}, "testing-utilities": {"root": "packages/testing-utilities", "sourceRoot": "packages/testing-utilities/src", "projectType": "library", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/testing-utilities"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/packages/testing-utilities"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/packages/testing-utilities"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/testing-utilities/**/*.{ts,tsx,js,jsx}"]}}}}}}