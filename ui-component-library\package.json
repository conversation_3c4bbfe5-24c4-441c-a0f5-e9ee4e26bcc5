{"name": "@ui-builder/component-library", "version": "1.0.0", "description": "Shared component library for UI Builder platform", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "nx run-many --target=build --all", "test": "nx run-many --target=test --all", "lint": "nx run-many --target=lint --all", "format": "nx format:write", "format:check": "nx format:check", "tokens:build": "nx run design-tokens:build", "react:build": "nx run react-components:build", "react:storybook": "nx run react-components:storybook", "react:test": "nx run react-components:test", "flutter:build": "nx run flutter-components:build", "flutter:test": "nx run flutter-components:test", "icons:build": "nx run icons:build", "docs:build": "nx run documentation:build", "docs:serve": "nx run documentation:serve", "release": "nx run-many --target=release --all", "clean": "nx reset && rm -rf node_modules packages/*/node_modules", "prepare": "husky install"}, "devDependencies": {"@nx/devkit": "^17.2.8", "@nx/eslint-plugin": "^17.2.8", "@nx/jest": "^17.2.8", "@nx/js": "^17.2.8", "@nx/linter": "^17.2.8", "@nx/node": "^17.2.8", "@nx/react": "^17.2.8", "@nx/storybook": "^17.2.8", "@nx/vite": "^17.2.8", "@nx/workspace": "^17.2.8", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "nx": "^17.2.8", "prettier": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.6.6", "style-dictionary": "^3.9.0", "typescript": "^5.2.2", "vite": "^5.0.5", "vitest": "^0.34.6"}, "dependencies": {"clsx": "^2.0.0", "framer-motion": "^10.16.5", "react-aria": "^3.30.0", "react-stately": "^3.28.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,scss}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/ui-builder/component-library.git"}, "keywords": ["design-system", "component-library", "react", "flutter", "ui-builder", "cross-platform"], "author": "UI Builder Team", "license": "MIT"}