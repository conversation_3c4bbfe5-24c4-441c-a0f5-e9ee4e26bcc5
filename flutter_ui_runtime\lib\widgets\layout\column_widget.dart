import 'package:flutter/material.dart';
import '../../core/widgets/dynamic_widget.dart';
import '../../core/widgets/dynamic_widget_renderer.dart';
import '../../core/models/ui_metadata.dart';

/// Dynamic column widget that renders vertical layouts from configuration
class ColumnWidget extends DynamicWidget {
  const ColumnWidget(super.config);

  @override
  Widget build() {
    final mainAxisAlignment = getProp<String>('mainAxisAlignment', 'start');
    final crossAxisAlignment = getProp<String>('crossAxisAlignment', 'center');
    final mainAxisSize = getProp<String>('mainAxisSize', 'max');
    final spacing = getProp<double>('spacing', 0.0);

    final children = _buildChildren();

    return Column(
      mainAxisAlignment: buildMainAxisAlignment(mainAxisAlignment),
      crossAxisAlignment: buildCrossAxisAlignment(crossAxisAlignment),
      mainAxisSize: buildMainAxisSize(mainAxisSize),
      children: spacing > 0 ? _addSpacing(children, spacing) : children,
    );
  }

  List<Widget> _buildChildren() {
    return children.map((childConfig) {
      return DynamicWidgetRenderer(
        config: childConfig,
        parentId: id,
      );
    }).toList();
  }

  List<Widget> _addSpacing(List<Widget> children, double spacing) {
    if (children.isEmpty) return children;

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(height: spacing));
      }
    }
    return spacedChildren;
  }
}
