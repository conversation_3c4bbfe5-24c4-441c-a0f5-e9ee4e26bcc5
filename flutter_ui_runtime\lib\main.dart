import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'core/app.dart';
import 'core/config/app_config.dart';
import 'core/services/storage_service.dart';
import 'core/services/notification_service.dart';
import 'core/utils/logger.dart';
import 'firebase_options.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error handling and monitoring
  await _initializeErrorHandling();

  // Initialize core services
  await _initializeServices();

  // Run the app with error monitoring
  await SentryFlutter.init(
    (options) {
      options.dsn = AppConfig.sentryDsn;
      options.environment = AppConfig.environment;
      options.tracesSampleRate = AppConfig.isDebug ? 1.0 : 0.1;
      options.profilesSampleRate = AppConfig.isDebug ? 1.0 : 0.1;
    },
    appRunner: () => runApp(
      ProviderScope(
        child: const UIRuntimeApp(),
      ),
    ),
  );
}

/// Initialize error handling and crash reporting
Future<void> _initializeErrorHandling() async {
  // Handle Flutter framework errors
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    AppLogger.error(
      'Flutter Error',
      error: details.exception,
      stackTrace: details.stack,
    );
  };

  // Handle platform errors
  PlatformDispatcher.instance.onError = (error, stack) {
    AppLogger.error(
      'Platform Error',
      error: error,
      stackTrace: stack,
    );
    return true;
  };
}

/// Initialize core application services
Future<void> _initializeServices() async {
  try {
    // Initialize Hive for local storage
    await Hive.initFlutter();
    await StorageService.initialize();

    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize notification service
    await NotificationService.initialize();

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    AppLogger.info('Core services initialized successfully');
  } catch (error, stackTrace) {
    AppLogger.error(
      'Failed to initialize services',
      error: error,
      stackTrace: stackTrace,
    );
    rethrow;
  }
}
