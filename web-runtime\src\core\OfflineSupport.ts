import { ComponentMetadata, UIConfiguration } from '../types/runtime';

export interface OfflineAction {
  id: string;
  type: 'create' | 'update' | 'delete';
  resource: string;
  data: any;
  timestamp: number;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: string;
}

export interface OfflineStorage {
  configurations: Map<string, UIConfiguration>;
  components: Map<string, ComponentMetadata>;
  assets: Map<string, Blob>;
  actions: OfflineAction[];
}

export interface SyncStatus {
  isOnline: boolean;
  lastSync: number;
  pendingActions: number;
  syncInProgress: boolean;
}

/**
 * Offline Support System for Web Runtime
 * 
 * Provides comprehensive offline functionality including:
 * - Local data storage with IndexedDB
 * - Offline action queuing and sync
 * - Service worker integration
 * - Network status monitoring
 * - Conflict resolution
 */
export class OfflineSupport {
  private db: IDBDatabase | null = null;
  private storage: OfflineStorage;
  private syncStatus: SyncStatus;
  private listeners: Set<(status: SyncStatus) => void> = new Set();
  private syncTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.storage = {
      configurations: new Map(),
      components: new Map(),
      assets: new Map(),
      actions: [],
    };

    this.syncStatus = {
      isOnline: navigator.onLine,
      lastSync: 0,
      pendingActions: 0,
      syncInProgress: false,
    };

    this.initializeOfflineSupport();
  }

  /**
   * Initialize offline support
   */
  private async initializeOfflineSupport(): Promise<void> {
    try {
      // Initialize IndexedDB
      await this.initializeDatabase();
      
      // Load cached data
      await this.loadCachedData();
      
      // Setup network monitoring
      this.setupNetworkMonitoring();
      
      // Setup service worker
      await this.setupServiceWorker();
      
      // Setup periodic sync
      this.setupPeriodicSync();
      
      console.log('Offline support initialized');
    } catch (error) {
      console.error('Failed to initialize offline support:', error);
    }
  }

  /**
   * Initialize IndexedDB database
   */
  private initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('UIBuilderOffline', 2);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Configurations store
        if (!db.objectStoreNames.contains('configurations')) {
          const configStore = db.createObjectStore('configurations', { keyPath: 'id' });
          configStore.createIndex('name', 'name', { unique: false });
          configStore.createIndex('updatedAt', 'updatedAt', { unique: false });
        }
        
        // Components store
        if (!db.objectStoreNames.contains('components')) {
          const componentStore = db.createObjectStore('components', { keyPath: 'id' });
          componentStore.createIndex('type', 'type', { unique: false });
          componentStore.createIndex('configurationId', 'configurationId', { unique: false });
        }
        
        // Assets store
        if (!db.objectStoreNames.contains('assets')) {
          const assetStore = db.createObjectStore('assets', { keyPath: 'id' });
          assetStore.createIndex('type', 'type', { unique: false });
          assetStore.createIndex('size', 'size', { unique: false });
        }
        
        // Offline actions store
        if (!db.objectStoreNames.contains('offlineActions')) {
          const actionStore = db.createObjectStore('offlineActions', { keyPath: 'id' });
          actionStore.createIndex('timestamp', 'timestamp', { unique: false });
          actionStore.createIndex('type', 'type', { unique: false });
        }
      };
    });
  }

  /**
   * Load cached data from IndexedDB
   */
  private async loadCachedData(): Promise<void> {
    if (!this.db) return;

    try {
      // Load configurations
      const configurations = await this.getAllFromStore('configurations');
      configurations.forEach(config => {
        this.storage.configurations.set(config.id, config);
      });

      // Load components
      const components = await this.getAllFromStore('components');
      components.forEach(component => {
        this.storage.components.set(component.id, component);
      });

      // Load offline actions
      const actions = await this.getAllFromStore('offlineActions');
      this.storage.actions = actions;
      this.updateSyncStatus({ pendingActions: actions.length });

      console.log('Cached data loaded:', {
        configurations: configurations.length,
        components: components.length,
        actions: actions.length,
      });
    } catch (error) {
      console.error('Failed to load cached data:', error);
    }
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    const updateOnlineStatus = () => {
      const wasOnline = this.syncStatus.isOnline;
      this.updateSyncStatus({ isOnline: navigator.onLine });
      
      // Trigger sync when coming back online
      if (!wasOnline && navigator.onLine) {
        this.syncOfflineActions();
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
  }

  /**
   * Setup service worker
   */
  private async setupServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', registration);
        
        // Listen for service worker messages
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data.type === 'SYNC_COMPLETE') {
            this.handleSyncComplete();
          }
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Setup periodic sync
   */
  private setupPeriodicSync(): void {
    // Sync every 5 minutes when online
    this.syncTimer = setInterval(() => {
      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {
        this.syncOfflineActions();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Store configuration offline
   */
  async storeConfiguration(configuration: UIConfiguration): Promise<void> {
    this.storage.configurations.set(configuration.id, configuration);
    
    if (this.db) {
      await this.putInStore('configurations', configuration);
    }
  }

  /**
   * Get configuration (offline-first)
   */
  async getConfiguration(id: string): Promise<UIConfiguration | null> {
    // Try local storage first
    const cached = this.storage.configurations.get(id);
    if (cached) {
      return cached;
    }

    // Try network if online
    if (this.syncStatus.isOnline) {
      try {
        const response = await fetch(`/api/configurations/${id}`);
        if (response.ok) {
          const configuration = await response.json();
          await this.storeConfiguration(configuration);
          return configuration;
        }
      } catch (error) {
        console.error('Failed to fetch configuration:', error);
      }
    }

    return null;
  }

  /**
   * Store component offline
   */
  async storeComponent(component: ComponentMetadata): Promise<void> {
    this.storage.components.set(component.id, component);
    
    if (this.db) {
      await this.putInStore('components', component);
    }
  }

  /**
   * Get component (offline-first)
   */
  async getComponent(id: string): Promise<ComponentMetadata | null> {
    const cached = this.storage.components.get(id);
    if (cached) {
      return cached;
    }

    if (this.syncStatus.isOnline) {
      try {
        const response = await fetch(`/api/components/${id}`);
        if (response.ok) {
          const component = await response.json();
          await this.storeComponent(component);
          return component;
        }
      } catch (error) {
        console.error('Failed to fetch component:', error);
      }
    }

    return null;
  }

  /**
   * Queue offline action
   */
  async queueOfflineAction(action: Omit<OfflineAction, 'id' | 'timestamp'>): Promise<void> {
    const offlineAction: OfflineAction = {
      ...action,
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
    };

    this.storage.actions.push(offlineAction);
    
    if (this.db) {
      await this.putInStore('offlineActions', offlineAction);
    }

    this.updateSyncStatus({ pendingActions: this.storage.actions.length });

    // Try to sync immediately if online
    if (this.syncStatus.isOnline) {
      this.syncOfflineActions();
    }
  }

  /**
   * Sync offline actions
   */
  async syncOfflineActions(): Promise<void> {
    if (this.syncStatus.syncInProgress || !this.syncStatus.isOnline) {
      return;
    }

    this.updateSyncStatus({ syncInProgress: true });

    try {
      const actionsToSync = [...this.storage.actions];
      const syncedActions: string[] = [];

      for (const action of actionsToSync) {
        try {
          await this.syncAction(action);
          syncedActions.push(action.id);
        } catch (error) {
          console.error('Failed to sync action:', action.id, error);
          // Continue with other actions
        }
      }

      // Remove synced actions
      this.storage.actions = this.storage.actions.filter(
        action => !syncedActions.includes(action.id)
      );

      // Update database
      if (this.db) {
        for (const actionId of syncedActions) {
          await this.deleteFromStore('offlineActions', actionId);
        }
      }

      this.updateSyncStatus({
        lastSync: Date.now(),
        pendingActions: this.storage.actions.length,
      });

      console.log(`Synced ${syncedActions.length} offline actions`);
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.updateSyncStatus({ syncInProgress: false });
    }
  }

  /**
   * Sync individual action
   */
  private async syncAction(action: OfflineAction): Promise<void> {
    const response = await fetch(action.url, {
      method: action.method,
      headers: action.headers,
      body: action.body,
    });

    if (!response.ok) {
      throw new Error(`Sync failed: ${response.status} ${response.statusText}`);
    }

    // Handle response based on action type
    if (action.type === 'create' || action.type === 'update') {
      const result = await response.json();
      
      if (action.resource === 'configurations') {
        await this.storeConfiguration(result);
      } else if (action.resource === 'components') {
        await this.storeComponent(result);
      }
    }
  }

  /**
   * Handle sync completion
   */
  private handleSyncComplete(): void {
    this.updateSyncStatus({
      lastSync: Date.now(),
      syncInProgress: false,
    });
  }

  /**
   * Subscribe to sync status changes
   */
  subscribe(listener: (status: SyncStatus) => void): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Get current sync status
   */
  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  /**
   * Clear offline data
   */
  async clearOfflineData(): Promise<void> {
    this.storage.configurations.clear();
    this.storage.components.clear();
    this.storage.assets.clear();
    this.storage.actions = [];

    if (this.db) {
      await this.clearStore('configurations');
      await this.clearStore('components');
      await this.clearStore('assets');
      await this.clearStore('offlineActions');
    }

    this.updateSyncStatus({ pendingActions: 0 });
  }

  // Private helper methods

  private updateSyncStatus(updates: Partial<SyncStatus>): void {
    this.syncStatus = { ...this.syncStatus, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.syncStatus);
      } catch (error) {
        console.error('Sync status listener error:', error);
      }
    });
  }

  private async getAllFromStore(storeName: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve([]);
        return;
      }

      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  private async putInStore(storeName: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve();
        return;
      }

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  private async deleteFromStore(storeName: string, key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve();
        return;
      }

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  private async clearStore(storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve();
        return;
      }

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }

    if (this.db) {
      this.db.close();
      this.db = null;
    }

    this.listeners.clear();
  }
}

// Global offline support instance
export const offlineSupport = new OfflineSupport();
