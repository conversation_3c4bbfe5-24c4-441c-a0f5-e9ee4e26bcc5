package com.uiplatform.collaboration;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Operational Transformation (OT) implementation for conflict resolution in collaborative editing.
 * This system ensures that concurrent operations on the same document converge to the same state.
 */
@Component
public class OperationalTransformation {

    private static final Logger logger = LoggerFactory.getLogger(OperationalTransformation.class);

    /**
     * Transform an operation against another operation to resolve conflicts.
     * 
     * @param operation The operation to transform
     * @param againstOperation The operation to transform against
     * @param priority Priority for conflict resolution (higher wins)
     * @return The transformed operation
     */
    public Operation transform(Operation operation, Operation againstOperation, int priority) {
        logger.debug("Transforming operation {} against {}", operation.getType(), againstOperation.getType());
        
        if (operation == null || againstOperation == null) {
            return operation;
        }
        
        // If operations are on different elements, no transformation needed
        if (!Objects.equals(operation.getElementId(), againstOperation.getElementId())) {
            return operation;
        }
        
        // Transform based on operation types
        return transformByType(operation, againstOperation, priority);
    }

    /**
     * Transform a list of operations against a base operation.
     */
    public List<Operation> transformOperations(List<Operation> operations, Operation baseOperation) {
        List<Operation> transformedOps = new ArrayList<>();
        
        for (Operation op : operations) {
            Operation transformed = transform(op, baseOperation, op.getPriority());
            if (transformed != null) {
                transformedOps.add(transformed);
            }
        }
        
        return transformedOps;
    }

    /**
     * Compose multiple operations into a single operation when possible.
     */
    public Operation compose(Operation op1, Operation op2) {
        if (op1 == null) return op2;
        if (op2 == null) return op1;
        
        // Can only compose operations on the same element
        if (!Objects.equals(op1.getElementId(), op2.getElementId())) {
            return null;
        }
        
        // Compose based on operation types
        return composeByType(op1, op2);
    }

    /**
     * Check if two operations conflict with each other.
     */
    public boolean hasConflict(Operation op1, Operation op2) {
        if (op1 == null || op2 == null) {
            return false;
        }
        
        // Operations on different elements don't conflict
        if (!Objects.equals(op1.getElementId(), op2.getElementId())) {
            return false;
        }
        
        // Check for specific conflict patterns
        return checkConflictByType(op1, op2);
    }

    // Private helper methods

    private Operation transformByType(Operation operation, Operation againstOperation, int priority) {
        OperationType opType = operation.getType();
        OperationType againstType = againstOperation.getType();
        
        switch (opType) {
            case INSERT:
                return transformInsert((InsertOperation) operation, againstOperation, priority);
            case DELETE:
                return transformDelete((DeleteOperation) operation, againstOperation, priority);
            case UPDATE:
                return transformUpdate((UpdateOperation) operation, againstOperation, priority);
            case MOVE:
                return transformMove((MoveOperation) operation, againstOperation, priority);
            case STYLE:
                return transformStyle((StyleOperation) operation, againstOperation, priority);
            default:
                return operation;
        }
    }

    private Operation transformInsert(InsertOperation insert, Operation against, int priority) {
        switch (against.getType()) {
            case INSERT:
                InsertOperation againstInsert = (InsertOperation) against;
                // If inserting at same position, use priority to determine order
                if (insert.getPosition() == againstInsert.getPosition()) {
                    if (priority <= against.getPriority()) {
                        // Shift position to after the other insert
                        insert.setPosition(insert.getPosition() + 1);
                    }
                } else if (insert.getPosition() > againstInsert.getPosition()) {
                    // Shift position to account for the other insert
                    insert.setPosition(insert.getPosition() + 1);
                }
                break;
                
            case DELETE:
                DeleteOperation againstDelete = (DeleteOperation) against;
                if (insert.getPosition() > againstDelete.getPosition()) {
                    // Shift position to account for deletion
                    insert.setPosition(insert.getPosition() - 1);
                }
                break;
                
            case MOVE:
                MoveOperation againstMove = (MoveOperation) against;
                // Adjust position based on move operation
                insert.setPosition(adjustPositionForMove(insert.getPosition(), againstMove));
                break;
        }
        
        return insert;
    }

    private Operation transformDelete(DeleteOperation delete, Operation against, int priority) {
        switch (against.getType()) {
            case INSERT:
                InsertOperation againstInsert = (InsertOperation) against;
                if (delete.getPosition() >= againstInsert.getPosition()) {
                    // Shift position to account for insertion
                    delete.setPosition(delete.getPosition() + 1);
                }
                break;
                
            case DELETE:
                DeleteOperation againstDelete = (DeleteOperation) against;
                if (delete.getPosition() == againstDelete.getPosition()) {
                    // Same element being deleted - operation becomes no-op
                    return null;
                } else if (delete.getPosition() > againstDelete.getPosition()) {
                    // Shift position to account for other deletion
                    delete.setPosition(delete.getPosition() - 1);
                }
                break;
                
            case MOVE:
                MoveOperation againstMove = (MoveOperation) against;
                // Adjust position based on move operation
                delete.setPosition(adjustPositionForMove(delete.getPosition(), againstMove));
                break;
        }
        
        return delete;
    }

    private Operation transformUpdate(UpdateOperation update, Operation against, int priority) {
        switch (against.getType()) {
            case DELETE:
                // If element is deleted, update becomes no-op
                return null;
                
            case UPDATE:
                UpdateOperation againstUpdate = (UpdateOperation) against;
                // Merge updates or use priority to resolve conflicts
                if (priority <= against.getPriority()) {
                    // Other operation has higher priority, merge properties
                    Map<String, Object> mergedProperties = new HashMap<>(againstUpdate.getProperties());
                    mergedProperties.putAll(update.getProperties());
                    update.setProperties(mergedProperties);
                }
                break;
                
            case STYLE:
                StyleOperation againstStyle = (StyleOperation) against;
                // Style operations don't conflict with content updates
                break;
        }
        
        return update;
    }

    private Operation transformMove(MoveOperation move, Operation against, int priority) {
        switch (against.getType()) {
            case DELETE:
                DeleteOperation againstDelete = (DeleteOperation) against;
                if (move.getFromPosition() == againstDelete.getPosition()) {
                    // Element being moved was deleted - operation becomes no-op
                    return null;
                }
                // Adjust positions for deletion
                if (move.getFromPosition() > againstDelete.getPosition()) {
                    move.setFromPosition(move.getFromPosition() - 1);
                }
                if (move.getToPosition() > againstDelete.getPosition()) {
                    move.setToPosition(move.getToPosition() - 1);
                }
                break;
                
            case INSERT:
                InsertOperation againstInsert = (InsertOperation) against;
                // Adjust positions for insertion
                if (move.getFromPosition() >= againstInsert.getPosition()) {
                    move.setFromPosition(move.getFromPosition() + 1);
                }
                if (move.getToPosition() >= againstInsert.getPosition()) {
                    move.setToPosition(move.getToPosition() + 1);
                }
                break;
                
            case MOVE:
                MoveOperation againstMove = (MoveOperation) against;
                // Complex move-move transformation
                transformMoveMoveConflict(move, againstMove, priority);
                break;
        }
        
        return move;
    }

    private Operation transformStyle(StyleOperation style, Operation against, int priority) {
        switch (against.getType()) {
            case DELETE:
                // If element is deleted, style operation becomes no-op
                return null;
                
            case STYLE:
                StyleOperation againstStyle = (StyleOperation) against;
                // Merge style properties or use priority
                if (priority <= against.getPriority()) {
                    Map<String, Object> mergedStyles = new HashMap<>(againstStyle.getStyles());
                    mergedStyles.putAll(style.getStyles());
                    style.setStyles(mergedStyles);
                }
                break;
        }
        
        return style;
    }

    private Operation composeByType(Operation op1, Operation op2) {
        if (op1.getType() == op2.getType()) {
            switch (op1.getType()) {
                case UPDATE:
                    return composeUpdates((UpdateOperation) op1, (UpdateOperation) op2);
                case STYLE:
                    return composeStyles((StyleOperation) op1, (StyleOperation) op2);
            }
        }
        
        // Can't compose different operation types
        return null;
    }

    private UpdateOperation composeUpdates(UpdateOperation op1, UpdateOperation op2) {
        Map<String, Object> composedProperties = new HashMap<>(op1.getProperties());
        composedProperties.putAll(op2.getProperties());
        
        UpdateOperation composed = new UpdateOperation();
        composed.setElementId(op1.getElementId());
        composed.setProperties(composedProperties);
        composed.setTimestamp(op2.getTimestamp());
        composed.setPriority(Math.max(op1.getPriority(), op2.getPriority()));
        
        return composed;
    }

    private StyleOperation composeStyles(StyleOperation op1, StyleOperation op2) {
        Map<String, Object> composedStyles = new HashMap<>(op1.getStyles());
        composedStyles.putAll(op2.getStyles());
        
        StyleOperation composed = new StyleOperation();
        composed.setElementId(op1.getElementId());
        composed.setStyles(composedStyles);
        composed.setTimestamp(op2.getTimestamp());
        composed.setPriority(Math.max(op1.getPriority(), op2.getPriority()));
        
        return composed;
    }

    private boolean checkConflictByType(Operation op1, Operation op2) {
        // Same position operations often conflict
        if (op1 instanceof PositionalOperation && op2 instanceof PositionalOperation) {
            PositionalOperation pos1 = (PositionalOperation) op1;
            PositionalOperation pos2 = (PositionalOperation) op2;
            return pos1.getPosition() == pos2.getPosition();
        }
        
        // Update operations on same properties conflict
        if (op1 instanceof UpdateOperation && op2 instanceof UpdateOperation) {
            UpdateOperation update1 = (UpdateOperation) op1;
            UpdateOperation update2 = (UpdateOperation) op2;
            return hasOverlappingProperties(update1.getProperties(), update2.getProperties());
        }
        
        return false;
    }

    private int adjustPositionForMove(int position, MoveOperation move) {
        int from = move.getFromPosition();
        int to = move.getToPosition();
        
        if (position == from) {
            return to;
        } else if (from < to) {
            if (position > from && position <= to) {
                return position - 1;
            }
        } else {
            if (position >= to && position < from) {
                return position + 1;
            }
        }
        
        return position;
    }

    private void transformMoveMoveConflict(MoveOperation move1, MoveOperation move2, int priority) {
        // Complex logic for handling move-move conflicts
        // This is a simplified version - real implementation would be more sophisticated
        if (move1.getFromPosition() == move2.getFromPosition()) {
            // Same element being moved - use priority
            if (priority <= move2.getPriority()) {
                // Adjust to move after the other operation
                move1.setToPosition(move2.getToPosition() + 1);
            }
        }
    }

    private boolean hasOverlappingProperties(Map<String, Object> props1, Map<String, Object> props2) {
        return props1.keySet().stream().anyMatch(props2::containsKey);
    }

    // Operation interfaces and classes

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
    @JsonSubTypes({
        @JsonSubTypes.Type(value = InsertOperation.class, name = "INSERT"),
        @JsonSubTypes.Type(value = DeleteOperation.class, name = "DELETE"),
        @JsonSubTypes.Type(value = UpdateOperation.class, name = "UPDATE"),
        @JsonSubTypes.Type(value = MoveOperation.class, name = "MOVE"),
        @JsonSubTypes.Type(value = StyleOperation.class, name = "STYLE")
    })
    public abstract static class Operation {
        protected UUID id;
        protected UUID userId;
        protected String elementId;
        protected OperationType type;
        protected LocalDateTime timestamp;
        protected int priority;
        
        // Getters and setters
        public UUID getId() { return id; }
        public void setId(UUID id) { this.id = id; }
        public UUID getUserId() { return userId; }
        public void setUserId(UUID userId) { this.userId = userId; }
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        public OperationType getType() { return type; }
        public void setType(OperationType type) { this.type = type; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public int getPriority() { return priority; }
        public void setPriority(int priority) { this.priority = priority; }
    }

    public interface PositionalOperation {
        int getPosition();
        void setPosition(int position);
    }

    public enum OperationType {
        INSERT, DELETE, UPDATE, MOVE, STYLE
    }

    public static class InsertOperation extends Operation implements PositionalOperation {
        private int position;
        private Object content;
        
        public InsertOperation() { this.type = OperationType.INSERT; }
        
        public int getPosition() { return position; }
        public void setPosition(int position) { this.position = position; }
        public Object getContent() { return content; }
        public void setContent(Object content) { this.content = content; }
    }

    public static class DeleteOperation extends Operation implements PositionalOperation {
        private int position;
        
        public DeleteOperation() { this.type = OperationType.DELETE; }
        
        public int getPosition() { return position; }
        public void setPosition(int position) { this.position = position; }
    }

    public static class UpdateOperation extends Operation {
        private Map<String, Object> properties;
        
        public UpdateOperation() { this.type = OperationType.UPDATE; }
        
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    }

    public static class MoveOperation extends Operation {
        private int fromPosition;
        private int toPosition;
        
        public MoveOperation() { this.type = OperationType.MOVE; }
        
        public int getFromPosition() { return fromPosition; }
        public void setFromPosition(int fromPosition) { this.fromPosition = fromPosition; }
        public int getToPosition() { return toPosition; }
        public void setToPosition(int toPosition) { this.toPosition = toPosition; }
    }

    public static class StyleOperation extends Operation {
        private Map<String, Object> styles;
        
        public StyleOperation() { this.type = OperationType.STYLE; }
        
        public Map<String, Object> getStyles() { return styles; }
        public void setStyles(Map<String, Object> styles) { this.styles = styles; }
    }
}
