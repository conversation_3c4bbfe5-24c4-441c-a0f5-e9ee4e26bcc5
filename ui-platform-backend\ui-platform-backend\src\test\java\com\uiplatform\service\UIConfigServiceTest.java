package com.uiplatform.service;

import com.uiplatform.dto.UIConfigDTO;
import com.uiplatform.dto.UIConfigCreateDTO;
import com.uiplatform.dto.UIConfigUpdateDTO;
import com.uiplatform.entity.UIConfig;
import com.uiplatform.entity.User;
import com.uiplatform.entity.Workspace;
import com.uiplatform.exception.ResourceNotFoundException;
import com.uiplatform.mapper.UIConfigMapper;
import com.uiplatform.repository.UIConfigRepository;
import com.uiplatform.repository.UserRepository;
import com.uiplatform.repository.WorkspaceRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UIConfigServiceTest {

    @Mock
    private UIConfigRepository uiConfigRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private WorkspaceRepository workspaceRepository;

    @Mock
    private UIConfigMapper uiConfigMapper;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private UIConfigService uiConfigService;

    private UIConfig sampleUIConfig;
    private UIConfigDTO sampleUIConfigDTO;
    private User sampleUser;
    private Workspace sampleWorkspace;

    @BeforeEach
    void setUp() {
        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");

        sampleUser = User.builder()
                .id(UUID.randomUUID())
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .isActive(true)
                .build();

        sampleWorkspace = Workspace.builder()
                .id(UUID.randomUUID())
                .name("Test Workspace")
                .description("A test workspace")
                .isPublic(false)
                .createdBy(sampleUser)
                .build();

        sampleUIConfig = UIConfig.builder()
                .id(UUID.randomUUID())
                .name("Sample Dashboard")
                .description("A sample dashboard configuration")
                .configData("{\"components\": []}")
                .version(1)
                .status("DRAFT")
                .isTemplate(false)
                .workspace(sampleWorkspace)
                .createdBy(sampleUser)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        sampleUIConfigDTO = UIConfigDTO.builder()
                .id(sampleUIConfig.getId())
                .name(sampleUIConfig.getName())
                .description(sampleUIConfig.getDescription())
                .configData(sampleUIConfig.getConfigData())
                .version(sampleUIConfig.getVersion())
                .status(sampleUIConfig.getStatus())
                .isTemplate(sampleUIConfig.getIsTemplate())
                .createdAt(sampleUIConfig.getCreatedAt())
                .updatedAt(sampleUIConfig.getUpdatedAt())
                .build();
    }

    @Test
    void getAllUIConfigs_ShouldReturnPagedResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        List<UIConfig> configs = Arrays.asList(sampleUIConfig);
        Page<UIConfig> page = new PageImpl<>(configs, pageable, 1);
        
        when(uiConfigRepository.findAll(any(Pageable.class))).thenReturn(page);
        when(uiConfigMapper.toDTO(sampleUIConfig)).thenReturn(sampleUIConfigDTO);

        // When
        Page<UIConfigDTO> result = uiConfigService.getAllUIConfigs(pageable, null, null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getName()).isEqualTo("Sample Dashboard");
        assertThat(result.getTotalElements()).isEqualTo(1);

        verify(uiConfigRepository).findAll(pageable);
        verify(uiConfigMapper).toDTO(sampleUIConfig);
    }

    @Test
    void getAllUIConfigs_WithSearch_ShouldReturnFilteredResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        String search = "dashboard";
        List<UIConfig> configs = Arrays.asList(sampleUIConfig);
        Page<UIConfig> page = new PageImpl<>(configs, pageable, 1);
        
        when(uiConfigRepository.findByNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
                eq(search), eq(search), any(Pageable.class))).thenReturn(page);
        when(uiConfigMapper.toDTO(sampleUIConfig)).thenReturn(sampleUIConfigDTO);

        // When
        Page<UIConfigDTO> result = uiConfigService.getAllUIConfigs(pageable, null, search);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getName()).isEqualTo("Sample Dashboard");

        verify(uiConfigRepository).findByNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
                search, search, pageable);
    }

    @Test
    void getUIConfigById_ShouldReturnConfig() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));
        when(uiConfigMapper.toDTO(sampleUIConfig)).thenReturn(sampleUIConfigDTO);

        // When
        UIConfigDTO result = uiConfigService.getUIConfigById(configId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(sampleUIConfig.getId());
        assertThat(result.getName()).isEqualTo("Sample Dashboard");

        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigMapper).toDTO(sampleUIConfig);
    }

    @Test
    void getUIConfigById_WithNonExistentId_ShouldThrowException() {
        // Given
        String configId = UUID.randomUUID().toString();
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> uiConfigService.getUIConfigById(configId))
                .isInstanceOf(ResourceNotFoundException.class)
                .hasMessageContaining("UI Configuration not found");

        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigMapper, never()).toDTO(any());
    }

    @Test
    void createUIConfig_ShouldReturnCreatedConfig() {
        // Given
        UIConfigCreateDTO createDTO = UIConfigCreateDTO.builder()
                .name("New Dashboard")
                .description("A new dashboard")
                .configData("{\"components\": []}")
                .workspaceId(sampleWorkspace.getId())
                .build();

        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(sampleUser));
        when(workspaceRepository.findById(sampleWorkspace.getId())).thenReturn(Optional.of(sampleWorkspace));
        when(uiConfigMapper.toEntity(createDTO)).thenReturn(sampleUIConfig);
        when(uiConfigRepository.save(any(UIConfig.class))).thenReturn(sampleUIConfig);
        when(uiConfigMapper.toDTO(sampleUIConfig)).thenReturn(sampleUIConfigDTO);

        // When
        UIConfigDTO result = uiConfigService.createUIConfig(createDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Sample Dashboard");
        assertThat(result.getDescription()).isEqualTo("A sample dashboard configuration");

        verify(userRepository).findByUsername("testuser");
        verify(workspaceRepository).findById(sampleWorkspace.getId());
        verify(uiConfigRepository).save(any(UIConfig.class));
    }

    @Test
    void createUIConfig_WithNonExistentWorkspace_ShouldThrowException() {
        // Given
        UIConfigCreateDTO createDTO = UIConfigCreateDTO.builder()
                .name("New Dashboard")
                .workspaceId(UUID.randomUUID())
                .build();

        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(sampleUser));
        when(workspaceRepository.findById(any(UUID.class))).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> uiConfigService.createUIConfig(createDTO))
                .isInstanceOf(ResourceNotFoundException.class)
                .hasMessageContaining("Workspace not found");

        verify(uiConfigRepository, never()).save(any());
    }

    @Test
    void updateUIConfig_ShouldReturnUpdatedConfig() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        UIConfigUpdateDTO updateDTO = UIConfigUpdateDTO.builder()
                .name("Updated Dashboard")
                .description("An updated dashboard")
                .configData("{\"components\": [{\"type\": \"text\"}]}")
                .build();

        UIConfig updatedConfig = sampleUIConfig.toBuilder()
                .name("Updated Dashboard")
                .description("An updated dashboard")
                .configData("{\"components\": [{\"type\": \"text\"}]}")
                .version(2)
                .build();

        UIConfigDTO updatedConfigDTO = sampleUIConfigDTO.toBuilder()
                .name("Updated Dashboard")
                .description("An updated dashboard")
                .configData("{\"components\": [{\"type\": \"text\"}]}")
                .version(2)
                .build();

        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(sampleUser));
        when(uiConfigRepository.save(any(UIConfig.class))).thenReturn(updatedConfig);
        when(uiConfigMapper.toDTO(updatedConfig)).thenReturn(updatedConfigDTO);

        // When
        UIConfigDTO result = uiConfigService.updateUIConfig(configId, updateDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Updated Dashboard");
        assertThat(result.getDescription()).isEqualTo("An updated dashboard");
        assertThat(result.getVersion()).isEqualTo(2);

        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigRepository).save(any(UIConfig.class));
    }

    @Test
    void deleteUIConfig_ShouldDeleteConfig() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));

        // When
        uiConfigService.deleteUIConfig(configId);

        // Then
        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigRepository).delete(sampleUIConfig);
    }

    @Test
    void deleteUIConfig_WithNonExistentId_ShouldThrowException() {
        // Given
        String configId = UUID.randomUUID().toString();
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> uiConfigService.deleteUIConfig(configId))
                .isInstanceOf(ResourceNotFoundException.class)
                .hasMessageContaining("UI Configuration not found");

        verify(uiConfigRepository, never()).delete(any());
    }

    @Test
    void duplicateUIConfig_ShouldReturnDuplicatedConfig() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        String newName = "Sample Dashboard (Copy)";
        
        UIConfig duplicatedConfig = sampleUIConfig.toBuilder()
                .id(UUID.randomUUID())
                .name(newName)
                .version(1)
                .build();

        UIConfigDTO duplicatedConfigDTO = sampleUIConfigDTO.toBuilder()
                .id(duplicatedConfig.getId())
                .name(newName)
                .version(1)
                .build();

        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(sampleUser));
        when(uiConfigRepository.save(any(UIConfig.class))).thenReturn(duplicatedConfig);
        when(uiConfigMapper.toDTO(duplicatedConfig)).thenReturn(duplicatedConfigDTO);

        // When
        UIConfigDTO result = uiConfigService.duplicateUIConfig(configId, newName);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo(newName);
        assertThat(result.getId()).isNotEqualTo(sampleUIConfig.getId());
        assertThat(result.getVersion()).isEqualTo(1);

        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigRepository).save(any(UIConfig.class));
    }

    @Test
    void publishUIConfig_ShouldReturnPublishedConfig() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        
        UIConfig publishedConfig = sampleUIConfig.toBuilder()
                .status("PUBLISHED")
                .publishedAt(LocalDateTime.now())
                .build();

        UIConfigDTO publishedConfigDTO = sampleUIConfigDTO.toBuilder()
                .status("PUBLISHED")
                .publishedAt(publishedConfig.getPublishedAt())
                .build();

        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));
        when(uiConfigRepository.save(any(UIConfig.class))).thenReturn(publishedConfig);
        when(uiConfigMapper.toDTO(publishedConfig)).thenReturn(publishedConfigDTO);

        // When
        UIConfigDTO result = uiConfigService.publishUIConfig(configId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("PUBLISHED");
        assertThat(result.getPublishedAt()).isNotNull();

        verify(uiConfigRepository).findById(UUID.fromString(configId));
        verify(uiConfigRepository).save(any(UIConfig.class));
    }

    @Test
    void isOwner_WithOwner_ShouldReturnTrue() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        String username = "testuser";
        
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));

        // When
        boolean result = uiConfigService.isOwner(configId, username);

        // Then
        assertThat(result).isTrue();
        verify(uiConfigRepository).findById(UUID.fromString(configId));
    }

    @Test
    void isOwner_WithNonOwner_ShouldReturnFalse() {
        // Given
        String configId = sampleUIConfig.getId().toString();
        String username = "otheruser";
        
        when(uiConfigRepository.findById(UUID.fromString(configId))).thenReturn(Optional.of(sampleUIConfig));

        // When
        boolean result = uiConfigService.isOwner(configId, username);

        // Then
        assertThat(result).isFalse();
        verify(uiConfigRepository).findById(UUID.fromString(configId));
    }
}
