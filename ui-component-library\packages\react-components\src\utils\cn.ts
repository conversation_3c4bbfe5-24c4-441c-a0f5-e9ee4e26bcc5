import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes with clsx
 * Handles conditional classes and removes conflicts
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Create a className string from design tokens
 */
export function createClassName(base: string, variants?: Record<string, any>) {
  if (!variants) return base;
  
  const variantClasses = Object.entries(variants)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => {
      if (typeof value === 'boolean') {
        return value ? key : '';
      }
      return `${key}-${value}`;
    })
    .filter(Boolean);
    
  return cn(base, ...variantClasses);
}

/**
 * Generate responsive classes
 */
export function responsive(
  classes: Record<string, string> | string,
  breakpoints: string[] = ['sm', 'md', 'lg', 'xl', '2xl']
) {
  if (typeof classes === 'string') {
    return classes;
  }
  
  return Object.entries(classes)
    .map(([breakpoint, className]) => {
      if (breakpoint === 'base') return className;
      if (breakpoints.includes(breakpoint)) {
        return `${breakpoint}:${className}`;
      }
      return className;
    })
    .join(' ');
}

/**
 * Generate state-based classes (hover, focus, active, etc.)
 */
export function withStates(
  baseClass: string,
  states: Partial<{
    hover: string;
    focus: string;
    active: string;
    disabled: string;
    visited: string;
    'focus-visible': string;
    'focus-within': string;
  }>
) {
  const stateClasses = Object.entries(states)
    .map(([state, className]) => `${state}:${className}`)
    .join(' ');
    
  return cn(baseClass, stateClasses);
}

/**
 * Generate dark mode classes
 */
export function withDarkMode(lightClass: string, darkClass?: string) {
  if (!darkClass) return lightClass;
  return cn(lightClass, `dark:${darkClass}`);
}

/**
 * Create component variant classes
 */
export function createVariants<T extends Record<string, Record<string, string>>>(
  variants: T
): T {
  return variants;
}

/**
 * Apply component size classes
 */
export function withSize(
  baseClass: string,
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'md',
  sizeMap: Record<string, string>
) {
  return cn(baseClass, sizeMap[size]);
}

/**
 * Apply component color classes
 */
export function withColor(
  baseClass: string,
  color: string,
  colorMap: Record<string, string>
) {
  return cn(baseClass, colorMap[color]);
}

/**
 * Create animation classes
 */
export function withAnimation(
  baseClass: string,
  animation?: {
    enter?: string;
    exit?: string;
    duration?: string;
    delay?: string;
    easing?: string;
  }
) {
  if (!animation) return baseClass;
  
  const animationClasses = [
    animation.enter,
    animation.exit,
    animation.duration,
    animation.delay,
    animation.easing,
  ].filter(Boolean);
  
  return cn(baseClass, ...animationClasses);
}
