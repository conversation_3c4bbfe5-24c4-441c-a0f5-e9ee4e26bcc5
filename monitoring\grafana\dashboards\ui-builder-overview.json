{"dashboard": {"id": null, "title": "UI Builder - System Overview", "tags": ["ui-builder", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health", "type": "stat", "targets": [{"expr": "up{job=~\"ui-.*-service\"} == 1", "legendFormat": "{{job}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (service)", "legendFormat": "{{service}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}}, {"id": 3, "title": "Error Rate", "type": "graph", "targets": [{"expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)", "legendFormat": "{{service}}"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 4, "title": "Response Time (95th percentile)", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))", "legendFormat": "{{service}}"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "Active Users", "type": "stat", "targets": [{"expr": "ui_builder_active_users", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}}, {"id": 6, "title": "Database Connections", "type": "graph", "targets": [{"expr": "hikaricp_connections_active", "legendFormat": "Active"}, {"expr": "hikaricp_connections_idle", "legendFormat": "Idle"}, {"expr": "hikaricp_connections_max", "legendFormat": "Max"}], "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}}, {"id": 7, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "jvm_memory_used_bytes{area=\"heap\"} / jvm_memory_max_bytes{area=\"heap\"}", "legendFormat": "{{service}} Heap"}], "yAxes": [{"label": "Usage %", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}}"}], "yAxes": [{"label": "CPU %", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "UI Configs Created", "type": "stat", "targets": [{"expr": "sum(increase(ui_builder_configs_created_total[1h]))", "legendFormat": "Last Hour"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 10, "title": "Templates Used", "type": "stat", "targets": [{"expr": "sum(increase(ui_builder_template_usage_total[1h]))", "legendFormat": "Last Hour"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}}, {"id": 11, "title": "Collaboration Sessions", "type": "stat", "targets": [{"expr": "ui_builder_active_collaboration_sessions", "legendFormat": "Active Sessions"}], "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}}, {"id": 12, "title": "<PERSON><PERSON> Hit Rate", "type": "stat", "targets": [{"expr": "sum(rate(cache_gets_total{result=\"hit\"}[5m])) / sum(rate(cache_gets_total[5m]))", "legendFormat": "Hit Rate"}], "fieldConfig": {"defaults": {"unit": "percentunit", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.8}, {"color": "green", "value": 0.9}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}}], "templating": {"list": [{"name": "environment", "type": "query", "query": "label_values(up, environment)", "current": {"value": "production", "text": "production"}}, {"name": "service", "type": "query", "query": "label_values(up{environment=\"$environment\"}, job)", "current": {"value": "All", "text": "All"}, "includeAll": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up[5m]) > 0", "titleFormat": "Deployment", "textFormat": "{{job}} restarted"}]}}}