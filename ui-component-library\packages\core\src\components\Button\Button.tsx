import React, { forwardRef, useMemo } from 'react';
import { ComponentConfig, ComponentProps } from '../../types/component';
import { useTheme } from '../../theme/ThemeProvider';
import './Button.scss';

export interface ButtonProps extends ComponentProps {
  // Content
  children?: React.ReactNode;
  text?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  
  // Appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'default' | 'round' | 'circle';
  
  // State
  loading?: boolean;
  disabled?: boolean;
  active?: boolean;
  
  // Behavior
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  download?: string | boolean;
  
  // Layout
  block?: boolean;
  
  // Events
  onClick?: (event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
  onMouseEnter?: (event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
}

export const Button = forwardRef<HTMLButtonElement | HTMLAnchorElement, ButtonProps>(({
  children,
  text,
  icon,
  iconPosition = 'left',
  variant = 'primary',
  size = 'md',
  shape = 'default',
  loading = false,
  disabled = false,
  active = false,
  type = 'button',
  href,
  target,
  download,
  block = false,
  className,
  style,
  onClick,
  onFocus,
  onBlur,
  onMouseEnter,
  onMouseLeave,
  ...props
}, ref) => {
  const { theme } = useTheme();

  // Determine if this should render as a link
  const isLink = Boolean(href);
  
  // Determine if button is disabled
  const isDisabled = disabled || loading;

  // Build class names
  const classNames = useMemo(() => {
    const classes = ['ui-button'];
    
    classes.push(`ui-button--${variant}`);
    classes.push(`ui-button--${size}`);
    classes.push(`ui-button--${shape}`);
    
    if (loading) classes.push('ui-button--loading');
    if (isDisabled) classes.push('ui-button--disabled');
    if (active) classes.push('ui-button--active');
    if (block) classes.push('ui-button--block');
    if (icon && !children && !text) classes.push('ui-button--icon-only');
    
    if (className) classes.push(className);
    
    return classes.join(' ');
  }, [variant, size, shape, loading, isDisabled, active, block, icon, children, text, className]);

  // Build button content
  const buttonContent = useMemo(() => {
    const content = children || text;
    
    if (loading) {
      return (
        <>
          <span className="ui-button__spinner" />
          {content && <span className="ui-button__text">{content}</span>}
        </>
      );
    }

    if (icon && content) {
      return iconPosition === 'left' ? (
        <>
          <span className="ui-button__icon ui-button__icon--left">{icon}</span>
          <span className="ui-button__text">{content}</span>
        </>
      ) : (
        <>
          <span className="ui-button__text">{content}</span>
          <span className="ui-button__icon ui-button__icon--right">{icon}</span>
        </>
      );
    }

    if (icon) {
      return <span className="ui-button__icon">{icon}</span>;
    }

    return <span className="ui-button__text">{content}</span>;
  }, [children, text, icon, iconPosition, loading]);

  // Common props for both button and anchor
  const commonProps = {
    className: classNames,
    style,
    onFocus,
    onBlur,
    onMouseEnter,
    onMouseLeave,
    'data-variant': variant,
    'data-size': size,
    'data-shape': shape,
    'data-loading': loading,
    'data-disabled': isDisabled,
    'data-active': active,
    ...props
  };

  // Render as link
  if (isLink) {
    return (
      <a
        ref={ref as React.Ref<HTMLAnchorElement>}
        href={isDisabled ? undefined : href}
        target={target}
        download={download}
        onClick={isDisabled ? undefined : onClick}
        aria-disabled={isDisabled}
        tabIndex={isDisabled ? -1 : undefined}
        {...commonProps}
      >
        {buttonContent}
      </a>
    );
  }

  // Render as button
  return (
    <button
      ref={ref as React.Ref<HTMLButtonElement>}
      type={type}
      disabled={isDisabled}
      onClick={onClick}
      aria-pressed={active}
      {...commonProps}
    >
      {buttonContent}
    </button>
  );
});

Button.displayName = 'Button';

// Component configuration for UI Builder
export const ButtonConfig: ComponentConfig = {
  id: 'button',
  type: 'button',
  displayName: 'Button',
  description: 'A clickable button component with various styles and states',
  category: 'form',
  tags: ['button', 'form', 'action', 'interactive'],
  version: '1.0.0',
  
  properties: {
    text: {
      type: 'string',
      label: 'Text',
      description: 'Button text content',
      defaultValue: 'Button',
      group: 'content',
      order: 1
    },
    variant: {
      type: 'select',
      label: 'Variant',
      description: 'Button style variant',
      defaultValue: 'primary',
      options: [
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Outline', value: 'outline' },
        { label: 'Ghost', value: 'ghost' },
        { label: 'Link', value: 'link' },
        { label: 'Danger', value: 'danger' }
      ],
      group: 'appearance',
      order: 2
    },
    size: {
      type: 'select',
      label: 'Size',
      description: 'Button size',
      defaultValue: 'md',
      options: [
        { label: 'Extra Small', value: 'xs' },
        { label: 'Small', value: 'sm' },
        { label: 'Medium', value: 'md' },
        { label: 'Large', value: 'lg' },
        { label: 'Extra Large', value: 'xl' }
      ],
      group: 'appearance',
      order: 3
    },
    shape: {
      type: 'select',
      label: 'Shape',
      description: 'Button shape',
      defaultValue: 'default',
      options: [
        { label: 'Default', value: 'default' },
        { label: 'Round', value: 'round' },
        { label: 'Circle', value: 'circle' }
      ],
      group: 'appearance',
      order: 4
    },
    disabled: {
      type: 'boolean',
      label: 'Disabled',
      description: 'Whether the button is disabled',
      defaultValue: false,
      group: 'state',
      order: 5
    },
    loading: {
      type: 'boolean',
      label: 'Loading',
      description: 'Whether the button is in loading state',
      defaultValue: false,
      group: 'state',
      order: 6
    },
    block: {
      type: 'boolean',
      label: 'Block',
      description: 'Whether the button should take full width',
      defaultValue: false,
      group: 'layout',
      order: 7
    },
    href: {
      type: 'url',
      label: 'Link URL',
      description: 'URL to navigate to (makes button behave as link)',
      group: 'behavior',
      order: 8
    },
    target: {
      type: 'select',
      label: 'Link Target',
      description: 'How to open the link',
      options: [
        { label: 'Same Window', value: '_self' },
        { label: 'New Window', value: '_blank' },
        { label: 'Parent Frame', value: '_parent' },
        { label: 'Top Frame', value: '_top' }
      ],
      conditional: {
        property: 'href',
        operator: 'exists'
      },
      group: 'behavior',
      order: 9
    }
  },
  
  requiredProperties: ['text'],
  
  defaultProps: {
    text: 'Button',
    variant: 'primary',
    size: 'md',
    shape: 'default',
    disabled: false,
    loading: false,
    block: false
  },
  
  styling: {
    supportedStyles: ['margin', 'padding', 'width', 'height'],
    customCSS: true,
    themes: ['light', 'dark']
  },
  
  layout: {
    canHaveChildren: false,
    maxChildren: 0
  },
  
  events: [
    {
      name: 'onClick',
      label: 'On Click',
      description: 'Triggered when button is clicked',
      parameters: [
        {
          name: 'event',
          type: 'object',
          description: 'Click event object'
        }
      ]
    },
    {
      name: 'onFocus',
      label: 'On Focus',
      description: 'Triggered when button receives focus'
    },
    {
      name: 'onBlur',
      label: 'On Blur',
      description: 'Triggered when button loses focus'
    }
  ],
  
  accessibility: {
    role: 'button',
    ariaLabels: ['aria-label', 'aria-describedby'],
    keyboardNavigation: true
  },
  
  performance: {
    lazy: false,
    preload: false,
    cacheStrategy: 'memory'
  }
};
