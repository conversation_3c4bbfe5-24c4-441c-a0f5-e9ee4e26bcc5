import 'package:flutter/material.dart';
import '../types/component_types.dart';
import '../types/variant_types.dart';
import '../foundation/design_tokens.dart';

/// UI Builder Tooltip component
class UITooltip extends StatelessWidget {
  const UITooltip({
    super.key,
    required this.message,
    required this.child,
    this.preferBelow = true,
    this.verticalOffset = 24,
    this.showDuration = const Duration(milliseconds: 1500),
    this.waitDuration = const Duration(milliseconds: 0),
  });

  final String message;
  final Widget child;
  final bool preferBelow;
  final double verticalOffset;
  final Duration showDuration;
  final Duration waitDuration;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: message,
      preferBelow: preferBelow,
      verticalOffset: verticalOffset,
      showDuration: showDuration,
      waitDuration: waitDuration,
      child: child,
    );
  }
}
