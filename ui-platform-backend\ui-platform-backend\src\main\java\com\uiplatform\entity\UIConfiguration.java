package com.uiplatform.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * UIConfiguration entity representing a complete UI page configuration.
 * This is the main entity that holds the metadata for dynamic UI pages.
 */
@Entity
@Table(name = "ui_configurations", indexes = {
    @Index(name = "idx_ui_config_name", columnList = "name"),
    @Index(name = "idx_ui_config_slug", columnList = "slug"),
    @Index(name = "idx_ui_config_organization", columnList = "organization_id"),
    @Index(name = "idx_ui_config_owner", columnList = "owner_id"),
    @Index(name = "idx_ui_config_status", columnList = "status"),
    @Index(name = "idx_ui_config_type", columnList = "type")
})
public class UIConfiguration extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @NotBlank
    @Size(max = 100)
    @Column(name = "slug", nullable = false, length = 100)
    private String slug;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private UIConfigurationType type = UIConfigurationType.PAGE;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UIConfigurationStatus status = UIConfigurationStatus.DRAFT;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "layout_config", columnDefinition = "jsonb")
    private Map<String, Object> layoutConfig = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "style_config", columnDefinition = "jsonb")
    private Map<String, Object> styleConfig = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "responsive_config", columnDefinition = "jsonb")
    private Map<String, Object> responsiveConfig = new HashMap<>();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "jsonb")
    private Map<String, Object> validationRules = new HashMap<>();

    @Column(name = "version_number", nullable = false)
    private Integer versionNumber = 1;

    @Column(name = "is_published", nullable = false)
    private Boolean isPublished = false;

    @Column(name = "is_template", nullable = false)
    private Boolean isTemplate = false;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    @Column(name = "tags")
    private String tags;

    @Column(name = "preview_url")
    private String previewUrl;

    @Column(name = "published_url")
    private String publishedUrl;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", nullable = false)
    @JsonIgnore
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", nullable = false)
    @JsonIgnore
    private User owner;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "theme_id")
    @JsonIgnore
    private Theme theme;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "layout_id")
    @JsonIgnore
    private Layout layout;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnore
    private UIConfiguration parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<UIConfiguration> children = new HashSet<>();

    @OneToMany(mappedBy = "uiConfiguration", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Component> components = new HashSet<>();

    @OneToMany(mappedBy = "uiConfiguration", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<FormField> formFields = new HashSet<>();

    // Constructors
    public UIConfiguration() {}

    public UIConfiguration(String name, String slug, Organization organization, User owner) {
        this.name = name;
        this.slug = slug;
        this.organization = organization;
        this.owner = owner;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UIConfigurationType getType() {
        return type;
    }

    public void setType(UIConfigurationType type) {
        this.type = type;
    }

    public UIConfigurationStatus getStatus() {
        return status;
    }

    public void setStatus(UIConfigurationStatus status) {
        this.status = status;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Map<String, Object> getLayoutConfig() {
        return layoutConfig;
    }

    public void setLayoutConfig(Map<String, Object> layoutConfig) {
        this.layoutConfig = layoutConfig;
    }

    public Map<String, Object> getStyleConfig() {
        return styleConfig;
    }

    public void setStyleConfig(Map<String, Object> styleConfig) {
        this.styleConfig = styleConfig;
    }

    public Map<String, Object> getResponsiveConfig() {
        return responsiveConfig;
    }

    public void setResponsiveConfig(Map<String, Object> responsiveConfig) {
        this.responsiveConfig = responsiveConfig;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public Boolean getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Boolean isTemplate) {
        this.isTemplate = isTemplate;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getPublishedUrl() {
        return publishedUrl;
    }

    public void setPublishedUrl(String publishedUrl) {
        this.publishedUrl = publishedUrl;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public User getOwner() {
        return owner;
    }

    public void setOwner(User owner) {
        this.owner = owner;
    }

    public Theme getTheme() {
        return theme;
    }

    public void setTheme(Theme theme) {
        this.theme = theme;
    }

    public Layout getLayout() {
        return layout;
    }

    public void setLayout(Layout layout) {
        this.layout = layout;
    }

    public UIConfiguration getParent() {
        return parent;
    }

    public void setParent(UIConfiguration parent) {
        this.parent = parent;
    }

    public Set<UIConfiguration> getChildren() {
        return children;
    }

    public void setChildren(Set<UIConfiguration> children) {
        this.children = children;
    }

    public Set<Component> getComponents() {
        return components;
    }

    public void setComponents(Set<Component> components) {
        this.components = components;
    }

    public Set<FormField> getFormFields() {
        return formFields;
    }

    public void setFormFields(Set<FormField> formFields) {
        this.formFields = formFields;
    }

    // Utility methods
    public void incrementVersion() {
        this.versionNumber++;
    }

    public void publish() {
        this.isPublished = true;
        this.status = UIConfigurationStatus.PUBLISHED;
    }

    public void unpublish() {
        this.isPublished = false;
        this.status = UIConfigurationStatus.DRAFT;
    }

    // Enums
    public enum UIConfigurationType {
        PAGE, SECTION, MODAL, SIDEBAR, HEADER, FOOTER, WIDGET, FORM
    }

    public enum UIConfigurationStatus {
        DRAFT, REVIEW, PUBLISHED, ARCHIVED, DEPRECATED
    }
}
