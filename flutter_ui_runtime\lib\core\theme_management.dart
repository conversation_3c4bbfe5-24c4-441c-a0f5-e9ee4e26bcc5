import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/theme_configuration.dart';

/// Theme Management System for Flutter UI Runtime
/// 
/// Provides dynamic theme switching, custom theme creation,
/// and responsive theme adaptation based on device characteristics.
class ThemeManager {
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal();

  final Map<String, ThemeConfiguration> _themes = {};
  String _currentThemeId = 'default';
  ThemeMode _themeMode = ThemeMode.system;

  /// Register a theme configuration
  void registerTheme(ThemeConfiguration theme) {
    _themes[theme.id] = theme;
  }

  /// Get theme configuration by ID
  ThemeConfiguration? getTheme(String themeId) {
    return _themes[themeId];
  }

  /// Get current theme configuration
  ThemeConfiguration? getCurrentTheme() {
    return _themes[_currentThemeId];
  }

  /// Set current theme
  void setCurrentTheme(String themeId) {
    if (_themes.contains<PERSON><PERSON>(themeId)) {
      _currentThemeId = themeId;
    }
  }

  /// Get all available themes
  List<ThemeConfiguration> getAllThemes() {
    return _themes.values.toList();
  }

  /// Set theme mode
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
  }

  /// Get current theme mode
  ThemeMode getThemeMode() {
    return _themeMode;
  }

  /// Create ThemeData from configuration
  ThemeData createThemeData(ThemeConfiguration config, {bool isDark = false}) {
    final colorScheme = isDark 
        ? _createDarkColorScheme(config.colorScheme)
        : config.colorScheme.toColorScheme();

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: config.typography.toTextTheme(),
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: config.typography.textStyles['titleLarge']?.toTextStyle(),
      ),

      // Card Theme
      cardTheme: CardTheme(
        elevation: config.shadows.small.blurRadius,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusMedium),
        ),
        shadowColor: Color(int.parse(
          config.shadows.small.color.replaceFirst('#', '0xff')
        )).withOpacity(config.shadows.small.opacity),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: config.shadows.small.blurRadius,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: config.spacing.lg,
            vertical: config.spacing.md,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          ),
          side: BorderSide(
            width: config.borders.thin,
            color: colorScheme.outline,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: config.spacing.lg,
            vertical: config.spacing.md,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: config.spacing.lg,
            vertical: config.spacing.md,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          borderSide: BorderSide(
            width: config.borders.thin,
            color: colorScheme.outline,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          borderSide: BorderSide(
            width: config.borders.thin,
            color: colorScheme.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          borderSide: BorderSide(
            width: config.borders.medium,
            color: colorScheme.primary,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
          borderSide: BorderSide(
            width: config.borders.thin,
            color: colorScheme.error,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: config.spacing.md,
          vertical: config.spacing.sm,
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        selectedColor: colorScheme.primaryContainer,
        labelStyle: config.typography.textStyles['bodyMedium']?.toTextStyle(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusRound),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: config.spacing.sm,
          vertical: config.spacing.xs,
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusLarge),
        ),
        elevation: config.shadows.large.blurRadius,
      ),

      // Bottom Sheet Theme
      bottomSheetTheme: BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(config.borders.radiusLarge),
          ),
        ),
        elevation: config.shadows.large.blurRadius,
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        thickness: config.borders.thin,
        color: colorScheme.outline.withOpacity(0.2),
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: 24,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: config.spacing.md,
          vertical: config.spacing.xs,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
        ),
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primaryContainer;
          }
          return colorScheme.surfaceVariant;
        }),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall / 2),
        ),
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return Colors.transparent;
        }),
      ),

      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: colorScheme.primary,
        inactiveTrackColor: colorScheme.surfaceVariant,
        thumbColor: colorScheme.primary,
        overlayColor: colorScheme.primary.withOpacity(0.12),
        trackHeight: 4,
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colorScheme.primary,
        linearTrackColor: colorScheme.surfaceVariant,
        circularTrackColor: colorScheme.surfaceVariant,
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarTheme(
        labelColor: colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurface.withOpacity(0.6),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: config.borders.medium,
            color: colorScheme.primary,
          ),
        ),
      ),

      // Navigation Bar Theme
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        labelTextStyle: MaterialStateProperty.all(
          config.typography.textStyles['labelSmall']?.toTextStyle(),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
        type: BottomNavigationBarType.fixed,
        elevation: config.shadows.small.blurRadius,
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primaryContainer,
        foregroundColor: colorScheme.onPrimaryContainer,
        elevation: config.shadows.medium.blurRadius,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusLarge),
        ),
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: config.typography.textStyles['bodyMedium']?.toTextStyle()?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
        ),
        behavior: SnackBarBehavior.floating,
        elevation: config.shadows.medium.blurRadius,
      ),

      // Tooltip Theme
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: colorScheme.inverseSurface,
          borderRadius: BorderRadius.circular(config.borders.radiusSmall),
        ),
        textStyle: config.typography.textStyles['bodySmall']?.toTextStyle()?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: config.spacing.sm,
          vertical: config.spacing.xs,
        ),
      ),
    );
  }

  /// Create dark color scheme from light configuration
  ColorScheme _createDarkColorScheme(ColorSchemeConfiguration lightConfig) {
    return ColorScheme.dark(
      primary: Color(int.parse(lightConfig.primary.replaceFirst('#', '0xff'))),
      onPrimary: Color(int.parse(lightConfig.onPrimary.replaceFirst('#', '0xff'))),
      secondary: Color(int.parse(lightConfig.secondary.replaceFirst('#', '0xff'))),
      onSecondary: Color(int.parse(lightConfig.onSecondary.replaceFirst('#', '0xff'))),
      error: Color(int.parse(lightConfig.error.replaceFirst('#', '0xff'))),
      onError: Color(int.parse(lightConfig.onError.replaceFirst('#', '0xff'))),
      surface: const Color(0xFF121212),
      onSurface: const Color(0xFFE0E0E0),
      background: const Color(0xFF121212),
      onBackground: const Color(0xFFE0E0E0),
    );
  }

  /// Get responsive theme based on screen size
  ThemeConfiguration getResponsiveTheme(BuildContext context, String baseThemeId) {
    final theme = _themes[baseThemeId];
    if (theme == null) return _themes[_currentThemeId]!;

    final screenWidth = MediaQuery.of(context).size.width;
    
    // Adjust spacing and sizing based on screen size
    if (screenWidth < 600) {
      // Mobile adjustments
      return theme.copyWith(
        spacing: theme.spacing.copyWith(
          xs: theme.spacing.xs * 0.8,
          sm: theme.spacing.sm * 0.8,
          md: theme.spacing.md * 0.8,
          lg: theme.spacing.lg * 0.8,
          xl: theme.spacing.xl * 0.8,
          xxl: theme.spacing.xxl * 0.8,
        ),
        typography: theme.typography.copyWith(
          textStyles: theme.typography.textStyles.map((key, style) => MapEntry(
            key,
            style.copyWith(fontSize: style.fontSize * 0.9),
          )),
        ),
      );
    } else if (screenWidth > 1200) {
      // Desktop adjustments
      return theme.copyWith(
        spacing: theme.spacing.copyWith(
          xs: theme.spacing.xs * 1.2,
          sm: theme.spacing.sm * 1.2,
          md: theme.spacing.md * 1.2,
          lg: theme.spacing.lg * 1.2,
          xl: theme.spacing.xl * 1.2,
          xxl: theme.spacing.xxl * 1.2,
        ),
        typography: theme.typography.copyWith(
          textStyles: theme.typography.textStyles.map((key, style) => MapEntry(
            key,
            style.copyWith(fontSize: style.fontSize * 1.1),
          )),
        ),
      );
    }

    return theme;
  }

  /// Clear all themes
  void clearThemes() {
    _themes.clear();
  }
}

/// Riverpod providers for theme management
final themeManagerProvider = Provider<ThemeManager>((ref) {
  return ThemeManager();
});

final currentThemeProvider = StateProvider<String>((ref) {
  return 'default';
});

final themeModeProvider = StateProvider<ThemeMode>((ref) {
  return ThemeMode.system;
});

final currentThemeDataProvider = Provider<ThemeData?>((ref) {
  final themeManager = ref.watch(themeManagerProvider);
  final currentThemeId = ref.watch(currentThemeProvider);
  final themeMode = ref.watch(themeModeProvider);
  
  final themeConfig = themeManager.getTheme(currentThemeId);
  if (themeConfig == null) return null;

  final brightness = MediaQuery.of(ref.context).platformBrightness;
  final isDark = themeMode == ThemeMode.dark || 
                 (themeMode == ThemeMode.system && brightness == Brightness.dark);

  return themeManager.createThemeData(themeConfig, isDark: isDark);
});

/// Theme Provider Widget
class ThemeProvider extends ConsumerWidget {
  final Widget child;

  const ThemeProvider({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeData = ref.watch(currentThemeDataProvider);
    final themeMode = ref.watch(themeModeProvider);

    if (themeData == null) {
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('Theme not found'),
          ),
        ),
      );
    }

    return MaterialApp(
      theme: themeData,
      darkTheme: themeData,
      themeMode: themeMode,
      home: child,
    );
  }
}

/// Initialize default themes
void initializeDefaultThemes() {
  final themeManager = ThemeManager();

  // Light theme
  themeManager.registerTheme(ThemeConfiguration(
    id: 'light',
    name: 'Light Theme',
    colorScheme: ColorSchemeConfiguration(
      primary: '#3b82f6',
      onPrimary: '#ffffff',
      secondary: '#64748b',
      onSecondary: '#ffffff',
      surface: '#ffffff',
      onSurface: '#1e293b',
      background: '#f8fafc',
      onBackground: '#1e293b',
      error: '#ef4444',
      onError: '#ffffff',
      brightness: Brightness.light,
    ),
    typography: TypographyConfiguration(
      primaryFont: FontConfiguration(
        family: 'Inter',
        weights: [FontWeight.w400, FontWeight.w500, FontWeight.w600, FontWeight.w700],
      ),
      textStyles: {
        'displayLarge': TextStyleConfiguration(fontSize: 32, fontWeight: FontWeight.w700),
        'displayMedium': TextStyleConfiguration(fontSize: 28, fontWeight: FontWeight.w600),
        'displaySmall': TextStyleConfiguration(fontSize: 24, fontWeight: FontWeight.w600),
        'headlineLarge': TextStyleConfiguration(fontSize: 22, fontWeight: FontWeight.w600),
        'headlineMedium': TextStyleConfiguration(fontSize: 20, fontWeight: FontWeight.w500),
        'headlineSmall': TextStyleConfiguration(fontSize: 18, fontWeight: FontWeight.w500),
        'titleLarge': TextStyleConfiguration(fontSize: 16, fontWeight: FontWeight.w500),
        'titleMedium': TextStyleConfiguration(fontSize: 14, fontWeight: FontWeight.w500),
        'titleSmall': TextStyleConfiguration(fontSize: 12, fontWeight: FontWeight.w500),
        'bodyLarge': TextStyleConfiguration(fontSize: 16, fontWeight: FontWeight.w400),
        'bodyMedium': TextStyleConfiguration(fontSize: 14, fontWeight: FontWeight.w400),
        'bodySmall': TextStyleConfiguration(fontSize: 12, fontWeight: FontWeight.w400),
        'labelLarge': TextStyleConfiguration(fontSize: 14, fontWeight: FontWeight.w500),
        'labelMedium': TextStyleConfiguration(fontSize: 12, fontWeight: FontWeight.w500),
        'labelSmall': TextStyleConfiguration(fontSize: 10, fontWeight: FontWeight.w500),
      },
    ),
    spacing: SpacingConfiguration(
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    ),
    borders: BorderConfiguration(
      thin: 1,
      medium: 2,
      thick: 4,
      radiusSmall: 4,
      radiusMedium: 8,
      radiusLarge: 16,
      radiusRound: 9999,
    ),
    shadows: ShadowConfiguration(
      small: ShadowDefinition(
        offsetX: 0,
        offsetY: 1,
        blurRadius: 3,
        spreadRadius: 0,
        color: '#000000',
        opacity: 0.1,
      ),
      medium: ShadowDefinition(
        offsetX: 0,
        offsetY: 4,
        blurRadius: 6,
        spreadRadius: -1,
        color: '#000000',
        opacity: 0.1,
      ),
      large: ShadowDefinition(
        offsetX: 0,
        offsetY: 10,
        blurRadius: 15,
        spreadRadius: -3,
        color: '#000000',
        opacity: 0.1,
      ),
      extraLarge: ShadowDefinition(
        offsetX: 0,
        offsetY: 25,
        blurRadius: 25,
        spreadRadius: -5,
        color: '#000000',
        opacity: 0.25,
      ),
    ),
    animations: AnimationConfiguration(
      fast: Duration(milliseconds: 150),
      normal: Duration(milliseconds: 300),
      slow: Duration(milliseconds: 500),
      defaultCurve: Curves.easeInOut,
    ),
  ));

  // Set default theme
  themeManager.setCurrentTheme('light');
}
