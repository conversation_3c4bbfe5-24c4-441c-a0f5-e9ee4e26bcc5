import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_metadata.dart';
import 'widget_registry.dart';

/// Dynamic Widget Renderer for Flutter UI Runtime
/// 
/// Renders UI components dynamically based on configuration metadata.
/// Supports nested components, conditional rendering, and responsive layouts.
class DynamicWidgetRenderer extends ConsumerWidget {
  final UIMetadata metadata;
  final Map<String, dynamic>? initialData;
  final VoidCallback? onError;

  const DynamicWidgetRenderer({
    Key? key,
    required this.metadata,
    this.initialData,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final registry = ref.watch(widgetRegistryProvider);
    final themeData = _buildThemeData(metadata.theme);

    return Theme(
      data: themeData,
      child: _buildLayout(context, metadata.configuration.layout, registry),
    );
  }

  /// Build the main layout from configuration
  Widget _buildLayout(BuildContext context, LayoutConfiguration layout, WidgetRegistry registry) {
    try {
      return _buildComponent(context, ComponentDefinition(
        id: 'root',
        type: layout.type,
        properties: {
          'mainAxisAlignment': layout.mainAxisAlignment?.toString(),
          'crossAxisAlignment': layout.crossAxisAlignment?.toString(),
          'mainAxisSize': layout.mainAxisSize?.toString(),
          'padding': layout.padding?.toJson(),
          'margin': layout.margin?.toJson(),
          ...?layout.properties,
        },
        children: layout.children,
      ), registry);
    } catch (e) {
      return _buildErrorWidget('Failed to build layout: $e');
    }
  }

  /// Build a single component from definition
  Widget _buildComponent(BuildContext context, ComponentDefinition definition, WidgetRegistry registry) {
    try {
      // Check conditional rendering
      if (definition.conditional != null && !_evaluateCondition(definition.conditional!)) {
        return const SizedBox.shrink();
      }

      // Get widget builder from registry
      final builder = registry.getBuilder(definition.type);
      if (builder == null) {
        return _buildErrorWidget('Unknown widget type: ${definition.type}');
      }

      // Build the widget
      return builder(definition, context);
    } catch (e) {
      return _buildErrorWidget('Error building ${definition.type}: $e');
    }
  }

  /// Build children widgets
  List<Widget> _buildChildren(BuildContext context, List<ComponentDefinition>? children, WidgetRegistry registry) {
    if (children == null || children.isEmpty) {
      return [];
    }

    return children.map((child) => _buildComponent(context, child, registry)).toList();
  }

  /// Evaluate conditional rendering
  bool _evaluateCondition(ConditionalConfiguration condition) {
    // Get the value to compare against
    final value = _getDataValue(condition.field);
    
    switch (condition.operator) {
      case 'equals':
        return value == condition.value;
      case 'not_equals':
        return value != condition.value;
      case 'contains':
        return value?.toString().contains(condition.value.toString()) ?? false;
      case 'greater_than':
        if (value is num && condition.value is num) {
          return value > condition.value;
        }
        return false;
      case 'less_than':
        if (value is num && condition.value is num) {
          return value < condition.value;
        }
        return false;
      case 'is_empty':
        return value == null || value.toString().isEmpty;
      case 'is_not_empty':
        return value != null && value.toString().isNotEmpty;
      default:
        return true;
    }
  }

  /// Get data value by field path
  dynamic _getDataValue(String fieldPath) {
    if (initialData == null) return null;
    
    final parts = fieldPath.split('.');
    dynamic current = initialData;
    
    for (final part in parts) {
      if (current is Map) {
        current = current[part];
      } else {
        return null;
      }
    }
    
    return current;
  }

  /// Build theme data from configuration
  ThemeData _buildThemeData(ThemeConfiguration themeConfig) {
    return ThemeData(
      colorScheme: themeConfig.colorScheme.toColorScheme(),
      textTheme: themeConfig.typography.toTextTheme(),
      // Add more theme properties as needed
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String message) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(height: 4),
          Text(
            message,
            style: TextStyle(color: Colors.red.shade800, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Responsive Widget Renderer
/// 
/// Renders widgets with responsive behavior based on screen size
class ResponsiveWidgetRenderer extends ConsumerWidget {
  final UIMetadata metadata;
  final Map<String, dynamic>? initialData;

  const ResponsiveWidgetRenderer({
    Key? key,
    required this.metadata,
    this.initialData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;
    final breakpoint = _getBreakpoint(screenSize.width, metadata.configuration.responsive);
    
    return DynamicWidgetRenderer(
      metadata: _applyResponsiveOverrides(metadata, breakpoint),
      initialData: initialData,
    );
  }

  /// Get current breakpoint based on screen width
  String _getBreakpoint(double width, ResponsiveConfiguration responsive) {
    String currentBreakpoint = responsive.defaultBreakpoint;
    
    for (final entry in responsive.breakpoints.entries) {
      final breakpoint = entry.value;
      if (width >= breakpoint.minWidth && 
          (breakpoint.maxWidth == null || width <= breakpoint.maxWidth!)) {
        currentBreakpoint = entry.key;
        break;
      }
    }
    
    return currentBreakpoint;
  }

  /// Apply responsive overrides to metadata
  UIMetadata _applyResponsiveOverrides(UIMetadata metadata, String breakpoint) {
    final breakpointConfig = metadata.configuration.responsive.breakpoints[breakpoint];
    if (breakpointConfig?.overrides == null) {
      return metadata;
    }

    // Apply overrides to components
    final updatedComponents = _applyOverridesToComponents(
      metadata.components, 
      breakpointConfig!.overrides!
    );

    return metadata.copyWith(
      components: updatedComponents,
    );
  }

  /// Apply responsive overrides to components
  List<ComponentDefinition> _applyOverridesToComponents(
    List<ComponentDefinition> components, 
    Map<String, dynamic> overrides
  ) {
    return components.map((component) {
      final componentOverrides = overrides[component.id] as Map<String, dynamic>?;
      if (componentOverrides == null) {
        return component;
      }

      return ComponentDefinition(
        id: component.id,
        type: component.type,
        name: component.name,
        properties: {
          ...component.properties,
          ...componentOverrides,
        },
        children: component.children != null 
            ? _applyOverridesToComponents(component.children!, overrides)
            : null,
        actions: component.actions,
        styling: component.styling,
        conditional: component.conditional,
      );
    }).toList();
  }
}

/// Widget Builder Extensions
/// 
/// Extension methods for building common widget patterns
extension WidgetBuilderExtensions on WidgetRegistry {
  /// Build a list of children widgets
  List<Widget> buildChildren(
    BuildContext context, 
    List<ComponentDefinition>? children
  ) {
    if (children == null || children.isEmpty) {
      return [];
    }

    return children.map((child) => buildWidget(child, context)).toList();
  }

  /// Build a single child widget
  Widget? buildChild(
    BuildContext context, 
    ComponentDefinition? child
  ) {
    if (child == null) return null;
    return buildWidget(child, context);
  }

  /// Build widget with error handling
  Widget buildWidgetSafe(
    ComponentDefinition definition, 
    BuildContext context,
    {Widget? fallback}
  ) {
    try {
      return buildWidget(definition, context);
    } catch (e) {
      return fallback ?? _buildErrorWidget('Error: $e');
    }
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Text(
        message,
        style: TextStyle(color: Colors.red.shade800, fontSize: 12),
      ),
    );
  }
}

/// Dynamic List Renderer
/// 
/// Renders dynamic lists with data binding
class DynamicListRenderer extends ConsumerWidget {
  final ComponentDefinition listDefinition;
  final List<dynamic> data;
  final ComponentDefinition itemTemplate;

  const DynamicListRenderer({
    Key? key,
    required this.listDefinition,
    required this.data,
    required this.itemTemplate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final registry = ref.watch(widgetRegistryProvider);

    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        final itemDefinition = _bindDataToTemplate(itemTemplate, item, index);
        return registry.buildWidget(itemDefinition, context);
      },
    );
  }

  /// Bind data to item template
  ComponentDefinition _bindDataToTemplate(
    ComponentDefinition template, 
    dynamic itemData, 
    int index
  ) {
    final boundProperties = <String, dynamic>{};
    
    // Bind properties with data
    for (final entry in template.properties.entries) {
      boundProperties[entry.key] = _bindValue(entry.value, itemData, index);
    }

    return ComponentDefinition(
      id: '${template.id}_$index',
      type: template.type,
      name: template.name,
      properties: boundProperties,
      children: template.children?.map((child) => 
        _bindDataToTemplate(child, itemData, index)
      ).toList(),
      actions: template.actions,
      styling: template.styling,
      conditional: template.conditional,
    );
  }

  /// Bind a single value with data
  dynamic _bindValue(dynamic value, dynamic itemData, int index) {
    if (value is String && value.startsWith('{{') && value.endsWith('}}')) {
      final expression = value.substring(2, value.length - 2).trim();
      return _evaluateExpression(expression, itemData, index);
    }
    return value;
  }

  /// Evaluate data binding expression
  dynamic _evaluateExpression(String expression, dynamic itemData, int index) {
    // Simple expression evaluation
    if (expression == 'index') return index;
    if (expression == 'item') return itemData;
    
    // Property access like 'item.name'
    if (expression.startsWith('item.')) {
      final propertyPath = expression.substring(5);
      return _getNestedProperty(itemData, propertyPath);
    }
    
    return expression;
  }

  /// Get nested property from object
  dynamic _getNestedProperty(dynamic object, String path) {
    final parts = path.split('.');
    dynamic current = object;
    
    for (final part in parts) {
      if (current is Map) {
        current = current[part];
      } else {
        return null;
      }
    }
    
    return current;
  }
}

/// Animation Renderer
/// 
/// Renders widgets with animations
class AnimatedWidgetRenderer extends ConsumerStatefulWidget {
  final ComponentDefinition definition;
  final Map<String, dynamic>? animationConfig;

  const AnimatedWidgetRenderer({
    Key? key,
    required this.definition,
    this.animationConfig,
  }) : super(key: key);

  @override
  ConsumerState<AnimatedWidgetRenderer> createState() => _AnimatedWidgetRendererState();
}

class _AnimatedWidgetRendererState extends ConsumerState<AnimatedWidgetRenderer>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    final duration = Duration(
      milliseconds: widget.animationConfig?['duration'] ?? 300
    );
    
    _controller = AnimationController(duration: duration, vsync: this);
    _animation = CurvedAnimation(
      parent: _controller,
      curve: _getCurve(widget.animationConfig?['curve']),
    );
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final registry = ref.watch(widgetRegistryProvider);
    final child = registry.buildWidget(widget.definition, context);
    
    final animationType = widget.animationConfig?['type'] ?? 'fadeIn';
    
    switch (animationType) {
      case 'fadeIn':
        return FadeTransition(opacity: _animation, child: child);
      case 'slideIn':
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(_animation),
          child: child,
        );
      case 'scaleIn':
        return ScaleTransition(scale: _animation, child: child);
      default:
        return child;
    }
  }

  Curve _getCurve(String? curveName) {
    switch (curveName) {
      case 'easeIn': return Curves.easeIn;
      case 'easeOut': return Curves.easeOut;
      case 'easeInOut': return Curves.easeInOut;
      case 'bounceIn': return Curves.bounceIn;
      case 'bounceOut': return Curves.bounceOut;
      default: return Curves.easeInOut;
    }
  }
}
