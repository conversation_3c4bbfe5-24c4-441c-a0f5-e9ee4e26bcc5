groups:
  - name: ui-builder-application
    rules:
      # High error rate alerts
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service)
            /
            sum(rate(http_requests_total[5m])) by (service)
          ) > 0.05
        for: 2m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "High error rate detected for {{ $labels.service }}"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.service }}"
          runbook_url: "https://runbooks.uibuilder.dev/high-error-rate"

      # High response time alerts
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service)
          ) > 2
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High response time for {{ $labels.service }}"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.service }}"

      # Database connection pool alerts
      - alert: DatabaseConnectionPoolExhausted
        expr: |
          hikaricp_connections_active / hikaricp_connections_max > 0.9
        for: 1m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "Connection pool usage is {{ $value | humanizePercentage }} for {{ $labels.pool }}"

      # Memory usage alerts
      - alert: HighMemoryUsage
        expr: |
          (
            jvm_memory_used_bytes{area="heap"} 
            / 
            jvm_memory_max_bytes{area="heap"}
          ) > 0.85
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High memory usage in {{ $labels.service }}"
          description: "Heap memory usage is {{ $value | humanizePercentage }}"

      # Disk space alerts
      - alert: LowDiskSpace
        expr: |
          (
            node_filesystem_avail_bytes{mountpoint="/"} 
            / 
            node_filesystem_size_bytes{mountpoint="/"}
          ) < 0.1
        for: 1m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk space is {{ $value | humanizePercentage }} full"

      # Redis connection alerts
      - alert: RedisConnectionFailure
        expr: |
          redis_up == 0
        for: 1m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Redis connection failure"
          description: "Redis instance {{ $labels.instance }} is down"

      # Kafka lag alerts
      - alert: KafkaConsumerLag
        expr: |
          kafka_consumer_lag_sum > 1000
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High Kafka consumer lag"
          description: "Consumer lag is {{ $value }} messages for {{ $labels.topic }}"

  - name: ui-builder-business-metrics
    rules:
      # User activity alerts
      - alert: LowUserActivity
        expr: |
          sum(rate(ui_builder_user_sessions_total[1h])) < 10
        for: 10m
        labels:
          severity: warning
          team: product
        annotations:
          summary: "Low user activity detected"
          description: "Only {{ $value }} user sessions in the last hour"

      # UI config creation rate
      - alert: HighUIConfigCreationRate
        expr: |
          sum(rate(ui_builder_configs_created_total[5m])) > 100
        for: 2m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Unusually high UI config creation rate"
          description: "{{ $value }} configs created per second"

      # Collaboration session alerts
      - alert: HighCollaborationSessions
        expr: |
          ui_builder_active_collaboration_sessions > 1000
        for: 5m
        labels:
          severity: info
          team: backend
        annotations:
          summary: "High number of active collaboration sessions"
          description: "{{ $value }} active collaboration sessions"

      # Template usage alerts
      - alert: TemplateUsageSpike
        expr: |
          sum(rate(ui_builder_template_usage_total[5m])) > 50
        for: 2m
        labels:
          severity: info
          team: product
        annotations:
          summary: "Template usage spike detected"
          description: "{{ $value }} template uses per second"

  - name: ui-builder-security
    rules:
      # Failed authentication attempts
      - alert: HighFailedAuthAttempts
        expr: |
          sum(rate(ui_builder_auth_failures_total[5m])) > 10
        for: 2m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "High number of failed authentication attempts"
          description: "{{ $value }} failed auth attempts per second"

      # Suspicious API activity
      - alert: SuspiciousAPIActivity
        expr: |
          sum(rate(http_requests_total{status="403"}[5m])) > 20
        for: 1m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "Suspicious API activity detected"
          description: "{{ $value }} forbidden requests per second"

      # Rate limiting triggered
      - alert: RateLimitingTriggered
        expr: |
          sum(rate(ui_builder_rate_limit_exceeded_total[5m])) > 5
        for: 1m
        labels:
          severity: info
          team: security
        annotations:
          summary: "Rate limiting frequently triggered"
          description: "{{ $value }} rate limit violations per second"

  - name: ui-builder-infrastructure
    rules:
      # Pod restart alerts
      - alert: PodRestartingFrequently
        expr: |
          increase(kube_pod_container_status_restarts_total[1h]) > 5
        for: 0m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Pod {{ $labels.pod }} restarting frequently"
          description: "Pod has restarted {{ $value }} times in the last hour"

      # Node not ready
      - alert: NodeNotReady
        expr: |
          kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Node {{ $labels.node }} not ready"
          description: "Node has been not ready for more than 5 minutes"

      # Persistent volume usage
      - alert: PersistentVolumeUsageHigh
        expr: |
          (
            kubelet_volume_stats_used_bytes 
            / 
            kubelet_volume_stats_capacity_bytes
          ) > 0.85
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Persistent volume usage high"
          description: "PV {{ $labels.persistentvolumeclaim }} is {{ $value | humanizePercentage }} full"

      # Certificate expiration
      - alert: SSLCertificateExpiringSoon
        expr: |
          probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 0m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "SSL certificate expiring soon"
          description: "Certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"

      # Website down
      - alert: WebsiteDown
        expr: |
          probe_success == 0
        for: 1m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Website {{ $labels.instance }} is down"
          description: "Website has been down for more than 1 minute"

  - name: ui-builder-performance
    rules:
      # Slow database queries
      - alert: SlowDatabaseQueries
        expr: |
          histogram_quantile(0.95, 
            sum(rate(spring_data_repository_invocations_seconds_bucket[5m])) by (le, repository)
          ) > 1
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s for {{ $labels.repository }}"

      # High CPU usage
      - alert: HighCPUUsage
        expr: |
          100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}%"

      # Cache hit rate low
      - alert: LowCacheHitRate
        expr: |
          (
            sum(rate(cache_gets_total{result="hit"}[5m])) 
            / 
            sum(rate(cache_gets_total[5m]))
          ) < 0.8
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value | humanizePercentage }} for {{ $labels.cache }}"
