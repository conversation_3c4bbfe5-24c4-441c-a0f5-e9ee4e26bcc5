package com.uiplatform.dto.collaboration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO representing user presence information.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserPresence {
    
    private UUID userId;
    private String username;
    private UUID organizationId;
    private boolean online;
    private String status; // "active", "away", "busy", "offline"
    private String statusMessage;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastSeen;
    
    private UUID currentConfigId; // Currently editing UI config
    private String currentElementId; // Currently editing element
    
    // Constructors
    public UserPresence() {}
    
    public UserPresence(UUID userId, String username, UUID organizationId, boolean online) {
        this.userId = userId;
        this.username = username;
        this.organizationId = organizationId;
        this.online = online;
        this.lastSeen = LocalDateTime.now();
        this.status = online ? "active" : "offline";
    }
    
    // Get<PERSON> and Setters
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public UUID getOrganizationId() {
        return organizationId;
    }
    
    public void setOrganizationId(UUID organizationId) {
        this.organizationId = organizationId;
    }
    
    public boolean isOnline() {
        return online;
    }
    
    public void setOnline(boolean online) {
        this.online = online;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getStatusMessage() {
        return statusMessage;
    }
    
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }
    
    public LocalDateTime getLastSeen() {
        return lastSeen;
    }
    
    public void setLastSeen(LocalDateTime lastSeen) {
        this.lastSeen = lastSeen;
    }
    
    public UUID getCurrentConfigId() {
        return currentConfigId;
    }
    
    public void setCurrentConfigId(UUID currentConfigId) {
        this.currentConfigId = currentConfigId;
    }
    
    public String getCurrentElementId() {
        return currentElementId;
    }
    
    public void setCurrentElementId(String currentElementId) {
        this.currentElementId = currentElementId;
    }
    
    @Override
    public String toString() {
        return "UserPresence{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", organizationId=" + organizationId +
                ", online=" + online +
                ", status='" + status + '\'' +
                ", lastSeen=" + lastSeen +
                ", currentConfigId=" + currentConfigId +
                '}';
    }
}
