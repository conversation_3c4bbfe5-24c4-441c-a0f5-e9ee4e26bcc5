package com.uiplatform.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uiplatform.dto.collaboration.CollaborationEvent;
import com.uiplatform.dto.collaboration.CollaborationEventType;
import com.uiplatform.entity.ActivityLog;
import com.uiplatform.repository.ActivityLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for managing activity logs and change history.
 */
@Service
@Transactional
public class ActivityLogService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityLogService.class);

    private final ActivityLogRepository activityLogRepository;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    public ActivityLogService(ActivityLogRepository activityLogRepository,
                             SimpMessagingTemplate messagingTemplate,
                             ObjectMapper objectMapper) {
        this.activityLogRepository = activityLogRepository;
        this.messagingTemplate = messagingTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Log an activity asynchronously.
     */
    @Async
    public void logActivity(UUID organizationId, UUID userId, String username,
                           ActivityLog.ActivityType activityType, String action, String description) {
        logActivity(organizationId, userId, username, activityType, action, description, null, null, null);
    }

    /**
     * Log an activity with detailed information.
     */
    @Async
    public void logActivity(UUID organizationId, UUID userId, String username,
                           ActivityLog.ActivityType activityType, String action, String description,
                           UUID uiConfigurationId, String entityType, String entityId) {
        try {
            ActivityLog log = new ActivityLog(organizationId, userId, username, activityType, action, description);
            log.setUiConfigurationId(uiConfigurationId);
            log.setEntityType(entityType);
            log.setEntityId(entityId);
            
            activityLogRepository.save(log);
            
            // Broadcast activity to real-time feed
            broadcastActivity(log);
            
            logger.debug("Logged activity: {} by user {} in organization {}", 
                        activityType, username, organizationId);
            
        } catch (Exception e) {
            logger.error("Error logging activity", e);
        }
    }

    /**
     * Log an activity with change tracking.
     */
    @Async
    public void logActivityWithChanges(UUID organizationId, UUID userId, String username,
                                     ActivityLog.ActivityType activityType, String action, String description,
                                     UUID uiConfigurationId, String entityType, String entityId,
                                     Map<String, Object> oldValues, Map<String, Object> newValues) {
        try {
            ActivityLog log = new ActivityLog(organizationId, userId, username, activityType, action, description);
            log.setUiConfigurationId(uiConfigurationId);
            log.setEntityType(entityType);
            log.setEntityId(entityId);
            
            // Serialize change data
            if (oldValues != null && !oldValues.isEmpty()) {
                log.setOldValues(objectMapper.writeValueAsString(oldValues));
            }
            if (newValues != null && !newValues.isEmpty()) {
                log.setNewValues(objectMapper.writeValueAsString(newValues));
            }
            
            activityLogRepository.save(log);
            
            // Broadcast activity with changes
            broadcastActivity(log);
            
            logger.debug("Logged activity with changes: {} by user {} in organization {}", 
                        activityType, username, organizationId);
            
        } catch (Exception e) {
            logger.error("Error logging activity with changes", e);
        }
    }

    /**
     * Log system activity.
     */
    @Async
    public void logSystemActivity(UUID organizationId, ActivityLog.ActivityType activityType, 
                                String action, String description) {
        try {
            ActivityLog log = new ActivityLog(organizationId, null, "SYSTEM", activityType, action, description);
            log.setIsSystemAction(true);
            log.setSeverity(ActivityLog.Severity.INFO);
            
            activityLogRepository.save(log);
            
            logger.debug("Logged system activity: {} in organization {}", activityType, organizationId);
            
        } catch (Exception e) {
            logger.error("Error logging system activity", e);
        }
    }

    /**
     * Log performance activity with duration.
     */
    @Async
    public void logPerformanceActivity(UUID organizationId, UUID userId, String username,
                                     ActivityLog.ActivityType activityType, String action, String description,
                                     long durationMs) {
        try {
            ActivityLog log = new ActivityLog(organizationId, userId, username, activityType, action, description);
            log.setDurationMs(durationMs);
            
            // Set severity based on duration
            if (durationMs > 5000) {
                log.setSeverity(ActivityLog.Severity.WARNING);
            } else if (durationMs > 10000) {
                log.setSeverity(ActivityLog.Severity.ERROR);
            }
            
            activityLogRepository.save(log);
            
            logger.debug("Logged performance activity: {} took {}ms", activityType, durationMs);
            
        } catch (Exception e) {
            logger.error("Error logging performance activity", e);
        }
    }

    /**
     * Get activity feed for an organization.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getActivityFeed(UUID organizationId, Pageable pageable) {
        logger.debug("Fetching activity feed for organization: {}", organizationId);
        return activityLogRepository.findByOrganizationId(organizationId, pageable);
    }

    /**
     * Get activity feed for a UI configuration.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getUIConfigurationActivityFeed(UUID configId, Pageable pageable) {
        logger.debug("Fetching activity feed for UI configuration: {}", configId);
        return activityLogRepository.findByUIConfigurationId(configId, pageable);
    }

    /**
     * Get user activity history.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getUserActivityHistory(UUID userId, Pageable pageable) {
        logger.debug("Fetching activity history for user: {}", userId);
        return activityLogRepository.findByUserId(userId, pageable);
    }

    /**
     * Get recent activities.
     */
    @Transactional(readOnly = true)
    public List<ActivityLog> getRecentActivities(UUID organizationId, int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return activityLogRepository.findRecentActivityByOrganization(organizationId, since);
    }

    /**
     * Get recent activities for a UI configuration.
     */
    @Transactional(readOnly = true)
    public List<ActivityLog> getRecentUIConfigurationActivities(UUID configId, int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return activityLogRepository.findRecentActivityByUIConfiguration(configId, since);
    }

    /**
     * Search activities by description.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> searchActivities(String searchText, Pageable pageable) {
        logger.debug("Searching activities for text: {}", searchText);
        return activityLogRepository.searchByDescription(searchText, pageable);
    }

    /**
     * Get activities by type.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getActivitiesByType(ActivityLog.ActivityType activityType, Pageable pageable) {
        return activityLogRepository.findByActivityType(activityType, pageable);
    }

    /**
     * Get activities by severity.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getActivitiesBySeverity(ActivityLog.Severity severity, Pageable pageable) {
        return activityLogRepository.findBySeverity(severity, pageable);
    }

    /**
     * Get error activities.
     */
    @Transactional(readOnly = true)
    public Page<ActivityLog> getErrorActivities(Pageable pageable) {
        return activityLogRepository.findErrorActivities(pageable);
    }

    /**
     * Get activity statistics.
     */
    @Transactional(readOnly = true)
    public ActivityLogRepository.ActivityStatistics getActivityStatistics(UUID organizationId, 
                                                                          LocalDateTime startTime, 
                                                                          LocalDateTime endTime) {
        return activityLogRepository.getActivityStatistics(organizationId, startTime, endTime);
    }

    /**
     * Get daily activity counts.
     */
    @Transactional(readOnly = true)
    public List<Object[]> getDailyActivityCounts(UUID organizationId, LocalDateTime startTime, LocalDateTime endTime) {
        return activityLogRepository.getDailyActivityCounts(organizationId, startTime, endTime);
    }

    /**
     * Get most active users.
     */
    @Transactional(readOnly = true)
    public List<Object[]> getMostActiveUsers(UUID organizationId, LocalDateTime startTime, 
                                           LocalDateTime endTime, Pageable pageable) {
        return activityLogRepository.findMostActiveUsers(organizationId, startTime, endTime, pageable);
    }

    /**
     * Get most modified UI configurations.
     */
    @Transactional(readOnly = true)
    public List<Object[]> getMostModifiedConfigurations(UUID organizationId, LocalDateTime startTime, 
                                                       LocalDateTime endTime, Pageable pageable) {
        return activityLogRepository.findMostModifiedConfigurations(organizationId, startTime, endTime, pageable);
    }

    /**
     * Get entity change history.
     */
    @Transactional(readOnly = true)
    public List<ActivityLog> getEntityChangeHistory(String entityType, String entityId) {
        return activityLogRepository.findByEntityTypeAndEntityId(entityType, entityId);
    }

    /**
     * Get session activities.
     */
    @Transactional(readOnly = true)
    public List<ActivityLog> getSessionActivities(String sessionId) {
        return activityLogRepository.findBySessionId(sessionId);
    }

    /**
     * Scheduled cleanup of old activity logs.
     */
    @Scheduled(cron = "0 0 2 * * ?") // Run daily at 2 AM
    public void cleanupOldActivities() {
        try {
            // Keep activities for 90 days
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(90);
            activityLogRepository.deleteOldActivities(cutoffDate);
            
            logger.info("Cleaned up activity logs older than {}", cutoffDate);
            
        } catch (Exception e) {
            logger.error("Error during activity log cleanup", e);
        }
    }

    /**
     * Export activities to JSON.
     */
    @Transactional(readOnly = true)
    public String exportActivities(UUID organizationId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Page<ActivityLog> activities = activityLogRepository.findByTimestampBetween(
                startTime, endTime, Pageable.unpaged());
            
            return objectMapper.writeValueAsString(activities.getContent());
            
        } catch (Exception e) {
            logger.error("Error exporting activities", e);
            throw new RuntimeException("Failed to export activities: " + e.getMessage());
        }
    }

    // Private helper methods

    private void broadcastActivity(ActivityLog log) {
        try {
            CollaborationEvent event = new CollaborationEvent();
            event.setType(CollaborationEventType.CHANGE_APPLIED);
            event.setUserId(log.getUserId());
            event.setUsername(log.getUsername());
            event.setConfigId(log.getUiConfigurationId());
            event.setData(log);
            event.setTimestamp(log.getTimestamp());
            
            // Broadcast to organization activity feed
            messagingTemplate.convertAndSend(
                "/topic/organization/" + log.getOrganizationId() + "/activity", 
                event
            );
            
            // Broadcast to UI configuration activity feed if applicable
            if (log.getUiConfigurationId() != null) {
                messagingTemplate.convertAndSend(
                    "/topic/ui-config/" + log.getUiConfigurationId() + "/activity", 
                    event
                );
            }
            
        } catch (Exception e) {
            logger.error("Error broadcasting activity", e);
        }
    }
}
