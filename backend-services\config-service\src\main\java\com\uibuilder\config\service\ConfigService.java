package com.uibuilder.config.service;

import com.uibuilder.config.dto.ConfigDto;
import com.uibuilder.config.dto.ConfigCreateRequest;
import com.uibuilder.config.dto.ConfigUpdateRequest;
import com.uibuilder.config.dto.ConfigSearchRequest;
import com.uibuilder.config.model.Config;
import com.uibuilder.config.model.ConfigVersion;
import com.uibuilder.config.repository.ConfigRepository;
import com.uibuilder.config.repository.ConfigVersionRepository;
import com.uibuilder.config.mapper.ConfigMapper;
import com.uibuilder.config.exception.ConfigNotFoundException;
import com.uibuilder.config.exception.UnauthorizedAccessException;
import com.uibuilder.config.exception.ConfigValidationException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service class for managing UI configurations
 * 
 * Provides business logic for CRUD operations, versioning,
 * validation, and search functionality for UI configurations.
 */
@Service
@Transactional
public class ConfigService {

    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private ConfigVersionRepository configVersionRepository;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private ConfigValidationService validationService;

    @Autowired
    private ConfigExportService exportService;

    @Autowired
    private ConfigImportService importService;

    /**
     * Get all configurations with pagination and filtering
     */
    @Transactional(readOnly = true)
    public Page<ConfigDto> getAllConfigs(ConfigSearchRequest searchRequest, int page, int size, String sortBy, String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Specification<Config> spec = createSpecification(searchRequest);
        Page<Config> configs = configRepository.findAll(spec, pageable);
        
        return configs.map(configMapper::toDto);
    }

    /**
     * Get a configuration by ID
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "configs", key = "#id")
    public ConfigDto getConfigById(UUID id, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        return configMapper.toDto(config);
    }

    /**
     * Get a specific version of a configuration
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "configVersions", key = "#id + '_' + #version")
    public ConfigDto getConfigVersion(UUID id, int version, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        ConfigVersion configVersion = configVersionRepository
            .findByConfigIdAndVersion(id, version)
            .orElseThrow(() -> new ConfigNotFoundException("Configuration version not found: " + version));
            
        return configMapper.toDto(configVersion);
    }

    /**
     * Get all versions of a configuration
     */
    @Transactional(readOnly = true)
    public List<ConfigDto> getConfigVersions(UUID id, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        List<ConfigVersion> versions = configVersionRepository
            .findByConfigIdOrderByVersionDesc(id);
            
        return versions.stream()
            .map(configMapper::toDto)
            .collect(Collectors.toList());
    }

    /**
     * Create a new configuration
     */
    @CacheEvict(value = {"configs", "configStats"}, allEntries = true)
    public ConfigDto createConfig(ConfigCreateRequest request) {
        // Validate the configuration
        validationService.validateCreateRequest(request);
        
        Config config = configMapper.toEntity(request);
        config.setId(UUID.randomUUID());
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());
        config.setVersion(1);
        config.setStatus("DRAFT");
        
        Config savedConfig = configRepository.save(config);
        
        // Create initial version
        createConfigVersion(savedConfig);
        
        return configMapper.toDto(savedConfig);
    }

    /**
     * Update an existing configuration
     */
    @CacheEvict(value = {"configs", "configVersions"}, key = "#id")
    public ConfigDto updateConfig(UUID id, ConfigUpdateRequest request, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        // Validate the update
        validationService.validateUpdateRequest(request, config);
        
        // Update configuration
        configMapper.updateEntity(config, request);
        config.setUpdatedAt(LocalDateTime.now());
        config.setUpdatedBy(request.getUpdatedBy());
        
        // Increment version if content changed
        if (hasContentChanged(config, request)) {
            config.setVersion(config.getVersion() + 1);
            createConfigVersion(config);
        }
        
        Config savedConfig = configRepository.save(config);
        return configMapper.toDto(savedConfig);
    }

    /**
     * Delete a configuration
     */
    @CacheEvict(value = {"configs", "configVersions", "configStats"}, allEntries = true)
    public void deleteConfig(UUID id, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        // Soft delete - mark as deleted
        config.setDeletedAt(LocalDateTime.now());
        config.setDeletedBy(userId);
        configRepository.save(config);
    }

    /**
     * Duplicate a configuration
     */
    @CacheEvict(value = {"configs", "configStats"}, allEntries = true)
    public ConfigDto duplicateConfig(UUID id, String name, UUID userId, UUID organizationId) {
        Config originalConfig = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        Config duplicatedConfig = configMapper.duplicate(originalConfig);
        duplicatedConfig.setId(UUID.randomUUID());
        duplicatedConfig.setName(name != null ? name : originalConfig.getName() + " (Copy)");
        duplicatedConfig.setCreatedBy(userId);
        duplicatedConfig.setCreatedAt(LocalDateTime.now());
        duplicatedConfig.setUpdatedAt(LocalDateTime.now());
        duplicatedConfig.setVersion(1);
        duplicatedConfig.setStatus("DRAFT");
        
        Config savedConfig = configRepository.save(duplicatedConfig);
        createConfigVersion(savedConfig);
        
        return configMapper.toDto(savedConfig);
    }

    /**
     * Validate a configuration
     */
    public Object validateConfig(ConfigDto configDto) {
        return validationService.validateConfiguration(configDto);
    }

    /**
     * Export a configuration
     */
    @Transactional(readOnly = true)
    public String exportConfig(UUID id, String format, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        return exportService.exportConfig(config, format);
    }

    /**
     * Import a configuration
     */
    @CacheEvict(value = {"configs", "configStats"}, allEntries = true)
    public ConfigDto importConfig(String data, String format, UUID userId, UUID organizationId) {
        Config config = importService.importConfig(data, format, userId, organizationId);
        Config savedConfig = configRepository.save(config);
        createConfigVersion(savedConfig);
        
        return configMapper.toDto(savedConfig);
    }

    /**
     * Publish a configuration
     */
    @CacheEvict(value = {"configs", "configVersions"}, key = "#id")
    public ConfigDto publishConfig(UUID id, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        // Validate before publishing
        Object validationResult = validationService.validateConfiguration(configMapper.toDto(config));
        if (validationService.hasErrors(validationResult)) {
            throw new ConfigValidationException("Cannot publish configuration with validation errors");
        }
        
        config.setStatus("PUBLISHED");
        config.setPublishedAt(LocalDateTime.now());
        config.setPublishedBy(userId);
        config.setUpdatedAt(LocalDateTime.now());
        
        Config savedConfig = configRepository.save(config);
        return configMapper.toDto(savedConfig);
    }

    /**
     * Unpublish a configuration
     */
    @CacheEvict(value = {"configs", "configVersions"}, key = "#id")
    public ConfigDto unpublishConfig(UUID id, UUID userId, UUID organizationId) {
        Config config = findConfigByIdAndValidateAccess(id, userId, organizationId);
        
        config.setStatus("DRAFT");
        config.setPublishedAt(null);
        config.setPublishedBy(null);
        config.setUpdatedAt(LocalDateTime.now());
        
        Config savedConfig = configRepository.save(config);
        return configMapper.toDto(savedConfig);
    }

    /**
     * Get configuration statistics
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "configStats", key = "#userId + '_' + #organizationId")
    public Object getConfigStats(UUID userId, UUID organizationId) {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalConfigs", configRepository.countByOrganizationIdAndDeletedAtIsNull(organizationId));
        stats.put("publishedConfigs", configRepository.countByOrganizationIdAndStatusAndDeletedAtIsNull(organizationId, "PUBLISHED"));
        stats.put("draftConfigs", configRepository.countByOrganizationIdAndStatusAndDeletedAtIsNull(organizationId, "DRAFT"));
        stats.put("userConfigs", configRepository.countByCreatedByAndDeletedAtIsNull(userId));
        
        return stats;
    }

    /**
     * Search configurations
     */
    @Transactional(readOnly = true)
    public Page<ConfigDto> searchConfigs(ConfigSearchRequest request, int page, int size, String sortBy, String sortDir) {
        return getAllConfigs(request, page, size, sortBy, sortDir);
    }

    // Private helper methods

    private Config findConfigByIdAndValidateAccess(UUID id, UUID userId, UUID organizationId) {
        Config config = configRepository.findByIdAndDeletedAtIsNull(id)
            .orElseThrow(() -> new ConfigNotFoundException("Configuration not found: " + id));
            
        if (!config.getOrganizationId().equals(organizationId)) {
            throw new UnauthorizedAccessException("Access denied to configuration: " + id);
        }
        
        return config;
    }

    private void createConfigVersion(Config config) {
        ConfigVersion version = new ConfigVersion();
        version.setId(UUID.randomUUID());
        version.setConfigId(config.getId());
        version.setVersion(config.getVersion());
        version.setName(config.getName());
        version.setDescription(config.getDescription());
        version.setConfiguration(config.getConfiguration());
        version.setCreatedAt(LocalDateTime.now());
        version.setCreatedBy(config.getUpdatedBy() != null ? config.getUpdatedBy() : config.getCreatedBy());
        
        configVersionRepository.save(version);
    }

    private boolean hasContentChanged(Config config, ConfigUpdateRequest request) {
        // Compare configuration content to determine if version should be incremented
        return !Objects.equals(config.getConfiguration(), request.getConfiguration()) ||
               !Objects.equals(config.getName(), request.getName()) ||
               !Objects.equals(config.getDescription(), request.getDescription());
    }

    private Specification<Config> createSpecification(ConfigSearchRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Organization filter
            if (request.getOrganizationId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("organizationId"), request.getOrganizationId()));
            }
            
            // Not deleted filter
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            
            // Search filter
            if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
                String searchPattern = "%" + request.getSearch().toLowerCase() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")), searchPattern);
                Predicate descriptionPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("description")), searchPattern);
                predicates.add(criteriaBuilder.or(namePredicate, descriptionPredicate));
            }
            
            // Category filter
            if (request.getCategory() != null && !request.getCategory().trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("category"), request.getCategory()));
            }
            
            // Tags filter
            if (request.getTags() != null && !request.getTags().isEmpty()) {
                // This would need to be implemented based on how tags are stored
                // For now, assuming tags are stored as JSON array in a text field
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
